<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.gclife</groupId>
        <artifactId>gclife-parent</artifactId>
        <version>1.0</version>
        <relativePath></relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.gclife</groupId>
    <artifactId>gclife-business-core</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>报表模块</description>

    <properties>
        <!-- BASE -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <!-- DOCKER -->
        <docker.plugin.version>0.4.14</docker.plugin.version>

    </properties>

    <modules>
        <module>gclife-report-core</module>
        <module>gclife-report-base</module>
        <module>gclife-report-service</module>
    </modules>

    <dependencies>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-platform</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-attachment</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-party</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-agent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-apply</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-thirdparty</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-renewal</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-payment</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-policy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-endorse</artifactId>
        </dependency>

        <dependency>
            <groupId>com.gclife</groupId>
            <artifactId>gclife-api-claim</artifactId>
        </dependency>

    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <!--  The plugin rewrites your manifest  -->
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.7</version>
                    <executions>
                        <execution>
                            <id>prepare-dockerfile</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>copy-resources</goal>
                            </goals>
                            <configuration>
                                <outputDirectory>${project.build.directory}/docker</outputDirectory>
                                <resources>
                                    <resource>
                                        <directory>docker</directory>
                                        <filtering>true</filtering>
                                    </resource>
                                </resources>
                            </configuration>
                        </execution>
                    </executions>
                    <configuration>
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                            <nonFilteredFileExtension>jks</nonFilteredFileExtension>
                            <nonFilteredFileExtension>html</nonFilteredFileExtension>
                            <nonFilteredFileExtension>css</nonFilteredFileExtension>
                            <nonFilteredFileExtension>js</nonFilteredFileExtension>
                            <nonFilteredFileExtension>lic</nonFilteredFileExtension>
                            <nonFilteredFileExtension>sh</nonFilteredFileExtension>
                            <nonFilteredFileExtension>gz</nonFilteredFileExtension>
                            <nonFilteredFileExtension>tar</nonFilteredFileExtension>
                            <nonFilteredFileExtension>zip</nonFilteredFileExtension>
                            <nonFilteredFileExtension>sql</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>it.ozimov</groupId>
                    <artifactId>yaml-properties-maven-plugin</artifactId>
                    <version>1.1.3</version>
                    <executions>
                        <execution>
                            <phase>initialize</phase>
                            <goals>
                                <goal>read-project-properties</goal>
                            </goals>
                            <configuration>
                                <files>
                                    <file>src/main/resources/application.yml</file>
                                </files>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <imageName>${project.artifactId}</imageName>
                        <dockerDirectory>${project.build.directory}/docker</dockerDirectory>
                        <!-- <baseImage>java:8</baseImage>
                        <entryPoint>["java", "-jar", "/${project.build.finalName}.jar"]</entryPoint> -->
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                    </configuration>
                </plugin>


                <!--<plugin>-->
                <!--<artifactId>maven-assembly-plugin</artifactId>-->
                <!--<configuration>-->
                <!--<archive>-->
                <!--<manifest>-->
                <!--<mainClass>com.gclife.platform.ServiceApplication</mainClass>-->
                <!--</manifest>-->
                <!--</archive>-->
                <!--<descriptorRefs>-->
                <!--<descriptorRef>jar-with-dependencies</descriptorRef>-->
                <!--</descriptorRefs>-->
                <!--</configuration>-->
                <!--</plugin>-->
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>

        <repository>
            <id>nexus</id>
            <name>Releases</name>
            <url>http://mvn-repo:8081/repository/gclife-mvn-releases/</url>
        </repository>

        <snapshotRepository>
            <id>nexus</id>
            <name>Snapshot</name>
            <url>http://mvn-repo:8081/repository/gclife-mvn-snapshots/</url>
        </snapshotRepository>

    </distributionManagement>

</project>