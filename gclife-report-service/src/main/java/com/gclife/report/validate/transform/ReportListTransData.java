package com.gclife.report.validate.transform;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.model.response.PolicyGroupReportUnderwritingResponse;
import com.gclife.policy.model.response.PolicyReportUnderwritingResponse;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.core.jooq.tables.pojos.ReportCustomerAgentPo;
import com.gclife.report.model.bo.ReportGroupPolicyBo;
import com.gclife.report.model.bo.ReportGroupRenewalBo;
import com.gclife.report.model.bo.ReportPolicyBo;
import com.gclife.report.model.bo.ReportRenewalBo;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.request.ReportOperationRequest;
import com.gclife.report.model.response.ReportOperationTypeResponse;
import com.gclife.report.model.response.ReportPolicyListResponse;
import com.gclife.report.model.response.ReportRenewalListResponse;
import com.gclife.report.service.ReportGroupRenewalBaseService;
import com.gclife.report.service.ReportQueryBaseService;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.gclife.report.model.config.ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH;

/**
 * <AUTHOR>
 * create 19-1-7
 * description:
 */
@Component
public class ReportListTransData extends BaseBusinessServiceImpl {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReportListTransData.class);
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Autowired
    private AgentApi agentApi;

    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    @Autowired
    private LanguageUtils languageUtils;
    @Autowired
    private ReportQueryBaseService reportQueryBaseService;
    @Autowired
    private ReportGroupRenewalBaseService reportGroupRenewalBaseService;

    public List<ReportRenewalListResponse> getReportRenewalList(Users users, List<ReportRenewalBo> reportRenewalBos, String renewalType) {
        List<ReportRenewalListResponse> reportRenewalListResponses = new ArrayList<>();
        if (!AssertUtils.isNotEmpty(reportRenewalBos)) {
            return reportRenewalListResponses;
        }

        String language = users.getLanguage();

        List<String> codeTypes = Arrays.asList(
                TerminologyTypeEnum.PAYMENT_METHODS.name(),
                TerminologyTypeEnum.POLICY_STATUS.name(),
                TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(),
                TerminologyTypeEnum.PAYMENT_STATUS.name(),
                TerminologyTypeEnum.RENEWAL_STATUS.name(),
                TerminologyTypeEnum.RENEWAL_INSURANCE_STATUS.name(),
                TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(),
                TerminologyTypeEnum.IS_HEAD_OFFICE.name(),
                TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name()
        );
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(language, codeTypes);
        Map<String, List<SyscodeResponse>> data = mapResultObject.getData();

        List<SyscodeResponse> paymentMethodsRespFcs = data.get(TerminologyTypeEnum.PAYMENT_METHODS.name());
        List<SyscodeResponse> policyStatusRespFcs = data.get(TerminologyTypeEnum.POLICY_STATUS.name());
        List<SyscodeResponse> premiumPeriodUnit = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name());
        List<SyscodeResponse> paymentStatusRespFcs = data.get(TerminologyTypeEnum.PAYMENT_STATUS.name());
        List<SyscodeResponse> renewalStatusRespFcs = data.get(TerminologyTypeEnum.RENEWAL_STATUS.name());
        List<SyscodeResponse> renewalInsuranceStatusRespFcs = data.get(TerminologyTypeEnum.RENEWAL_INSURANCE_STATUS.name());
        List<SyscodeResponse> premiumFrequencyRespFcs = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
        List<SyscodeResponse> productMainProductFlagRespFcs = data.get(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name());
        List<SyscodeResponse> isHeadOfficeRespFcs = data.get(TerminologyTypeEnum.IS_HEAD_OFFICE.name());

        List<String> agentIds = reportRenewalBos.stream().filter(reportRenewalBo -> AssertUtils.isNotEmpty(reportRenewalBo.getAgentId())).map(ReportRenewalBo::getAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest applyAgentReqFc = new AgentApplyQueryRequest();
        applyAgentReqFc.setListAgentId(agentIds);
        List<AgentResponse> agentRespFcs = agentApi.agentsGet(applyAgentReqFc).getData();


        reportRenewalBos.forEach(reportRenewalBo -> {
            //附加险没有续保设置缴费方式为 空
            if (TerminologyConfigEnum.WHETHER.NO.name().equals(reportRenewalBo.getRenewalPermitFlag())) {
                reportRenewalBo.setPaymentMethodCode(null);
                reportRenewalBo.setApplyDate(null);
                reportRenewalBo.setConfirmDate(null);
                reportRenewalBo.setPaymentDate(null);
            }

            ReportRenewalListResponse reportRenewalListResponse = (ReportRenewalListResponse) this.converterObject(reportRenewalBo, ReportRenewalListResponse.class);

            //特殊处理 将"[\"a\",\"b\"]"转换成对应的国际化
            List<String> methodCodeTrans = new ArrayList<>();
            List<String> paymentMethodCodes = AssertUtils.isNotEmpty(reportRenewalBo.getPaymentMethodCode()) ? (List<String>) JSON.parse(reportRenewalBo.getPaymentMethodCode()) : null;
            if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                paymentMethodCodes.forEach(paymentMethodCode -> methodCodeTrans.add(languageUtils.findCodeName(paymentMethodsRespFcs, paymentMethodCode)));
                String paymentMethodCode = StringUtils.join(methodCodeTrans.toArray(), ",");
                reportRenewalListResponse.setPaymentMethod(paymentMethodCode);
            }

            if (AssertUtils.isNotEmpty(agentRespFcs) && AssertUtils.isNotEmpty(reportRenewalBo.getAgentId())) {
                agentRespFcs.stream().filter(applyAgentRespFc -> reportRenewalBo.getAgentId().equals(applyAgentRespFc.getAgentId())).findFirst().ifPresent((value) -> {
                    reportRenewalListResponse.setAgentName(value.getAgentName() + "(" + value.getAgentCode() + ")");
                    // 设置初始服务业务人员
                    reportRenewalListResponse.setServiceAgentCode(value.getAgentCode());
                    reportRenewalListResponse.setServiceAgentName(value.getAgentName());
                    reportRenewalListResponse.setServiceAgentMobil(value.getMobile());
                });
            }
            //日期格式化
            reportRenewalListResponse.setApproveDate(DateUtils.timeStrToString(reportRenewalBo.getApproveDate(), DateUtils.FORMATE3));
            reportRenewalListResponse.setApplyDate(DateUtils.timeStrToString(reportRenewalBo.getApplyDate(), DateUtils.FORMATE3));
            reportRenewalListResponse.setPaymentDate(DateUtils.timeStrToString(reportRenewalBo.getPaymentDate(), DateUtils.FORMATE3));
            reportRenewalListResponse.setConfirmDate(DateUtils.timeStrToString(reportRenewalBo.getConfirmDate(), DateUtils.FORMATE3));
            reportRenewalListResponse.setRenewalDate(DateUtils.timeStrToString(reportRenewalBo.getRenewalDate(), DateUtils.FORMATE3));
            reportRenewalListResponse.setPaymentEndDate(DateUtils.timeStrToString(reportRenewalBo.getPaymentEndDate(), DateUtils.FORMATE3));
            reportRenewalListResponse.setProductName(reportRenewalBo.getCoverageProductName());

            reportRenewalListResponse.setPolicyStatus(languageUtils.findCodeName(policyStatusRespFcs, reportRenewalBo.getPolicyStatus()));
            reportRenewalListResponse.setPaymentStatus(languageUtils.findCodeName(paymentStatusRespFcs, reportRenewalBo.getPaymentStatus()));
            if (ReportTermEnum.RENEWAL_TYPE.RENEWAL.name().equals(renewalType)) {
                reportRenewalListResponse.setRenewalStatus(languageUtils.findCodeName(renewalStatusRespFcs, reportRenewalBo.getRenewalStatus()));
            }
            if (ReportTermEnum.RENEWAL_TYPE.RENEWAL_INSURANCE.name().equals(renewalType)) {
                reportRenewalListResponse.setRenewalStatus(languageUtils.findCodeName(renewalInsuranceStatusRespFcs, reportRenewalBo.getRenewalStatus()));
            }
            reportRenewalListResponse.setPremiumFrequency(languageUtils.findCodeName(premiumFrequencyRespFcs, reportRenewalBo.getPremiumFrequency()));
            reportRenewalListResponse.setPrimaryFlagName(languageUtils.findCodeName(productMainProductFlagRespFcs, reportRenewalBo.getPrimaryFlag()));
            reportRenewalListResponse.setRenewalPermitFlag(languageUtils.findCodeName(isHeadOfficeRespFcs, reportRenewalBo.getRenewalPermitFlag()));
            //缴费周期
            if (AssertUtils.isNotEmpty(reportRenewalBo.getCoveragePremiumPeriod()) && AssertUtils.isNotEmpty(reportRenewalBo.getCoveragePremiumPeriodUnit())) {
                reportRenewalListResponse.setPremiumPeriod(reportRenewalBo.getCoveragePremiumPeriod() + languageUtils.findCodeName(premiumPeriodUnit, reportRenewalBo.getCoveragePremiumPeriodUnit()));
                if ("1".equals(reportRenewalBo.getPremiumPeriod()) && ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(reportRenewalBo.getPremiumPeriodUnit())) {
                    reportRenewalListResponse.setPremiumPeriod(languageUtils.findCodeName(premiumPeriodUnit, reportRenewalBo.getPremiumPeriodUnit()));
                }
            }
            reportRenewalListResponses.add(reportRenewalListResponse);
        });
        return reportRenewalListResponses;
    }

    public String getPaymentMethodCode(List<SyscodeResponse> syscodeResponses, String paymentMethodCode) {
        String newPaymentMethodCode = paymentMethodCode;
        if (AssertUtils.isNotEmpty(paymentMethodCode)) {
            List<String> methodCodeTrans = new ArrayList<>();
            List<String> paymentMethodCodes = (List<String>) JSON.parse(paymentMethodCode);
            if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                paymentMethodCodes.forEach(s -> methodCodeTrans.add(languageUtils.getCodeName(syscodeResponses, s)));
                newPaymentMethodCode = StringUtils.join(methodCodeTrans.toArray(), ",");
            }
        }
        return newPaymentMethodCode;
    }

    public List<ReportPolicyListResponse> transReportPolicyList(Users users, List<ReportPolicyBo> reportPolicyBos) {
        List<ReportPolicyListResponse> reportPolicyListResponse = new ArrayList<>();
        if (!AssertUtils.isNotEmpty(reportPolicyBos)) {
            return reportPolicyListResponse;
        }
        List<String> codeTypes = Arrays.asList(
                TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(),
                TerminologyTypeEnum.PAYMENT_METHODS.name(),
                TerminologyTypeEnum.CHANNEL_TYPE.name(),
                TerminologyTypeEnum.POLICY_STATUS.name(),
                TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(),
                TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name()
        );
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(users.getLanguage(), codeTypes);
        Map<String, List<SyscodeResponse>> data = mapResultObject.getData();

        //缴费周期
        List<SyscodeResponse> premiumFrequencyRespFcs = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
        //缴费方式
        List<SyscodeResponse> paymentMethodsRespFcs = data.get(TerminologyTypeEnum.PAYMENT_METHODS.name());
        //渠道类型
        List<SyscodeResponse> channelTypeRespFcs = data.get(TerminologyTypeEnum.CHANNEL_TYPE.name());
        //保单状态
        List<SyscodeResponse> policyStatusRespFcs = data.get(TerminologyTypeEnum.POLICY_STATUS.name());
        //主附险标示
        List<SyscodeResponse> mainProductFlagRespFcs = data.get(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name());
        List<SyscodeResponse> coveragePeriodUnitRespFcs = data.get(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name());

        List<String> agentIds = reportPolicyBos.stream().filter(reportPolicyBo -> AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())).map(ReportPolicyBo::getAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest applyAgentReqFc = new AgentApplyQueryRequest();
        applyAgentReqFc.setListAgentId(agentIds);
        List<AgentResponse> agentRespFcs = agentApi.agentsGet(applyAgentReqFc).getData();

        reportPolicyBos.forEach(reportPolicyBo -> {
            ReportPolicyListResponse reportPolicy = new ReportPolicyListResponse();
            ClazzUtils.copyPropertiesIgnoreNull(reportPolicyBo, reportPolicy);
            reportPolicy.setPremiumFrequency(languageUtils.findCodeName(premiumFrequencyRespFcs, reportPolicyBo.getPremiumFrequency()));

            //特殊处理 将"[\"a\",\"b\"]"转换成对应的国际化
            List<String> methodCodeTrans = new ArrayList<>();
            List<String> paymentMethodCodes = AssertUtils.isNotEmpty(reportPolicyBo.getPaymentMethodCode()) ? (List<String>) JSON.parse(reportPolicyBo.getPaymentMethodCode()) : null;
            if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                paymentMethodCodes.forEach(s -> {
                    methodCodeTrans.add(languageUtils.findCodeName(paymentMethodsRespFcs, s));
                });
                String paymentMethodCode = StringUtils.join(methodCodeTrans.toArray(), ",");

                reportPolicy.setPaymentMethodCode(paymentMethodCode);
            }

            if (AssertUtils.isNotEmpty(agentRespFcs) && AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())) {
                agentRespFcs.stream().filter(applyAgentRespFc -> reportPolicyBo.getAgentId().equals(applyAgentRespFc.getAgentId())).findFirst().ifPresent((value) -> {
                    reportPolicy.setAgentCodeAgentName(value.getAgentName() + "/" + value.getAgentCode());
                    // 设置初始服务业务人员
                    reportPolicy.setServiceAgentCode(value.getAgentCode());
                    reportPolicy.setServiceAgentName(value.getAgentName());
                    reportPolicy.setServiceAgentMobil(value.getMobile());
                });
            }

            ResultObject<BranchResponse> saleBranches = platformBranchBaseApi.queryOneBranchById(reportPolicyBo.getSalesBranchId());
            reportPolicy.setPolicyStatus(languageUtils.findCodeName(policyStatusRespFcs, reportPolicyBo.getPolicyStatus()));
            reportPolicy.setPrimaryFlag(languageUtils.findCodeName(mainProductFlagRespFcs, reportPolicyBo.getPrimaryFlag()));
            reportPolicy.setSalesBranchChannelType(saleBranches.getData().getBranchName() + "/" + languageUtils.findCodeName(channelTypeRespFcs, reportPolicyBo.getChannelTypeCode()));
            reportPolicy.setCreateDate(DateUtils.timeStrToString(reportPolicyBo.getCreateDate(), DateUtils.FORMATE6));
            reportPolicy.setApproveDate(DateUtils.timeStrToString(reportPolicyBo.getApproveDate(), DateUtils.FORMATE6));
            reportPolicy.setReceivableDate(DateUtils.timeStrToString(reportPolicyBo.getReceivableDate(), DateUtils.FORMATE6));
            reportPolicy.setUpdateDate(DateUtils.timeStrToString(reportPolicyBo.getUpdateDate(), DateUtils.FORMATE6));
            reportPolicy.setProductTypeCode(reportPolicyBo.getProductType());
            if (AssertUtils.isNotEmpty(reportPolicyBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(reportPolicyBo.getCoveragePeriodUnit())) {
                reportPolicy.setCoveragePeriod(reportPolicyBo.getCoveragePeriod() + languageUtils.findCodeName(coveragePeriodUnitRespFcs, reportPolicyBo.getCoveragePeriodUnit()));
            }
            if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_19.id(), ProductTermEnum.PRODUCT.PRODUCT_20.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(reportPolicyBo.getProductId())
                    && AssertUtils.isNotNull(reportPolicyBo.getTotalPremium()) && MONTH.name().equals(reportPolicyBo.getPrimaryFlag())) {
                reportPolicy.setTotalPremium(reportPolicyBo.getTotalPremium().divide(new BigDecimal(3), 2, BigDecimal.ROUND_HALF_UP));
            }
            reportPolicyListResponse.add(reportPolicy);
        });
        return reportPolicyListResponse;
    }

    public ReportOperationTypeResponse getApprovePolicyByType(ReportOperationRequest reportOperationRequest, String agentTypeCode) {
        List<ReportPolicyBo> lifeConsultantReportPolicyBos = reportQueryBaseService.queryReportPolicys(reportOperationRequest, agentTypeCode, null);
        List<ReportGroupPolicyBo> lifeConsultantReportGroupPolicyBos = reportQueryBaseService.queryReportGroupPolicys(reportOperationRequest, agentTypeCode, null);
        ReportOperationTypeResponse approvePolicy = new ReportOperationTypeResponse();
        if (AssertUtils.isNotEmpty(lifeConsultantReportPolicyBos)) {
            approvePolicy.setNumber(AssertUtils.isNotNull(lifeConsultantReportPolicyBos.get(0).getTotalLine()) ? lifeConsultantReportPolicyBos.get(0).getTotalLine() : 0);
            approvePolicy.setTotalPremium((AssertUtils.isNotNull(lifeConsultantReportPolicyBos.get(0).getTotalPremium()) ? lifeConsultantReportPolicyBos.get(0).getTotalPremium() : 0).toString());
            approvePolicy.setNewApplicantNumber(AssertUtils.isNotNull(lifeConsultantReportPolicyBos.get(0).getNewApplicant()) ? lifeConsultantReportPolicyBos.get(0).getNewApplicant() : 0);
            approvePolicy.setNewInsuredNumber(AssertUtils.isNotNull(lifeConsultantReportPolicyBos.get(0).getNewInsured()) ? lifeConsultantReportPolicyBos.get(0).getNewInsured() : 0);
        } else {
            approvePolicy.setNumber(0);
            approvePolicy.setTotalPremium("0");
            approvePolicy.setNewApplicantNumber(0);
            approvePolicy.setNewInsuredNumber(0);
        }

        if (AssertUtils.isNotEmpty(lifeConsultantReportGroupPolicyBos)) {
            approvePolicy.setNumber(AssertUtils.isNotNull(lifeConsultantReportGroupPolicyBos.get(0).getTotalLine()) ? approvePolicy.getNumber() + lifeConsultantReportGroupPolicyBos.get(0).getTotalLine() : approvePolicy.getNumber());
            approvePolicy.setTotalPremium(AssertUtils.isNotNull(lifeConsultantReportGroupPolicyBos.get(0).getTotalPremium()) ? new BigDecimal(approvePolicy.getTotalPremium()).add(lifeConsultantReportGroupPolicyBos.get(0).getTotalPremium()).toString() : approvePolicy.getTotalPremium());
            approvePolicy.setNewApplicantNumber(AssertUtils.isNotNull(lifeConsultantReportGroupPolicyBos.get(0).getNewApplicant()) ? approvePolicy.getNewApplicantNumber() + lifeConsultantReportGroupPolicyBos.get(0).getNewApplicant() : approvePolicy.getNewApplicantNumber());
        }
        return approvePolicy;
    }

    public ReportOperationTypeResponse getRevokeByType(ReportOperationRequest reportOperationRequest, String agentTypeCode, String policyStatus) {
        List<ReportPolicyBo> reportPolicyBos = reportQueryBaseService.queryReportPolicys(reportOperationRequest, agentTypeCode, policyStatus);
        ReportOperationTypeResponse hesitationRevoke = new ReportOperationTypeResponse();
        if (AssertUtils.isNotEmpty(reportPolicyBos)) {
            hesitationRevoke.setNumber(AssertUtils.isNotNull(reportPolicyBos.get(0).getTotalLine()) ? reportPolicyBos.get(0).getTotalLine() : 0);
            hesitationRevoke.setTotalPremium((AssertUtils.isNotNull(reportPolicyBos.get(0).getTotalPremium()) ? reportPolicyBos.get(0).getTotalPremium() : 0).toString());
        } else {
            hesitationRevoke.setNumber(0);
            hesitationRevoke.setTotalPremium("0");
        }
        return hesitationRevoke;
    }

    public ReportOperationTypeResponse RenewalData(ReportOperationRequest reportOperationRequest, String agentTypeCode, String renewalType, List<String> renewalStatus) {
        List<ReportRenewalBo> reportRenewalBos = reportQueryBaseService.queryReportRenewal(reportOperationRequest, agentTypeCode, renewalType, renewalStatus);
        List<ReportGroupRenewalBo> reportGroupRenewalBoList = reportGroupRenewalBaseService.queryReportRenewal(reportOperationRequest, agentTypeCode, renewalType, renewalStatus);
        ReportOperationTypeResponse reportOperationTypeResponse = new ReportOperationTypeResponse();
        reportOperationTypeResponse.setNumber(0);
        reportOperationTypeResponse.setTotalPremium("0");
        if (AssertUtils.isNotEmpty(reportRenewalBos)) {
            reportOperationTypeResponse.setNumber(AssertUtils.isNotNull(reportRenewalBos.get(0).getTotalLine()) ? reportRenewalBos.get(0).getTotalLine() : 0);
            reportOperationTypeResponse.setTotalPremium((AssertUtils.isNotNull(reportRenewalBos.get(0).getTotalPremium()) ? reportRenewalBos.get(0).getTotalPremium() : 0).toString());
        }
        if (AssertUtils.isNotEmpty(reportGroupRenewalBoList) && AssertUtils.isNotNull(reportGroupRenewalBoList.get(0))) {
            ReportGroupRenewalBo reportGroupRenewalBo = reportGroupRenewalBoList.get(0);
            reportOperationTypeResponse.setNumber(reportGroupRenewalBo.getTotalLine() + reportOperationTypeResponse.getNumber());
            reportOperationTypeResponse.setTotalPremium(reportGroupRenewalBo.getTotalPremium().add(new BigDecimal(reportOperationTypeResponse.getTotalPremium())).toString());
        }
        return reportOperationTypeResponse;
    }

    public ReportOperationTypeResponse getTotalOperation(ReportOperationTypeResponse reportOperationTypeResponse1, ReportOperationTypeResponse reportOperationTypeResponse2) {
        ReportOperationTypeResponse reportOperationTypeResponse = new ReportOperationTypeResponse();
        reportOperationTypeResponse.setNumber((AssertUtils.isNotNull(reportOperationTypeResponse1.getNumber()) ? reportOperationTypeResponse1.getNumber() : 0) + (AssertUtils.isNotNull(reportOperationTypeResponse2.getNumber()) ? reportOperationTypeResponse2.getNumber() : 0));
        reportOperationTypeResponse.setTotalPremium(new BigDecimal(AssertUtils.isNotEmpty(reportOperationTypeResponse1.getTotalPremium()) ? reportOperationTypeResponse1.getTotalPremium() : "0").add(new BigDecimal(AssertUtils.isNotEmpty(reportOperationTypeResponse2.getTotalPremium()) ? reportOperationTypeResponse2.getTotalPremium() : "0")).toString());
        reportOperationTypeResponse.setNewApplicantNumber((AssertUtils.isNotNull(reportOperationTypeResponse1.getNewApplicantNumber()) ? reportOperationTypeResponse1.getNewApplicantNumber() : 0) + (AssertUtils.isNotNull(reportOperationTypeResponse2.getNewApplicantNumber()) ? reportOperationTypeResponse2.getNewApplicantNumber() : 0));
        reportOperationTypeResponse.setNewInsuredNumber((AssertUtils.isNotNull(reportOperationTypeResponse1.getNewInsuredNumber()) ? reportOperationTypeResponse1.getNewInsuredNumber() : 0) + (AssertUtils.isNotNull(reportOperationTypeResponse2.getNewInsuredNumber()) ? reportOperationTypeResponse2.getNewInsuredNumber() : 0));
        return reportOperationTypeResponse;
    }

    public ReportCustomerAgentPo transReportCustomerAgent(PolicyReportUnderwritingResponse reportPolicy, String customerType) {
        ReportCustomerAgentPo reportCustomerAgentPo = new ReportCustomerAgentPo();
        reportCustomerAgentPo.setAgentId(reportPolicy.getAgentId());
        reportCustomerAgentPo.setPolicyId(reportPolicy.getPolicyId());
        reportCustomerAgentPo.setCustomerType(customerType);
        if (customerType.equals(ReportTermEnum.CUSTOMER_TYPE.APPLICANT.name())) {
            reportCustomerAgentPo.setCustomerId(reportPolicy.getApplicantCustomerId());
            reportCustomerAgentPo.setFirstPolicyDate(reportPolicy.getApplicantFirstPolicyDate());
        } else {
            reportCustomerAgentPo.setCustomerId(reportPolicy.getInsuredCustomerId());
            reportCustomerAgentPo.setFirstPolicyDate(reportPolicy.getInsuredFirstPolicyDate());
        }
        return reportCustomerAgentPo;
    }

    public ReportCustomerAgentPo transReportCustomerAgent(PolicyGroupReportUnderwritingResponse reportPolicy, String customerType) {
        ReportCustomerAgentPo reportCustomerAgentPo = new ReportCustomerAgentPo();
        reportCustomerAgentPo.setAgentId(reportPolicy.getAgentId());
        reportCustomerAgentPo.setPolicyId(reportPolicy.getPolicyId());
        reportCustomerAgentPo.setCustomerType(customerType);
        reportCustomerAgentPo.setCustomerId(reportPolicy.getApplicantCustomerId());
        reportCustomerAgentPo.setFirstPolicyDate(reportPolicy.getApplicantFirstPolicyDate());
        return reportCustomerAgentPo;
    }
}
