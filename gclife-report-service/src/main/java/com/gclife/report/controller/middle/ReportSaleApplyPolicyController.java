package com.gclife.report.controller.middle;

import com.gclife.apply.model.respone.ApplyRemarksResponse;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.report.model.response.ReportRemarkResponse;
import com.gclife.report.model.vo.ReportSaleApplyPolicyVo;
import com.gclife.report.model.response.ReportSaleApplyPolicyListResponse;
import com.gclife.report.service.business.ReportSaleApplyPolicyBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @program: gclife-report-service
 * @description: 销售报表控制层
 * @author: baizhongying
 * @create: 2021-02-03 12:48
 **/
@Api(tags = "销售报表", description = "销售报表")
@RestController
@RequestMapping(value = "/v1/report/sale")
public class ReportSaleApplyPolicyController extends BaseController {
    @Autowired
    private ReportSaleApplyPolicyBusinessService reportSaleApplyPolicyBusinessService;
    @ApiOperation(value = "分页查询销售报表", notes = "分页查询销售报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "apply/policy/list")
    public ResultObject<BasePageResponse<ReportSaleApplyPolicyListResponse>> querySaleApplyPolicyPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        reportSaleApplyPolicyVo.setReportType("SALES");
        return reportSaleApplyPolicyBusinessService.querySaleApplyPolicyPageList(this.getCurrentLoginUsers(), reportSaleApplyPolicyVo);
    }

    @ApiOperation(value = "导出销售报表", notes = "导出销售报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "apply/policy/export")
    public void exportReportSaleApplyPolicyList(HttpServletResponse httpServletResponse,ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) throws IOException {
        reportSaleApplyPolicyVo.setReportType("SALES");
        reportSaleApplyPolicyBusinessService.exportReportSaleApplyPolicyList(httpServletResponse, this.getCurrentLoginUsers(), reportSaleApplyPolicyVo);
    }

    @ApiOperation(value = "查看备注详情", notes = "查看备注详情")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "remarks")
    public ResultObject<List<ReportRemarkResponse>> getRemarkList(String saleId) {
        return reportSaleApplyPolicyBusinessService.getRemarkList(saleId);
    }

    @ApiOperation(value = "分页查询待报表", notes = "分页查询待报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "pending/list")
    public ResultObject<BasePageResponse<ReportSaleApplyPolicyListResponse>> queryPendingApplyPolicyPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        reportSaleApplyPolicyVo.setReportType("UW");
        return reportSaleApplyPolicyBusinessService.querySaleApplyPolicyPageList(this.getCurrentLoginUsers(), reportSaleApplyPolicyVo);
    }

    @ApiOperation(value = "导出待报表", notes = "导出待报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "pending/export")
    public void exportReportPendingApplyPolicyList(HttpServletResponse httpServletResponse,ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) throws IOException {
        reportSaleApplyPolicyVo.setReportType("UW");
        reportSaleApplyPolicyBusinessService.exportReportSaleApplyPolicyList(httpServletResponse, this.getCurrentLoginUsers(), reportSaleApplyPolicyVo);
    }
}
