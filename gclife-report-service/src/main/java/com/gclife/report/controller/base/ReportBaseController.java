package com.gclife.report.controller.base;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import com.gclife.report.model.request.ReportUWSpecialTreatmentRequest;
import com.gclife.report.service.base.ReportBaseBusinessService;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.*;

/**
 * <AUTHOR>
 * create 18-11-7
 * description:
 */
@Api(tags = "报表基础服务", description = "报表基础服务")
@RestController
@RequestMapping(value = "/v1/report/")
public class ReportBaseController extends BaseController {
    @Autowired
    private ReportBaseBusinessService reportBaseBusinessService;

    @ApiOperation(value = "批量同步财务数据", notes = "批量同步财务数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageSize", value = "每一页的大小", paramType = "query", required = true),
            @ApiImplicitParam(name = "currentPage", value = "当前页", paramType = "query", required = true)
    })
    @GetMapping(value = "sync/payment")
    public String syncReportPayment(@RequestParam(name = "pageSize") Integer pageSize,
                                    @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportPayment(basePageRequest);
    }

    @ApiOperation(value = "实收业绩报表实收时间特殊处理(暂收转实收)", notes = "实收业绩报表实收时间特殊处理(暂收转实收)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "actual/performance/special/treatment")
    public ResultObject<Void> actualPerformanceSpecialTreatment(@RequestParam(value = "paymentId") String paymentId, @RequestParam(value = "actualPayDate") Long actualPayDate) {
        return reportBaseBusinessService.actualPerformanceSpecialTreatment(paymentId, actualPayDate);
    }

    @ApiOperation(value = "批量同步投保人资料", notes = "批量同步投保人资料")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/customer")
    public String syncReportCustomer(@RequestParam(name = "pageSize") Integer pageSize,
                                     @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportCustomer(basePageRequest);
    }

    @ApiOperation(value = "批量同步被保人资料", notes = "批量同步被保人资料")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/insured")
    public String syncReportInsured(@RequestParam(name = "pageSize") Integer pageSize,
                                    @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportInsured(basePageRequest);
    }

    @ApiOperation(value = "批量同步查询承保清单", notes = "批量同步查询承保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/policy")
    public String syncReportPolicy(@RequestParam(name = "pageSize") Integer pageSize,
                                   @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportPolicy(basePageRequest);
    }

    @ApiOperation(value = "批量同步查询团险承保清单", notes = "批量同步查询承保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/group/policy")
    public String syncReportGroupPolicy(@RequestParam(name = "pageSize") Integer pageSize,
                                        @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportGroupPolicy(basePageRequest);
    }

    @ApiOperation(value = "批量同步查询监管报表-承保清单", notes = "批量同步查询监管报表-承保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/regulatory/policy")
    public String syncReportRegulatoryPolicy(@RequestParam(name = "pageSize") Integer pageSize,
                                             @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportRegulatoryPolicy(basePageRequest);
    }


    @ApiOperation(value = "批量同步续期清单", notes = "批量同步续期清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/renewal")
    public String syncReportRenewal(@RequestParam(name = "pageSize") Integer pageSize,
                                    @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportRenewal(basePageRequest);
    }


    @ApiOperation(value = "批量同步续期清单", notes = "批量同步续期清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/group/renewal")
    public String syncReportGroupRenewal(@RequestParam(name = "pageSize") Integer pageSize,
                                         @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportGroupRenewal(basePageRequest);
    }

    @ApiOperation(value = "删除续期失效数据(由renewal服务调用)", notes = "删除续期失效数据(由renewal服务调用)")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "delete/invalid/renewal")
    public ResultObject deleteInvalidRenewal(@RequestBody List<String> renewalIds) {
        return reportBaseBusinessService.deleteInvalidRenewal(renewalIds);
    }

    @ApiOperation(value = "批量同步理赔报表", notes = "批量同步理赔报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/claim")
    public String syncReportClaim(@RequestParam(name = "pageSize") Integer pageSize,
                                  @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportClaim(basePageRequest);
    }

    @ApiOperation(value = "批量同步业务员报表", notes = "批量同步业务员报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/agent")
    public String syncReportAgent(@RequestParam(name = "pageSize") Integer pageSize,
                                  @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportAgent(basePageRequest);
    }

    @ApiOperation(value = "批量同步客户回访报表", notes = "批量同步客户回访报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/return/visit")
    public String syncReportReturnVisit(@RequestParam(name = "pageSize") Integer pageSize,
                                        @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportReturnVisit(basePageRequest);
    }

    @ApiOperation(value = "批量同步保全报表", notes = "批量同步保全报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/endorse")
    public String syncReportEndorse(@RequestParam(name = "pageSize") Integer pageSize,
                                    @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportEndorse(basePageRequest);
    }

    @ApiOperation(value = "执行月度统计报表", notes = "执行月度统计报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/monthly/statistics")
    public ResultObject<Void> syncMonthlyStatistics() {
        return reportBaseBusinessService.syncMonthlyStatistics();
    }

    @ApiOperation(value = "月度统计特殊处理", notes = "月度统计特殊处理")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "statistics/special/treatment")
    public ResultObject<Void> monthlyStatisticsSpecialTreatment(@RequestBody ReportUWSpecialTreatmentRequest reportUWSpecialTreatmentRequest) {
        return reportBaseBusinessService.monthlyStatisticsSpecialTreatment(reportUWSpecialTreatmentRequest);
    }


    @ApiOperation(value = "保存 准备金提取报表 数据", notes = "保存 准备金提取报表 数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "quarterly/statistics/reserve/withdrawal/report")
    public ResultObject<Void> syncReserveWithdrawalReport(@RequestBody List<ReserveWithdrawalReportBo> reserveWithdrawalReportBoList) {
        return reportBaseBusinessService.syncReserveWithdrawalReport(reserveWithdrawalReportBoList);
    }

    @ApiOperation(value = "生成 准备金提取报表 附件", notes = "生成 准备金提取报表 附件")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "save/reserve/withdrawal/attachment")
    public ResultObject saveReserveWithdrawalAttachment(@RequestParam(name = "quarterDate") String quarterDate) throws IOException {
        reportBaseBusinessService.saveReserveWithdrawalAttachment(quarterDate, ZH_CN.name());
        reportBaseBusinessService.saveReserveWithdrawalAttachment(quarterDate, EN_US.name());
        ResultObject resultObject = new ResultObject();
        return resultObject;
    }


    @ApiOperation(value = "执行同步银保渠道手续费费用明细表", notes = "执行同步银保渠道手续费费用明细表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/service/charge/bank/channel")
    public ResultObject<Void> syncServiceChargeBankChannel() {
        return reportBaseBusinessService.syncServiceChargeBankChannel();
    }


    @ApiOperation(value = "执行同步银保渠道手续费费用明细表-支付明细", notes = "执行同步银保渠道手续费费用明细表-支付明细")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/service/charge/bank/channel/payment")
    public ResultObject<Void> syncServiceChargeBankChannelPayment() {
        return reportBaseBusinessService.syncServiceChargeBankChannelPayment();
    }


    @ApiOperation(value = "销售投保单保单数据同步", notes = "销售投保单保单数据同步")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/sale/apply/policy")
    public ResultObject<Void> syncSaleApplyPolicy() throws Exception {
        // 同步销售申请信息到报表系统
        reportBaseBusinessService.syncSaleApply();
        //睡两秒，防止数据读取问题
        Thread.sleep(2000);
        // 同步销售策略，确保数据一致性
        return reportBaseBusinessService.syncSalePolicy();
    }

    @ApiOperation(value = "生成月度预缴保费报表", notes = "生成月度预缴保费报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "sync/suspense")
    public ResultObject syncSuspense()  {
        return reportBaseBusinessService.syncSuspense();
    }

    @ApiOperation(value = "批量同步学生发展基金报表", notes = "批量同步学生发展基金报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/group/sdf/policy")
    public String syncReportGroupPolicySdf(@RequestParam(name = "pageSize") Integer pageSize,
                                        @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return reportBaseBusinessService.syncReportGroupPolicySdf(basePageRequest);
    }
}
