package com.gclife.report.controller;

import com.gclife.common.controller.base.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021/5/19
 */
@Api(tags = "测试接口", description = "测试接口")
@RefreshScope
@RestController
@RequestMapping("v1/test/")
public class TestController extends BaseController {
    @Value("${test.configDynamicRefresh:nothing config}")
    private String configInfo;

    @ApiOperation(value = "修改配置动态刷新测试接口", notes = "修改配置动态刷新测试接口")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping("dynamic/refresh")
    public String testConfigUpdateDynamicRefresh() {
        return "configInfo： " + configInfo;
    }
}
