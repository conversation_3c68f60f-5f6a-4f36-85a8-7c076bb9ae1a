package com.gclife.report.controller.middle;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.ResultObject;
import com.gclife.report.model.response.ReportMarketListResponse;
import com.gclife.report.model.vo.ReportMonthlyStatisticsVo;
import com.gclife.report.service.business.ReportMarketBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ Author     ：bzy
 * @ Date       ：Created in 下午3:17 2020/3/24
 * @ Description： 市场月度统计
 * @ Modified By：
 * @Version:
 */
@Api(tags = "市场月度统计", description = "市场月度统计")
@RestController
@RequestMapping(value = "/v1/report/market")
public class ReportMarketController  extends BaseController {


    @Autowired
    private ReportMarketBusinessService reportMarketBusinessService;

    @ApiOperation(value = "市场月度统计列表", notes = "市场月度统计列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "list")
    public ResultObject<List<ReportMarketListResponse>> queryReportMarketList(ReportMonthlyStatisticsVo reportMarketVo) {
        return reportMarketBusinessService.queryReportMarketList(this.getCurrentLoginUsers(), reportMarketVo);
    }

    @ApiOperation(value = "导出市场月度统计列表", notes = "导出市场月度统计列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @GetMapping(value = "export/statistics")
    @ResponseBody
    public void exportReportMarketData(HttpServletResponse httpServletResponse, ReportMonthlyStatisticsVo reportMarketVo) throws Exception {
        reportMarketBusinessService.exportReportMarketData(httpServletResponse,this.getCurrentLoginUsers(), reportMarketVo);
    }

}
