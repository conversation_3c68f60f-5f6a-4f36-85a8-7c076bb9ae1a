package com.gclife.report.controller.middle;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.report.model.response.ReportGroupRenewalListResponse;
import com.gclife.report.model.vo.ReportGroupRenewalVo;
import com.gclife.report.service.business.ReportGroupRenewalBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * create 19-1-7
 * description:
 */
@Api(tags = "团险续保清单", description = "团险续保清单")
@RestController
@RequestMapping(value = "/v1/report/group/renewal")
public class ReportGroupRenewalController extends BaseController {
    @Autowired
    private ReportGroupRenewalBusinessService reportGroupRenewalBusinessService;

    @ApiOperation(value = "分页查询团险续保清单", notes = "分页查询团险续保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<ReportGroupRenewalListResponse>> queryRenewalPageList(ReportGroupRenewalVo groupRenewalVo) {
        return reportGroupRenewalBusinessService.queryRenewalPageList(this.getCurrentLoginUsers(), groupRenewalVo);
    }

    @ApiOperation(value = "导出团险续保清单", notes = "导出团险续保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "export")
    public void exportReportRenewalList(HttpServletResponse httpServletResponse, @RequestBody ReportGroupRenewalVo groupRenewalVo) throws IOException {
        reportGroupRenewalBusinessService.exportReportRenewalList(httpServletResponse, this.getCurrentLoginUsers(), groupRenewalVo);
    }

}
