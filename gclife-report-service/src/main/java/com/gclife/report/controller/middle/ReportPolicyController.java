package com.gclife.report.controller.middle;

import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.configuration.system.version.ApiVersion;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.request.ReportCashTransactionRequest;
import com.gclife.report.model.request.ReportPolicyListRequest;
import com.gclife.report.model.response.ReportCashTransactionResponse;
import com.gclife.report.model.response.ReportPolicyInitResponse;
import com.gclife.report.model.response.ReportPolicyListResponse;
import com.gclife.report.service.business.ReportPolicyBusinessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * create 18-11-16
 * description:
 */
@Api(tags = "查询承保清单", description = "查询承保清单")
@RestController
@RequestMapping(value = "/v1/report/policy")
public class ReportPolicyController extends BaseController {
    @Autowired
    private ReportPolicyBusinessService reportPolicyBusinessService;

    @ApiOperation(value = "list", notes = "分页查询承保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "list")
    public ResultObject<BasePageResponse<ReportPolicyListResponse>> queryPolicyPageList(ReportPolicyListRequest reportPolicyListRequest) {
        reportPolicyListRequest.setReportType(ReportTermEnum.REPORT_TYPE.POLICY.name());
        return reportPolicyBusinessService.queryReportPolicyPageList(this.getCurrentLoginUsers(), reportPolicyListRequest);
    }

    @ApiOperation(value = "分页查询监管报表-承保清单", notes = "分页查询监管报表-承保清单")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "regulatory/list")
    public ResultObject<BasePageResponse<ReportPolicyListResponse>> queryRegulatoryPolicyPageList(ReportPolicyListRequest reportPolicyListRequest) {
        reportPolicyListRequest.setReportType(ReportTermEnum.REPORT_TYPE.REGULATORY_POLICY.name());
        return reportPolicyBusinessService.queryReportPolicyPageList(this.getCurrentLoginUsers(), reportPolicyListRequest);
    }

    @ApiOperation(value = "导出承保清单列表", notes = "导出承保清单列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "export/receivable/list")
    public ResultObject exportReportPolicyList(HttpServletResponse httpServletResponse, @RequestBody ReportPolicyListRequest reportPolicyListRequest) {
        return reportPolicyBusinessService.exportReportPolicyList(httpServletResponse, this.getCurrentLoginUsers(), reportPolicyListRequest);
    }

    @ApiOperation(value = "init", notes = "数据字典")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "init")
    public ResultObject<ReportPolicyInitResponse> queryPolicyPageinit() {
        return reportPolicyBusinessService.initReportPolicy(this.getCurrentLoginUsers());
    }

    @ApiOperation(value = "导出承保清单列表--长期险", notes = "导出承保清单列表--长期险")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "export/receivable/long")
    public void exportReportPolicyLong(HttpServletResponse httpServletResponse, @RequestBody ReportPolicyListRequest reportPolicyListRequest) {
        reportPolicyBusinessService.exportReportPolicyLong(httpServletResponse, this.getCurrentLoginUsers(), reportPolicyListRequest);
    }

    @ApiOperation(value = "导出承保清单列表--短期险", notes = "导出承保清单列表--短期险")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiVersion(1)
    @AutoCheckPermissions
    @PostMapping(value = "export/receivable/short")
    public void exportReportPolicyShort(HttpServletResponse httpServletResponse, @RequestBody ReportPolicyListRequest reportPolicyListRequest) {
        reportPolicyBusinessService.exportReportPolicyShort(httpServletResponse, this.getCurrentLoginUsers(), reportPolicyListRequest);
    }

    @ApiOperation(value = "分页查询监管报表-现金交易", notes = "分页查询监管报表-现金交易")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "cash/transaction/list")
    public ResultObject<BasePageResponse<ReportCashTransactionResponse>> queryCashTransactionPageList(ReportCashTransactionRequest reportCashTransactionRequest) {
        return reportPolicyBusinessService.queryCashTransactionPageList(this.getCurrentLoginUsers(), reportCashTransactionRequest);
    }

    @ApiOperation(value = "导出现金交易报表", notes = "导出现金交易报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping("export/cash/transaction")
    public void exportCashTransaction(HttpServletResponse httpServletResponse, @RequestBody ReportCashTransactionRequest reportCashTransactionRequest){
        reportPolicyBusinessService.exportCashTransaction(httpServletResponse,this.getCurrentLoginUsers(), reportCashTransactionRequest);
    }

    @ApiOperation(value = "钉钉消息推送月度保费实缴超额预警", notes = "钉钉消息推送月度保费实缴超额预警")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "send/monthly/cash/transaction/msg")
    public ResultObject<Void> sendMonthlyCashTransactionMsg(Long currentMonth) {
        return reportPolicyBusinessService.sendMonthlyCashTransactionMsg(currentMonth);
    }
}