package com.gclife.report.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentListResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.ApplyPlanApi;
import com.gclife.apply.model.respone.ApplyRemarksResponse;
import com.gclife.apply.model.respone.PartnerInfoResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.response.PolicyServiceAgentResponse;
import com.gclife.product.model.response.ProductResponse;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import com.gclife.report.core.jooq.tables.pojos.ReportAttachmentPo;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.core.jooq.tables.pojos.ReportSaleApplyPolicyPo;
import com.gclife.report.model.bo.ReportSaleApplyPolicyBo;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.response.ReportRemarkResponse;
import com.gclife.report.model.response.ReportSaleApplyPolicyListResponse;
import com.gclife.report.model.vo.ReportSaleApplyPolicyVo;
import com.gclife.report.service.ReportSaleApplyPolicyBaseService;
import com.gclife.report.service.business.ReportSaleApplyPolicyBusinessService;
import com.gclife.report.validate.transform.LanguageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.common.model.config.TerminologyTypeEnum.*;
import static com.gclife.report.model.config.ReportTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP;
import static com.gclife.report.model.config.ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;

/**
 * @program: gclife-report-service
 * @description:
 * @author: baizhongying
 * @create: 2021-02-03 13:09
 **/
@Slf4j
@Service
public class ReportSaleApplyPolicyBusinessServiceImpl extends BaseBusinessServiceImpl implements ReportSaleApplyPolicyBusinessService {

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private LanguageUtils languageUtils;
    @Autowired
    private ReportSaleApplyPolicyBaseService reportSaleApplyPolicyBaseService;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    @Autowired
    private AttachmentApi attachmentApi;
    public Map<String, List<SyscodeResponse>> mapSyscodeList;

    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private ApplyPlanApi applyPlanApi;

    @Override
    public ResultObject<BasePageResponse<ReportSaleApplyPolicyListResponse>> querySaleApplyPolicyPageList(Users currentLoginUsers, ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        ResultObject<BasePageResponse<ReportSaleApplyPolicyListResponse>> resultObject = new ResultObject<>();
        reportSaleApplyPolicyVo.setLanguage(currentLoginUsers.getLanguage());
        List<String> codeTypes = Arrays.asList(TerminologyTypeEnum.APPLY_STATUS.name(), COMPANY_ID_TYPE.name(), DATE_TYPE.name(), ID_TYPE.name(), CHANNEL_TYPE.name(), TerminologyTypeEnum.POLICY_STATUS.name(),
                PRODUCT_PREMIUM_FREQUENCY.name(), PRODUCT_COVERAGE_PERIOD_UNIT.name(), PRODUCT_PREMIUM_PERIOD_UNIT.name());
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
        mapSyscodeList = mapResultObject.getData();
        ResultObject<List<String>> listResultObject = policyApi.queryPolicyNos(reportSaleApplyPolicyVo.getKeyword());
        if (AssertUtils.isNotNull(listResultObject)) {
            List<String> policyIds = listResultObject.getData();
            if (AssertUtils.isNotEmpty(policyIds)) {
                reportSaleApplyPolicyVo.setPolicyIds(policyIds);
            }

        }
        List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBoList = querySaleApplyPolicyList(reportSaleApplyPolicyVo);

        //查询结果为空，直接返回
        if (!AssertUtils.isNotEmpty(reportSaleApplyPolicyBoList)) {
            return resultObject;
        }
        List<ReportSaleApplyPolicyListResponse> reportSaleApplyPolicyListResponseList = (List<ReportSaleApplyPolicyListResponse>) this.converterList(reportSaleApplyPolicyBoList, new TypeToken<List<ReportSaleApplyPolicyListResponse>>() {
        }.getType());
        // 销售报表 新增服务业务员信息
        List<String> agentIds = reportSaleApplyPolicyBoList.stream()
                .map(ReportSaleApplyPolicyBo::getAgentId).filter(AssertUtils::isNotEmpty)
                .collect(Collectors.toList());
        ResultObject<List<AgentListResponse>> userAgents = agentBaseAgentApi.queryOneAgentById(agentIds);
        if (!AssertUtils.isResultObjectDataNull(userAgents)) {
            List<AgentListResponse> agentListResponses = userAgents.getData();
            reportSaleApplyPolicyListResponseList.forEach(reportGroupPolicyResponse -> {
                agentListResponses.forEach(agentListResponse -> {
                    if (reportGroupPolicyResponse.getAgentId().equals(agentListResponse.getAgentId())) {
                        reportGroupPolicyResponse.setServiceAgentCode(agentListResponse.getAgentCode());
                        reportGroupPolicyResponse.setServiceAgentName(agentListResponse.getAgentName());
                        reportGroupPolicyResponse.setServiceAgentMobil(agentListResponse.getMobile());
                    }
                });
            });
        }

        List<String> policyIds = reportSaleApplyPolicyListResponseList.stream()
                .map(ReportSaleApplyPolicyListResponse::getPolicyId)
                .collect(Collectors.toList());

        // 设置最新的服务业务员
        ResultObject<List<PolicyServiceAgentResponse>> resultObjectListNewServiceAgentResponse = policyApi.listNewServiceAgentPoByPolicyIds(policyIds);
        if (!AssertUtils.isResultObjectDataNull(resultObjectListNewServiceAgentResponse)) {
            List<PolicyServiceAgentResponse> newServiceAgentResponses = resultObjectListNewServiceAgentResponse.getData();

            List<String> serviceAgentIds = newServiceAgentResponses.stream()
                    .map(PolicyServiceAgentResponse::getServiceAgentId)
                    .collect(Collectors.toList());

            userAgents = agentBaseAgentApi.queryOneAgentById(serviceAgentIds);
            if (!AssertUtils.isResultObjectDataNull(userAgents)) {
                List<AgentListResponse> serviceAgents = userAgents.getData();
                reportSaleApplyPolicyListResponseList.stream()
                        .filter(reportPolicyResponse -> AssertUtils.isNotEmpty(reportPolicyResponse.getPolicyId()))
                        .forEach(reportPolicyResponse -> {
                            Optional<PolicyServiceAgentResponse> serviceAgentResponseOptional = newServiceAgentResponses.stream()
                                    .filter(newServiceAgentResponse -> newServiceAgentResponse.getPolicyId().equals(reportPolicyResponse.getPolicyId()))
                                    .findFirst();
                            if (serviceAgentResponseOptional.isPresent()) {
                                PolicyServiceAgentResponse policyServiceAgentResponse = serviceAgentResponseOptional.get();
                                serviceAgents.forEach(serviceAgent -> {
                                    if (serviceAgent.getAgentId().equals(policyServiceAgentResponse.getServiceAgentId())) {
                                        reportPolicyResponse.setServiceAgentCode(serviceAgent.getAgentCode());
                                        reportPolicyResponse.setServiceAgentName(serviceAgent.getAgentName());
                                        reportPolicyResponse.setServiceAgentMobil(serviceAgent.getMobile());
                                    }
                                });
                            }
                        });
            }
        }

        //获取总数
        Integer totalLine = AssertUtils.isNotNull(reportSaleApplyPolicyBoList) ? reportSaleApplyPolicyBoList.get(0).getTotalLine() : null;
        BasePageResponse<ReportSaleApplyPolicyListResponse> basePageResponse = BasePageResponse.getData(reportSaleApplyPolicyVo.getCurrentPage(), reportSaleApplyPolicyVo.getPageSize(), totalLine, reportSaleApplyPolicyListResponseList);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    private List<ReportSaleApplyPolicyBo> querySaleApplyPolicyList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        // 查询销售申请策略的分页列表
        List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBoList = reportSaleApplyPolicyBaseService.querySaleApplyPolicyPageList(reportSaleApplyPolicyVo);
        if (!AssertUtils.isNotEmpty(reportSaleApplyPolicyBoList)) {
            return reportSaleApplyPolicyBoList;
        }
        List<String> agentIdList = reportSaleApplyPolicyBoList.stream().map(ReportSaleApplyPolicyPo::getAgentId).distinct().collect(Collectors.toList());
        // 设置刮刮卡合作伙伴信息
        List<String> partnerIds = reportSaleApplyPolicyBoList.stream()
                .map(ReportSaleApplyPolicyBo::getPartnerId).filter(AssertUtils::isNotEmpty)
                .collect(Collectors.toList());
        List<PartnerInfoResponse> partnerInfoResponses = new ArrayList<>();
        if (AssertUtils.isNotEmpty(partnerIds)) {
            ResultObject<List<PartnerInfoResponse>> partnerProfileDetailList = applyPlanApi.partnerProfileDetailList(partnerIds);
            partnerInfoResponses = partnerProfileDetailList.getData();
        }
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(agentIdList);
        ResultObject<List<AgentResponse>> agentResultObject = agentApi.agentsGet(agentApplyQueryRequest);
        AssertUtils.isResultObjectError(log, agentResultObject);
        List<AgentResponse> agentResponseList = agentResultObject.getData();
        List<String> salesBranchIdList = reportSaleApplyPolicyBoList.stream().map(ReportSaleApplyPolicyPo::getSalesBranchId).distinct().collect(Collectors.toList());
        ResultObject<List<BranchResponse>> branchResultObject = platformBranchBaseApi.queryBranchByIds(salesBranchIdList);
        AssertUtils.isResultObjectError(log, branchResultObject);
        List<BranchResponse> branchResponseList = branchResultObject.getData();
        List<PartnerInfoResponse> finalPartnerInfoResponses = partnerInfoResponses;
        reportSaleApplyPolicyBoList.forEach(reportSaleApplyPolicyBo -> {
            if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getPolicyId())) {
                ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(reportSaleApplyPolicyBo.getPolicyId());
                if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                    reportSaleApplyPolicyBo.setRiskCommencementDateFormats(DateUtils.timeStrToString(policyResponseResultObject.getData().getRiskCommencementDate(), DateUtils.FORMATE3));

                    //团险风险承担开始日期取承保日期
                    if (ReportTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP.name().equals(policyResponseResultObject.getData().getPolicyType())) {
                        reportSaleApplyPolicyBo.setRiskCommencementDateFormat(DateUtils.timeStrToString(policyResponseResultObject.getData().getApproveDate(), DateUtils.FORMATE3));
                    } else {
                        if (!AssertUtils.isNotNull(policyResponseResultObject.getData().getRiskCommencementDate())) {
                            reportSaleApplyPolicyBo.setRiskCommencementDateFormat(DateUtils.timeStrToString(policyResponseResultObject.getData().getApproveDate(), DateUtils.FORMATE3));
                        } else {
                            reportSaleApplyPolicyBo.setRiskCommencementDateFormat(DateUtils.timeStrToString(policyResponseResultObject.getData().getRiskCommencementDate(), DateUtils.FORMATE3));
                        }
                    }
                }
            }

            String premiumFrequency = reportSaleApplyPolicyBo.getPremiumFrequency();
            reportSaleApplyPolicyBo.setPremiumFrequencyName(languageUtils.getCodeName(mapSyscodeList.get(PRODUCT_PREMIUM_FREQUENCY.name()), reportSaleApplyPolicyBo.getPremiumFrequency()));
            if (SINGLE.name().equals(premiumFrequency)) {
                reportSaleApplyPolicyBo.setPremiumPeriod(" ");
            }
            reportSaleApplyPolicyBo.setCoveragePeriodUnitName(languageUtils.getCodeName(mapSyscodeList.get(PRODUCT_COVERAGE_PERIOD_UNIT.name()), reportSaleApplyPolicyBo.getCoveragePeriodUnit()));
            reportSaleApplyPolicyBo.setPremiumPeriodUnitName(languageUtils.getCodeName(mapSyscodeList.get(PRODUCT_PREMIUM_PERIOD_UNIT.name()), reportSaleApplyPolicyBo.getPremiumPeriodUnit()));
            reportSaleApplyPolicyBo.setChannelTypeCodeName(languageUtils.getCodeName(mapSyscodeList.get(CHANNEL_TYPE.name()), reportSaleApplyPolicyBo.getChannelTypeCode()));
            BigDecimal actualPremium = reportSaleApplyPolicyBo.getActualPremium();
            // 投保单的首期月交三个月保费需要处理，保单不用，保单的实收保费已经换算过
            if (!AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPolicyNo()) && Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_19.id(),
                    ProductTermEnum.PRODUCT.PRODUCT_20.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(reportSaleApplyPolicyBo.getMainProductId())
                    && AssertUtils.isNotNull(reportSaleApplyPolicyBo.getActualPremium()) && MONTH.name().equals(reportSaleApplyPolicyBo.getPremiumFrequency())) {
                actualPremium = reportSaleApplyPolicyBo.getActualPremium().divide(new BigDecimal(3), 3, RoundingMode.HALF_UP);
            }
            long frequency = AssertUtils.isNotNull(reportSaleApplyPolicyBo.getFrequency()) ? reportSaleApplyPolicyBo.getFrequency() : 1L;
            if (AssertUtils.isNotNull(actualPremium)) {
                BigDecimal firstYearPremium = BigDecimal.ZERO;
                BigDecimal annualNetPremium = BigDecimal.ZERO;
                if (YEAR.name().equals(premiumFrequency) || SINGLE.name().equals(premiumFrequency)) {
                    firstYearPremium = actualPremium;
                    annualNetPremium = actualPremium;
                } else if (MONTH.name().equals(premiumFrequency)) {
                    firstYearPremium = actualPremium.multiply(new BigDecimal(Math.min(frequency, 12L)));
                    annualNetPremium = actualPremium.multiply(new BigDecimal(12));
                } else if (SEASON.name().equals(premiumFrequency)) {
                    firstYearPremium = actualPremium.multiply(new BigDecimal(Math.min(frequency, 4L)));
                    annualNetPremium = actualPremium.multiply(new BigDecimal(4));
                } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                    firstYearPremium = actualPremium.multiply(new BigDecimal(Math.min(frequency, 2L)));
                    annualNetPremium = actualPremium.multiply(new BigDecimal(2));
                }
                reportSaleApplyPolicyBo.setFirstYearPremium(firstYearPremium.setScale(2, RoundingMode.HALF_UP));
                reportSaleApplyPolicyBo.setAnnualNetPremium(annualNetPremium.setScale(2, RoundingMode.HALF_UP));
                if (reportSaleApplyPolicyBo.getActualPremium().subtract(firstYearPremium).abs().compareTo(new BigDecimal("0.03")) < 0) {
                    reportSaleApplyPolicyBo.setFirstYearPremium(reportSaleApplyPolicyBo.getActualPremium());
                }
            }

            // 算出累计首年保费/年净保费之后，重新赋值月交保单三个月的实收保费 = 每期实收保费 * 3
            if (AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPolicyNo()) && Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_19.id(),
                    ProductTermEnum.PRODUCT.PRODUCT_20.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(reportSaleApplyPolicyBo.getMainProductId())
                    && AssertUtils.isNotNull(reportSaleApplyPolicyBo.getActualPremium()) && MONTH.name().equals(reportSaleApplyPolicyBo.getPremiumFrequency())) {
                reportSaleApplyPolicyBo.setActualPremium(reportSaleApplyPolicyBo.getActualPremium().multiply(new BigDecimal(3)).setScale(2, RoundingMode.HALF_UP));
            }

            reportSaleApplyPolicyBo.setApplicantIdTypeName(languageUtils.getCodeName(mapSyscodeList.get(ID_TYPE.name()), reportSaleApplyPolicyBo.getApplicantIdType()));
            if (LIFE_INSURANCE_GROUP.name().equals(reportSaleApplyPolicyBo.getApplyType())) {
                reportSaleApplyPolicyBo.setApplicantName(reportSaleApplyPolicyBo.getCompanyName());
                reportSaleApplyPolicyBo.setApplicantIdTypeName(languageUtils.getCodeName(mapSyscodeList.get(COMPANY_ID_TYPE.name()), reportSaleApplyPolicyBo.getCompanyIdType()));
                reportSaleApplyPolicyBo.setApplicantIdNo(reportSaleApplyPolicyBo.getCompanyIdNo());
                reportSaleApplyPolicyBo.setApplicantMobile(reportSaleApplyPolicyBo.getCompanyPhone());
                if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getTotalActualPremium())) {
                    reportSaleApplyPolicyBo.setFirstYearPremium(reportSaleApplyPolicyBo.getTotalActualPremium());
                } else {
                    reportSaleApplyPolicyBo.setFirstYearPremium(reportSaleApplyPolicyBo.getActualPremium());
                }
            }
            Long ageEndLongTime = AssertUtils.isNotNull(reportSaleApplyPolicyBo.getApproveDate()) ? reportSaleApplyPolicyBo.getApproveDate() : reportSaleApplyPolicyBo.getApplyDate();
            try {
                if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getInsuredBirthday()) && AssertUtils.isNotNull(ageEndLongTime)) {
                    reportSaleApplyPolicyBo.setInsuredAge(DateUtils.getAgeYear(new Date(reportSaleApplyPolicyBo.getInsuredBirthday()), new Date(ageEndLongTime)));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (AssertUtils.isNotEmpty(branchResponseList)) {
                branchResponseList.stream().filter(branchResponse -> branchResponse.getBranchId().equals(reportSaleApplyPolicyBo.getSalesBranchId()))
                        .findFirst().ifPresent(branchResponse -> {
                    reportSaleApplyPolicyBo.setSalesBranchName(branchResponse.getBranchName());
                });
            }
            if (AssertUtils.isNotEmpty(agentResponseList)) {
                agentResponseList.stream().filter(agentResponse -> agentResponse.getAgentId().equals(reportSaleApplyPolicyBo.getAgentId()))
                        .findFirst().ifPresent(agentResponse -> {
                    reportSaleApplyPolicyBo.setAgentName(agentResponse.getAgentName());
                    reportSaleApplyPolicyBo.setAgentCode(agentResponse.getAgentCode());
                    reportSaleApplyPolicyBo.setAgentMobile(agentResponse.getMobile());
                });
            }
            // 设置刮刮卡合作伙伴名称
            if (AssertUtils.isNotEmpty(finalPartnerInfoResponses) && AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPartnerId())) {
                finalPartnerInfoResponses.stream().filter(partnerInfoResponse -> partnerInfoResponse.getPartnerId().equals(reportSaleApplyPolicyBo.getPartnerId()))
                        .findFirst().ifPresent(partnerInfoResponse -> reportSaleApplyPolicyBo.setPartnerName(partnerInfoResponse.getPartnerName()));
            }
            reportSaleApplyPolicyBo.setAppSubmitUnderwritingDateFormat(DateUtils.timeStrToString(reportSaleApplyPolicyBo.getAppSubmitUnderwritingDate(), DateUtils.FORMATE3));
            reportSaleApplyPolicyBo.setUnderwriteStartDateFormat(DateUtils.timeStrToString(reportSaleApplyPolicyBo.getUnderwriteStartDate(), DateUtils.FORMATE3));
            reportSaleApplyPolicyBo.setReceiptSubmitDateFormat(DateUtils.timeStrToString(reportSaleApplyPolicyBo.getReceiptDate(), DateUtils.FORMATE3));
            reportSaleApplyPolicyBo.setApproveDateFormat(DateUtils.timeStrToString(reportSaleApplyPolicyBo.getApproveDate(), DateUtils.FORMATE3));
            reportSaleApplyPolicyBo.setThoroughInvalidDateFormat(DateUtils.timeStrToString(reportSaleApplyPolicyBo.getThoroughInvalidDate(), DateUtils.FORMATE3));
            if (AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPolicyStatus())) {
                reportSaleApplyPolicyBo.setPolicyStatusName(languageUtils.getCodeName(mapSyscodeList.get(POLICY_STATUS.name()), reportSaleApplyPolicyBo.getPolicyStatus()));
            } else {
                reportSaleApplyPolicyBo.setPolicyStatusName(languageUtils.getCodeName(mapSyscodeList.get(APPLY_STATUS.name()), reportSaleApplyPolicyBo.getApplyStatus()));
            }
            Long underwriteEndDate = reportSaleApplyPolicyBo.getUnderwriteEndDate();
            Long underwriteStartDate = reportSaleApplyPolicyBo.getUnderwriteStartDate();
            Long appSubmitUnderwritingDate = reportSaleApplyPolicyBo.getAppSubmitUnderwritingDate();
            Long approveDate = AssertUtils.isNotNull(reportSaleApplyPolicyBo.getGainedDate()) ? reportSaleApplyPolicyBo.getGainedDate() : reportSaleApplyPolicyBo.getApproveDate();
            reportSaleApplyPolicyBo.setAppUnderwriteAlwaysFormat(alwaysFormat(appSubmitUnderwritingDate, underwriteStartDate));
            reportSaleApplyPolicyBo.setUnderwriteAlwaysFormat(alwaysFormat(underwriteStartDate, underwriteEndDate));
            reportSaleApplyPolicyBo.setUnderwriteApproveAlwaysFormat(alwaysFormat(underwriteEndDate, approveDate));
        });
        return reportSaleApplyPolicyBoList;
    }

    private String alwaysFormat(Long startTime, Long endTime) {
        endTime = AssertUtils.isNotNull(endTime) ? endTime : DateUtils.getCurrentTime();
        if (AssertUtils.isNotNull(startTime) && AssertUtils.isNotNull(endTime) && endTime.longValue() > startTime.longValue()) {
            String day = languageUtils.getCodeName(mapSyscodeList.get(DATE_TYPE.name()), "DAY");
            String hour = languageUtils.getCodeName(mapSyscodeList.get(DATE_TYPE.name()), "HOUR");
            long underwriteAlways = endTime - startTime;
            long dayl = 1 * 24 * 60 * 60 * 1000l;
            day = underwriteAlways > dayl ? underwriteAlways / dayl + day : "";
            hour = ((underwriteAlways - (underwriteAlways / dayl) * dayl) / (60 * 60 * 1000l)) + hour;
            return day + hour;
        }
        return "";

    }

    @Override
    public void exportReportSaleApplyPolicyList(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) throws IOException {
        //由输入流得到工作簿
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.SALE_APPLY_POLICY_REPORT_TEMPLATE.name());
        AttachmentResponse attachmentResponse = attachmentRespFcResultObject.getData();
        URL url = new URL(attachmentResponse.getUrl());
        InputStream inputStream = url.openStream();
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(workbook);

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setFillForegroundColor(HSSFColor.BLACK.index);
        cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cellStyle.setBorderLeft(CellStyle.BORDER_THIN);// 左边框
        cellStyle.setBorderTop(CellStyle.BORDER_THIN);// 上边框
        cellStyle.setBorderRight(CellStyle.BORDER_THIN);// 右边框
        //国际化
        List<String> codeTypes = Arrays.asList(TerminologyTypeEnum.APPLY_STATUS.name(), COMPANY_ID_TYPE.name(), DATE_TYPE.name(), ID_TYPE.name(), CHANNEL_TYPE.name(), TerminologyTypeEnum.POLICY_STATUS.name(),
                PRODUCT_PREMIUM_FREQUENCY.name(), PRODUCT_COVERAGE_PERIOD_UNIT.name(), PRODUCT_PREMIUM_PERIOD_UNIT.name());
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
        mapSyscodeList = mapResultObject.getData();

        exportReportSaleApplyPolicySheet1(cellStyle, sxssfWorkbook.getSheetAt(0), reportSaleApplyPolicyVo);


        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        sxssfWorkbook.write(byteArrayOutputStream);
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/x-download");
        httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachmentResponse.getTemplateName() + "." + attachmentResponse.getFileSuffix(), "UTF-8"));
        OutputStream outputStream = httpServletResponse.getOutputStream();
        outputStream.write(byteArrayOutputStream.toByteArray());
        outputStream.close();
        inputStream.close();
        byteArrayOutputStream.close();
    }

    /**
     * 导出报告 - 销售申请策略表单的第一张表
     * 该方法负责查询销售申请策略列表，并将其打印到Excel表单中
     *
     * @param cellStyle 单元格样式，用于统一设置Excel表格中单元格的样式
     * @param sheetAt Excel工作表对象，用于确定要写入的数据位置
     * @param reportSaleApplyPolicyVo 报告销售申请策略视图对象，封装了查询条件和分页信息
     */
    private void exportReportSaleApplyPolicySheet1(CellStyle cellStyle, Sheet sheetAt, ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        // 设置每页大小为10000条记录，以确保一次性获取足够多的数据
        reportSaleApplyPolicyVo.setPageSize(10000);
        // 用于存储总行数，以判断是否需要继续查询
        Integer totalLine;
        // 使用do-while循环不断查询并打印数据，直到没有更多数据为止
        do {
            // 根据查询条件获取销售申请策略列表
            this.getLogger().info("查询销售申请策略列表，查询条件：{}", JSONObject.toJSON(reportSaleApplyPolicyVo));
            List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBos = querySaleApplyPolicyList(reportSaleApplyPolicyVo);
            // 记录查询结果日志
            this.getLogger().info("查询结果：{}", JSONObject.toJSON(reportSaleApplyPolicyBos));
            if (!AssertUtils.isNotEmpty(reportSaleApplyPolicyBos)) {
                break;
            }
            // 将查询结果打印到Excel表单中
            printReportSaleApplyPolicySheet1(cellStyle, sheetAt, reportSaleApplyPolicyBos, reportSaleApplyPolicyVo);
            // 获取总行数，用于判断是否需要继续查询
            totalLine = reportSaleApplyPolicyBos.get(0).getTotalLine();
            // 如果当前页乘以每页大小大于等于总行数，则说明所有数据已经查询完毕，跳出循环
            if (totalLine <= (reportSaleApplyPolicyVo.getCurrentPage() * reportSaleApplyPolicyVo.getPageSize())) {
                break;
            }
            // 将当前页码加1，准备下一轮查询
            reportSaleApplyPolicyVo.setCurrentPage(reportSaleApplyPolicyVo.getCurrentPage() + 1);
        } while (true);
    }

    /**
     * 打印报表中的销售申请策略信息
     * 该方法负责将销售申请策略的相关数据填充到Excel报表中
     *
     * @param cellStyle 单元格样式，用于格式化Excel中的单元格
     * @param sheetAt 当前操作的Sheet对象，用于在Excel中创建行和单元格
     * @param reportSaleApplyPolicyBos 包含销售申请策略信息的列表，用于填充到报表中
     * @param reportSaleApplyPolicyVo 销售申请策略的视图对象，包含当前页码和页面大小等信息
     */
    private void printReportSaleApplyPolicySheet1(CellStyle cellStyle, Sheet sheetAt, List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBos, ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        // 获取服务业务人员信息
        List<String> serviceAgentIds = new ArrayList<>();
        List<String> policyIds = reportSaleApplyPolicyBos.stream()
                .map(ReportSaleApplyPolicyBo::getPolicyId)
                .collect(Collectors.toList());
        // 根据策略ID列表获取新的服务代理响应列表
        // 返回值是一个包含PolicyServiceAgentResponse对象列表的ResultObject，用于存储查询结果
        ResultObject<List<PolicyServiceAgentResponse>> resultObjectListNewServiceAgentResponse = policyApi.listNewServiceAgentPoByPolicyIds(policyIds);
        if (!AssertUtils.isResultObjectDataNull(resultObjectListNewServiceAgentResponse)) {
            List<PolicyServiceAgentResponse> newServiceAgentResponses = resultObjectListNewServiceAgentResponse.getData();
            serviceAgentIds = newServiceAgentResponses.stream()
                    .map(PolicyServiceAgentResponse::getServiceAgentId)
                    .collect(Collectors.toList());
        }
        serviceAgentIds = serviceAgentIds.stream().distinct().collect(Collectors.toList());
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(serviceAgentIds);
        List<AgentResponse> agentResponses = agentApi.agentsGet(agentApplyQueryRequest).getData();

        int no = (reportSaleApplyPolicyVo.getCurrentPage() - 1) * reportSaleApplyPolicyVo.getPageSize();
        for (ReportSaleApplyPolicyBo reportSaleApplyPolicyBo : reportSaleApplyPolicyBos) {
            // 创建用于书写的数据行，行号为 no + 2
            Row writeRow = sheetAt.createRow(no + 2);
            writeRow.setHeight((short) (20 * 20));
            int column = 0;
            BigDecimal specialDiscount = new BigDecimal("0.00");
            getCell(cellStyle, writeRow, column++).setCellValue(++no);
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getPolicyNo()));//保单号
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getApplyNo()));//投保单号
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(reportSaleApplyPolicyBo.getProductId(), reportSaleApplyPolicyBo.getProductName())));//险种名称
            if (AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPartnerId())) {
                getCell(cellStyle, writeRow, column++).setCellValue("SC");
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue("");
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getPremiumFrequencyName()));//缴费周期
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getPremiumPeriod()) + exportPrint(reportSaleApplyPolicyBo.getPremiumPeriodUnitName()));//缴费期限
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getCoveragePeriod()) + exportPrint(reportSaleApplyPolicyBo.getCoveragePeriodUnitName()));//保障期限
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getActualPremium()));//首期保费
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getFirstYearPremium()));//累计首年年费
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAnnualNetPremium()));//年净保费
            //我们要求为任何折扣政策添加"首期保费折扣后" 和 "折扣率"字段，如果没有折扣的情况下，在"首期保费折扣后"的字段出现原始保费，而"折扣率"字段出现空白表格
            Object specialDiscount1 = null;
            BigDecimal totalPremium = reportSaleApplyPolicyBo.getActualPremium();
            if (AssertUtils.isNotNull(totalPremium)) {
                if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(reportSaleApplyPolicyBo.getDiscountModel())) {
                    totalPremium = reportSaleApplyPolicyBo.getActualPremium().subtract(reportSaleApplyPolicyBo.getActualPremium().multiply(reportSaleApplyPolicyBo.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
                    specialDiscount1 = reportSaleApplyPolicyBo.getSpecialDiscount().multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
                }
                if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(reportSaleApplyPolicyBo.getDiscountModel())) {
                    totalPremium = reportSaleApplyPolicyBo.getActualPremium().subtract(reportSaleApplyPolicyBo.getSpecialDiscount());
                    specialDiscount1 = "$" + reportSaleApplyPolicyBo.getSpecialDiscount().stripTrailingZeros().toPlainString();
                }
            }

            if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getSpecialDiscount()) && !specialDiscount.equals(reportSaleApplyPolicyBo.getSpecialDiscount())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(totalPremium));//首期保费折扣后
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(specialDiscount1));//折扣率
            }else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(totalPremium));//首期保费
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));//折扣率
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getPromotionalCode()));//优惠券
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAmount()));//保险金额
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getApplicantName()));//投保人姓名
            //getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getApplicantIdTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getApplicantIdNo()));//投保人身份证号
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getApplicantMobile()));//投保人手机号
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getInsuredName()));//被保人姓名
            //getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getInsuredAge()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getSalesBranchName()));//销售机构
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getChannelTypeCodeName()));//渠道
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAgentCode()));//业务员代码
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAgentName()));//业务员姓名
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getPartnerName()));//合作伙伴名称

            AgentResponse serviceAgent = null;
            if (AssertUtils.isNotEmpty(agentResponses) && AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getAgentId())) {
                if (!AssertUtils.isResultObjectDataNull(resultObjectListNewServiceAgentResponse)) {
                    List<PolicyServiceAgentResponse> newServiceAgentResponses = resultObjectListNewServiceAgentResponse.getData();
                    Optional<PolicyServiceAgentResponse> policyServiceAgentResponseOptional = newServiceAgentResponses.stream()
                            .filter(newServiceAgentResponse -> newServiceAgentResponse.getPolicyId().equals(reportSaleApplyPolicyBo.getPolicyId()))
                            .findFirst();
                    if (policyServiceAgentResponseOptional.isPresent()) {
                        PolicyServiceAgentResponse policyServiceAgentResponse = policyServiceAgentResponseOptional.get();
                        Optional<AgentResponse> serviceAgentResponse = agentResponses.stream()
                                .filter(agentResponse -> agentResponse.getAgentId().equals(policyServiceAgentResponse.getServiceAgentId()))
                                .findFirst();
                        if (serviceAgentResponse.isPresent()) {
                            serviceAgent = serviceAgentResponse.get();
                        }
                    }
                }
            }
            // 设置服务业务人员信息 没有服务业务员则取初始业务员
            if (AssertUtils.isNotNull(serviceAgent)) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(serviceAgent.getAgentCode()));//服务业务员编码
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(serviceAgent.getAgentName()));//服务业务员名称
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(serviceAgent.getMobile()));//服务业务员手机
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAgentCode()));//服务业务员编码
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAgentName()));//服务业务员名称
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAgentMobile()));//服务业务员手机
            }

            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAppSubmitUnderwritingDateFormat()));//提交承保日期
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getUnderwriteStartDateFormat()));//点核保日期
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getAppUnderwriteAlwaysFormat()));//时长（从提交承保至首次点核保）
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getUnderwriteAlwaysFormat()));//核保时长
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getUnderwriteApproveAlwaysFormat()));//待缴费时长
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getReceiptSubmitDateFormat()));//回执日期
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getThoroughInvalidDateFormat()));//犹豫期撤单日期/保单失效日期
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getApproveDateFormat()));//承保日期
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getRiskCommencementDateFormat()));//风险开始承担日期
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getPolicyStatusName()));//保单/投保单状态

            /*// 销售报表里只展示刮刮卡分配备注, 其他保单都不用展示备注
            if (AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPartnerId()) && "SALES".equals(reportSaleApplyPolicyVo.getReportType())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getRemark()));
            }
            // 待报表展示除刮刮卡以外的备注信息
            if (!AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPartnerId()) && "UW".equals(reportSaleApplyPolicyVo.getReportType())) {
                // 格式化备注信息
                List<ApplyRemarksResponse> applyRemarksResponses = JSON.parseArray(reportSaleApplyPolicyBo.getRemark(), ApplyRemarksResponse.class);
                StringBuilder stringBuilder = new StringBuilder();
                if (AssertUtils.isNotEmpty(applyRemarksResponses)) {
                    for (int i = 0; i < applyRemarksResponses.size(); i++) {
                        stringBuilder.append(i + 1).append(".").append(applyRemarksResponses.get(i).getRemarkDesc()).append("\n");
                    }
                }
                if (stringBuilder.length() > 0) {
                    RichTextString richTextString = new HSSFRichTextString(stringBuilder.toString());
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(richTextString));
                }
            }*/
            // 原样输出刮刮卡备注信息
            if (AssertUtils.isNotEmpty(reportSaleApplyPolicyBo.getPartnerId())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportSaleApplyPolicyBo.getRemark()));//  备注信息
            } else {
                // 格式化备注信息
                List<ApplyRemarksResponse> applyRemarksResponses = JSON.parseArray(reportSaleApplyPolicyBo.getRemark(), ApplyRemarksResponse.class);
                StringBuilder stringBuilder = new StringBuilder();
                if (AssertUtils.isNotEmpty(applyRemarksResponses)) {
                    for (int i = 0; i < applyRemarksResponses.size(); i++) {
                        stringBuilder.append(i + 1).append(".").append(applyRemarksResponses.get(i).getRemarkDesc()).append("\n");
                    }
                }
                if (stringBuilder.length() > 0) {
                    RichTextString richTextString = new HSSFRichTextString(stringBuilder.toString());
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(richTextString));//备注信息
                }
            }
        }

    }

    @Override
    public ResultObject<List<ReportRemarkResponse>> getRemarkList(String saleId) {
        ResultObject<List<ReportRemarkResponse>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(saleId)) {
            return resultObject;
        }
        ReportSaleApplyPolicyPo reportSaleApplyPolicyPo = reportSaleApplyPolicyBaseService.getRemarkList(saleId);
        if (!AssertUtils.isNotNull(reportSaleApplyPolicyPo)) {
            return resultObject;
        }
        if (AssertUtils.isNotEmpty(reportSaleApplyPolicyPo.getRemark())) {
            List<ApplyRemarksResponse> applyRemarksResponses = new ArrayList<>();
            try {
                applyRemarksResponses = JSON.parseArray(reportSaleApplyPolicyPo.getRemark(), ApplyRemarksResponse.class);
            } catch (Exception e) {
                log.error("获取备注信息失败");
            }
            if (!AssertUtils.isNotEmpty(applyRemarksResponses)) {
                return resultObject;
            }
            int index = 1;
            for (ApplyRemarksResponse applyRemarksResponse : applyRemarksResponses) {
                applyRemarksResponse.setIndex(getNo(index++));
            }
            List<ReportRemarkResponse> reportRemarkResponseList = (List<ReportRemarkResponse>) this.converterList(applyRemarksResponses, new TypeToken<List<ReportRemarkResponse>>() {
            }.getType());
            resultObject.setData(reportRemarkResponseList);
        }
        return resultObject;
    }

    public static Cell getCell(CellStyle cellStyle, Row writeRow, int column) {
        if (AssertUtils.isNotNull(writeRow.getCell(column))) {
            return writeRow.getCell(column);
        }
        Cell cell = writeRow.createCell(column);
        cell.setCellStyle(cellStyle);
        return cell;
    }

    //金额格式化
    private static final DecimalFormat decimalFormat = new DecimalFormat("###,###,###,###,##0.00");

    public static String exportPrint(Object o) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                return decimalFormat.format(o);
            }
            return o + "";
        }
        return "";
    }

    /**
     * 备注序号
     * @param no
     * @return
     */
    private String getNo(int no) {
        String stringNo = no + "";
        int length = 3 - stringNo.length();
        for (int i = 0; i < length; i++) {
            stringNo = "0" + stringNo;
        }
        return stringNo;
    }
}
