package com.gclife.report.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.AppApplyApi;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.request.SyscodeRequest;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.api.ProductApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.response.ProductResponse;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.model.bo.ReportPaymentBo;
import com.gclife.report.model.config.ReportErrorConfigEnum;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.request.ReportPaymentListRequest;
import com.gclife.report.model.response.ReportPaymentInitResponse;
import com.gclife.report.model.response.ReportPaymentListResponse;
import com.gclife.report.service.ReportActualPerformanceBaseService;
import com.gclife.report.service.ReportQueryBaseService;
import com.gclife.report.service.ReportServiceChargeBankChannelBaseService;
import com.gclife.report.service.business.ReportPaymentBusinessService;
import com.gclife.report.validate.transform.LanguageUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jooq.tools.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.APPLY;
import static com.gclife.common.model.config.TerminologyConfigEnum.ROLE_BASE_FLAG.NO;
import static com.gclife.common.model.config.TerminologyConfigEnum.WHETHER.YES;
import static com.gclife.report.model.config.ReportErrorConfigEnum.REPORT_PAYMENT_IS_NULL;
import static com.gclife.report.model.config.ReportTermEnum.AGENT_TYPE_CODE.LIFE_CONSULTANT;
import static com.gclife.report.model.config.ReportTermEnum.ENDORSE_PROJECT.*;
import static com.gclife.report.model.config.ReportTermEnum.PAYMENT_BUSINESS_TYPE.GROUP_ADD_ADDITIONAL;
import static com.gclife.report.model.config.ReportTermEnum.PAYMENT_BUSINESS_TYPE.*;

/**
 * <AUTHOR>
 * create 18-11-14
 * description:
 */
@Service
public class ReportPaymentBusinessServiceImpl extends BaseBusinessServiceImpl implements ReportPaymentBusinessService {
    @Autowired
    private ReportQueryBaseService reportQueryBaseService;
    @Autowired
    private LanguageUtils languageUtils;
    @Autowired
    private AttachmentApi attachmentServiceInterface;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ReportActualPerformanceBaseService reportActualPerformanceBaseService;
    @Autowired
    private ReportServiceChargeBankChannelBaseService reportServiceChargeBankChannelBaseService;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private AppApplyApi appApplyApi;
    private List<BranchResponse> branchResponseList;
    private List<ProductResponse> productResponseList;
    private List<SyscodeResponse> productPremiumFrequencySyscodeList;
    private List<SyscodeResponse> productPremiumPeriodUnitSyscodeList;
    private List<SyscodeResponse> paymentMethodsSyscodeList;
    private List<SyscodeResponse> productProductLevelSyscodeList;
    private List<SyscodeResponse> policyStatusSyscodeList;
    private List<SyscodeResponse> applyStatusSyscodeList;

    @Override
    public ResultObject<BasePageResponse<ReportPaymentListResponse>> queryReportPaymentPageList(Users users, ReportPaymentListRequest reportPaymentListRequest) {
        ResultObject<BasePageResponse<ReportPaymentListResponse>> resultObject = new ResultObject<>();
        List<ReportPaymentListResponse> reportPaymentListResponse = new ArrayList<>();
        try {
            List<ReportPaymentBo> reportPaymentbos = reportQueryBaseService.queryListReportPayment(reportPaymentListRequest, "list");
            if (!AssertUtils.isNotEmpty(reportPaymentbos)) {
                return resultObject;
            }
            List<SyscodeResponse> paymentMethodResponse = platformInternationalBaseApi.queryInternational(ReportTermEnum.PAYMENT_METHODS.PAYMENT_METHODS.name(), users.getLanguage()).getData();
            reportPaymentbos.forEach(reportPaymentPo -> {
                ReportPaymentListResponse reportPayment = (ReportPaymentListResponse) this.converterObject(reportPaymentPo, ReportPaymentListResponse.class);
                //特殊处理 将"[\"a\",\"b\"]"转换成对应的国际化
                List<String> methodCodeTrans = new ArrayList<>();
                List<String> paymentMethodCodes = AssertUtils.isNotEmpty(reportPaymentPo.getPaymentMethodCode()) ? (List<String>) JSON.parse(reportPaymentPo.getPaymentMethodCode()) : null;
                if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                    paymentMethodCodes.forEach(paymentMethodCode -> methodCodeTrans.add(languageUtils.getCodeName(paymentMethodResponse, paymentMethodCode)));
                    String paymentMethodCode = StringUtils.join(methodCodeTrans.toArray(), ",");
                    reportPayment.setPaymentMethod(paymentMethodCode);
                }
                reportPayment.setPaymentMethodCode(reportPaymentPo.getPaymentMethodCode());
                reportPayment.setBusinessTypeCode(reportPaymentPo.getBusinessType());
                reportPayment.setStatusCode(reportPaymentPo.getStatus());
                reportPaymentListResponse.add(reportPayment);
            });
            //获取总数
            Integer totalLine = AssertUtils.isNotNull(reportPaymentbos) ? reportPaymentbos.get(0).getTotalLine() : null;
            BasePageResponse<ReportPaymentListResponse> basePageResponse = BasePageResponse.getData(reportPaymentListRequest.getCurrentPage(), reportPaymentListRequest.getPageSize(), totalLine, reportPaymentListResponse);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ReportErrorConfigEnum.REPORT_QUERY_REPORT_PAYMENT_DATA_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<ReportPaymentInitResponse> initReportPayment(Users users) {
        ResultObject<ReportPaymentInitResponse> resultObject = new ResultObject<>();
        try {
            List<String> codeTypes = Arrays.asList(TerminologyTypeEnum.PAYMENT_METHODS.name(),
                    TerminologyTypeEnum.PAYMENT_BUSINESS_TYPE.name(),
                    TerminologyTypeEnum.COVERAGE_STATUS.name(),
                    TerminologyTypeEnum.PAYMENT_STATUS.name());
            ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(users.getLanguage(), codeTypes);
            Map<String, List<SyscodeResponse>> data = mapResultObject.getData();

            ReportPaymentInitResponse reportPaymentInitResponse = new ReportPaymentInitResponse();
            reportPaymentInitResponse.setPaymentMethod(data.get(TerminologyTypeEnum.PAYMENT_METHODS.name()));
            reportPaymentInitResponse.setBusinessType(data.get(TerminologyTypeEnum.PAYMENT_BUSINESS_TYPE.name()));
            reportPaymentInitResponse.setStatus(data.get(TerminologyTypeEnum.PAYMENT_STATUS.name()));
            reportPaymentInitResponse.setCoverageStatus(data.get(TerminologyTypeEnum.COVERAGE_STATUS.name()));
            resultObject.setData(reportPaymentInitResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ReportErrorConfigEnum.REPORT_QUERY_REPORT_PAYMENT_INIT_DATA_ERROR);
        }
        return resultObject;
    }

    @Override
    public void exportReportPaymenList(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPaymentListRequest reportPaymentListRequest) {
        try {
            List<String> codeTypes = Arrays.asList(ReportTermEnum.PAYMENT_METHODS.PAYMENT_METHODS.name(),
                    ReportTermEnum.PAYMENT_TYPE1.PAYMENT_TYPE1.name(),
                    TerminologyTypeEnum.PAYMENT_BUSINESS_TYPE.name(),
                    TerminologyTypeEnum.PAYMENT_STATUS.name(),
                    TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
            ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
            Map<String, List<SyscodeResponse>> data = mapResultObject.getData();
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_PAYMENT_TEMPLATE.name());
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);

            try {
                List<ReportPaymentBo> reportPaymentBos = reportQueryBaseService.queryListReportPayment(reportPaymentListRequest, "export");
                AssertUtils.isNotEmpty(this.getLogger(), reportPaymentBos, REPORT_PAYMENT_IS_NULL);
                int num = 0;
                for (int i = 2; i < sheet.getLastRowNum(); i++) {
                    if (!(reportPaymentBos.size() > num && AssertUtils.isNotNull(reportPaymentBos.get(num)))) {
                        break;
                    }
                    ReportPaymentBo reportPaymentBo = reportPaymentBos.get(num);
                    num++;
                    Row writeRow = sheet.getRow(i);
                    writeRow.getCell(0).setCellValue(num);
                    String businessDate = DateUtils.timeStrToString(reportPaymentBo.getBusinessDate(), DateUtils.FORMATE3);
                    if (!AssertUtils.isNotEmpty(businessDate)) {
                        businessDate = "--";
                    }
                    writeRow.getCell(1).setCellValue(businessDate);
                    String paymentType = languageUtils.getCodeName(data.get(ReportTermEnum.PAYMENT_TYPE1.PAYMENT_TYPE1.name()), reportPaymentBo.getPaymentType());
                    if (!AssertUtils.isNotEmpty(paymentType)) {
                        paymentType = "--";
                    }
                    writeRow.getCell(2).setCellValue(paymentType);
                    String businessType = languageUtils.getCodeName(data.get(TerminologyTypeEnum.PAYMENT_BUSINESS_TYPE.name()), reportPaymentBo.getBusinessType());
                    if (!AssertUtils.isNotEmpty(businessType)) {
                        businessType = "--";
                    }
                    writeRow.getCell(3).setCellValue(businessType);

                    //特殊处理 将"[\"a\",\"b\"]"转换成对应的国际化
                    String paymentMethodCode = "--";
                    if (AssertUtils.isNotEmpty(reportPaymentBo.getPaymentMethodCode())) {
                        List<String> methodCodeTrans = new ArrayList<>();
                        List<String> paymentMethodCodes = (List<String>) JSON.parse(reportPaymentBo.getPaymentMethodCode());
                        if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                            paymentMethodCodes.forEach(s -> methodCodeTrans.add(languageUtils.getCodeName(data.get(ReportTermEnum.PAYMENT_METHODS.PAYMENT_METHODS.name()), s)));
                            paymentMethodCode = StringUtils.join(methodCodeTrans.toArray(), ",");
                        }
                    }
                    writeRow.getCell(4).setCellValue(paymentMethodCode);
                    String duePayAmount = "--";
                    if (AssertUtils.isNotNull(reportPaymentBo.getDuePayAmount())) {
                        duePayAmount = reportPaymentBo.getDuePayAmount().toString();
                    }
                    writeRow.getCell(5).setCellValue(duePayAmount);
                    String status = languageUtils.getCodeName(data.get(TerminologyTypeEnum.PAYMENT_STATUS.name()), reportPaymentBo.getStatus());
                    if (!AssertUtils.isNotEmpty(status)) {
                        status = "--";
                    }
                    writeRow.getCell(6).setCellValue(status);
                    String confirmDate = DateUtils.timeStrToString(reportPaymentBo.getConfirmDate(), DateUtils.FORMATE3);
                    if (!AssertUtils.isNotEmpty(confirmDate)) {
                        confirmDate = "--";
                    }
                    writeRow.getCell(7).setCellValue(confirmDate);
                    String applyNo = reportPaymentBo.getApplyNo();
                    if (!AssertUtils.isNotEmpty(applyNo)) {
                        applyNo = "--";
                    }
                    writeRow.getCell(8).setCellValue(applyNo);
                    String policyNo = reportPaymentBo.getPolicyNo();
                    if (!AssertUtils.isNotEmpty(policyNo)) {
                        policyNo = "--";
                    }
                    writeRow.getCell(9).setCellValue(policyNo);
                    String productCode = reportPaymentBo.getProductCode();
                    if (!AssertUtils.isNotEmpty(productCode)) {
                        productCode = "--";
                    }
                    writeRow.getCell(10).setCellValue(productCode);
                    String productName = LanguageUtils.getENProductName(reportPaymentBo.getProductCode(), reportPaymentBo.getProductName());
                    if (!AssertUtils.isNotEmpty(productName)) {
                        productName = "--";
                    }
                    writeRow.getCell(11).setCellValue(productName);
                    String productLevel = reportPaymentBo.getProductLevel();
                    if (!AssertUtils.isNotEmpty(productLevel)) {
                        productLevel = "--";
                    }
                    writeRow.getCell(12).setCellValue(productLevel);
                    String applicantName = reportPaymentBo.getApplicantName();
                    if (!AssertUtils.isNotEmpty(applicantName)) {
                        applicantName = "--";
                    }
                    writeRow.getCell(13).setCellValue(applicantName);
                    String insuredName = reportPaymentBo.getInsuredName();
                    if (!AssertUtils.isNotEmpty(insuredName)) {
                        insuredName = "--";
                    }
                    writeRow.getCell(14).setCellValue(insuredName);
                    String insuredBirthday = DateUtils.timeStrToString(reportPaymentBo.getInsuredBirthday(), DateUtils.FORMATE3);
                    if (!AssertUtils.isNotEmpty(insuredBirthday)) {
                        insuredBirthday = "--";
                    }
                    writeRow.getCell(15).setCellValue(insuredBirthday);
                    String premiumFrequency = languageUtils.getCodeName(data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name()), reportPaymentBo.getPremiumFrequency());
                    if (!AssertUtils.isNotEmpty(premiumFrequency)) {
                        premiumFrequency = "--";
                    }
                    writeRow.getCell(16).setCellValue(premiumFrequency);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("财务报表.xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException(ReportErrorConfigEnum.REPORT_REPORT_PAYMENT_ERROR);
            }
        }

    }


    @Override
    public void exportActualPerformance(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPaymentListRequest reportPaymentListRequest) throws Exception {
        String language = currentLoginUsers.getLanguage();
        try {
            String date = reportPaymentListRequest.getDate();
            long thisMonthFirstDay = DateUtils.getThisMonthFirstDay(date);
            long thisMonthLastDay = DateUtils.getThisMonthLastDay(date);
            List<ActualPerformanceReportBo> reportPaymentPoList = reportActualPerformanceBaseService.queryByDate(thisMonthFirstDay, thisMonthLastDay);
            List<BranchResponse> branchResponseList = null;
            List<String> salesBranchIdList = reportPaymentPoList.stream()
                    .filter(actualPerformanceReportBo -> AssertUtils.isNotEmpty(actualPerformanceReportBo.getSalesBranchId()))
                    .map(ActualPerformanceReportBo::getSalesBranchId).distinct()
                    .collect(Collectors.toList());
            List<String> agentIdList = reportPaymentPoList.stream()
                    .filter(actualPerformanceReportBo -> AssertUtils.isNotEmpty(actualPerformanceReportBo.getSalesBranchId()))
                    .map(ActualPerformanceReportBo::getAgentId).distinct()
                    .collect(Collectors.toList());
            List<String> agentIds = agentIdList.stream().distinct().collect(Collectors.toList());
            AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
            agentApplyQueryRequest.setListAgentId(agentIds);
            List<AgentResponse> agentResponseList = agentApi.agentsGet(agentApplyQueryRequest).getData();
            if (AssertUtils.isNotEmpty(salesBranchIdList)) {
                ResultObject<List<BranchResponse>> branchResultObject = platformBranchApi.branchsPost(salesBranchIdList);
                AssertUtils.isResultObjectError(this.getLogger(), branchResultObject);
                branchResponseList = branchResultObject.getData();
            }
            List<SyscodeResponse> paymentMethodResponse = platformInternationalBaseApi.queryInternational(ReportTermEnum.PAYMENT_METHODS.PAYMENT_METHODS.name(), language).getData();
            List<String> productIdList = reportPaymentPoList.stream().map(ActualPerformanceReportBo::getProductId).distinct().collect(Collectors.toList());
            ResultObject<List<ProductResponse>> productResultObject = productApi.queryProductByIds(productIdList);
            List<ProductResponse> productResponseList = productResultObject.getData();
            List<BranchResponse> finalBranchResponseList = branchResponseList;
            reportPaymentPoList.forEach(actualPerformanceReportBo -> {
                //支付方式国际化
                if (AssertUtils.isNotEmpty(actualPerformanceReportBo.getPaymentMethodCode())) {
                    List<String> methodCodeTrans = new ArrayList<>();
                    List<String> paymentMethodCodes = (List<String>) JSON.parse(actualPerformanceReportBo.getPaymentMethodCode());
                    if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                        paymentMethodCodes.forEach(s -> methodCodeTrans.add(languageUtils.getCodeName(paymentMethodResponse, s)));
                        actualPerformanceReportBo.setPaymentMethodName(StringUtils.join(methodCodeTrans.toArray(), ","));
                    }
                }
                //国际化销售ID
                if (AssertUtils.isNotEmpty(finalBranchResponseList) && AssertUtils.isNotEmpty(actualPerformanceReportBo.getSalesBranchId())) {
                    finalBranchResponseList.stream().filter(branchResponse -> branchResponse.getBranchId().equals(actualPerformanceReportBo.getSalesBranchId()))
                            .findFirst()
                            .ifPresent(branchResponse -> {
                                actualPerformanceReportBo.setSalesBranchName(branchResponse.getBranchName());
                            });
                }
                //产品编码
                if (AssertUtils.isNotEmpty(actualPerformanceReportBo.getProductId()) && AssertUtils.isNotEmpty(productResponseList)) {
                    productResponseList.stream().filter(productResponse -> productResponse.getProductId().equals(actualPerformanceReportBo.getProductId())).findFirst()
                            .ifPresent(productResponse -> {
                                actualPerformanceReportBo.setProductNo("#" + productResponse.getProductNo());
                            });
                }
                String effectiveDateFormat = DateUtils.timeStrToString(actualPerformanceReportBo.getEffectiveDate(), DateUtils.FORMATE3);
                actualPerformanceReportBo.setEffectiveDateFormat(effectiveDateFormat);
                String paymentEndDateFormat = DateUtils.timeStrToString(actualPerformanceReportBo.getPaymentEndDate(), DateUtils.FORMATE3);
                actualPerformanceReportBo.setPaymentEndDateFormat(paymentEndDateFormat);
                String renewalDateFormat = DateUtils.timeStrToString(actualPerformanceReportBo.getRenewalDate(), DateUtils.FORMATE3);
                actualPerformanceReportBo.setRenewalDateFormat(renewalDateFormat);
                String actualPayDateFormat = DateUtils.timeStrToString(actualPerformanceReportBo.getActualPayDate(), DateUtils.FORMATE3);
                actualPerformanceReportBo.setActualPayDateFormat(actualPayDateFormat);
                //是否有佣金
                actualPerformanceReportBo.setIsCommission(NO.name());
                if (LIFE_CONSULTANT.name().equals(actualPerformanceReportBo.getAgentTypeCode())) {
                    actualPerformanceReportBo.setIsCommission(YES.name());
                }
                agentResponseList.stream().filter(agentResponse -> agentResponse.getAgentId().equals(actualPerformanceReportBo.getAgentId()))
                        .forEach(agentResponse -> {
                            actualPerformanceReportBo.setAgentCode(agentResponse.getAgentCode());
                            actualPerformanceReportBo.setAgentName(agentResponse.getAgentName());
                        });
            });
            reportPaymentPoList = JSON.parseArray(JackSonUtils.toJson(reportPaymentPoList, language), ActualPerformanceReportBo.class);
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.REVENUE_REPORT_TEMPLATE.name() + "_" + language);
            AttachmentResponse data = attachmentRespFcResultObject.getData();
            URL url = new URL(data.getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            cellStyle.setFillForegroundColor(HSSFColor.BLACK.index);
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
            cellStyle.setBorderLeft(CellStyle.BORDER_THIN);// 左边框
            cellStyle.setBorderTop(CellStyle.BORDER_THIN);// 上边框
            cellStyle.setBorderRight(CellStyle.BORDER_THIN);// 右边框
            //新契约
            this.exportActualPerformanceApply(workbook, cellStyle, reportPaymentPoList, currentLoginUsers);
            //续期
            this.exportActualPerformanceRenewal(workbook, cellStyle, reportPaymentPoList, currentLoginUsers);
            //续保
            this.exportActualPerformanceRenewalPolicy(workbook, cellStyle, reportPaymentPoList, currentLoginUsers);
            //个险保全
            this.exportActualPerformancePersonalEndorse(workbook, cellStyle, reportPaymentPoList, currentLoginUsers);
            //团险保全
            this.exportActualPerformanceGroupEndorse(workbook, cellStyle, reportPaymentPoList, currentLoginUsers);

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(data.getTemplateName() + ".xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();
        } catch (RequestException e) {
            e.printStackTrace();
            throw new RequestException(e.getiEnum());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RequestException(ReportErrorConfigEnum.REPORT_REPORT_PAYMENT_ERROR);
        }
    }

    /**
     * 团险保全
     *
     * @param workbook
     * @param cellStyle
     */
    private void exportActualPerformanceGroupEndorse(XSSFWorkbook workbook, CellStyle cellStyle, List<ActualPerformanceReportBo> reportPaymentPoList, Users currentLoginUsers) throws Exception {
        List<String> endorseTypeList = Arrays.asList(GROUP_ADD_INSURED.name(), GROUP_SUBTRACT_INSURED.name(), GROUP_ADD_ADDITIONAL.name(), GROUP_ADD_SUBTRACT_INSURED.name());
        XSSFSheet sheet = workbook.getSheetAt(4);
        List<ActualPerformanceReportBo> aprbList = reportPaymentPoList.stream().filter(actualPerformanceReportBo -> endorseTypeList.contains(actualPerformanceReportBo.getBusinessType())).collect(Collectors.toList());
        if (!AssertUtils.isNotEmpty(aprbList)) {
            return;
        }
        List<SyscodeResponse> groupEndorseProject = platformInternationalBaseApi.queryInternational(GROUP_ENDORSE_PROJECT.name(), currentLoginUsers.getLanguage()).getData();
        SyscodeResponse policyType = platformInternationalBaseApi.queryOneInternational("POLICY_TYPE", "LIFE_INSURANCE_GROUP", currentLoginUsers.getLanguage()).getData();
        String typeName = policyType.getCodeName();
        Map<String, Integer> productMap = new HashMap<>();
        // 产品开始列
        int productAmountColumn = 34;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_3.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_8.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_9.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_5.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_13.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_19.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20A.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_21.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_24.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_28.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_34.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_4.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_14.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_15.id(), productAmountColumn++);
        // 16wop空置一列
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_22.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23A.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23B.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_17.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_29.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_11.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_12.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_18.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_26.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_27.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_33.id(), productAmountColumn++);
        //国际化
        int i = 1;
        for (ActualPerformanceReportBo aprb : aprbList) {
            int rownum = i + 3;
            Row writeRow = sheet.getRow(rownum);
            if (!AssertUtils.isNotNull(writeRow)) {
                writeRow = sheet.createRow(rownum);
                writeRow.setHeight((short) (20 * 20));
            }
            int column = 0;
            getCell(cellStyle, writeRow, column++).setCellValue(i);
            getCell(cellStyle, writeRow, column++).setCellValue(languageUtils.getCodeName(groupEndorseProject, aprb.getProjectCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyYear()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplicantName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(aprb.getProductId(), aprb.getProductName())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPrimaryFlagName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getDutyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductLevelName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(typeName));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyEffectiveDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getEffectiveDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getActualPayDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getMult()));
            BigDecimal totalPremium = aprb.getTotalPremium();
            String pay = "";
            if (AssertUtils.isNotNull(aprb.getRefundAmount()) && aprb.getRefundAmount().compareTo(BigDecimal.ZERO) != 0) {
                totalPremium = aprb.getRefundAmount();
                pay = "-";
            }
            if (ADD_ADDITIONAL.name().equals(aprb.getProjectCode()) && AssertUtils.isNotNull(aprb.getMult())
                    && AssertUtils.isNotNull(totalPremium) && 0 != totalPremium.compareTo(BigDecimal.ZERO)) {
                aprb.setPremium(totalPremium.divide(new BigDecimal(aprb.getMult()), 2, BigDecimal.ROUND_HALF_UP));
            }
            //getCell(cellStyle, writeRow, column++).setCellValue((ProductTermEnum.PRODUCT.PRODUCT_12.id().equals(aprb.getProductId()) ? "--" : exportPrint(aprb.getPremium())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(pay + exportPrint(totalPremium));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getReceiptNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumFrequencyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getFrequency()));
            if (AssertUtils.isNotNull(aprb.getPremiumPeriod())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumPeriod()));
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentMethodName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getBankName()));
            //网销的不展示支付码
            if (AssertUtils.isNotEmpty(aprb.getSalesBranchId()) && "ONLINE".equals(aprb.getSalesBranchId())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentCodeNo()));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getSalesBranchName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getChannelTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint("FYP"));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRemarks()));
            exportPrint(cellStyle, writeRow, productMap.get(aprb.getProductId()), aprb);
            i++;
        }
    }

    /**
     * 个险保全
     *
     * @param workbook
     * @param cellStyle
     */
    private void exportActualPerformancePersonalEndorse(XSSFWorkbook workbook, CellStyle cellStyle, List<ActualPerformanceReportBo> reportPaymentPoList, Users currentLoginUsers) throws Exception {
        XSSFSheet sheet = workbook.getSheetAt(3);
        List<ActualPerformanceReportBo> aprbList = reportPaymentPoList.stream().filter(actualPerformanceReportBo -> POLICY_ENDORSE.name().equals(actualPerformanceReportBo.getBusinessType())).collect(Collectors.toList());
        if (!AssertUtils.isNotEmpty(aprbList)) {
            return;
        }
        List<SyscodeResponse> endorseProject = platformInternationalBaseApi.queryInternational(ENDORSE_PROJECT.name(), currentLoginUsers.getLanguage()).getData();
        SyscodeResponse policyType = platformInternationalBaseApi.queryOneInternational("POLICY_TYPE", "LIFE_INSURANCE_PERSONAL", currentLoginUsers.getLanguage()).getData();
        String typeName = policyType.getCodeName();
        Map<String, Integer> productMap = new HashMap<>();
        // 产品开始列
        int productAmountColumn = 34;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_3.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_8.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_9.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_5.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_13.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_19.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20A.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_21.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_24.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_28.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_34.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_4.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_14.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_15.id(), productAmountColumn++);
        // 16wop空置一列
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_22.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23A.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23B.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_17.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_29.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_11.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_12.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_18.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_26.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_27.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_33.id(), productAmountColumn++);
        //国际化
        int i = 1;
        for (ActualPerformanceReportBo aprb : aprbList) {
            int rownum = i + 3;
            Row writeRow = sheet.getRow(rownum);
            if (!AssertUtils.isNotNull(writeRow)) {
                writeRow = sheet.createRow(rownum);
                writeRow.setHeight((short) (20 * 20));
            }
            int column = 0;
            getCell(cellStyle, writeRow, column++).setCellValue(i);
            getCell(cellStyle, writeRow, column++).setCellValue(languageUtils.getCodeName(endorseProject, aprb.getProjectCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyYear()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplicantName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(aprb.getProductId(), aprb.getProductName())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPrimaryFlagName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getDutyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductLevelName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(typeName));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyEffectiveDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getEffectiveDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getActualPayDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getMult()));
            BigDecimal totalPremium = aprb.getTotalPremium();
            String pay = "";
            if (AssertUtils.isNotNull(aprb.getRefundAmount()) && aprb.getRefundAmount().compareTo(BigDecimal.ZERO) != 0) {
                totalPremium = aprb.getRefundAmount();
                pay = "-";
            }
            if (ADD_ADDITIONAL.name().equals(aprb.getProjectCode()) && AssertUtils.isNotNull(aprb.getMult())
                    && AssertUtils.isNotNull(totalPremium) && 0 != totalPremium.compareTo(BigDecimal.ZERO)) {
                aprb.setPremium(totalPremium.divide(new BigDecimal(aprb.getMult()), 2, BigDecimal.ROUND_HALF_UP));
            }
            //getCell(cellStyle, writeRow, column++).setCellValue((ProductTermEnum.PRODUCT.PRODUCT_12.id().equals(aprb.getProductId()) ? "--" : exportPrint(aprb.getPremium())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(pay + exportPrint(totalPremium));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getReceiptNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumFrequencyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getFrequency()));
            if (AssertUtils.isNotNull(aprb.getPremiumPeriod())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumPeriod()));
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentMethodName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getBankName()));
            //网销的不展示支付码
            if (AssertUtils.isNotEmpty(aprb.getSalesBranchId()) && "ONLINE".equals(aprb.getSalesBranchId())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentCodeNo()));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getSalesBranchName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getChannelTypeName()));
            String premiumType = "FYP";
//            if (AssertUtils.isNotEmpty(aprb.getEffectiveDateFormat()) && AssertUtils.isNotEmpty(aprb.getPolicyEffectiveDateFormat())) {
//                try {
//                    /**
//                     * 如果（保费到期日 - 生效日）小于或等于365天，则分类为FYP。
//                     * 如果（保费到期日 - 生效日）大于365天，则分类为RYP。`
//                     * 这两条逻辑仅适用于续期和个险保全表。
//                     */
//                    long startTime = DateUtils.stringToTime(aprb.getPolicyEffectiveDateFormat());
//                    long endTime = DateUtils.stringToTime(aprb.getEffectiveDateFormat());
//                    long days = DateUtils.intervalDayByMillisCeil(startTime, endTime);
//                    if (days > 365) {
//                        premiumType = "RYP";
//                    }
//                } catch (Exception e) {
//                }
//            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(premiumType));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRemarks()));
            exportPrint(cellStyle, writeRow, productMap.get(aprb.getProductId()), aprb);
            i++;
        }
    }

    /**
     * 续保
     *
     * @param workbook
     * @param cellStyle
     */
    private void exportActualPerformanceRenewalPolicy(XSSFWorkbook workbook, CellStyle cellStyle, List<ActualPerformanceReportBo> reportPaymentPoList, Users currentLoginUsers) throws Exception {
        XSSFSheet sheet = workbook.getSheetAt(2);
        List<String> policyTypes = Arrays.asList(POLICY_RENEWAL_INSURANCE.name(), GROUP_RENEWAL.name());
        List<ActualPerformanceReportBo> aprbList = reportPaymentPoList.stream()
                .filter(actualPerformanceReportBo -> policyTypes.contains(actualPerformanceReportBo.getBusinessType()))
                .collect(Collectors.toList());
        if (!AssertUtils.isNotEmpty(aprbList)) {
            return;
        }

        Map<String, Integer> productMap = new HashMap<>();
        // 产品开始列
        int productAmountColumn = 34;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_3.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_8.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_9.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_5.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_13.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_19.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20A.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_21.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_24.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_28.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_34.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_4.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_14.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_15.id(), productAmountColumn++);
        // 16wop空置一列
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_22.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23A.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23B.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_17.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_29.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_11.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_12.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_18.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_26.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_27.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_33.id(), productAmountColumn++);
        //国际化
        int i = 1;
        for (ActualPerformanceReportBo aprb : aprbList) {
            int rownum = i + 3;
            Row writeRow = sheet.getRow(rownum);
            if (!AssertUtils.isNotNull(writeRow)) {
                writeRow = sheet.createRow(rownum);
                writeRow.setHeight((short) (20 * 20));
            }
            int column = 0;
            getCell(cellStyle, writeRow, column++).setCellValue(i);
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getBusinessTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyYear()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getFrequency()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplicantName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(aprb.getProductId(), aprb.getProductName())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPrimaryFlagName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getDutyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductLevelName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getCoverageTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getEffectiveDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRenewalDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getActualPayDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getMult()));
            //getCell(cellStyle, writeRow, column++).setCellValue((ProductTermEnum.PRODUCT.PRODUCT_12.id().equals(aprb.getProductId()) ? "--" : exportPrint(aprb.getPremium())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getTotalPremium()));
            Object specialDiscount = null;
            BigDecimal totalPremium = aprb.getTotalPremium();
            if (AssertUtils.isNotNull(aprb.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(aprb.getDiscountModel())) {
                totalPremium = aprb.getTotalPremium().subtract(aprb.getTotalPremium().multiply(aprb.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
                specialDiscount = aprb.getSpecialDiscount().multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
            }
            if (AssertUtils.isNotNull(aprb.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(aprb.getDiscountModel())) {
                totalPremium = aprb.getTotalPremium().subtract(aprb.getSpecialDiscount());
                specialDiscount = "$" + aprb.getSpecialDiscount().stripTrailingZeros().toPlainString();
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(specialDiscount));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(totalPremium));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getReceiptNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumFrequencyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getFrequency()));
            if (AssertUtils.isNotNull(aprb.getPremiumPeriod())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumPeriod()));
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentMethodName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getBankName()));
            //网销的不展示支付码
            if (AssertUtils.isNotEmpty(aprb.getSalesBranchId()) && "ONLINE".equals(aprb.getSalesBranchId())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentCodeNo()));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getSalesBranchName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getChannelTypeName()));
            // 续期是RYP
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint("FYP"));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRemarks()));
            exportPrint(cellStyle, writeRow, productMap.get(aprb.getProductId()), aprb);
            i++;
        }

    }

    /**
     * 续期
     *
     * @param workbook
     * @param cellStyle
     */
    private void exportActualPerformanceRenewal(XSSFWorkbook workbook, CellStyle cellStyle, List<ActualPerformanceReportBo> reportPaymentPoList, Users currentLoginUsers) throws Exception {
        XSSFSheet sheet = workbook.getSheetAt(1);
        List<ActualPerformanceReportBo> aprbList = reportPaymentPoList.stream().filter(actualPerformanceReportBo -> POLICY_RENEWAL_PAYMENT.name().equals(actualPerformanceReportBo.getBusinessType())).collect(Collectors.toList());
        if (!AssertUtils.isNotEmpty(aprbList)) {
            return;
        }
        SyscodeResponse policyType = platformInternationalBaseApi.queryOneInternational("PAYMENT_BUSINESS_TYPE", "POLICY_RENEWAL_PAYMENT", currentLoginUsers.getLanguage()).getData();
        String codeName = policyType.getCodeName();
        Map<String, Integer> productMap = new HashMap<>();
        // 产品开始列
        int productAmountColumn = 34;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_3.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_8.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_9.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_5.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_13.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_19.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20A.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_21.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_24.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_28.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_34.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_4.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_14.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_15.id(), productAmountColumn++);
        // 16wop空置一列
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_22.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23A.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23B.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_17.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_29.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_11.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_12.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_18.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_26.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_27.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_33.id(), productAmountColumn++);
        //国际化
        int i = 1;
        for (ActualPerformanceReportBo aprb : aprbList) {
            int rownum = i + 3;
            Row writeRow = sheet.getRow(rownum);
            if (!AssertUtils.isNotNull(writeRow)) {
                writeRow = sheet.createRow(rownum);
                writeRow.setHeight((short) (20 * 20));
            }
            int column = 0;
            getCell(cellStyle, writeRow, column++).setCellValue(i);
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(codeName));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyYear()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplicantName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(aprb.getProductId(), aprb.getProductName())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPrimaryFlagName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getDutyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductLevelName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getCoverageTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyEffectiveDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRenewalDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getActualPayDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getMult()));
            //getCell(cellStyle, writeRow, column++).setCellValue((ProductTermEnum.PRODUCT.PRODUCT_12.id().equals(aprb.getProductId()) ? "--" : exportPrint(aprb.getPremium())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getTotalPremium()));
            Object specialDiscount = null;
            BigDecimal totalPremium = aprb.getTotalPremium();
            if (AssertUtils.isNotNull(aprb.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(aprb.getDiscountModel())) {
                totalPremium = aprb.getTotalPremium().subtract(aprb.getTotalPremium().multiply(aprb.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
                specialDiscount = aprb.getSpecialDiscount().multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
            }
            if (AssertUtils.isNotNull(aprb.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(aprb.getDiscountModel())) {
                totalPremium = aprb.getTotalPremium().subtract(aprb.getSpecialDiscount());
                specialDiscount = "$" + aprb.getSpecialDiscount().stripTrailingZeros().toPlainString();
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(specialDiscount));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(totalPremium));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getReceiptNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumFrequencyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getFrequency()));
            if (AssertUtils.isNotNull(aprb.getPremiumPeriod())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumPeriod()));
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentMethodName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getBankName()));
            //网销的不展示支付码
            if (AssertUtils.isNotEmpty(aprb.getSalesBranchId()) && "ONLINE".equals(aprb.getSalesBranchId())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentCodeNo()));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getSalesBranchName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getChannelTypeName()));
            String premiumType = "RYP";
            if (AssertUtils.isNotEmpty(aprb.getRenewalDateFormat()) && AssertUtils.isNotEmpty(aprb.getPolicyEffectiveDateFormat())) {
                try {
                    /**
                     * 如果（保费到期日 - 生效日）小于或等于365天，则分类为FYP。
                     * 如果（保费到期日 - 生效日）大于365天，则分类为RYP。`
                     * 这两条逻辑仅适用于续期和个险保全表。
                     */
                    long startTime = DateUtils.stringToTime(aprb.getPolicyEffectiveDateFormat());
                    long endTime = DateUtils.stringToTime(aprb.getRenewalDateFormat());
                    long days = DateUtils.intervalDayByMillisCeil(startTime, endTime);
                    if (days <= 365) {
                        premiumType = "FYP";
                    }
                } catch (Exception e) {
                }
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(premiumType));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRemarks()));
            exportPrint(cellStyle, writeRow, productMap.get(aprb.getProductId()), aprb);
            i++;
        }
    }

    /**
     * 新契约
     *
     * @param workbook
     * @param cellStyle
     */
    private void exportActualPerformanceApply(XSSFWorkbook workbook, CellStyle cellStyle, List<ActualPerformanceReportBo> reportPaymentPoList, Users currentLoginUsers) throws Exception {
        XSSFSheet sheet = workbook.getSheetAt(0);
        List<String> policyTypeList = Arrays.asList(APPLY.name(), APPLY_GROUP.name());
        List<ActualPerformanceReportBo> aprbList = reportPaymentPoList.stream().filter(actualPerformanceReportBo -> policyTypeList.contains(actualPerformanceReportBo.getBusinessType())).collect(Collectors.toList());
        if (!AssertUtils.isNotEmpty(aprbList)) {
            return;
        }
        SyscodeResponse policyType = platformInternationalBaseApi.queryOneInternational("RESOURCE", "RESOURCE_APPLY", currentLoginUsers.getLanguage()).getData();
        String codeName = policyType.getCodeName();
        Map<String, Integer> productMap = new HashMap<>();
        // 产品开始列
        int productAmountColumn = 34;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_3.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_8.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_9.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_5.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_13.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_19.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_20A.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_21.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_24.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_28.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_34.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_4.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_14.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_15.id(), productAmountColumn++);
        // 16wop空置一列
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_22.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23A.id(), productAmountColumn);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_23B.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_17.id(), productAmountColumn++);
        productAmountColumn++;
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_29.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_11.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_12.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_18.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_26.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_27.id(), productAmountColumn++);
        productMap.put(ProductTermEnum.PRODUCT.PRODUCT_33.id(), productAmountColumn++);
        int i = 1;
        for (ActualPerformanceReportBo aprb : aprbList) {
            int rownum = i + 3;
            Row writeRow = sheet.getRow(rownum);
            if (!AssertUtils.isNotNull(writeRow)) {
                writeRow = sheet.createRow(rownum);
                writeRow.setHeight((short) (20 * 20));
            }
            int column = 0;
            getCell(cellStyle, writeRow, column++).setCellValue(i);
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(codeName));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPolicyNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint("1"));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getApplicantName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(aprb.getProductId(), aprb.getProductName())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPrimaryFlagName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getDutyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getProductLevelName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getCoverageTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getEffectiveDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRenewalDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getActualPayDateFormat()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getMult()));
            //getCell(cellStyle, writeRow, column++).setCellValue((ProductTermEnum.PRODUCT.PRODUCT_12.id().equals(aprb.getProductId()) ? "--" : exportPrint(aprb.getPremium())));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getTotalPremium()));
            Object specialDiscount = null;
            BigDecimal totalPremium = aprb.getTotalPremium();
            if (AssertUtils.isNotNull(aprb.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(aprb.getDiscountModel())) {
                totalPremium = aprb.getTotalPremium().subtract(aprb.getTotalPremium().multiply(aprb.getSpecialDiscount())).setScale(2, BigDecimal.ROUND_HALF_UP);
                specialDiscount = aprb.getSpecialDiscount().multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString() + "%";
            }
            if (AssertUtils.isNotNull(aprb.getSpecialDiscount()) && ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(aprb.getDiscountModel())) {
                totalPremium = aprb.getTotalPremium().subtract(aprb.getSpecialDiscount());
                specialDiscount = "$" + aprb.getSpecialDiscount().stripTrailingZeros().toPlainString();
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(specialDiscount));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(totalPremium));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getReceiptNo()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumFrequencyName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint("1"));
            if (AssertUtils.isNotNull(aprb.getPremiumPeriod())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPremiumPeriod()));
            } else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentMethodName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getBankName()));
            //网销的不展示支付码
            if (AssertUtils.isNotEmpty(aprb.getSalesBranchId()) && "ONLINE".equals(aprb.getSalesBranchId())) {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(null));
            }else {
                getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getPaymentCodeNo()));
            }
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getSalesBranchName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentCode()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getAgentTypeName()));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getChannelTypeName()));
            // 新契约展示FYP
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint("FYP"));
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aprb.getRemarks()));
            exportPrint(cellStyle, writeRow, productMap.get(aprb.getProductId()), aprb);
            i++;
        }
    }


    private void exportPrint(CellStyle cellStyle, Row writeRow, Integer column, ActualPerformanceReportBo aprb) throws Exception {
        if (!AssertUtils.isNotNull(column)) {
            return;
        }
        Long frequency = aprb.getFrequency();
        String productId = aprb.getProductId();
        String productLevel = aprb.getProductLevel();
        String mult = aprb.getMult();
        mult = AssertUtils.isNotEmpty(mult) ? mult : "1";
        Long insuredBirthday = aprb.getInsuredBirthday();
        int age = AssertUtils.isNotNull(insuredBirthday) ? DateUtils.getAgeYear(new Date(insuredBirthday)) : 0;
        String coveragePeriod = AssertUtils.isNotNull(aprb.getCoveragePeriod()) ? aprb.getCoveragePeriod() : "0";
        BigDecimal coverageTotalAmount = AssertUtils.isNotNull(aprb.getTotalAmount()) ? aprb.getTotalAmount() : new BigDecimal(0);
        BigDecimal coverageAmount = AssertUtils.isNotNull(aprb.getAmount()) ? aprb.getAmount() : new BigDecimal(0);
        coverageTotalAmount = coverageTotalAmount.doubleValue() > coverageAmount.doubleValue() ? coverageTotalAmount : coverageAmount;
        String dutyId = aprb.getDutyId();
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_1.id())) {
            //1号：意外死亡或高残金乘以份数，
            int amount = "A".equals(productLevel) ? 5000 : "B".equals(productLevel) ? 10000 : "C".equals(productLevel) ? 20000 : 0;
            BigDecimal multiply = new BigDecimal(amount).multiply(new BigDecimal(mult));
            if (AssertUtils.isNotNull(frequency)) {
                if (frequency.equals(new Long(1))) {
                    multiply = multiply.add(multiply.multiply(new BigDecimal(0.1)));
                } else if (frequency.equals(new Long(2))) {
                    multiply = multiply.add(multiply.multiply(new BigDecimal(0.2)));
                } else if (frequency >= new Long(3)) {
                    multiply = multiply.add(multiply.multiply(new BigDecimal(0.3)));
                }
            }
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(multiply));
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_3.id())) {
            // 3号：每份可以领取教育金总额乘以份数
            BigDecimal totalXxInsuranceAmount = new BigDecimal(300)
                    .multiply(new BigDecimal(mult)).multiply(new BigDecimal("A".equals(productLevel) && age < 13 ? 13 - (age > 7 ? age : 7) : 0));
            BigDecimal totalZxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 400 : "B".equals(productLevel) ? 500 : 0)
                    .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age < 19 ? 19 - (age > 13 ? age : 13) : 0));
            BigDecimal totalDxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 500 : "B".equals(productLevel) ? 700 : "C".equals(productLevel) ? 1200 : 0)
                    .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age < 23 ? 23 - (age > 19 ? age : 19) : 0));
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(totalXxInsuranceAmount.add(totalZxInsuranceAmount).add(totalDxInsuranceAmount)));
        }
        // 4号：保额，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_4.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount.multiply(new BigDecimal(mult))));
        }
        // 7号：保险金额乘以份数，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_7.id())) {
            int amount = "A".equals(productLevel) ? 480 : "B".equals(productLevel) ? 1200 : "C".equals(productLevel) ? 2880 : "D".equals(productLevel) ? 6600 : 0;
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(new BigDecimal(amount).multiply(new BigDecimal(mult))));
        }
        // 8号： 1.保健金：一共可以领取的保健金总和，      2.意外死亡或高残金：保额乘以300%，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_8.id())) {
            BigDecimal protectHealthGold = new BigDecimal((Integer.parseInt(coveragePeriod) - 6) / 3)
                    .multiply(coverageTotalAmount.multiply(new BigDecimal(0.1))).add(coverageTotalAmount);
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(protectHealthGold));
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount.multiply(new BigDecimal(3))));
        }
        // 9号：保额乘以400%，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_9.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount.multiply(new BigDecimal(4))));
        }
        // 5号：保额
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_5.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }
        // 1+：意外死亡或高残金乘以份数，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id())) {
            int amount = "A".equals(productLevel) ? 5000 : "B".equals(productLevel) ? 10000 : "C".equals(productLevel) ? 20000 : 0;
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(new BigDecimal(amount).multiply(new BigDecimal(mult))));
        }
        // 7+：每个档次保险金额份数之和
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id())) {
            int amount = "A".equals(productLevel) ? 1440 : "B".equals(productLevel) ? 2880 : "C".equals(productLevel) ? 6600 : 0;
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(new BigDecimal(amount).multiply(new BigDecimal(mult))));
        }
        // 11号：每个档次保险金额（1）份数之和,
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_11.id())) {
            int amount = "A".equals(productLevel) ? 400 : "B".equals(productLevel) ? 600 : "C".equals(productLevel) ? 800 : 0;
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(new BigDecimal(amount).multiply(new BigDecimal(mult))));
        }
        // 12号：每个选项对应档次保险金额乘以份数之和
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_12.id())) {
            int amount = "PRO8800000000000G12_DUTY_1".equals(dutyId) ? ("A".equals(productLevel) ? 300 : "B".equals(productLevel) ? 500 : "C".equals(productLevel) ? 1000 : 0) :
                    "PRO8800000000000G12_DUTY_2".equals(dutyId) ? ("A".equals(productLevel) ? 2000 : "B".equals(productLevel) ? 3000 : "C".equals(productLevel) ? 5000 : 0) :
                            "PRO8800000000000G12_DUTY_3".equals(dutyId) ? ("A".equals(productLevel) ? 3000 : "B".equals(productLevel) ? 6000 : "C".equals(productLevel) ? 10000 : 0) : 0;
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(new BigDecimal(amount).multiply(new BigDecimal(mult))));
        }
        //
        if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_14.id(), ProductTermEnum.PRODUCT.PRODUCT_15.id()).contains(productId)) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }
        // 17号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_17.id())) {
            getCell(cellStyle, writeRow, column++).setCellValue(exportPrint("TA".equals(productLevel) ? null : coverageTotalAmount));
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint("TA".equals(productLevel) ? coverageTotalAmount : null));
        }
        //18号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_18.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //19号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_19.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //20号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_20.id()) || productId.equals(ProductTermEnum.PRODUCT.PRODUCT_20A.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //21号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_21.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //22号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_22.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //23A号,23B号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_23A.id()) || productId.equals(ProductTermEnum.PRODUCT.PRODUCT_23B.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //24号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_24.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //34号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_34.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }
        //26号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_26.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //27号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_27.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }
        //28号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_28.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //29号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_29.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }

        //29号
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_33.id())) {
            getCell(cellStyle, writeRow, column).setCellValue(exportPrint(coverageTotalAmount));
        }
    }

    //金额格式化
    private static final DecimalFormat decimalFormat = new DecimalFormat("###,###,###,###,##0.00");

    public static String exportPrint(Object o) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                o = ((BigDecimal) o).setScale(2, RoundingMode.HALF_UP);
                return decimalFormat.format(o);
            }
            return o + "";
        }
        return "";
    }

    public static Cell getCell(CellStyle cellStyle, Row writeRow, int column) {
        if (AssertUtils.isNotNull(writeRow.getCell(column))) {
            return writeRow.getCell(column);
        }
        Cell cell = writeRow.createCell(column);
        cell.setCellStyle(cellStyle);
        return cell;
    }


}
