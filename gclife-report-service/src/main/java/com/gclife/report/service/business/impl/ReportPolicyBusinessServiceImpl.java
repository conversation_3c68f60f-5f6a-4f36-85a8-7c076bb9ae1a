package com.gclife.report.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentListResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.BaseApplyApi;
import com.gclife.apply.model.respone.ApplyApplicantListResponse;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.message.api.BusinessMessageApi;
import com.gclife.message.model.request.BusinessMessagePushBatchRequest;
import com.gclife.message.model.request.BusinessMessagePushRequest;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformCareerApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformUsersApi;
import com.gclife.platform.model.response.AreaTreeResponse;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyApplicantListResponse;
import com.gclife.policy.model.response.PolicyResponse;
import com.gclife.policy.model.response.PolicyServiceAgentResponse;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.model.bo.ReportCashTransactionBo;
import com.gclife.report.model.bo.ReportPolicyBo;
import com.gclife.report.model.bo.ReportSalePolicyBo;
import com.gclife.report.model.bo.SendMonthlyCashTransactionMsgBo;
import com.gclife.report.model.config.ReportErrorConfigEnum;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.request.ReportCashTransactionRequest;
import com.gclife.report.model.request.ReportPolicyListRequest;
import com.gclife.report.model.response.ReportCashTransactionResponse;
import com.gclife.report.model.response.ReportPolicyInitResponse;
import com.gclife.report.model.response.ReportPolicyListResponse;
import com.gclife.report.model.response.SendMonthlyCashTransactionMsgResponse;
import com.gclife.report.service.ReportActualPerformanceBaseService;
import com.gclife.report.service.ReportQueryBaseService;
import com.gclife.report.service.ReportSaleApplyPolicyBaseService;
import com.gclife.report.service.business.ReportPolicyBusinessService;
import com.gclife.report.validate.ReportBusinessValidate;
import com.gclife.report.validate.transform.LanguageUtils;
import com.gclife.report.validate.transform.ReportListTransData;
import com.gclife.thirdparty.api.ThirdPartyMsgApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jooq.tools.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.report.model.config.ReportErrorConfigEnum.REPORT_QUERY_OPERATION_DATE_IS_NOT_NULL;
import static com.gclife.report.model.config.ReportErrorConfigEnum.REPORT_UNDERWRITING_REPORT_IS_NULL;
import static com.gclife.report.model.config.ReportTermEnum.PAYMENT_BUSINESS_TYPE.POLICY_ENDORSE;
import static com.gclife.report.model.config.ReportTermEnum.PRODUCT_PREMIUM_FREQUENCY.*;

/**
 * <AUTHOR>
 * create 18-11-16
 * description:
 */
@Slf4j
@Service
public class ReportPolicyBusinessServiceImpl extends BaseBusinessServiceImpl implements ReportPolicyBusinessService {
    @Autowired
    private ReportQueryBaseService reportQueryBaseService;
    @Autowired
    private AttachmentApi attachmentServiceInterface;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Autowired
    private PlatformCareerApi platformCareerApi;

    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;

    @Autowired
    private LanguageUtils languageUtils;
    @Autowired
    private ReportListTransData reportListTransData;
    @Autowired
    private ReportBusinessValidate reportBusinessValidate;

    @Autowired
    private AgentApi agentApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;

    @Autowired
    private PolicyApi policyApi;

    @Autowired
    private ReportActualPerformanceBaseService reportActualPerformanceBaseService;

    @Autowired
    private ReportSaleApplyPolicyBaseService reportSaleApplyPolicyBaseService;
    @Autowired
    private BaseApplyApi baseApplyApi;

    @Autowired
    private PlatformUsersApi platformUsersApi;

    @Autowired
    private BusinessMessageApi businessMessageApi;

    @Override
    public ResultObject<BasePageResponse<ReportPolicyListResponse>> queryReportPolicyPageList(Users users, ReportPolicyListRequest reportPolicyListRequest) {
        ResultObject<BasePageResponse<ReportPolicyListResponse>> resultObject = new ResultObject<>();
        try {
            List<ReportPolicyBo> reportPolicyBos = new ArrayList<>();
            //业务报表-承保清单
            if (ReportTermEnum.REPORT_TYPE.POLICY.name().equals(reportPolicyListRequest.getReportType())) {
                ResultObject<List<String>> listResultObject = policyApi.queryPolicyNos(reportPolicyListRequest.getKeyword());
                if (AssertUtils.isNotNull(listResultObject)) {
                    List<String> policyIds = listResultObject.getData();
                    reportPolicyListRequest.setPolicyIds(policyIds);
                }
                reportPolicyBos = reportQueryBaseService.queryListReportPolicy(reportPolicyListRequest, "list");
            }

            //监管报表-承保清单
            if (ReportTermEnum.REPORT_TYPE.REGULATORY_POLICY.name().equals(reportPolicyListRequest.getReportType())) {
                ResultObject<List<String>> listResultObject = policyApi.queryPolicyNos(reportPolicyListRequest.getKeyword());
                if (AssertUtils.isNotNull(listResultObject)) {
                    List<String> policyIds = listResultObject.getData();
                    reportPolicyListRequest.setPolicyIds(policyIds);
                }
                reportPolicyBos = reportQueryBaseService.queryListReportRegulatoryPolicy(reportPolicyListRequest, "list");
            }
            if (!AssertUtils.isNotEmpty(reportPolicyBos)) {
                return resultObject;
            }
            List<ReportPolicyListResponse> reportPolicyListResponse = reportListTransData.transReportPolicyList(users, reportPolicyBos);

            // 个险承保清单 新增服务业务员信息
            if (ReportTermEnum.REPORT_TYPE.POLICY.name().equals(reportPolicyListRequest.getReportType())) {
                List<String> policyIds = reportPolicyListResponse.stream()
                        .map(ReportPolicyListResponse::getPolicyId)
                        .collect(Collectors.toList());

                // 设置最新的服务业务员
                ResultObject<List<PolicyServiceAgentResponse>> resultObjectListNewServiceAgentResponse = policyApi.listNewServiceAgentPoByPolicyIds(policyIds);
                if (!AssertUtils.isResultObjectDataNull(resultObjectListNewServiceAgentResponse)) {
                    List<PolicyServiceAgentResponse> newServiceAgentResponses = resultObjectListNewServiceAgentResponse.getData();

                    List<String> serviceAgentIds = newServiceAgentResponses.stream()
                            .map(PolicyServiceAgentResponse::getServiceAgentId)
                            .collect(Collectors.toList());

                    ResultObject<List<AgentListResponse>> userAgents = agentBaseAgentApi.queryOneAgentById(serviceAgentIds);
                    if (!AssertUtils.isResultObjectDataNull(userAgents)) {
                        List<AgentListResponse> serviceAgents = userAgents.getData();
                        reportPolicyListResponse.stream()
                                .filter(reportPolicyResponse -> AssertUtils.isNotEmpty(reportPolicyResponse.getPolicyId()))
                                .forEach(reportPolicyResponse -> newServiceAgentResponses.forEach(newServiceAgentResponse -> {
                                    if (newServiceAgentResponse.getPolicyId().equals(reportPolicyResponse.getPolicyId())) {
                                        serviceAgents.forEach(serviceAgent -> {
                                            if (serviceAgent.getAgentId().equals(newServiceAgentResponse.getServiceAgentId())) {
                                                reportPolicyResponse.setServiceAgentCode(serviceAgent.getAgentCode());
                                                reportPolicyResponse.setServiceAgentName(serviceAgent.getAgentName());
                                                reportPolicyResponse.setServiceAgentMobil(serviceAgent.getMobile());
                                            }
                                        });
                                    }
                                }));
                    }
                }

                reportPolicyListResponse.forEach(reportPolicyListResponse1 -> {
                    if (AssertUtils.isNotNull(reportPolicyListResponse1.getPolicyId())) {
                        ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(reportPolicyListResponse1.getPolicyId());
                        if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                            reportPolicyListResponse1.setRiskCommencementDateFormat(DateUtils.timeStrToString(policyResponseResultObject.getData().getRiskCommencementDate(), DateUtils.FORMATE3));
                        }
                    }
                });

            }
            //获取总数
            Integer totalLine = AssertUtils.isNotNull(reportPolicyBos) ? reportPolicyBos.get(0).getTotalLine() : null;
            BasePageResponse<ReportPolicyListResponse> basePageResponse = BasePageResponse.getData(reportPolicyListRequest.getCurrentPage(), reportPolicyListRequest.getPageSize(), totalLine, reportPolicyListResponse);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ReportErrorConfigEnum.REPORT_QUERY_REPORT_POLICY_DATA_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<ReportPolicyInitResponse> initReportPolicy(Users users) {
        ResultObject<ReportPolicyInitResponse> resultObject = new ResultObject<>();
        try {
            List<SyscodeResponse> productTypeRespFcs = platformInternationalBaseApi.queryInternational(ReportTermEnum.PRODUCT_TYPE.PRODUCT_TYPE.name(), users.getLanguage()).getData();
            ReportPolicyInitResponse reportPolicyInitResponse = new ReportPolicyInitResponse();
            reportPolicyInitResponse.setProductTypeCode(productTypeRespFcs);
            resultObject.setData(reportPolicyInitResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ReportErrorConfigEnum.REPORT_QUERY_REPORT_POLICY_INIT_DATA_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject exportReportPolicyList(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPolicyListRequest reportPolicyListRequest) {
        ResultObject resultObject = new ResultObject();
        try {
            reportBusinessValidate.validateExportReportPolicyList(reportPolicyListRequest);

            List<String> codeTypes = Arrays.asList(
                    TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(),
                    TerminologyTypeEnum.CHANNEL_TYPE.name(),
                    TerminologyTypeEnum.POLICY_STATUS.name(),
                    TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(),
                    TerminologyTypeEnum.PAYMENT_METHODS.name(),
                    TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(),
                    TerminologyTypeEnum.IS_HEAD_OFFICE.name(),
                    TerminologyTypeEnum.ID_TYPE.name()
            );
            ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
            Map<String, List<SyscodeResponse>> data = mapResultObject.getData();
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.REPORT_POLICY_TEMPLATE.name() + "_" + currentLoginUsers.getLanguage());
            AssertUtils.isResultObjectError(this.getLogger(), attachmentRespFcResultObject);
            AttachmentResponse attachmentResponse = attachmentRespFcResultObject.getData();
            URL url = new URL(attachmentResponse.getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            cellStyle.setFillForegroundColor(HSSFColor.BLACK.index);
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
            cellStyle.setBorderLeft(CellStyle.BORDER_THIN);// 左边框
            cellStyle.setBorderTop(CellStyle.BORDER_THIN);// 上边框
            cellStyle.setBorderRight(CellStyle.BORDER_THIN);// 右边框
            //得到工作表
            Sheet sheet = workbook.getSheetAt(0);
            try {
                List<ReportPolicyBo> reportPolicyBos = reportQueryBaseService.queryListReportPolicy(reportPolicyListRequest, "export");
                AssertUtils.isNotEmpty(this.getLogger(), reportPolicyBos, REPORT_UNDERWRITING_REPORT_IS_NULL);

                //增加风险开始日期
                reportPolicyBos.forEach(reportPolicyBo -> {
                    if (AssertUtils.isNotNull(reportPolicyBo)) {
                        ResultObject<PolicyResponse> policyResponseResultObject = policyApi.queryOnePolicy(reportPolicyBo.getPolicyId());
                        if (!AssertUtils.isResultObjectDataNull(policyResponseResultObject)) {
                            reportPolicyBo.setRiskCommencementDateFormat(DateUtils.timeStrToString(policyResponseResultObject.getData().getRiskCommencementDate(), DateUtils.FORMATE3));
                        }
                    }
                });

                //职业
                List<String> careerIdas = reportPolicyBos.stream().filter(reportCustomerBo -> AssertUtils.isNotEmpty(reportCustomerBo.getApplicantOccupationCode()))
                        .map(ReportPolicyBo::getApplicantOccupationCode).collect(Collectors.toList());
                ResultObject<List<CareerNameResponse>> applicantCareerNameRespFcObject = platformCareerApi.postCareerName(careerIdas);

                List<String> careerIdis = reportPolicyBos.stream().filter(reportCustomerBo -> AssertUtils.isNotEmpty(reportCustomerBo.getInsuredOccupationCode()))
                        .map(ReportPolicyBo::getInsuredOccupationCode).collect(Collectors.toList());
                ResultObject<List<CareerNameResponse>> insuredCareerNameRespFcObject = platformCareerApi.postCareerName(careerIdis);

                List<String> agentIds = reportPolicyBos.stream().filter(reportPolicyBo -> AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())).map(ReportPolicyBo::getAgentId).distinct().collect(Collectors.toList());
                // agentIds 加入服务业务人员ID
                List<String> serviceAgentIds = new ArrayList<>();
                List<String> policyIds = reportPolicyBos.stream()
                        .map(ReportPolicyBo::getPolicyId)
                        .collect(Collectors.toList());
                ResultObject<List<PolicyServiceAgentResponse>> resultObjectListNewServiceAgentResponse = policyApi.listNewServiceAgentPoByPolicyIds(policyIds);
                if (!AssertUtils.isResultObjectDataNull(resultObjectListNewServiceAgentResponse)) {
                    List<PolicyServiceAgentResponse> newServiceAgentResponses = resultObjectListNewServiceAgentResponse.getData();
                    serviceAgentIds = newServiceAgentResponses.stream()
                            .map(PolicyServiceAgentResponse::getServiceAgentId)
                            .collect(Collectors.toList());
                }
                // 加入服务业务人员ID
                agentIds.addAll(serviceAgentIds);
                agentIds = agentIds.stream().distinct().collect(Collectors.toList());
                AgentApplyQueryRequest applyAgentReqFc = new AgentApplyQueryRequest();
                applyAgentReqFc.setListAgentId(agentIds);
                List<AgentResponse> agentRespFcs = agentApi.agentsGet(applyAgentReqFc).getData();

                int num = 1;
                for (ReportPolicyBo reportPolicyBo : reportPolicyBos) {
                    Row writeRow = sheet.createRow(num + 1);
                    writeRow.setHeight((short) (20 * 20));
                    int column = 0;
                    getCell(cellStyle, writeRow, column++).setCellValue(num++);
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getPolicyNo()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getApplyNo()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(LanguageUtils.getENProductName(reportPolicyBo.getProductId(), reportPolicyBo.getProductName())));
                    String primaryFlag = languageUtils.getCodeName(data.get(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name()), reportPolicyBo.getPrimaryFlag());
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(primaryFlag));
                    String premiumFrequency = languageUtils.getCodeName(data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name()), reportPolicyBo.getPremiumFrequency());
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(premiumFrequency));
                    //特殊处理 将"[\"a\",\"b\"]"转换成对应的国际化
                    String paymentMethodCode = null;
                    if (AssertUtils.isNotEmpty(reportPolicyBo.getPaymentMethodCode())) {
                        List<String> methodCodeTrans = new ArrayList<>();
                        List<String> paymentMethodCodes = (List<String>) JSON.parse(reportPolicyBo.getPaymentMethodCode());
                        if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                            paymentMethodCodes.forEach(s -> methodCodeTrans.add(languageUtils.getCodeName(data.get(TerminologyTypeEnum.PAYMENT_METHODS.name()), s)));
                            paymentMethodCode = StringUtils.join(methodCodeTrans.toArray(), ",");
                        }
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(paymentMethodCode));
                    String coveragePeriodUnit = languageUtils.getCodeName(data.get(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name()), reportPolicyBo.getCoveragePeriodUnit());
                    String coveragePeriod = reportPolicyBo.getCoveragePeriod();
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(coveragePeriod) + exportPrint(coveragePeriodUnit));
                    if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_19.id(), ProductTermEnum.PRODUCT.PRODUCT_20.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id()).contains(reportPolicyBo.getProductId())
                            && AssertUtils.isNotNull(reportPolicyBo.getTotalPremium()) && MONTH.name().equals(reportPolicyBo.getPrimaryFlag())) {
                        reportPolicyBo.setTotalPremium(reportPolicyBo.getTotalPremium().divide(new BigDecimal(3), 2, BigDecimal.ROUND_HALF_UP));
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getTotalPremium()));
                    BigDecimal totalPremium = reportPolicyBo.getTotalPremium();
                    if (MONTH.name().equals(reportPolicyBo.getPremiumFrequency())) {
                        totalPremium = totalPremium.multiply(new BigDecimal(12));
                    } else if (SEASON.name().equals(reportPolicyBo.getPremiumFrequency())) {
                        totalPremium = totalPremium.multiply(new BigDecimal(4));
                    } else if (SEMIANNUAL.name().equals(reportPolicyBo.getPremiumFrequency())) {
                        totalPremium = totalPremium.multiply(new BigDecimal(2));
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(totalPremium));
                    String approveDate = DateUtils.timeStrToString(reportPolicyBo.getApproveDate(), DateUtils.FORMATE6);
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(approveDate));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getRiskCommencementDateFormat()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getApplicantName()));

                    String aipplicantIdType = languageUtils.getCodeName(data.get(TerminologyTypeEnum.ID_TYPE.name()), reportPolicyBo.getApplicantIdType());
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(aipplicantIdType));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getApplicantIdNo()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getApplicantMobile()));

                    String applicantOccupationCode = null;
                    if (AssertUtils.isNotEmpty(reportPolicyBo.getApplicantOccupationCode()) && !AssertUtils.isResultObjectListDataNull(applicantCareerNameRespFcObject)) {
                        applicantOccupationCode = languageUtils.getBaseCareerName(applicantCareerNameRespFcObject.getData(), reportPolicyBo.getApplicantOccupationCode());
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(applicantOccupationCode));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getApplicantCareerRiskLevel()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getInsuredName()));
                    String insuredIdType = languageUtils.getCodeName(data.get(TerminologyTypeEnum.ID_TYPE.name()), reportPolicyBo.getInsuredIdType());
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(insuredIdType));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getInsuredIdNo()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getInsuredMobile()));
                    String insuredOccupationCode = null;
                    if (AssertUtils.isNotEmpty(reportPolicyBo.getInsuredOccupationCode()) && !AssertUtils.isResultObjectListDataNull(insuredCareerNameRespFcObject)) {
                        insuredOccupationCode = languageUtils.getBaseCareerName(insuredCareerNameRespFcObject.getData(), reportPolicyBo.getInsuredOccupationCode());
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(insuredOccupationCode));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(reportPolicyBo.getInsuredCareerRiskLevel()));

                    ResultObject<BranchResponse> saleBranches = platformBranchBaseApi.queryOneBranchById(reportPolicyBo.getSalesBranchId());
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(saleBranches.getData().getBranchName()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.getCodeName(data.get(TerminologyTypeEnum.CHANNEL_TYPE.name()), reportPolicyBo.getChannelTypeCode())));

                    AgentResponse agent = new AgentResponse();
                    AgentResponse serviceAgent = null;
                    if (AssertUtils.isNotEmpty(agentRespFcs) && AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())) {
                        Optional<AgentResponse> optionalAgentResponse = agentRespFcs.stream().filter(applyAgentRespFc -> reportPolicyBo.getAgentId().equals(applyAgentRespFc.getAgentId())).findFirst();
                        if (optionalAgentResponse.isPresent()) {
                            agent = optionalAgentResponse.get();
                        }
                        if (!AssertUtils.isResultObjectDataNull(resultObjectListNewServiceAgentResponse)) {
                            List<PolicyServiceAgentResponse> newServiceAgentResponses = resultObjectListNewServiceAgentResponse.getData();
                            Optional<PolicyServiceAgentResponse> policyServiceAgentResponseOptional = newServiceAgentResponses.stream()
                                    .filter(newServiceAgentResponse -> newServiceAgentResponse.getPolicyId().equals(reportPolicyBo.getPolicyId()))
                                    .findFirst();
                            if (policyServiceAgentResponseOptional.isPresent()) {
                                PolicyServiceAgentResponse policyServiceAgentResponse = policyServiceAgentResponseOptional.get();
                                Optional<AgentResponse> serviceAgentResponse = agentRespFcs.stream()
                                        .filter(agentResponse -> agentResponse.getAgentId().equals(policyServiceAgentResponse.getServiceAgentId()))
                                        .findFirst();
                                if (serviceAgentResponse.isPresent()) {
                                    serviceAgent = serviceAgentResponse.get();
                                }
                            }
                        }
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(agent.getAgentName()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(agent.getAgentCode()));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(agent.getMobile()));

                    // 设置服务业务人员信息 没有服务业务员则取初始业务员
                    if (AssertUtils.isNotNull(serviceAgent)) {
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(serviceAgent.getAgentName()));
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(serviceAgent.getAgentCode()));
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(serviceAgent.getMobile()));
                    } else {
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(agent.getAgentName()));
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(agent.getAgentCode()));
                        getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(agent.getMobile()));
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.getCodeName(data.get(TerminologyTypeEnum.IS_HEAD_OFFICE.name()), reportPolicyBo.getSelfInsuranceFlag())));
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(languageUtils.getCodeName(data.get(TerminologyTypeEnum.POLICY_STATUS.name()), reportPolicyBo.getPolicyStatus())));

                    String failuretime;
                    if (AssertUtils.isNotNull(reportPolicyBo.getThoroughInvalidDate())) {
                        failuretime = DateUtils.timeStrToString(reportPolicyBo.getThoroughInvalidDate(), DateUtils.FORMATE3);
                    } else {
                        failuretime = DateUtils.timeStrToString(reportPolicyBo.getInvalidDate(), DateUtils.FORMATE3);
                    }
                    getCell(cellStyle, writeRow, column++).setCellValue(exportPrint(failuretime));

                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/octet-stream;charset=UTF-8");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attachmentResponse.getTemplateName() + ".xlsx", "UTF-8").replaceAll("\\+", "%20"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ReportErrorConfigEnum.REPORT_POLICY_RENEWAL_ERROR);
            }
        }
        return resultObject;
    }

    public static Cell getCell(CellStyle cellStyle, Row writeRow, int column) {
        if (AssertUtils.isNotNull(writeRow.getCell(column))) {
            return writeRow.getCell(column);
        }
        Cell cell = writeRow.createCell(column);
        cell.setCellStyle(cellStyle);
        return cell;
    }

    //金额格式化
    private static final DecimalFormat decimalFormat = new DecimalFormat("###,###,###,###,##0.00");

    public static String exportPrint(Object o) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                return decimalFormat.format(o);
            }
            return o + "";
        }
        return "";
    }

    @Override
    public void exportReportPolicyLong(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPolicyListRequest reportPolicyListRequest) {
        try {
            List<String> codeTypes = Arrays.asList(
                    TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(),
                    TerminologyTypeEnum.CHANNEL_TYPE.name(),
                    TerminologyTypeEnum.POLICY_STATUS.name(),
                    TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(),
                    TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(),
                    TerminologyTypeEnum.ID_TYPE.name()
            );
            ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
            Map<String, List<SyscodeResponse>> data = mapResultObject.getData();

            //缴费周期
            List<SyscodeResponse> premiumFrequencyRespFcs = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
            //渠道类型
            List<SyscodeResponse> channelTypeRespFcs = data.get(TerminologyTypeEnum.CHANNEL_TYPE.name());
            //保单状态
            List<SyscodeResponse> policyStatusRespFcs = data.get(TerminologyTypeEnum.POLICY_STATUS.name());
            List<SyscodeResponse> coveragePeriodUnitRespFcs = data.get(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name());
            List<SyscodeResponse> idTypeRespFcs = data.get(TerminologyTypeEnum.ID_TYPE.name());
            List<SyscodeResponse> mainFlags = data.get(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name());
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.LONG_TERM_INSURANCE_TEMPLATE.name());
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            try {
                List<ReportPolicyBo> reportPolicyBos = reportQueryBaseService.queryListReportRegulatoryPolicy(reportPolicyListRequest, "export");
                AssertUtils.isNotEmpty(this.getLogger(), reportPolicyBos, REPORT_UNDERWRITING_REPORT_IS_NULL);

                List<String> agentIds = reportPolicyBos.stream().filter(reportPolicyBo -> AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())).map(ReportPolicyBo::getAgentId).distinct().collect(Collectors.toList());
                AgentApplyQueryRequest applyAgentReqFc = new AgentApplyQueryRequest();
                applyAgentReqFc.setListAgentId(agentIds);
                List<AgentResponse> agentRespFcs = agentApi.agentsGet(applyAgentReqFc).getData();

                int num = 0;
                for (int i = 2; i < sheet.getLastRowNum(); i++) {
                    if (!(reportPolicyBos.size() > num && AssertUtils.isNotNull(reportPolicyBos.get(num)))) {
                        break;
                    }
                    ReportPolicyBo reportPolicyBo = reportPolicyBos.get(num);
                    num++;
                    Row writeRow = sheet.getRow(i);
                    writeRow.getCell(0).setCellValue(num);
                    String policyNo = reportPolicyBo.getPolicyNo();
                    if (!AssertUtils.isNotEmpty(policyNo)) {
                        policyNo = "--";
                    }
                    writeRow.getCell(1).setCellValue(policyNo);
                    String applyNo = reportPolicyBo.getApplyNo();
                    if (!AssertUtils.isNotEmpty(applyNo)) {
                        applyNo = "--";
                    }
                    writeRow.getCell(2).setCellValue(applyNo);
                    String productName = LanguageUtils.getENProductName(reportPolicyBo.getProductId(), reportPolicyBo.getProductName());
                    if (!AssertUtils.isNotEmpty(productName)) {
                        productName = "--";
                    }
                    writeRow.getCell(3).setCellValue(productName);

                    String primaryFlag = languageUtils.findCodeName(mainFlags, reportPolicyBo.getPrimaryFlag());
                    if (!AssertUtils.isNotEmpty(primaryFlag)) {
                        primaryFlag = "--";
                    }
                    writeRow.getCell(4).setCellValue(primaryFlag);

                    String premiumFrequency = languageUtils.findCodeName(premiumFrequencyRespFcs, reportPolicyBo.getPremiumFrequency());
                    if (!AssertUtils.isNotEmpty(premiumFrequency)) {
                        premiumFrequency = "--";
                    }
                    writeRow.getCell(5).setCellValue(premiumFrequency);
                    String coveragePeriodUnit = languageUtils.findCodeName(coveragePeriodUnitRespFcs, reportPolicyBo.getCoveragePeriodUnit());
                    if (!AssertUtils.isNotEmpty(coveragePeriodUnit)) {
                        coveragePeriodUnit = "--";
                    }
                    String coveragePeriod = reportPolicyBo.getCoveragePeriod();
                    if (!AssertUtils.isNotEmpty(coveragePeriod)) {
                        coveragePeriod = "--";
                    }
                    writeRow.getCell(6).setCellValue(coveragePeriod + coveragePeriodUnit);
                    String totalPremium = "--";
                    if (AssertUtils.isNotNull(reportPolicyBo.getTotalPremium())) {
                        totalPremium = reportPolicyBo.getTotalPremium().toString();
                    }
                    writeRow.getCell(7).setCellValue(totalPremium);
                    String approveDate = DateUtils.timeStrToString(reportPolicyBo.getApproveDate(), DateUtils.FORMATE3);
                    if (!AssertUtils.isNotEmpty(approveDate)) {
                        approveDate = "--";
                    }
                    writeRow.getCell(8).setCellValue(approveDate);
                    String receivableDate = DateUtils.timeStrToString(reportPolicyBo.getReceivableDate(), DateUtils.FORMATE3);
                    if (!AssertUtils.isNotEmpty(receivableDate)) {
                        receivableDate = "--";
                    }
                    writeRow.getCell(9).setCellValue(receivableDate);
                    String paidTimes = reportPolicyBo.getPaidTimes();
                    if (!AssertUtils.isNotEmpty(paidTimes)) {
                        paidTimes = "--";
                    }
                    writeRow.getCell(10).setCellValue(paidTimes);
                    String applicantName = reportPolicyBo.getApplicantName();
                    if (!AssertUtils.isNotEmpty(applicantName)) {
                        applicantName = "--";
                    }
                    writeRow.getCell(11).setCellValue(applicantName);
                    String aipplicantIdType = languageUtils.findCodeName(idTypeRespFcs, reportPolicyBo.getApplicantIdType());
                    if (!AssertUtils.isNotEmpty(aipplicantIdType)) {
                        aipplicantIdType = "--";
                    }
                    writeRow.getCell(12).setCellValue(aipplicantIdType);
                    String applicantIdNo = reportPolicyBo.getApplicantIdNo();
                    if (!AssertUtils.isNotEmpty(applicantIdNo)) {
                        applicantIdNo = "--";
                    }
                    writeRow.getCell(13).setCellValue(applicantIdNo);
                    String applicantMobile = reportPolicyBo.getApplicantMobile();
                    if (!AssertUtils.isNotEmpty(applicantMobile)) {
                        applicantMobile = "--";
                    }
                    writeRow.getCell(14).setCellValue(applicantMobile);
                    String insuredName = reportPolicyBo.getInsuredName();
                    if (!AssertUtils.isNotEmpty(insuredName)) {
                        insuredName = "--";
                    }
                    writeRow.getCell(15).setCellValue(insuredName);
                    String insuredIdType = languageUtils.findCodeName(idTypeRespFcs, reportPolicyBo.getInsuredIdType());
                    if (!AssertUtils.isNotEmpty(insuredIdType)) {
                        insuredIdType = "--";
                    }
                    writeRow.getCell(16).setCellValue(insuredIdType);
                    String insuredIdNo = reportPolicyBo.getInsuredIdNo();
                    if (!AssertUtils.isNotEmpty(insuredIdNo)) {
                        insuredIdNo = "--";
                    }
                    writeRow.getCell(17).setCellValue(insuredIdNo);
                    String insuredMobile = reportPolicyBo.getInsuredMobile();
                    if (!AssertUtils.isNotEmpty(insuredMobile)) {
                        insuredMobile = "--";
                    }
                    writeRow.getCell(18).setCellValue(insuredMobile);
                    ResultObject<BranchResponse> saleBranches = platformBranchBaseApi.queryOneBranchById(reportPolicyBo.getSalesBranchId());
                    String salesBranch = saleBranches.getData().getBranchName();
                    if (!AssertUtils.isNotEmpty(salesBranch)) {
                        salesBranch = "--";
                    }
                    String channelTypeCode = languageUtils.findCodeName(channelTypeRespFcs, reportPolicyBo.getChannelTypeCode());
                    if (!AssertUtils.isNotEmpty(channelTypeCode)) {
                        channelTypeCode = "--";
                    }
                    writeRow.getCell(19).setCellValue(salesBranch + "/" + channelTypeCode);

                    final String[] agentName = {"--"};
                    if (AssertUtils.isNotEmpty(agentRespFcs) && AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())) {
                        agentRespFcs.stream().filter(applyAgentRespFc -> reportPolicyBo.getAgentId().equals(applyAgentRespFc.getAgentId())).findFirst().ifPresent((value) -> {
                            agentName[0] = value.getAgentName() + "/" + value.getAgentCode();
                        });
                    }
                    writeRow.getCell(20).setCellValue(agentName[0]);
                    String policyStatus = languageUtils.findCodeName(policyStatusRespFcs, reportPolicyBo.getPolicyStatus());
                    if (!AssertUtils.isNotEmpty(policyStatus)) {
                        policyStatus = "--";
                    }
                    writeRow.getCell(21).setCellValue(policyStatus);

                    //设置地址
                    if (AssertUtils.isNotEmpty(reportPolicyBo.getHomeAreaCode())) {
                        ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(reportPolicyBo.getHomeAreaCode());
                        if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                            String s = areaNameGet(areaResultObject);
                            if (AssertUtils.isNotEmpty(reportPolicyBo.getHomeAddress())) {
                                s = s + " " + reportPolicyBo.getHomeAddress();
                            }
                            writeRow.getCell(22).setCellValue(s);
                        }
                    }else {
                        writeRow.getCell(22).setCellValue("--");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("业务承保清单-长期险.xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException(ReportErrorConfigEnum.REPORT_LONG_REPORT_POLICY_ERROR);
            }
        }
    }

    private String areaNameGet(ResultObject<List<AreaTreeResponse>> areaResultObject) {
        List<String> areaNames=areaResultObject.getData().stream().filter(areaTreeResponse ->
                AssertUtils.isNotEmpty(areaTreeResponse.getAreaName())).map(AreaTreeResponse::getAreaName).collect(Collectors.toList());
        return StringUtils.join(areaNames.toArray(), " ");
    }

    @Override
    public void exportReportPolicyShort(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPolicyListRequest reportPolicyListRequest) {
        try {
            List<String> codeTypes = Arrays.asList(
                    TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(),
                    TerminologyTypeEnum.CHANNEL_TYPE.name(),
                    TerminologyTypeEnum.POLICY_STATUS.name(),
                    TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name(),
                    TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(),
                    TerminologyTypeEnum.ID_TYPE.name()
            );
            ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
            Map<String, List<SyscodeResponse>> data = mapResultObject.getData();

            //缴费周期
            List<SyscodeResponse> premiumFrequencyRespFcs = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
            //渠道类型
            List<SyscodeResponse> channelTypeRespFcs = data.get(TerminologyTypeEnum.CHANNEL_TYPE.name());
            //保单状态
            List<SyscodeResponse> policyStatusRespFcs = data.get(TerminologyTypeEnum.POLICY_STATUS.name());
            List<SyscodeResponse> coveragePeriodUnitRespFcs = data.get(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name());
            List<SyscodeResponse> idTypeRespFcs = data.get(TerminologyTypeEnum.ID_TYPE.name());
            List<SyscodeResponse> mainFlags = data.get(TerminologyTypeEnum.PRODUCT_MAIN_PRODUCT_FLAG.name());
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.SHORT_TERM_INSURANCE_TEMPLATE.name());
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            try {
                List<ReportPolicyBo> reportPolicyBos = reportQueryBaseService.queryListReportRegulatoryPolicy(reportPolicyListRequest, "export");
                AssertUtils.isNotEmpty(this.getLogger(), reportPolicyBos, REPORT_UNDERWRITING_REPORT_IS_NULL);

                List<String> agentIds = reportPolicyBos.stream().filter(reportPolicyBo -> AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())).map(ReportPolicyBo::getAgentId).distinct().collect(Collectors.toList());
                AgentApplyQueryRequest applyAgentReqFc = new AgentApplyQueryRequest();
                applyAgentReqFc.setListAgentId(agentIds);
                List<AgentResponse> agentRespFcs = agentApi.agentsGet(applyAgentReqFc).getData();

                int num = 0;
                for (int i = 2; i < sheet.getLastRowNum(); i++) {
                    if (!(reportPolicyBos.size() > num && AssertUtils.isNotNull(reportPolicyBos.get(num)))) {
                        break;
                    }
                    ReportPolicyBo reportPolicyBo = reportPolicyBos.get(num);
                    num++;
                    Row writeRow = sheet.getRow(i);
                    writeRow.getCell(0).setCellValue(num);
                    String policyNo = reportPolicyBo.getPolicyNo();
                    if (!AssertUtils.isNotEmpty(policyNo)) {
                        policyNo = "--";
                    }
                    writeRow.getCell(1).setCellValue(policyNo);
                    String applyNo = reportPolicyBo.getApplyNo();
                    if (!AssertUtils.isNotEmpty(applyNo)) {
                        applyNo = "--";
                    }
                    writeRow.getCell(2).setCellValue(applyNo);
                    String productName = LanguageUtils.getENProductName(reportPolicyBo.getProductId(), reportPolicyBo.getProductName());
                    if (!AssertUtils.isNotEmpty(productName)) {
                        productName = "--";
                    }
                    writeRow.getCell(3).setCellValue(productName);

                    String primaryFlag = languageUtils.findCodeName(mainFlags, reportPolicyBo.getPrimaryFlag());
                    if (!AssertUtils.isNotEmpty(primaryFlag)) {
                        primaryFlag = "--";
                    }
                    writeRow.getCell(4).setCellValue(primaryFlag);

                    String premiumFrequency = languageUtils.findCodeName(premiumFrequencyRespFcs, reportPolicyBo.getPremiumFrequency());
                    if (!AssertUtils.isNotEmpty(premiumFrequency)) {
                        premiumFrequency = "--";
                    }
                    writeRow.getCell(5).setCellValue(premiumFrequency);
                    String coveragePeriodUnit = languageUtils.findCodeName(coveragePeriodUnitRespFcs, reportPolicyBo.getCoveragePeriodUnit());
                    if (!AssertUtils.isNotEmpty(coveragePeriodUnit)) {
                        coveragePeriodUnit = "--";
                    }
                    String coveragePeriod = reportPolicyBo.getCoveragePeriod();
                    if (!AssertUtils.isNotEmpty(coveragePeriod)) {
                        coveragePeriod = "--";
                    }
                    writeRow.getCell(6).setCellValue(coveragePeriod + coveragePeriodUnit);
                    String totalPremium = "--";
                    if (AssertUtils.isNotNull(reportPolicyBo.getTotalPremium())) {
                        totalPremium = reportPolicyBo.getTotalPremium().toString();
                    }
                    writeRow.getCell(7).setCellValue(totalPremium);
                    String approveDate = DateUtils.timeStrToString(reportPolicyBo.getApproveDate(), DateUtils.FORMATE3);
                    if (!AssertUtils.isNotEmpty(approveDate)) {
                        approveDate = "--";
                    }
                    writeRow.getCell(8).setCellValue(approveDate);
                    String applicantName = reportPolicyBo.getApplicantName();
                    if (!AssertUtils.isNotEmpty(applicantName)) {
                        applicantName = "--";
                    }
                    writeRow.getCell(9).setCellValue(applicantName);
                    String applicantIdType = languageUtils.findCodeName(idTypeRespFcs, reportPolicyBo.getApplicantIdType());
                    if (!AssertUtils.isNotEmpty(applicantIdType)) {
                        applicantIdType = "--";
                    }
                    writeRow.getCell(10).setCellValue(applicantIdType);
                    String applicantIdNo = reportPolicyBo.getApplicantIdNo();
                    if (!AssertUtils.isNotEmpty(applicantIdNo)) {
                        applicantIdNo = "--";
                    }
                    writeRow.getCell(11).setCellValue(applicantIdNo);
                    String applicantMobile = reportPolicyBo.getApplicantMobile();
                    if (!AssertUtils.isNotEmpty(applicantMobile)) {
                        applicantMobile = "--";
                    }
                    writeRow.getCell(12).setCellValue(applicantMobile);
                    String insuredName = reportPolicyBo.getInsuredName();
                    if (!AssertUtils.isNotEmpty(insuredName)) {
                        insuredName = "--";
                    }
                    writeRow.getCell(13).setCellValue(insuredName);
                    String insuredIdType = languageUtils.findCodeName(idTypeRespFcs, reportPolicyBo.getInsuredIdType());
                    if (!AssertUtils.isNotEmpty(insuredIdType)) {
                        insuredIdType = "--";
                    }
                    writeRow.getCell(14).setCellValue(insuredIdType);
                    String insuredIdNo = reportPolicyBo.getInsuredIdNo();
                    if (!AssertUtils.isNotEmpty(insuredIdNo)) {
                        insuredIdNo = "--";
                    }
                    writeRow.getCell(15).setCellValue(insuredIdNo);
                    String insuredMobile = reportPolicyBo.getInsuredMobile();
                    if (!AssertUtils.isNotEmpty(insuredMobile)) {
                        insuredMobile = "--";
                    }
                    writeRow.getCell(16).setCellValue(insuredMobile);
                    ResultObject<BranchResponse> saleBranches = platformBranchBaseApi.queryOneBranchById(reportPolicyBo.getSalesBranchId());
                    String salesBranch = saleBranches.getData().getBranchName();
                    if (!AssertUtils.isNotEmpty(salesBranch)) {
                        salesBranch = "--";
                    }
                    String channelTypeCode = languageUtils.findCodeName(channelTypeRespFcs, reportPolicyBo.getChannelTypeCode());
                    if (!AssertUtils.isNotEmpty(channelTypeCode)) {
                        channelTypeCode = "--";
                    }
                    writeRow.getCell(17).setCellValue(salesBranch + "/" + channelTypeCode);

                    final String[] agentName = {"--"};
                    if (AssertUtils.isNotEmpty(agentRespFcs) && AssertUtils.isNotEmpty(reportPolicyBo.getAgentId())) {
                        agentRespFcs.stream().filter(applyAgentRespFc -> reportPolicyBo.getAgentId().equals(applyAgentRespFc.getAgentId())).findFirst().ifPresent((value) -> {
                            agentName[0] = value.getAgentName() + "/" + value.getAgentCode();
                        });
                    }
                    writeRow.getCell(18).setCellValue(agentName[0]);

                    String policyStatus = languageUtils.findCodeName(policyStatusRespFcs, reportPolicyBo.getPolicyStatus());
                    if (!AssertUtils.isNotEmpty(policyStatus)) {
                        policyStatus = "--";
                    }
                    writeRow.getCell(19).setCellValue(policyStatus);
                    String renewalTimes = reportPolicyBo.getRenewalTimes();
                    if (!AssertUtils.isNotEmpty(renewalTimes)) {
                        renewalTimes = "--";
                    }
                    writeRow.getCell(20).setCellValue(renewalTimes);

                    //设置地址
                    if (AssertUtils.isNotEmpty(reportPolicyBo.getHomeAreaCode())) {
                        ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(reportPolicyBo.getHomeAreaCode());
                        if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                            String s = areaNameGet(areaResultObject);
                            if (AssertUtils.isNotEmpty(reportPolicyBo.getHomeAddress())) {
                                s = s + " " + reportPolicyBo.getHomeAddress();
                            }
                            writeRow.getCell(21).setCellValue(s);
                        }
                    }else {
                        writeRow.getCell(21).setCellValue("--");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("业务承保清单-短期险.xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException(ReportErrorConfigEnum.REPORT_SHORT_REPORT_POLICY_ERROR);
            }
        }
    }

    @Override
    public ResultObject<BasePageResponse<ReportCashTransactionResponse>> queryCashTransactionPageList(Users currentLoginUsers, ReportCashTransactionRequest reportCashTransactionRequest) {
        ResultObject<BasePageResponse<ReportCashTransactionResponse>> resultObject = new ResultObject<>();
        //查询支付表格
        List<ReportCashTransactionBo> reportCashTransactionBos = reportActualPerformanceBaseService.queryCashTransactionPageList(reportCashTransactionRequest);
        if (!AssertUtils.isNotEmpty(reportCashTransactionBos)) {
            return resultObject;
        }
        this.transReportCashData(reportCashTransactionBos, currentLoginUsers);
        List<ReportCashTransactionResponse> reportCashTransactionResponses = (List<ReportCashTransactionResponse>) this.converterList(
                reportCashTransactionBos, new TypeToken<List<ReportCashTransactionResponse>>() {
                }.getType()
        );
        Integer totalLine = AssertUtils.isNotNull(reportCashTransactionBos) ? reportCashTransactionBos.get(0).getTotalLine() : null;
        BasePageResponse<ReportCashTransactionResponse> basePageResponse = BasePageResponse.getData(reportCashTransactionRequest.getCurrentPage(), reportCashTransactionRequest.getPageSize(), totalLine, reportCashTransactionResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    public void exportCashTransaction(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportCashTransactionRequest reportCashTransactionRequest) {

        try {
            List<ReportCashTransactionBo> ReportCashTransactionBos = reportActualPerformanceBaseService.exportCashTransaction(reportCashTransactionRequest);
            this.transReportCashData(ReportCashTransactionBos,currentLoginUsers);
            // 获取excel文件
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentServiceInterface.templateGet(ReportTermEnum.IMPORT_EXPORT_REPORT.CASH_TRANSACTION_TEMPLATE.name());
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            cellStyle.setFillForegroundColor(HSSFColor.BLACK.index);
            cellStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER);
            cellStyle.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
            cellStyle.setBorderLeft(CellStyle.BORDER_THIN);// 左边框
            cellStyle.setBorderTop(CellStyle.BORDER_THIN);// 上边框
            cellStyle.setBorderRight(CellStyle.BORDER_THIN);// 右边框
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            int num = 0;
            for (int i = 0; i < ReportCashTransactionBos.size(); i++) {
                if (!(ReportCashTransactionBos.size() > num && AssertUtils.isNotNull(ReportCashTransactionBos.get(num)))) {
                    break;
                }
                int column = 0;
                ReportCashTransactionBo reportCashTransactionBo = ReportCashTransactionBos.get(i);
                Row row = sheet.createRow(i+1);
                getCell(cellStyle, row, column++).setCellValue(++num);
                String cashTransactionNo = reportCashTransactionBo.getCashTransactionNo();
                if(!AssertUtils.isNotEmpty(cashTransactionNo)) {
                    cashTransactionNo = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(cashTransactionNo);
                String policyNo = reportCashTransactionBo.getPolicyNo();
                if (!AssertUtils.isNotEmpty(policyNo))  {
                    policyNo = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(policyNo);
                String applyDateFormat = DateUtils.timeStrToString(reportCashTransactionBo.getApplyDate(), DateUtils.FORMATE3);;
                if (!AssertUtils.isNotEmpty(applyDateFormat)) {
                    applyDateFormat = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(applyDateFormat);
                String underwriteEndDateFormat = DateUtils.timeStrToString(reportCashTransactionBo.getUnderwriteEndDate(), DateUtils.FORMATE3);;
                if (!AssertUtils.isNotEmpty(underwriteEndDateFormat)) {
                    underwriteEndDateFormat = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(underwriteEndDateFormat);
                String applicantName = reportCashTransactionBo.getApplicantName();
                if (!AssertUtils.isNotEmpty(applicantName)) {
                    applicantName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(applicantName);
                String birthdayDateFormat = DateUtils.timeStrToString(reportCashTransactionBo.getBirthday(), DateUtils.FORMATE3);;
                if (!AssertUtils.isNotEmpty(birthdayDateFormat)) {
                    birthdayDateFormat = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(birthdayDateFormat);
                String idTypeName = reportCashTransactionBo.getIdTypeName();
                if (!AssertUtils.isNotEmpty(idTypeName)) {
                    idTypeName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(idTypeName);
                String sexName = reportCashTransactionBo.getSexName();
                if (!AssertUtils.isNotEmpty(sexName)) {
                    sexName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(sexName);
                String nationalityName = reportCashTransactionBo.getNationalityName();
                if (!AssertUtils.isNotEmpty(nationalityName)) {
                    nationalityName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(nationalityName);
                String occupationName = reportCashTransactionBo.getOccupationName();
                if (!AssertUtils.isNotEmpty(occupationName)) {
                    occupationName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(occupationName);
                String companyLegalPersonName = reportCashTransactionBo.getCompanyLegalPersonName();
                if (!AssertUtils.isNotEmpty(companyLegalPersonName)) {
                    companyLegalPersonName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(companyLegalPersonName);
                String homeAddress = reportCashTransactionBo.getHomeAddress();
                if (!AssertUtils.isNotEmpty(homeAddress)) {
                    homeAddress = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(homeAddress);
                String mobile = reportCashTransactionBo.getMobile();
                if (!AssertUtils.isNotEmpty(mobile)) {
                    mobile = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(mobile);
                String actualPayDateFormat = DateUtils.timeStrToString(reportCashTransactionBo.getActualPayDate(), DateUtils.FORMATE3);;
                if (!AssertUtils.isNotEmpty(actualPayDateFormat)) {
                    actualPayDateFormat = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(actualPayDateFormat);
                String productName = reportCashTransactionBo.getProductName();
                if (!AssertUtils.isNotEmpty(productName)) {
                    productName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(productName);
                String policyStatusName = reportCashTransactionBo.getPolicyStatusName();
                if (!AssertUtils.isNotEmpty(policyStatusName)) {
                    policyStatusName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(policyStatusName);
                String insuredName = reportCashTransactionBo.getInsuredName();
                if (!AssertUtils.isNotEmpty(insuredName)) {
                    insuredName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(insuredName);
                String duePayAmount = "--";
                if (AssertUtils.isNotNull(reportCashTransactionBo.getDuePayAmount())) {
                    duePayAmount = reportCashTransactionBo.getDuePayAmount().toString();
                }
                getCell(cellStyle, row, column++).setCellValue(duePayAmount);
                String totalAmount = "--";
                if (AssertUtils.isNotNull(reportCashTransactionBo.getTotalAmount())) {
                     totalAmount = reportCashTransactionBo.getTotalAmount().toString();
                }
                getCell(cellStyle, row, column++).setCellValue(totalAmount);
                String premium = "--";
                if (AssertUtils.isNotNull(reportCashTransactionBo.getTotalActualPremium())) {
                    premium = reportCashTransactionBo.getTotalActualPremium().toString();
                }
                getCell(cellStyle, row, column++).setCellValue(premium);
                String actualPayAmount = "--";
                if (AssertUtils.isNotNull(reportCashTransactionBo.getActualPayAmount())) {
                    actualPayAmount = reportCashTransactionBo.getActualPayAmount().toString();
                }
                getCell(cellStyle, row, column++).setCellValue(actualPayAmount);
                String actualPremium = "--";
                if (AssertUtils.isNotNull(reportCashTransactionBo.getTotalActualPremium())) {
                    actualPremium = reportCashTransactionBo.getActualPremium().toString();
                }
                getCell(cellStyle, row, column++).setCellValue(actualPremium);

                String paymentMethodCodeName = reportCashTransactionBo.getPaymentMethodCodeName();
                if (!AssertUtils.isNotEmpty(paymentMethodCodeName)) {
                    paymentMethodCodeName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(paymentMethodCodeName);
                String premiumFrequencyName = reportCashTransactionBo.getPremiumFrequencyName();
                if (!AssertUtils.isNotEmpty(premiumFrequencyName)) {
                    premiumFrequencyName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(premiumFrequencyName);
                String bankAccountNo = reportCashTransactionBo.getBankAccountNo();
                if (!AssertUtils.isNotEmpty(bankAccountNo)) {
                    bankAccountNo = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(bankAccountNo);
                String coveragePeriodName = reportCashTransactionBo.getCoveragePeriodName();
                if (!AssertUtils.isNotEmpty(coveragePeriodName)) {
                    coveragePeriodName = "--";
                }
                getCell(cellStyle, row, column++).setCellValue(coveragePeriodName);
            }
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            httpServletResponse.setCharacterEncoding("UTF-8");
            httpServletResponse.setContentType("application/x-download");
            httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("现金交易.xlsx", "UTF-8"));
            OutputStream outputStream = httpServletResponse.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException(ReportErrorConfigEnum.REPORT_EXPORT_CASH_TRANSACTION_ERROR);
            }
        }
    }

    private void transReportCashData(List<ReportCashTransactionBo> reportCashTransactionBos, Users currentLoginUsers) {
        List<String> codeTypes = Arrays.asList(
                TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(),
                TerminologyTypeEnum.POLICY_STATUS.name(),
                TerminologyTypeEnum.PRODUCT_PRODUCT_LEVEL.name(),
                TerminologyTypeEnum.PAYMENT_METHODS.name(),
                TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(),
                TerminologyTypeEnum.GENDER.name(),
                TerminologyTypeEnum.NATIONALITY.name(),
                TerminologyTypeEnum.ID_TYPE.name(),
                "PRODUCT_ID"
        );
        ResultObject<Map<String, List<SyscodeResponse>>> mapResultObject = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(currentLoginUsers.getLanguage(), codeTypes);
        Map<String, List<SyscodeResponse>> data = mapResultObject.getData();
        // 缴费周期
        List<SyscodeResponse> premiumFrequencyRespFcs = data.get(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name());
        // 保单状态
        List<SyscodeResponse> policyStatusRespFcs = data.get(TerminologyTypeEnum.POLICY_STATUS.name());
        // 产品档次
        List<SyscodeResponse> productLevelRespFcs = data.get(TerminologyTypeEnum.PRODUCT_PRODUCT_LEVEL.name());
        // 缴费单位
        List<SyscodeResponse> coveragePeriodUnitRespFcs = data.get(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name());
        // 性别
        List<SyscodeResponse> genderRespFcs = data.get(TerminologyTypeEnum.GENDER.name());
        // 国籍
        List<SyscodeResponse> nationalityRespFcs = data.get(TerminologyTypeEnum.NATIONALITY.name());
        // 证件类型
        List<SyscodeResponse> idTypeRespFcs = data.get(TerminologyTypeEnum.ID_TYPE.name());
        List<SyscodeResponse> productNameRespFc = data.get("PRODUCT_ID");
        List<String> applyIds = new ArrayList<>();
        // List<String> policyIds = new ArrayList<>();
        reportCashTransactionBos.forEach(reportCashTransactionBo -> {
            String businessType = reportCashTransactionBo.getBusinessType();
            // 投保单
            if (ReportTermEnum.PAYMENT_BUSINESS_TYPE.APPLY.name().equals(businessType)
                    || ReportTermEnum.PAYMENT_BUSINESS_TYPE.APPLY_GROUP.name().equals(businessType)) {
                applyIds.add(reportCashTransactionBo.getApplyId());
            }
        });
        // 通过投保单id获取保单号
        if (AssertUtils.isNotEmpty(applyIds)) {
            ResultObject<List<ApplyApplicantListResponse>> applyApplicantList = baseApplyApi.applyApplicantList(applyIds);
            List<ApplyApplicantListResponse> applyApplicantListResponses = applyApplicantList.getData();
            applyApplicantListResponses.forEach(applyApplicantListResponse -> {
                reportCashTransactionBos.forEach(reportCashTransactionBo -> {
                    if (applyApplicantListResponse.getApplyId().equals(reportCashTransactionBo.getApplyId())) {
                        reportCashTransactionBo.setPolicyNo(applyApplicantListResponse.getPolicyNo());
                    }
                });
            });
        }
        List<String> policyNos = reportCashTransactionBos.stream().map(ReportCashTransactionBo::getPolicyNo).collect(Collectors.toList());
        // 设置保单投保人
        if (AssertUtils.isNotEmpty(policyNos)) {
            // 通过保单号查询投保人信息
            ResultObject<List<PolicyApplicantListResponse>> policyApplicantList = policyApi.policyApplicantList(policyNos);
            // 查询核保日期
            List<ReportSalePolicyBo> reportSaleApplyPolicyBos = reportSaleApplyPolicyBaseService.getSaleApplyPolicyList(policyNos);
            List<PolicyApplicantListResponse> policyApplicantListResponses = policyApplicantList.getData();
            if (AssertUtils.isNotEmpty(policyApplicantListResponses)) {
                policyApplicantListResponses.forEach(policyApplicantListResponse -> {
                    reportCashTransactionBos.forEach(reportCashTransactionBo -> {
                        if (policyApplicantListResponse.getPolicyNo().equals(reportCashTransactionBo.getPolicyNo())) {
                            reportCashTransactionBo.setPolicyId(policyApplicantListResponse.getPolicyId());
                            reportCashTransactionBo.setApplyDate(policyApplicantListResponse.getApplyDate());
                            reportCashTransactionBo.setApproveDate(policyApplicantListResponse.getApproveDate());
                            reportCashTransactionBo.setBirthday(policyApplicantListResponse.getBirthday());
                            reportCashTransactionBo.setIdNo(policyApplicantListResponse.getIdNo());
                            reportCashTransactionBo.setIdType(policyApplicantListResponse.getIdType());
                            reportCashTransactionBo.setNationality(policyApplicantListResponse.getNationality());
                            reportCashTransactionBo.setOccupationCode(policyApplicantListResponse.getOccupationCode());
                            reportCashTransactionBo.setHomeAddress(policyApplicantListResponse.getHomeAddress());
                            reportCashTransactionBo.setSex(policyApplicantListResponse.getSex());
                            reportCashTransactionBo.setMobile(policyApplicantListResponse.getMobile());
                            reportCashTransactionBo.setPolicyStatus(policyApplicantListResponse.getPolicyStatus());
                            reportCashTransactionBo.setProductName(policyApplicantListResponse.getProductName());
                            reportCashTransactionBo.setProductId(policyApplicantListResponse.getProductId());
                            reportCashTransactionBo.setCoveragePeriod(policyApplicantListResponse.getCoveragePeriod());
                            reportCashTransactionBo.setCoveragePeriodUnit(policyApplicantListResponse.getCoveragePeriodUnit());
                            reportCashTransactionBo.setCompanyLegalPersonName(policyApplicantListResponse.getCompanyLegalPersonName());
                        }
                    });
                });
            }
            if (AssertUtils.isNotEmpty(reportSaleApplyPolicyBos)) {
                for (ReportCashTransactionBo reportCashTransactionBo : reportCashTransactionBos) {
                    BigDecimal totalAmount = BigDecimal.ZERO;
                    BigDecimal actualPremium = BigDecimal.ZERO;
                    BigDecimal totalActualPremium = BigDecimal.ZERO;
                    Long underwriteEndDate = null;
                    for (ReportSalePolicyBo reportSaleApplyPolicyBo : reportSaleApplyPolicyBos) {
                        if (reportSaleApplyPolicyBo.getPolicyNo().equals(reportCashTransactionBo.getPolicyNo())) {
                            if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getAmount())) {
                                totalAmount = totalAmount.add(reportSaleApplyPolicyBo.getAmount());
                            }
                            if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getActualPremium())) {
                                actualPremium = actualPremium.add(reportSaleApplyPolicyBo.getActualPremium());
                            }
                            if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getTotalActualPremium())) {
                                totalActualPremium = totalActualPremium.add(reportSaleApplyPolicyBo.getTotalActualPremium());
                            }
                            if (AssertUtils.isNotNull(reportSaleApplyPolicyBo.getUnderwriteEndDate())) {
                                underwriteEndDate = reportSaleApplyPolicyBo.getUnderwriteEndDate();
                            }
                        }
                    }
                    reportCashTransactionBo.setTotalAmount(totalAmount);
                    reportCashTransactionBo.setActualPremium(actualPremium);
                    reportCashTransactionBo.setTotalActualPremium(totalActualPremium);
                    reportCashTransactionBo.setUnderwriteEndDate(underwriteEndDate);
                }
            }
        }
        List<String> careerIds = reportCashTransactionBos.stream().map(reportCashTransactionBo -> reportCashTransactionBo.getOccupationCode()).distinct().collect(Collectors.toList());
        // 查询职业
        ResultObject<List<CareerResponse>> listResultObject = platformCareerApi.careerInfoPost(careerIds, currentLoginUsers.getLanguage());
        reportCashTransactionBos.forEach(reportCashTransactionBo -> {
            reportCashTransactionBo.setSexName(languageUtils.findCodeName(genderRespFcs, reportCashTransactionBo.getSex()));
            reportCashTransactionBo.setNationalityName(languageUtils.findCodeName(nationalityRespFcs, reportCashTransactionBo.getNationality()));
            String paymentMethodCodeName = null;
            if (AssertUtils.isNotEmpty(reportCashTransactionBo.getPaymentMethodCode())) {
                List<String> methodCodeTrans = new ArrayList<>();
                List<String> paymentMethodCodes = (List<String>) JSON.parse(reportCashTransactionBo.getPaymentMethodCode());
                if (AssertUtils.isNotEmpty(paymentMethodCodes)) {
                    paymentMethodCodes.forEach(s -> methodCodeTrans.add(languageUtils.getCodeName(data.get(TerminologyTypeEnum.PAYMENT_METHODS.name()), s)));
                    paymentMethodCodeName = StringUtils.join(methodCodeTrans.toArray(), ",");
                }
            }
            reportCashTransactionBo.setPaymentMethodCodeName(paymentMethodCodeName);
            reportCashTransactionBo.setPremiumFrequencyName(languageUtils.findCodeName(premiumFrequencyRespFcs, reportCashTransactionBo.getPremiumFrequency()));
            reportCashTransactionBo.setProductLevelName(languageUtils.findCodeName(productLevelRespFcs, reportCashTransactionBo.getProductLevel()));

            if (AssertUtils.isNotEmpty(listResultObject.getData())) {
                listResultObject.getData().forEach(result -> {
                    if (result.getCareerId().equals(reportCashTransactionBo.getOccupationCode())) {
                        reportCashTransactionBo.setOccupationName(result.getCareerName());
                    }
                });
            }
            reportCashTransactionBo.setPolicyStatusName(languageUtils.findCodeName(policyStatusRespFcs, reportCashTransactionBo.getPolicyStatus()));
            reportCashTransactionBo.setProductName(languageUtils.findCodeName(productNameRespFc, reportCashTransactionBo.getProductId()));
            reportCashTransactionBo.setIdTypeName(languageUtils.findCodeName(idTypeRespFcs, reportCashTransactionBo.getIdType()));
            if (AssertUtils.isNotEmpty(reportCashTransactionBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(reportCashTransactionBo.getCoveragePeriodUnit())) {
                reportCashTransactionBo.setCoveragePeriodName(reportCashTransactionBo.getCoveragePeriod() + languageUtils.findCodeName(coveragePeriodUnitRespFcs, reportCashTransactionBo.getCoveragePeriodUnit()));
            }
        });

    }

    @Override
    public ResultObject<Void> sendMonthlyCashTransactionMsg(Long currentMonth) {
        AssertUtils.isNotNull(log, currentMonth, REPORT_QUERY_OPERATION_DATE_IS_NOT_NULL);
        //统计上个月的 1713171189000
        long lastMonth = DateUtils.addStringMonthRT(currentMonth, -1);
        long fistDay = DateUtils.getThisMonthFirstDay(lastMonth);
        long endDay = DateUtils.getThisMonthLastDay(lastMonth);
        List<ReportCashTransactionBo> reportCashTransactionBoList = reportActualPerformanceBaseService.getCashTransactionMsg(fistDay,endDay);
        if (AssertUtils.isNotEmpty(reportCashTransactionBoList)) {
            List<String> applyIds = new ArrayList<>();
            // List<String> policyIds = new ArrayList<>();
            reportCashTransactionBoList.forEach(reportCashTransactionBo -> {
                String businessType = reportCashTransactionBo.getBusinessType();
                // 投保单
                if (ReportTermEnum.PAYMENT_BUSINESS_TYPE.APPLY.name().equals(businessType)
                        || ReportTermEnum.PAYMENT_BUSINESS_TYPE.APPLY_GROUP.name().equals(businessType)) {
                    applyIds.add(reportCashTransactionBo.getApplyId());
                }
            });
            // 通过投保单id获取保单号
            if(AssertUtils.isNotEmpty(applyIds)) {
                ResultObject<List<ApplyApplicantListResponse>> applyApplicantList = baseApplyApi.applyApplicantList(applyIds);
                List<ApplyApplicantListResponse> applyApplicantListResponses = applyApplicantList.getData();
                applyApplicantListResponses.forEach(applyApplicantListResponse->{
                    reportCashTransactionBoList.forEach(reportCashTransactionBo->{
                        if(applyApplicantListResponse.getApplyId().equals(reportCashTransactionBo.getApplyId())) {
                            reportCashTransactionBo.setPolicyNo(applyApplicantListResponse.getPolicyNo());
                        }
                    });
                });
            }
            List<String> policyNos = reportCashTransactionBoList.stream().map(ReportCashTransactionBo::getPolicyNo).collect(Collectors.toList());
            // 设置保单投保人
            if(AssertUtils.isNotEmpty(policyNos)) {
                // 通过保单号查询投保人信息
                ResultObject<List<PolicyApplicantListResponse>> policyApplicantList = policyApi.policyApplicantList(policyNos);
                List<PolicyApplicantListResponse> policyApplicantListResponses = policyApplicantList.getData();
                if (AssertUtils.isNotEmpty(policyApplicantListResponses)) {
                    policyApplicantListResponses.forEach(policyApplicantListResponse -> {
                        reportCashTransactionBoList.forEach(reportCashTransactionBo -> {
                            if (policyApplicantListResponse.getPolicyNo().equals(reportCashTransactionBo.getPolicyNo())) {
                                reportCashTransactionBo.setApplicantName(policyApplicantListResponse.getName());
                                reportCashTransactionBo.setIdNo(policyApplicantListResponse.getIdNo());
                            }
                        });
                    });
                }
            }
            // 转换格式
            List<SendMonthlyCashTransactionMsgBo> sendMonthlyCashTransactionMsgBos = (List<SendMonthlyCashTransactionMsgBo>) this.converterList(
                    reportCashTransactionBoList, new TypeToken<List<SendMonthlyCashTransactionMsgBo>>() {
                    }.getType()
            );
            // 通过客户号进行数据分组
            Map<String, List<SendMonthlyCashTransactionMsgBo>> cashTransactionMap = sendMonthlyCashTransactionMsgBos.
                    stream().collect(Collectors.groupingBy(SendMonthlyCashTransactionMsgBo::getApplicantCustomerNo));
            List<SendMonthlyCashTransactionMsgResponse> sendMonthlyCashTransactionMsgResponseList = new ArrayList<>();
            cashTransactionMap.forEach((applicantCustomerNo, cashTransaction)->{
                this.transSendCashData(applicantCustomerNo, cashTransaction, sendMonthlyCashTransactionMsgResponseList);
            });
            AssertUtils.isNotEmpty(log, sendMonthlyCashTransactionMsgResponseList, "钉钉消息推送月度保费实缴超额预警" + lastMonth);
            // 发送钉钉消息
            BusinessMessagePushBatchRequest businessMessagePushBatchRequest = new BusinessMessagePushBatchRequest();
            String businessCode = PaymentTermEnum.MSG_BUSINESS_TYPE.AML_REPORTING_ON_MONTHLY_PREMIUM_EXCEEDING_LIMITATION.name();
            List<BusinessMessagePushRequest> businessMessagePushRequestList = new ArrayList<>();
            sendMonthlyCashTransactionMsgResponseList.forEach(sendMonthlyCashTransactionMsgResponse -> {
                HashMap<String, String> resultMap = new HashMap<>();
                resultMap.put("applicantName", sendMonthlyCashTransactionMsgResponse.getApplicantName());
                resultMap.put("policyNo", sendMonthlyCashTransactionMsgResponse.getPolicyNos());
                resultMap.put("idNo", sendMonthlyCashTransactionMsgResponse.getIdNo());
                resultMap.put("monthlyPaymentsTimes", sendMonthlyCashTransactionMsgResponse.getCount().toString());
                resultMap.put("excessPremium", sendMonthlyCashTransactionMsgResponse.getExcessPremium().toString());
                List<String> userIds = platformUsersApi.getBusinessUsers(businessCode, sendMonthlyCashTransactionMsgResponse.getBranchId(), null).getData();
                if (AssertUtils.isNotEmpty(userIds)) {
                    BusinessMessagePushRequest businessMessagePushReqFc = new BusinessMessagePushRequest();
                    businessMessagePushReqFc.setBusinessCode(businessCode);
                    businessMessagePushReqFc.setUserIdList(userIds);
                    businessMessagePushReqFc.setMessageParam(JSON.toJSONString(resultMap));
                    businessMessagePushRequestList.add(businessMessagePushReqFc);
                }
            });
            if (AssertUtils.isNotEmpty(businessMessagePushRequestList)) {
                businessMessagePushBatchRequest.setBusinessMessagePushRequestList(businessMessagePushRequestList);
                businessMessagePushBatchRequest.setMessageCount(businessMessagePushRequestList.size());
                businessMessageApi.batchPushBusinessMessage(businessMessagePushBatchRequest);
            }
        }
        return ResultObject.success();
    }

    private void transSendCashData(String applicantCustomerNo, List<SendMonthlyCashTransactionMsgBo> cashTransaction, List<SendMonthlyCashTransactionMsgResponse> sendMonthlyCashTransactionMsgResponseList) {
        StringBuilder str = new StringBuilder();
        SendMonthlyCashTransactionMsgResponse sendMonthlyCashTransactionMsgResponse = new SendMonthlyCashTransactionMsgResponse();
        BigDecimal excessPremium = BigDecimal.ZERO;
        for (int i = 0; i < cashTransaction.size(); i++) {
            SendMonthlyCashTransactionMsgBo data = cashTransaction.get(i);
            if (i == cashTransaction.size() -1) {
                str.append("[").append(data.getPolicyNo()).append("]");
            }else {
                str.append("[").append(data.getPolicyNo()).append("],");
            }
            excessPremium = excessPremium.add(data.getActualPayAmount());
        }
        sendMonthlyCashTransactionMsgResponse.setCount(cashTransaction.size());
        sendMonthlyCashTransactionMsgResponse.setApplicantName(cashTransaction.get(0).getApplicantName());
        sendMonthlyCashTransactionMsgResponse.setIdNo(cashTransaction.get(0).getIdNo());
        sendMonthlyCashTransactionMsgResponse.setPolicyNos(str.toString());
        sendMonthlyCashTransactionMsgResponse.setExcessPremium(excessPremium);
        sendMonthlyCashTransactionMsgResponseList.add(sendMonthlyCashTransactionMsgResponse);

    }
}
