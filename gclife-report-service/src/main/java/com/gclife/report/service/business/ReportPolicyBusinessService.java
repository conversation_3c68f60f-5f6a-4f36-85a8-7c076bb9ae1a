package com.gclife.report.service.business;

import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.report.model.request.ReportCashTransactionRequest;
import com.gclife.report.model.request.ReportPolicyListRequest;
import com.gclife.report.model.response.ReportCashTransactionResponse;
import com.gclife.report.model.response.ReportPolicyInitResponse;
import com.gclife.report.model.response.ReportPolicyListResponse;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 *         create 18-11-16
 *         description:
 */
public interface ReportPolicyBusinessService {

    /**
     * 查询承保清单
     * @param users
     * @param reportPolicyListRequest
     */
    ResultObject<BasePageResponse<ReportPolicyListResponse>> queryReportPolicyPageList(Users users, ReportPolicyListRequest reportPolicyListRequest);

    /**
     * 导出业务承保清单
     * @param httpServletResponse
     * @param currentLoginUsers
     * @param reportPolicyListRequest
     * @return
     */
    ResultObject exportReportPolicyList(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPolicyListRequest reportPolicyListRequest);

    /**
     * 获取财务报表初始化
     */
    ResultObject<ReportPolicyInitResponse> initReportPolicy(Users users);

    /**
     * 导出业务承保清单--长期险
     *
     * @param httpServletResponse
     * @param currentLoginUsers
     * @param reportPolicyListRequest
     */
    void exportReportPolicyLong(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPolicyListRequest reportPolicyListRequest);

    /**
     * 导出业务承保清单--短期险
     *
     * @param httpServletResponse
     * @param currentLoginUsers
     * @param reportPolicyListRequest
     */
    void exportReportPolicyShort(HttpServletResponse httpServletResponse, Users currentLoginUsers, ReportPolicyListRequest reportPolicyListRequest);

    /**
     *  分页查询监管报表-现金交易
     * @param currentLoginUsers 当前用户信息
     * @param reportCashTransactionRequest 分页查询参数
     * @return
     */
    ResultObject<BasePageResponse<ReportCashTransactionResponse>> queryCashTransactionPageList(Users currentLoginUsers, ReportCashTransactionRequest reportCashTransactionRequest);

    /**
     * 导出现金交易报表
     * @param httpServletResponse
     * @param reportCashTransactionRequest
     */
    void exportCashTransaction(HttpServletResponse httpServletResponse,Users currentLoginUsers, ReportCashTransactionRequest reportCashTransactionRequest);

    /**
     * 月保费实缴超额预警
     * @param currentMonth
     * @return
     */
    ResultObject<Void> sendMonthlyCashTransactionMsg(Long currentMonth);
}