package com.gclife.apply.service.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentSimpleBaseResponse;
import com.gclife.apply.core.jooq.tables.daos.*;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyBaseDao;
import com.gclife.apply.dao.ApplyPaymentTransactionBaseDao;
import com.gclife.apply.dao.ApplyPlanBaseDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.app.ApplyPlanBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.vo.ApplyInsuredPrintVo;
import com.gclife.apply.service.*;
import com.gclife.certify.api.CertifyApplyApi;
import com.gclife.certify.model.response.CertifyNumberResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.payment.api.PaymentReportApi;
import com.gclife.payment.model.response.PolicyReportResponse;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyTermEnum.PAYMENT_TYPE.*;
import static com.gclife.apply.model.config.PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 投保单基础服务
 * @date 18-4-27
 */
@Service
public class ApplyBaseServiceImpl extends BaseBusinessServiceImpl implements ApplyBaseService {
    @Autowired
    private ApplyDao applyDao;
    @Autowired
    private ScratchCardBaseService scratchCardBaseService;
    @Autowired
    private ApplyBaseDao applyBaseDao;
    @Autowired
    private PaymentReportApi paymentReportApi;
    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private ApplyAccountDao applyAccountDao;
    @Autowired
    private CertifyApplyApi certifyApplyApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private ApplyContactInfoDao applyContactInfoDao;
    @Autowired
    private ApplySpecialContractDao applySpecialContractDao;
    @Autowired
    private ApplyAbandonedDao applyAbandonedDao;
    @Autowired
    private ApplyFactAgentDao applyFactAgentDao;
    @Autowired
    private ApplyPlanTraceDao applyPlanTraceDao;
    @Autowired
    private ApplyGroupHealthQuestionnaireAnswerDao applyGroupHealthQuestionnaireAnswerDao;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;
    @Autowired
    private ApplyAttachmentBaseService applyAttachmentBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyInsuredBaseService applyInsuredBaseService;
    @Autowired
    private ApplyAgentBaseService applyAgentBaseService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private ApplySpecialContractBaseService applySpecialContractBaseService;
    @Autowired
    private ApplyInsuredPrintDao applyInsuredPrintDao;
    @Autowired
    private ApplyLoanBaseService applyLoanBaseService;
    @Autowired
    private ApplyOtherInfoBaseService applyOtherInfoBaseService;
    @Autowired
    private ApplyOperationDao applyOperationDao;
    @Autowired
    private ApplyOtherInsuranceDao applyOtherInsuranceDao;
    @Autowired
    private ApplyReferralInfoBaseService applyReferralInfoBaseService;
    @Autowired
    private ApplyPrintInfoDao applyPrintInfoDao;

    @Autowired
    private ApplyPaymentTransactionBaseDao applyPaymentTransactionBaseDao;
    @Autowired
    private ApplyPlanBaseDao applyPlanBaseDao;

    /**
     * 根据投保单ID查询投保单基本数据
     *
     * @param applyId 投保单ID
     * @return ApplyPo
     */
    @Override
    public ApplyPo queryApplyPo(String applyId) {
        return applyBaseDao.getApply(applyId);
    }

    /**
     * 根据代理人ID查询投保单数据
     *
     * @param agentIds 代理人ID
     * @return 投保单list
     */
    @Override
    public List<ApplyPo> queryApplyByAgentId(List<String> agentIds) {
        List<ApplyPo> applyPos = new ArrayList<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), agentIds, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_AGENT_ID_IS_NOT_NULL);
            getLogger().info("agentIds:" + JSON.toJSONString(agentIds));

            applyPos = applyBaseDao.getApplyByAgentId(agentIds, null);

            getLogger().info("applyPos:" + JSON.toJSONString(applyPos));
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ERROR);
        }
        return applyPos;
    }

    @Override
    public List<ApplyBeneficiaryInfoBo> queryApplyBeneficiaryList(String applyId) {
        return applyBaseDao.queryApplyBeneficiaryListByApplyId(applyId);
    }

    @Override
    public List<ApplyInsuredBo> queryApplyInsured(String applyId) {
        return applyBaseDao.getApplyInsuredList(applyId, null);
    }

    @Override
    public List<ApplyCoverageBo> queryApplyCoverageList(String applyId) {
        List<ApplyCoveragePo> applyCoveragePos = applyBaseDao.getApplyCoverageList(applyId, null);
        List<ApplyCoverageBo> applyCoverageBos = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applyCoveragePos)) {
            applyCoveragePos.forEach(applyCoveragePo -> {
                ApplyCoverageBo applyCoverageBo = new ApplyCoverageBo();
                ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, applyCoverageBo);
                applyCoverageBos.add(applyCoverageBo);
            });
        }
        return applyCoverageBos;
    }

    @Override
    public List<ApplyPo> queryApplyByApplyId(List<String> applyIds) {
        List<ApplyPo> applyPos = new ArrayList<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), applyIds, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);
            getLogger().info("applyIds:" + JSON.toJSONString(applyIds));

            applyPos = applyBaseDao.getApplyByApplyId(applyIds);

            getLogger().info("applyPos:" + JSON.toJSONString(applyPos));
        } catch (Exception e) {
            e.printStackTrace();
            throwsException(getLogger(), e, ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ERROR);
        }
        return applyPos;
    }

    /**
     * 查询投保单账户信息
     *
     * @param applyId 投保单ID
     * @return List<ApplyAccountBo>
     */
    @Override
    public List<ApplyAccountBo> listApplyAccount(String applyId) {
        return applyBaseDao.getApplyAccountList(applyId);
    }

    /**
     * 根据投保单ID查询账户信息
     *
     * @param applyId 投保单ID
     * @return
     */
    @Override
    public ApplyAccountPo queryApplyAccount(String applyId) {
        return applyBaseDao.queryApplyAccount(applyId);
    }

    /**
     * 授权账户信息表
     *
     * @param applyAccountPo
     * @param userId
     */
    @Override
    public void saveApplyAccount(ApplyAccountPo applyAccountPo, String userId) {
        if (!AssertUtils.isNotEmpty(applyAccountPo.getApplyAccountId())) {
            //执行新增
            applyAccountPo.setApplyAccountId(UUIDUtils.getUUIDShort());
            applyAccountPo.setCreatedUserId(userId);
            applyAccountPo.setCreatedDate(DateUtils.getCurrentTime());
            applyAccountPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            applyAccountDao.insert(applyAccountPo);
        } else {
            //执行修改
            applyAccountPo.setUpdatedDate(DateUtils.getCurrentTime());
            applyAccountPo.setUpdatedUserId(userId);
            applyAccountDao.update(applyAccountPo);
        }
    }

    /**
     * 根据投保单ID查询投保单详细信息
     *
     * @param applyId 投保单ID
     * @return ApplyBo
     */
    @Override
    public ApplyBo queryApply(String applyId) {
        ApplyBo applyBo = null;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);
            getLogger().info("applyId:" + applyId);

            // 查询投保单基本信息
            ApplyPo applyPo = this.queryApplyPo(applyId);
            if (AssertUtils.isNotNull(applyPo)) {
                applyBo = (ApplyBo) this.converterObject(applyPo, ApplyBo.class);
                if (!AssertUtils.isNotNull(applyBo)) {
                    return applyBo;
                }
                //查询计划书
                ApplyPlanPo applyPlanPo = queryApplyPlan(applyId);
                if (AssertUtils.isNotNull(applyPlanPo)) {
                    applyBo.setApplyPlanNo(applyPlanPo.getApplyPlanNo());
                }
                // 查询投保人信息
                ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
                if (AssertUtils.isNotNull(applyApplicantBo)) {
                    applyBo.setApplicant(applyApplicantBo);
                }

                // 查询投保单附件信息
                List<ApplyAttachmentPo> applyAttachmentPos = applyAttachmentBaseService.listApplyAttachment(applyId);
                if (AssertUtils.isNotEmpty(applyAttachmentPos)) {
                    List<ApplyAttachmentBo> applyAttachmentBos = (List<ApplyAttachmentBo>) this.converterList(
                            applyAttachmentPos, new TypeToken<List<ApplyAttachmentBo>>() {
                            }.getType()
                    );
                    applyBo.setListAttachment(applyAttachmentBos);
                }

                // 查询被保人信息
                List<ApplyInsuredBo> applyInsuredBos = applyInsuredBaseService.listApplyInsured(applyId);
                if (AssertUtils.isNotEmpty(applyInsuredBos)) {
                    applyBo.setListInsured(applyInsuredBos);

                    // 提取被保人险种
                    List<ApplyCoverageBo> insuredCoverageBos = new ArrayList<>();
                    applyInsuredBos.stream()
                            .filter(applyInsuredBo -> AssertUtils.isNotEmpty(applyInsuredBo.getListCoverage()))
                            .forEach(applyInsuredBo -> insuredCoverageBos.addAll(applyInsuredBo.getListCoverage()));
                    applyBo.setListInsuredCoverage(insuredCoverageBos);
                }

                // 查询被保人统计信息
                ApplyInsuredCollectPo applyInsuredCollectPo = applyInsuredBaseService.queryApplyInsuredCollect(applyId);
                applyBo.setApplyInsuredCollect(applyInsuredCollectPo);

                // 查询公共险种信息
                List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.listApplyCoverage(applyId);
                if (AssertUtils.isNotEmpty(applyCoveragePos)) {
                    List<ApplyCoverageBo> applyCoverageBos = (List<ApplyCoverageBo>) this.converterList(
                            applyCoveragePos, new TypeToken<List<ApplyCoverageBo>>() {
                            }.getType()
                    );
                    applyBo.setListCoverage(applyCoverageBos);
                }

                //查询受理险种信息
                List<ApplyCoverageAcceptPo> applyCoverageAcceptPos = applyCoverageBaseService.listApplyCoverageAccept(applyId);
                if (AssertUtils.isNotEmpty(applyCoverageAcceptPos)) {
                    List<ApplyCoverageAcceptBo> applyCoverageAcceptBos = (List<ApplyCoverageAcceptBo>) this.converterList(
                            applyCoverageAcceptPos, new TypeToken<List<ApplyCoverageAcceptBo>>() {
                            }.getType()
                    );
                    applyBo.setListApplyCoverageAcceptBo(applyCoverageAcceptBos);
                }
                // 查询联系信息
                ApplyContactInfoPo applyContactInfoPo = this.queryApplyContactInfo(applyId);
                if (AssertUtils.isNotNull(applyContactInfoPo)) {
                    applyBo.setApplyContact((ApplyContactInfoBo) this.converterObject(applyContactInfoPo, ApplyContactInfoBo.class));
                }

                // 查询投保单代理人信息
                ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(applyId);
                if (AssertUtils.isNotNull(applyAgentPo)) {
                    ApplyAgentBo applyAgentBo = (ApplyAgentBo) this.converterObject(applyAgentPo, ApplyAgentBo.class);
                    applyBo.setApplyAgentBo(applyAgentBo);
                }

                // 查询投保单账户信息
                List<ApplyAccountBo> applyAccountBos = this.listApplyAccount(applyId);
                applyBo.setListApplyAccount(applyAccountBos);

                // 查询支付事务
                ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransaction(applyId);
                applyBo.setApplyPaymentTransactionBo(applyPaymentTransactionBo);

                // 查询投保单保费
                ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(applyId);
                applyBo.setApplyPremiumBo(applyPremiumBo);

                // 查询投保单加费
                List<ApplyAddPremiumPo> applyAddPremiumPos = applyPremiumBaseService.getApplyAddPremium(applyId);
                applyBo.setListApplyAddPremiumPo(applyAddPremiumPos);

                // 查询特别约定
                List<ApplySpecialContractPo> applySpecialContractPos = applySpecialContractBaseService.listApplySpecialContract(applyId);
                applyBo.setListPolicySpecialContract(applySpecialContractPos);
                //关联业务员
                List<ApplyFactAgentPo> applyFactAgentPos = applyFactAgentDao.fetchByApplyId(applyId);
                if (AssertUtils.isNotEmpty(applyFactAgentPos)) {
                    applyBo.setApplyFactAgentPo(applyFactAgentPos.get(0));
                }
                //贷款合同信息
                ApplyLoanBo applyLoanPo = applyLoanBaseService.queryApplyLoanPo(applyId);
                if (AssertUtils.isNotNull(applyLoanPo)) {
                    applyBo.setLoanContract(applyLoanPo);
                }

                //推荐信息
                ApplyReferralInfoPo applyReferralInfoPo = applyReferralInfoBaseService.queryApplyReferralInfoPo(applyId);
                if (AssertUtils.isNotNull(applyReferralInfoPo)) {
                    applyBo.setReferralInfo(applyReferralInfoPo);
                }

                //保单持有人
                ApplyHolderBo applyHolderBo = applyOtherInfoBaseService.getApplyHolderBo(applyId);
                if (AssertUtils.isNotNull(applyHolderBo)) {
                    applyBo.setHolder(applyHolderBo);
                }

                //职业性质
                List<ApplyOccupationNaturePo> applyOccupationNaturePos = applyOtherInfoBaseService.queryAllApplyOccupationNaturePo(applyId,null);
                if (AssertUtils.isNotNull(applyOccupationNaturePos)) {
                    applyBo.setOccupationNature(applyOccupationNaturePos);
                }
                //其他保险
                List<ApplyOtherInsuranceBo> applyOtherInsurancePos = applyOtherInfoBaseService.queryApplyOtherInsurancePo(applyId);
                if (AssertUtils.isNotNull(applyOtherInsurancePos)) {
                    applyBo.setOtherInsurance(applyOtherInsurancePos);
                }
                //投保单声明
                List<ApplyStatementPo> applyStatementPos = applyOtherInfoBaseService.queryApplyStatementPo(applyId);
                if (AssertUtils.isNotNull(applyStatementPos)) {
                    applyBo.setStatements(applyStatementPos);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_ERROR);
            }
        }
        return applyBo;
    }

    public ApplyPlanPo queryApplyPlan(String applyId) {
        return applyBaseDao.queryApplyPlan(applyId);
    }

    /**
     * 保存投保单基本信息
     *
     * @param userId  用户ID
     * @param applyPo 投保单基本信息
     */
    @Override
    public void saveApplyPo(String userId, ApplyPo applyPo) {
        if (AssertUtils.isNotEmpty(applyPo.getApplyId())) {
            applyPo.setUpdatedUserId(userId);
            applyPo.setUpdatedDate(System.currentTimeMillis());
            applyDao.update(applyPo);
        } else {
            applyPo.setApplyId(UUIDUtils.getUUIDShort());
            applyPo.setCreatedUserId(userId);
            applyPo.setCreatedDate(System.currentTimeMillis());
            applyPo.setUpdatedUserId(userId);
            applyPo.setUpdatedDate(System.currentTimeMillis());
            applyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            applyDao.insert(applyPo);
        }
    }

    /**
     * 保存投保单基本信息
     *
     * @param createUserId 创建用户ID
     * @param applyPo      投保单基本信息
     * @return applyId
     */
    @Override
    public String saveApply(String createUserId, ApplyPo applyPo) {
        return saveApply(createUserId, null, applyPo);
    }

    /**
     * 保存投保单基本信息
     *
     * @param createUserId 创建用户ID
     * @param agentUserId  代理人用户ID
     * @param applyPo      投保单基本信息
     * @return applyId
     */
    @Override
    @Transactional
    public String saveApply(String createUserId, String agentUserId, ApplyPo applyPo) {
        try {
            // 参数校验
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_IS_NOT_NULL);
            getLogger().info("createUserId:" + createUserId);
            getLogger().info("agentUserId:" + agentUserId);
            getLogger().info("applyPo:" + JSON.toJSONString(applyPo));

            String applyId = applyPo.getApplyId();

            if (AssertUtils.isNotEmpty(applyId)) {
                // 更新
                applyPo.setUpdatedDate(DateUtils.getCurrentTime());
                applyPo.setUpdatedUserId(createUserId);
                applyDao.update(applyPo);
            } else {
                // 新增
                // 查询代理人信息
                AssertUtils.isNotEmpty(getLogger(), agentUserId, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_AGENT_ID_IS_NOT_NULL);
                ResultObject<AgentSimpleBaseResponse> agentResultObject = baseAgentApi.getUserAgents(agentUserId);
                AssertUtils.isResultObjectError(getLogger(), agentResultObject);
                AgentSimpleBaseResponse agentSimpleBaseRespFc = agentResultObject.getData();
                // 查询机构信息
                ResultObject<BranchResponse> branchResultObject = platformBranchBaseApi.queryOneBranchById(agentSimpleBaseRespFc.getBranchId());
                AssertUtils.isResultObjectError(getLogger(), branchResultObject);
                BranchResponse branchResponse = branchResultObject.getData();

                // 保存投保单基本信息
                applyId = UUIDUtils.getUUIDShort();
                applyPo.setApplyId(applyId);
                if (!AssertUtils.isNotEmpty(applyPo.getApplyNo())) {
                    // 生成投保单号
                    ResultObject<CertifyNumberResponse> certifyNumberRespFcResultObject = certifyApplyApi.certifyNumberGetNew(branchResponse.getChannelTypeCode()
                            , ApplyTermEnum.BUSINESS_TYPE.APPLY.name(), ApplyTermEnum.APPLICANT_TYPE.GROUP.name(),
                            AssertUtils.isNotNull(branchResponse.getBranchBank()) ? branchResponse.getBranchBank().getBankAbbreviation() : null,
                            branchResponse.getBranchId(),null,null,null);
                    AssertUtils.isResultObjectError(getLogger(), certifyNumberRespFcResultObject);

                    applyPo.setApplyNo(certifyNumberRespFcResultObject.getData().getCertifyNumber());
                }
                applyPo.setSalesBranchId(branchResponse.getBranchId());
                applyPo.setManagerBranchId(branchResponse.getManagerBranchId());
                applyPo.setChannelTypeCode(branchResponse.getChannelTypeCode());
                applyPo.setCreatedDate(DateUtils.getCurrentTime());
                applyPo.setCreatedUserId(createUserId);
                applyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());

                applyDao.insert(applyPo);

                //查询计划书组装推荐活动信息
                ApplyPlanBo applyPlanBo = applyPlanBaseDao.queryApplyPlan(applyId);

                // 保存投保单代理人信息
                ApplyAgentPo applyAgentPo = new ApplyAgentPo();
                applyAgentPo.setApplyId(applyId);
                applyAgentPo.setAgentId(agentSimpleBaseRespFc.getAgentId());
                applyAgentPo.setAgentCode(agentSimpleBaseRespFc.getAgentCode());
                if (AssertUtils.isNotNull(applyPlanBo)) {
                    applyAgentPo.setReferralName(applyPlanBo.getReferralName());
                    applyAgentPo.setAbaAccount(applyPlanBo.getAbaAccount());
                }
                applyAgentBaseService.saveApplyAgent(createUserId, applyAgentPo);
            }
            getLogger().info("applyId:" + applyId);
            return applyId;
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_SAVE_APPLY_ERROR);
            }
        }
    }

    /**
     * 根据投保单ID查询投保单联系信息
     *
     * @param applyId 投保单ID
     * @return ApplyContactInfoPo
     */
    @Override
    public ApplyContactInfoPo queryApplyContactInfo(String applyId) {
        ApplyContactInfoPo applyContactInfoPo;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);
            getLogger().info("applyId:" + applyId);

            applyContactInfoPo = applyBaseDao.getApplyContactInfo(applyId);

            getLogger().info("applyContactInfoPo:" + JSON.toJSONString(applyContactInfoPo));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_CONTACT_INFO_ERROR);
            }
        }
        return applyContactInfoPo;
    }

    /**
     * 根据投保单ID查询投保单问题件信息
     *
     * @param applyId 投保单ID
     * @return List<ApplyQuestionFlowPo>
     */
    @Override
    public List<ApplyQuestionFlowPo> listApplyQuestionFlow(String applyId) {
        return this.listApplyQuestionFlow(applyId, null);
    }

    /**
     * 根据投保单ID和操作状态查询投保单问题件信息
     *
     * @param applyId      投保单ID
     * @param optionStatus 问题件操作状态(INIT"初始化",COMPLETE"已经提交") 为空查所有
     * @return List<ApplyQuestionFlowPo>
     */
    @Override
    public List<ApplyQuestionFlowPo> listApplyQuestionFlow(String applyId, String optionStatus) {
        List<ApplyQuestionFlowPo> applyQuestionFlowPos;
        try {
            // 参数校验
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_BASE_PARAMETER_APPLY_ID_IS_NOT_NULL);
            getLogger().info("applyId:" + applyId);

            applyQuestionFlowPos = applyBaseDao.getApplyQuestionFlowBos(applyId, optionStatus);

            getLogger().info("applyQuestionFlowPos:" + JSON.toJSONString(applyQuestionFlowPos));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                throw e;
            } else {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_QUESTION_FLOW_ERROR);
            }
        }
        return applyQuestionFlowPos;
    }

    @Override
    public ApplyInsuredPo querySokSanCustomer(String applyId, String name, String sex, Long birthday, String idNo) {
        return applyBaseDao.querySokSanCustomer(applyId, name, sex, birthday, idNo);
    }

    @Override
    public void saveApplyContactInfo(String userId, ApplyContactInfoPo applyContactInfoPo) {
        try {
            getLogger().info("userId:" + userId);
            getLogger().info("applyContactInfoPo:" + JSON.toJSONString(applyContactInfoPo));
            if (AssertUtils.isNotEmpty(applyContactInfoPo.getApplyContactId())) {
                applyContactInfoPo.setUpdatedDate(DateUtils.getCurrentTime());
                applyContactInfoPo.setUpdatedUserId(userId);
                applyContactInfoDao.update(applyContactInfoPo);
            } else {
                //执行新增
                applyContactInfoPo.setApplyContactId(UUIDUtils.getUUIDShort());
                applyContactInfoPo.setCreatedDate(DateUtils.getCurrentTime());
                applyContactInfoPo.setCreatedUserId(userId);
                applyContactInfoPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                applyContactInfoDao.insert(applyContactInfoPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(getLogger(), e, ApplyErrorConfigEnum.APPLY_BASE_SAVE_APPLY_CONTACT_INFO_ERROR);
        }
    }

    @Override
    @Transactional
    public void saveApplyAbandoned(String userId, ApplyAbandonedPo applyAbandonedPo) {
        try {
            if (AssertUtils.isNotEmpty(applyAbandonedPo.getAbandonedId())) {
                // 执行修改
                applyAbandonedPo.setUpdatedDate(DateUtils.getCurrentTime());
                applyAbandonedPo.setUpdatedUserId(userId);
                applyAbandonedDao.update(applyAbandonedPo);
            } else {
                // 执行新增
                applyAbandonedPo.setAbandonedId(UUIDUtils.getUUIDShort());
                applyAbandonedPo.setCreatedDate(DateUtils.getCurrentTime());
                applyAbandonedPo.setCreatedUserId(userId);
                applyAbandonedPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                applyAbandonedDao.insert(applyAbandonedPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(getLogger(), e, ApplyErrorConfigEnum.APPLY_BASE_SAVE_APPLY_ABANDONED_ERROR);
        }
    }

    @Override
    public ApplyAbandonedPo queryApplyAbandoned(String applyId) {
        return applyBaseDao.queryApplyAbandoned(applyId);
    }

    /**
     * 查询投保单信息跟投保人信息（收付费明细报表）
     *
     * @param businessId payment关联投保单id
     */
    @Override
    public List<ApplyReportBo> queryApplyReport(List<String> businessId) {
        return applyBaseDao.queryApplyReport(businessId);
    }

    /**
     * 保存计划书追踪信息
     *
     * @param userId           用户
     * @param applyPlanTracePo 计划书追踪
     */
    @Override
    public void saveApplyPlanTrace(String userId, ApplyPlanTracePo applyPlanTracePo) {
        if (AssertUtils.isNotEmpty(applyPlanTracePo.getPlanTraceId())) {
            applyPlanTracePo.setUpdatedDate(DateUtils.getCurrentTime());
            applyPlanTracePo.setUpdatedUserId(userId);
            applyPlanTraceDao.update(applyPlanTracePo);
        } else {
            applyPlanTracePo.setPlanTraceId(UUIDUtils.getUUIDShort());
            applyPlanTracePo.setCreatedDate(DateUtils.getCurrentTime());
            applyPlanTracePo.setCreatedUserId(userId);
            applyPlanTracePo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            applyPlanTraceDao.insert(applyPlanTracePo);
        }
    }

    /**
     * 查询计划书追踪信息
     *
     * @param applyPlanId 计划书id
     * @return ApplyPlanTracePos
     */
    @Override
    public List<ApplyPlanTracePo> queryApplyPlanTracePo(String applyPlanId) {
        return applyBaseDao.queryApplyPlanTracePo(applyPlanId);
    }

    /**
     * 查询计划书追踪信息
     *
     * @param applyPlanId     计划书id
     * @param basePageRequest
     * @return ApplyPlanTracePos
     */
    @Override
    public List<ApplyPlanTraceBo> queryApplyPlanTraceByPage(String applyPlanId, BasePageRequest basePageRequest) {
        return applyBaseDao.queryApplyPlanTraceByPage(applyPlanId, basePageRequest);
    }

    /**
     * 被保人下的团险健康告知
     *
     * @param applyId
     * @param insuredId
     * @return
     */
    @Override
    public List<ApplyGroupHealthQuestionnaireAnswerPo> queryApplyGroupHealthQuestionnaireAnswer(String
                                                                                                        applyId, String insuredId) {
        return applyBaseDao.queryApplyGroupHealthQuestionnaireAnswer(applyId, insuredId);
    }

    /**
     * 被保人下的团险健康告知
     *
     * @param insuredIds 被保人ID集
     * @return
     */
    @Override
    public List<ApplyGroupHealthQuestionnaireAnswerPo> listApplyGroupHealthQuestionnaireAnswer
    (List<String> insuredIds) {
        return applyBaseDao.listApplyGroupHealthQuestionnaireAnswer(insuredIds);
    }

    /**
     * 删除团险健康告知
     *
     * @param applyId
     */
    @Override
    public void deleteApplyGroupHealthQuestionnaireAnswer(String applyId) {
        List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos = this.queryApplyGroupHealthQuestionnaireAnswer(applyId, null);
        if (AssertUtils.isNotEmpty(applyGroupHealthQuestionnaireAnswerPos)) {
            applyGroupHealthQuestionnaireAnswerDao.delete(applyGroupHealthQuestionnaireAnswerPos);
        }
    }

    /**
     * 删除团险健康告知
     *
     * @param applyGroupHealthQuestionnaireAnswerPos 健康告知数据
     */
    @Override
    public void deleteApplyGroupHealthQuestionnaireAnswer
    (List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos) {
        if (AssertUtils.isNotEmpty(applyGroupHealthQuestionnaireAnswerPos)) {
            applyGroupHealthQuestionnaireAnswerDao.delete(applyGroupHealthQuestionnaireAnswerPos);
        }
    }

    /**
     * 保存团险健康告知
     *
     * @param applyGroupHealthQuestionnaireAnswerPos
     * @param userId
     */
    @Override
    public void saveApplyGroupHealthQuestionnaireAnswer
    (List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos, String userId) {
        if (!AssertUtils.isNotEmpty(applyGroupHealthQuestionnaireAnswerPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<ApplyGroupHealthQuestionnaireAnswerPo> insertData = new ArrayList<>();
        List<ApplyGroupHealthQuestionnaireAnswerPo> updateData = new ArrayList<>();
        applyGroupHealthQuestionnaireAnswerPos.forEach(msgItemPo -> {
            if (AssertUtils.isNotEmpty(msgItemPo.getAghqaId())) {
                msgItemPo.setUpdatedDate(currentTime);
                msgItemPo.setUpdatedUserId(userId);
                updateData.add(msgItemPo);
            } else {
                msgItemPo.setAghqaId(UUIDUtils.getUUIDShort());
                msgItemPo.setCreatedDate(currentTime);
                msgItemPo.setCreatedUserId(userId);
                msgItemPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(msgItemPo);
            }
        });
        applyGroupHealthQuestionnaireAnswerDao.insert(insertData);
        applyGroupHealthQuestionnaireAnswerDao.update(updateData);
    }


    @Override
    public PrintApplyInsuredBo queryPrintApplyData(String applyId) {
        return applyBaseDao.queryPrintApplyData(applyId);
    }

    @Override
    public PrintApplyInsuredBo queryPrintApplyDataNew(String applyId) {
        return applyBaseDao.queryPrintApplyDataNew(applyId);
    }

    @Override
    public List<ApplyInsuredPrintBo> queryApplyInsuredPrint(ApplyInsuredPrintVo applyInsuredPrintVo) {
        return applyBaseDao.queryApplyInsuredPrint(applyInsuredPrintVo);
    }

    @Override
    public List<ApplyInsuredPrintBo> queryApplyInsuredPrintManage(ApplyInsuredPrintVo applyInsuredPrintVo) {
        return applyBaseDao.queryApplyInsuredPrintManage(applyInsuredPrintVo);
    }

    @Override
    public ApplyInsuredPrintPo queryApplyInsuredPrintPo(String applyInsuredPrintId) {
        return applyBaseDao.queryApplyInsuredPrintPo(applyInsuredPrintId);
    }

    @Override
    public ApplyInsuredPrintDetailPo queryApplyInsuredPrintPoByLanguage(String applyInsuredPrintId, String languageCode) {
        return applyBaseDao.queryApplyInsuredPrintPoByLanguage(applyInsuredPrintId,languageCode);
    }

    @Override
    public void saveApplyInsuredPrint(String userId, ApplyInsuredPrintPo applyInsuredPrintPo) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(applyInsuredPrintPo.getApplyInsuredPrintId())) {
            applyInsuredPrintPo.setUpdatedDate(currentTime);
            applyInsuredPrintPo.setUpdatedUserId(userId);
            applyInsuredPrintDao.update(applyInsuredPrintPo);
        } else {
            applyInsuredPrintPo.setApplyInsuredPrintId(UUIDUtils.getUUIDShort());
            applyInsuredPrintPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            applyInsuredPrintPo.setCreatedDate(currentTime);
            applyInsuredPrintPo.setCreatedUserId(userId);
            applyInsuredPrintDao.insert(applyInsuredPrintPo);
        }
    }

    @Override
    public ApplyInsuredPrintPo queryApplyInsuredPrintByApplyId(String applyId) {
        return applyBaseDao.queryApplyInsuredPrintByApplyId(applyId);
    }

    @Override
    public List<ApplyBeneficiaryBo> queryApplyBeneficiaryByApplyId(String applyId) {
        return applyBaseDao.queryApplyBeneficiaryByApplyId(applyId);
    }

    /**
     * 需要设置的投保人代表数据
     *
     * @return
     */
    @Override
    public List<ApplyGroupReportSyncApplicantBo> querySyncApplicantCustomer() {
        return applyBaseDao.querySyncApplicantCustomer();
    }

    /**
     * 需要设置的投保人代表Po数据
     *
     * @return
     */
    @Override
    public List<ApplyApplicantPo> queryApplicantCustomer() {
        return applyBaseDao.queryApplicantCustomer();
    }

    @Override
    public List<ActualPerformanceReportBo> queryActualPerformance(List<String> policyIdList) {

        return applyBaseDao.queryActualPerformance(policyIdList);
    }

    /**
     * 投保单ID查询受益人信息
     *
     * @param applyId
     * @param modifyFlag
     * @return
     */
    @Override
    public List<ApplyBeneficiaryInfoBo> queryApplyLoanBeneficiary(String applyId, String modifyFlag) {
        return applyBaseDao.queryApplyLoanBeneficiary(applyId, modifyFlag);
    }

    /**
     * 查询投保单操作表
     *
     * @param applyId 投保单ID
     * @return ApplyOperationPo
     */
    @Override
    public ApplyOperationPo queryApplyOperationPo(String applyId) {
        List<ApplyOperationPo> applyOperationPos = applyOperationDao.fetchByApplyId(applyId);
        return AssertUtils.isNotEmpty(applyOperationPos) ? applyOperationPos.get(0) : null;
    }

    /**
     * 保存投保单操作表
     *
     * @param applyOperationPo 投保单操作表
     * @param userId           用户
     */
    @Override
    public void saveApplyOperationPo(ApplyOperationPo applyOperationPo, String userId) {
        Long currentTime = DateUtils.getCurrentTime();
        if (AssertUtils.isNotEmpty(applyOperationPo.getApplyOperationId())) {
            applyOperationPo.setUpdatedDate(currentTime);
            applyOperationPo.setUpdatedUserId(userId);
            applyOperationDao.update(applyOperationPo);
        } else {
            applyOperationPo.setApplyOperationId(UUIDUtils.getUUIDShort());
            applyOperationPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            applyOperationPo.setCreatedDate(currentTime);
            applyOperationPo.setCreatedUserId(userId);
            applyOperationDao.insert(applyOperationPo);
        }
    }

    @Override
    public List<ServiceChargeBankChannelBo> syncApplyServiceChargeBankChannel(BasePageRequest
                                                                                      basePageRequest, String syncDate) {

        return applyBaseDao.syncApplyServiceChargeBankChannel(basePageRequest, syncDate);
    }

    @Override
    public List<SaleApplyPolicyBo> syncSaleApplyPolicy(BasePageRequest basePageRequest, String syncDate) {
        List<SaleApplyPolicyBo> saleApplyPolicyBos = applyBaseDao.syncSaleApplyPolicy(basePageRequest, syncDate);
        if (!AssertUtils.isNotEmpty(saleApplyPolicyBos)) {
            return saleApplyPolicyBos;
        }
        List<String> applyIdList = saleApplyPolicyBos.stream().map(SaleApplyPolicyBo::getApplyId).distinct().collect(Collectors.toList());

        List<SaleApplyPolicyBo> saleApplyPolicyCoverageList = applyBaseDao.syncSaleApplyPolicyCoverage(applyIdList);

        List<PolicyReportResponse> policyReportResponseList = paymentReportApi.queryPaymentMethod(applyIdList).getData();

        List<ApplyPaymentTransactionBo> applyPaymentTransactionBoList = applyPaymentTransactionBaseService.queryApplyPaymentTransactionList(applyIdList);

        // 设置产品名称
        List<ProductTermEnum.PRODUCT> products = Arrays.asList(ProductTermEnum.PRODUCT.values());

        List<ApplyPo> applyPos = applyDao.fetchByApplyId(applyIdList.toArray(new String[0]));

        saleApplyPolicyCoverageList.forEach(saleApplyPolicyBo -> {
                Optional<SaleApplyPolicyBo> first = saleApplyPolicyBos.stream().filter(applyPolicyBo -> applyPolicyBo.getApplyId().equals(saleApplyPolicyBo.getApplyId())).findFirst();
                if (first.isPresent()) {
                    SaleApplyPolicyBo applyPolicyBo = first.get();
                    ClazzUtils.copyPropertiesIgnoreNull(applyPolicyBo, saleApplyPolicyBo);
                    if (AssertUtils.isNotEmpty(applyPaymentTransactionBoList)) {
                        List<ApplyPaymentTransactionBo> paymentTransactionBoList = applyPaymentTransactionBoList.stream().filter(applyPaymentTransactionBo -> saleApplyPolicyBo.getApplyId().equals(applyPaymentTransactionBo.getApplyId())).collect(Collectors.toList());
                        if (AssertUtils.isNotEmpty(paymentTransactionBoList)) {
                            paymentTransactionBoList.stream().filter(applyPaymentTransactionBo -> SUSPENSE_PREMIUM.name().equals(applyPaymentTransactionBo.getFeeType()) && PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus()))
                                    .findFirst().ifPresent(applyPaymentTransactionBo -> {
                                        saleApplyPolicyBo.setSuspensePremium(applyPaymentTransactionBo.getPaymentAmount());
                                        saleApplyPolicyBo.setSuspensePremiumDate(applyPaymentTransactionBo.getArrivalDate());
                                    });
                            paymentTransactionBoList.stream().filter(applyPaymentTransactionBo -> PAYMENT.name().equals(applyPaymentTransactionBo.getFeeType()) && PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus()))
                                    .findFirst().ifPresent(applyPaymentTransactionBo -> {
                                        saleApplyPolicyBo.setSuspensePremiumAdd(applyPaymentTransactionBo.getPaymentAmount());
                                        saleApplyPolicyBo.setSuspensePremiumAddDate(applyPaymentTransactionBo.getArrivalDate());
                                    });
                            paymentTransactionBoList.stream().filter(applyPaymentTransactionBo -> SUSPENSE_PREMIUM_REFUND.name().equals(applyPaymentTransactionBo.getFeeType()))
                                    .findFirst().ifPresent(applyPaymentTransactionBo -> {
                                        saleApplyPolicyBo.setSuspensePremiumRefund(applyPaymentTransactionBo.getPaymentAmount());
                                        saleApplyPolicyBo.setSuspensePremiumRefundDate(applyPaymentTransactionBo.getArrivalDate());
                                        saleApplyPolicyBo.setSuspensePremiumRefundStatus(applyPaymentTransactionBo.getPaymentStatus());
                                    });
                        }
                    }
                    products.stream().filter(product -> product.id().equals(saleApplyPolicyBo.getProductId()))
                            .findFirst().ifPresent(product -> saleApplyPolicyBo.setProductName(product.desc()));

                    if (AssertUtils.isNotEmpty(policyReportResponseList)) {
                        policyReportResponseList.stream().filter(policyReportResponse -> policyReportResponse.getBusinessId().equals(saleApplyPolicyBo.getApplyId()) &&
                                        SUSPENSE_PREMIUM.name().equals(policyReportResponse.getPaymentType()))
                                .findFirst().ifPresent(policyReportResponse -> {
                                    saleApplyPolicyBo.setSuspensePaymentBankCode(policyReportResponse.getBankCode());
                                    saleApplyPolicyBo.setSuspensePaymentMethodCode(policyReportResponse.getPaymentMethodCode());
                                    if(AssertUtils.isNotEmpty(policyReportResponse.getReceiptNoJson())){
                                        saleApplyPolicyBo.setReceiptNoJson(policyReportResponse.getReceiptNoJson());
                                    }
                                });
                        policyReportResponseList.stream().filter(policyReportResponse -> policyReportResponse.getBusinessId().equals(saleApplyPolicyBo.getApplyId()) &&
                                        PAYMENT.name().equals(policyReportResponse.getPaymentType()))
                                .findFirst().ifPresent(policyReportResponse -> {
                                    saleApplyPolicyBo.setPaymentBankCode(policyReportResponse.getBankCode());
                                    saleApplyPolicyBo.setPaymentMethodCode(policyReportResponse.getPaymentMethodCode());
                                    if(AssertUtils.isNotEmpty(policyReportResponse.getReceiptNoJson())){
                                        saleApplyPolicyBo.setReceiptNoJson(policyReportResponse.getReceiptNoJson());
                                    }
                                });
                    }

                        ApplyPremiumBo applyPremiumBo = applyPaymentTransactionBaseDao.queryApplyPremiumBo(saleApplyPolicyBo.getApplyId());
                        if (AssertUtils.isNotNull(applyPremiumBo)) {
                            //若是现金折扣，则只设置主险，不用设置附加险
                            if (AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount()) && AssertUtils.isNotNull(applyPremiumBo.getDiscountModel())) {
                                if (ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(applyPremiumBo.getDiscountModel())) {
                                    saleApplyPolicyBo.setSpecialDiscount(applyPremiumBo.getSpecialDiscount());
                                    saleApplyPolicyBo.setDiscountModel(applyPremiumBo.getDiscountModel());
                                    saleApplyPolicyBo.setPremiumBeforeDiscount(applyPremiumBo.getPremiumBeforeDiscount());
                                    saleApplyPolicyBo.setApplyTotalActualPremium(applyPremiumBo.getTotalActualPremium());
                                    saleApplyPolicyBo.setPromotionalCode(applyPremiumBo.getPromotionalCode());
                                }
                                if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(applyPremiumBo.getDiscountModel()) && ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(saleApplyPolicyBo.getPrimaryFlag())) {
                                    saleApplyPolicyBo.setSpecialDiscount(applyPremiumBo.getSpecialDiscount());
                                    saleApplyPolicyBo.setDiscountModel(applyPremiumBo.getDiscountModel());
                                    saleApplyPolicyBo.setPremiumBeforeDiscount(applyPremiumBo.getPremiumBeforeDiscount());
                                    saleApplyPolicyBo.setApplyTotalActualPremium(applyPremiumBo.getTotalActualPremium());
                                }
                            }
                        }
                    applyPos.stream().filter(applyPo -> applyPo.getApplyId().equals(saleApplyPolicyBo.getApplyId())).findFirst().ifPresent(applyPo -> {
                        // eMoney PromoCode
                        if ("INIT_AGENT_ONLINE003".equals(saleApplyPolicyBo.getAgentId()) && ApplyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(saleApplyPolicyBo.getChannelTypeCode())) {
                            saleApplyPolicyBo.setPromotionalCode(applyPo.getReferralCode());
                        }
                        // 激活码对应的参考号作为销售报表里的优惠码
                        if (AssertUtils.isNotEmpty(applyPo.getActivationCode())) {
                            ScratchCardPo scratchCardPo = scratchCardBaseService.queryActivationCode(applyPo.getActivationCode());
                            if (AssertUtils.isNotNull(scratchCardPo)) {
                                saleApplyPolicyBo.setPromotionalCode(scratchCardPo.getReferenceNo());
                                saleApplyPolicyBo.setPartnerId(scratchCardPo.getPartnerId());
                                ScratchCardAssignPo scratchCardAssign = scratchCardBaseService.getScratchCardAssignPo(applyPo.getActivationCode());
                                saleApplyPolicyBo.setRemark(AssertUtils.isNotNull(scratchCardAssign) ? scratchCardAssign.getRemark() : null);
                            }
                        }
                    });
                }
        });
        return saleApplyPolicyCoverageList;

    }

    /**
     * 保存投保单打印信息
     *
     * @param applyPrintInfoPo 投保单打印信息
     */
    @Override
    @Transactional
    public void saveApplyPrintInfo(ApplyPrintInfoPo applyPrintInfoPo) {
        try {
            this.getLogger().info("applyPrintInfoPo:" + JSON.toJSONString(applyPrintInfoPo));
            if (!AssertUtils.isNotEmpty(applyPrintInfoPo.getPrintInfoId())) {
                //执行新增
                applyPrintInfoPo.setPrintInfoId(UUIDUtils.getUUIDShort());
                applyPrintInfoPo.setCreatedDate(DateUtils.getCurrentTime());
                applyPrintInfoPo.setValidFlag(com.gclife.common.TerminologyConfigEnum.VALID_FLAG.effective.name());
                applyPrintInfoDao.insert(applyPrintInfoPo);
            } else {
                //执行修改
                applyPrintInfoPo.setUpdatedDate(DateUtils.getCurrentTime());
                applyPrintInfoDao.update(applyPrintInfoPo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            throwsTransactionalException(this.getLogger(), e,ApplyErrorConfigEnum.APPLY_PRINT_INFO_SAVE_ERROR);
        }
    }

    /**
     * 获取投保单存在客户列表
     *
     * @param applyId        投保单ID
     * @param allCustomerIds 客户ID
     * @param notApproveFlag
     * @return ApplyRealClientListBos
     */
    @Override
    public List<ApplyRealClientListBo> getApplyRealClient(String applyId, List<String> allCustomerIds, boolean notApproveFlag) {
        return applyBaseDao.getApplyRealClient(applyId, allCustomerIds, notApproveFlag);
    }

    /**
     * 查询网销重复的投保单
     *
     * @param applyId    投保单ID
     * @param customerId 客户ID
     * @return ApplyPos
     */
    @Override
    public List<ApplyPo> getRepeatOnlineApply(String applyId, String customerId) {
        return applyBaseDao.getRepeatOnlineApply(applyId, customerId);
    }
}
