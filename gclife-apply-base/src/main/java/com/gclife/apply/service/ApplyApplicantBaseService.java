package com.gclife.apply.service;

import com.gclife.apply.core.jooq.tables.pojos.ApplyApplicantPo;
import com.gclife.apply.model.bo.ApplyApplicantBo;
import com.gclife.apply.model.bo.group.ApplyApplicantListBo;
import com.gclife.common.model.ResultObject;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 投保人基础服务
 * @date 19-8-16
 */
public interface ApplyApplicantBaseService {

    /**
     * 根据投保单ID查询投保人信息
     *
     * @param applyId 投保单ID
     * @return ApplyApplicantBo
     */
    ApplyApplicantBo queryApplyApplicant(String applyId);

    /**
     * 保存投保单投保人信息
     *
     * @param userId           用户ID
     * @param applyApplicantBo 投保人信息
     */
    void saveApplyApplicant(String userId, ApplyApplicantBo applyApplicantBo);

    /**
     * 保存投保单投保人信息
     *
     * @param userId           用户ID
     * @param applyApplicantPo 投保人信息
     */
    void saveApplyApplicant(String userId, ApplyApplicantPo applyApplicantPo);

    /**
     * 根据投保单ID查询投保人信息
     *
     * @param applyType 投保单类型
     * @return ApplyApplicantBo
     */
    List<ApplyApplicantBo> queryApplyApplicantList(String applyType);

    List<ApplyApplicantListBo> applyApplicantList(List<String> applyIds);
}
