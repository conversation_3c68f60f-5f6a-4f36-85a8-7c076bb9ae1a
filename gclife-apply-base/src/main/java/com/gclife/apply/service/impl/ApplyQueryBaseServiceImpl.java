package com.gclife.apply.service.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentSimpleBaseResponse;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.dao.ApplyBusinessDao;
import com.gclife.apply.model.bo.ApplyHcBo;
import com.gclife.apply.model.bo.ApplyListBo;
import com.gclife.apply.model.bo.ApplyUnderwriteNoticeListBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.vo.ApplyListVo;
import com.gclife.apply.service.ApplyQueryBaseService;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import com.gclife.workflow.model.response.WorkItemResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
@Service
@Slf4j
public class ApplyQueryBaseServiceImpl extends BaseBusinessServiceImpl implements ApplyQueryBaseService {

    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private ApplyBusinessDao applyBusinessDao;
    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;

    /**
     * 查询用户所属的投保单列表
     *
     * @param userId      当前用户
     * @param applyListVo 列表请求数据
     * @param applyType   投保单类型
     * @param applyStatus 投保单状态
     * @return List
     */
    @Override
    public List<ApplyListBo> loadApplyListByUserId(String userId, ApplyListVo applyListVo, String applyType, String applyStatus) {
        List<ApplyListBo> applyListBos = new ArrayList<>();
        try {
            ResultObject<AgentSimpleBaseResponse> simpleBaseRespFcResultObject = baseAgentApi.getUserAgents(userId);
            AssertUtils.isResultObjectError(this.getLogger(), simpleBaseRespFcResultObject, ApplyErrorConfigEnum.APPLY_BASE_QUERY_APPLY_AGENT_ERROR);
            applyListBos = applyBusinessDao.loadApplyListByAgentId(simpleBaseRespFcResultObject.getData().getAgentId(), applyListVo, applyType, applyStatus);
            if (AssertUtils.isNotEmpty(applyListBos)) {
                applyListBos.forEach(applyListBo -> {
                    applyListBo.setApplyDateFormat(DateUtils.timeStrToString(applyListBo.getApplyDate()));
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyListBos;
    }

    @Override
    public List<ApplyListBo> loadGroupApplyListByUserId(String userId, ApplyListVo applyListVo, String applyType) {
        List<ApplyListBo> applyListBos = new ArrayList<>();
        try {
            ResultObject<List<BranchResponse>> branchBaseRespFcs = platformBranchBaseApi.queryUserOptionBranchTreeLeaf(userId);
            if (AssertUtils.isResultObjectListDataNull(branchBaseRespFcs)) {
                return applyListBos;
            }
            List<String> branchIds = branchBaseRespFcs.getData().stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
            applyListVo.setBranchIds(branchIds);
            applyListBos = applyBusinessDao.loadGroupApplyList(applyListVo, applyType);
            if (AssertUtils.isNotEmpty(applyListBos)) {
                applyListBos.forEach(applyListBo -> {
                    applyListBo.setApplyDateFormat(DateUtils.timeStrToString(applyListBo.getApplyDate(), DateUtils.FORMATE5));
                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyListBos;
    }

    /**
     * 查询当前用户所属的投保单列表
     *
     * @param applyListVo   列表请求数据
     * @param applicantType 投保人类型(GROUP/PERSONAL)
     * @return List
     */
    @Override
    public List<ApplyListBo> loadApplyListByBranch(String userId, ApplyListVo applyListVo, String applicantType) {
        List<ApplyListBo> applyListBos = new ArrayList<>();
        try {
            ResultObject<List<BranchResponse>> branchBaseRespFcs = platformBranchBaseApi.queryUserOptionBranchTreeLeaf(userId);
            if (AssertUtils.isResultObjectListDataNull(branchBaseRespFcs)) {
                return applyListBos;
            }
            List<String> branchIds = branchBaseRespFcs.getData().stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());

            applyListBos = applyBusinessDao.loadApplyListByBranchIds(branchIds, applyListVo, applicantType);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return applyListBos;
    }

    /**
     * 查询用户能处理的投保单列表(待办任务)
     *
     * @param waitingTaskRequest 工作流请求数据
     * @param applyListVo        列表请求数据
     * @param applicantType      投保人类型(GROUP/PERSONAL)
     * @return List
     */
    @Override
    public List<ApplyListBo> loadApplyListByWorkFlow(WaitingTaskRequest waitingTaskRequest, ApplyListVo applyListVo, String applicantType) {
        List<ApplyListBo> newApplyList = new ArrayList<>();
        try {
            //获取当前用户的流程中的待办任务
            log.info("tasksRequest:" + JSON.toJSON(waitingTaskRequest));
            ResultObject<List<WorkItemResponse>> resultObject = workFlowApi.queryWaitingTasks(waitingTaskRequest);
            AssertUtils.isResultObjectError(this.getLogger(), resultObject, ApplyErrorConfigEnum.APPLY_BASE_QUERY_WORKFLOW_SERVICE_FAIL);
            List<WorkItemResponse> listWorkflowTaskResponse = resultObject.getData();
            List<String> listApplyIds = new ArrayList<>();
            if (AssertUtils.isNotEmpty(listWorkflowTaskResponse)) {
                listApplyIds = listWorkflowTaskResponse.stream().map(WorkItemResponse::getBusinessId).distinct().collect(Collectors.toList());
            }
            if (!AssertUtils.isNotEmpty(listApplyIds)) {
                return null;
            }
            List<ApplyListBo> applyListBos = applyBusinessDao.loadApplyListByApplyIds(listApplyIds, applyListVo, applicantType);

            for (WorkItemResponse workItemResponse : listWorkflowTaskResponse) {
                for (ApplyListBo applyListBo : applyListBos) {
                    if (workItemResponse.getBusinessId().equals(applyListBo.getApplyId())) {
                        if (ApplyTermEnum.WORKFLOW_ITEM_STATUS.NEW_TASK.name().equals(workItemResponse.getWorkItemStatus())) {
                            applyListBo.setOrderIndex(3);
                        } else if (ApplyTermEnum.WORKFLOW_ITEM_STATUS.PROCESSING.name().equals(workItemResponse.getWorkItemStatus())) {
                            applyListBo.setOrderIndex(2);
                        } else if (ApplyTermEnum.WORKFLOW_ITEM_STATUS.GO_BACK.name().equals(workItemResponse.getWorkItemStatus())) {
                            applyListBo.setOrderIndex(1);
                        }
                        applyListBo.setWorkflowStatus(workItemResponse.getWorkItemStatus());
                        newApplyList.add(applyListBo);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return newApplyList;
    }

    /**
     * 分页查询核保通知书打印列表
     *
     * @param userId      当前用户
     * @param applyListVo 列表请求参数
     * @return ApplyUnderwriteNoticeListBo
     */
    @Override
    public List<ApplyUnderwriteNoticeListBo> getApplyUnderwriteNoticeList(String userId, ApplyListVo applyListVo) {
        ResultObject<List<BranchResponse>> branchResponses = platformBranchBaseApi.queryUserOptionBranchTreeLeaf(userId);
        if (AssertUtils.isResultObjectListDataNull(branchResponses)) {
            return null;
        }
        List<String> branchIds = branchResponses.getData().stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
        applyListVo.setBranchIds(branchIds);
        return applyBusinessDao.getApplyUnderwriteNoticeList(applyListVo);
    }

    /**
     * 查询暂予承保待生效的单
     *
     * @param basePageRequest 分页
     * @return ApplyPos
     */
    @Override
    public List<ApplyHcBo> queryApprovedHcEffective(BasePageRequest basePageRequest) {
        return applyBusinessDao.queryApprovedHcEffective(basePageRequest);
    }
}
