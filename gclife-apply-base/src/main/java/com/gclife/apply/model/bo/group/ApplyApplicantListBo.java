package com.gclife.apply.model.bo.group;

import com.gclife.apply.core.jooq.tables.pojos.ApplyApplicantPo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ApplyApplicantListBo extends ApplyApplicantPo {
    @ApiModelProperty(value = "投保时间")
    private Long applyDate;

    @ApiModelProperty(value = "核保时间")
    private Long approveDate;

    private String policyNo;
}
