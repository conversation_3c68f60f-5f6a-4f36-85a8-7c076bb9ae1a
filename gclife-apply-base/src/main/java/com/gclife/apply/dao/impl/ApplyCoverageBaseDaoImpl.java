package com.gclife.apply.dao.impl;

import com.gclife.apply.core.jooq.tables.ApplyCoverage;
import com.gclife.apply.core.jooq.tables.pojos.ApplyCoverageLevelPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.dao.ApplyCoverageBaseDao;
import com.gclife.attachment.model.policy.policy.PolicyCoverageBo;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.product.model.config.ProductTermEnum;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.gclife.apply.core.jooq.Tables.APPLY;
import static com.gclife.apply.core.jooq.Tables.APPLY_APPLICANT;
import static com.gclife.apply.core.jooq.Tables.APPLY_COVERAGE;
import static com.gclife.apply.core.jooq.Tables.APPLY_COVERAGE_LEVEL;
import static com.gclife.apply.core.jooq.Tables.APPLY_INSURED;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW_COMPLETE;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_PAID_PENDING_ON_UW;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_ARTIFICIAL;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 19-8-27
 */
@Repository
public class ApplyCoverageBaseDaoImpl extends BaseDaoImpl implements ApplyCoverageBaseDao {

    /**
     * 查询险种档次信息
     *
     * @param applyId 投保单ID
     * @return
     */
    @Override
    public List<ApplyCoverageLevelPo> listApplyCoverageLevel(String applyId, String coverageDutyId) {
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(coverageDutyId)) {
            conditions.add(APPLY_COVERAGE_LEVEL.COVERAGE_DUTY_ID.eq(coverageDutyId));
        }
        conditions.add(APPLY_COVERAGE_LEVEL.APPLY_ID.eq(applyId).and(APPLY_COVERAGE_LEVEL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));
        return this.getDslContext()
                .selectFrom(APPLY_COVERAGE_LEVEL)
                .where(conditions)
                .orderBy(APPLY_COVERAGE_LEVEL.COVERAGE_ID, APPLY_COVERAGE_LEVEL.PRODUCT_LEVEL)
                .fetchInto(ApplyCoverageLevelPo.class);
    }

    /**
     * 根据险种ID集查询险种档次信息
     *
     * @param coverageIds 险种ID集
     * @return
     */
    @Override
    public List<ApplyCoverageLevelPo> listApplyCoverageLevel(List<String> coverageIds) {
        return this.getDslContext()
                .selectFrom(APPLY_COVERAGE_LEVEL)
                .where(APPLY_COVERAGE_LEVEL.COVERAGE_ID.in(coverageIds))
                .and(APPLY_COVERAGE_LEVEL.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .fetchInto(ApplyCoverageLevelPo.class);
    }

    @Override
    public List<PolicyCoverageBo> queryApplyCoverageBo(String applyId) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY_COVERAGE.fields())
                .select(APPLY_COVERAGE.AMOUNT.as("totalAmount"))
                .from(APPLY_COVERAGE)
                .where(APPLY_COVERAGE.APPLY_ID.eq(applyId));
        return selectConditionStep.fetchInto(PolicyCoverageBo.class);
    }

    @Override
    public List<PolicyCoverageBo> queryApplyCoverageByInsured(String applyId, String customerAgentId, boolean applicantFlag) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY_COVERAGE.fields())
                .select(APPLY_COVERAGE.AMOUNT.as("totalAmount"))
                .from(APPLY_COVERAGE)
                .innerJoin(APPLY_INSURED).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY_INSURED.APPLY_ID).and(APPLY_INSURED.CUSTOMER_ID.eq(customerAgentId)))
                .where(APPLY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()), APPLY_COVERAGE.APPLY_ID.eq(applyId));
        List<PolicyCoverageBo> policyCoverageBos = new ArrayList<>(selectConditionStep.fetchInto(PolicyCoverageBo.class));
        if (applicantFlag) {
            ApplyCoverage c2 = APPLY_COVERAGE.as("c2");
            SelectConditionStep<Record> applicantCoverage = this.getDslContext()
                    .select(APPLY_COVERAGE.fields())
                    .select(APPLY_COVERAGE.AMOUNT.as("totalAmount"))
                    .from(APPLY_COVERAGE)
                    .innerJoin(APPLY_APPLICANT).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID).and(APPLY_APPLICANT.CUSTOMER_ID.eq(customerAgentId)))
                    .innerJoin(c2).on(c2.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID), c2.PRODUCT_ID.in(ProductTermEnum.PRODUCT.PRODUCT_16B.id(), ProductTermEnum.PRODUCT.PRODUCT_23B.id()))
                    .where(APPLY_COVERAGE.APPLY_ID.eq(applyId), APPLY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            policyCoverageBos.addAll(applicantCoverage.fetchInto(PolicyCoverageBo.class));
        }
        return AssertUtils.isNotEmpty(policyCoverageBos) ? policyCoverageBos.stream().distinct().collect(Collectors.toList()) : null;
    }

    @Override
    public List<PolicyCoverageBo> queryOtherApplyCoverageByInsured(String applyId, List<String> customerAgentIds, boolean applicantFlag) {
        List<String> effectiveApply = Arrays.asList(
                APPLY_STATUS_INITIAL_COMPLETE.name(),//待复核
                APPLY_STATUS_INPUT_REVIEW.name(), //复核中
                APPLY_STATUS_INPUT_REVIEW_COMPLETE.name(), //待核保
                APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name(),//核保中
                APPLY_STATUS_PAID_PENDING_ON_UW.name(),//已交费待核保
                APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE.name(),//暂保生效
                APPLY_STATUS_UNDERWRITE_PASS.name());//待支付
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY_COVERAGE.fields())
                .select(APPLY_COVERAGE.AMOUNT.as("totalAmount"))
                .from(APPLY_COVERAGE)
                .innerJoin(APPLY).on(APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID), APPLY.APPLY_STATUS.in(effectiveApply), APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .innerJoin(APPLY_INSURED).on(APPLY.APPLY_ID.eq(APPLY_INSURED.APPLY_ID), APPLY_INSURED.CUSTOMER_ID.in(customerAgentIds), APPLY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .where(APPLY.APPLY_ID.ne(applyId));
        List<PolicyCoverageBo> policyCoverageBos = new ArrayList<>(selectConditionStep.fetchInto(PolicyCoverageBo.class));
        if (applicantFlag) {
            ApplyCoverage c2 = APPLY_COVERAGE.as("c2");
            SelectConditionStep<Record> applicantCoverage = this.getDslContext()
                    .select(APPLY_COVERAGE.fields())
                    .select(APPLY_COVERAGE.AMOUNT.as("totalAmount"))
                    .from(APPLY_COVERAGE)
                    .innerJoin(APPLY).on(APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID), APPLY.APPLY_STATUS.in(effectiveApply), APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                    .innerJoin(APPLY_APPLICANT).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID).and(APPLY_APPLICANT.CUSTOMER_ID.in(customerAgentIds)))
                    .innerJoin(c2).on(c2.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID), c2.PRODUCT_ID.in(ProductTermEnum.PRODUCT.PRODUCT_16B.id(), ProductTermEnum.PRODUCT.PRODUCT_23B.id()))
                    .where(APPLY.APPLY_ID.ne(applyId), APPLY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
            policyCoverageBos.addAll(applicantCoverage.fetchInto(PolicyCoverageBo.class));
        }
        return AssertUtils.isNotEmpty(policyCoverageBos) ? policyCoverageBos.stream().distinct().collect(Collectors.toList()) : null;
    }

    @Override
    public List<ApplyPo> queryOtherApplyByApplicant(String applyId, List<String> customerAgentIds) {
        List<String> effectiveApply = Arrays.asList(
                APPLY_STATUS_INITIAL_COMPLETE.name(),//待复核
                APPLY_STATUS_INPUT_REVIEW.name(), //复核中
                APPLY_STATUS_INPUT_REVIEW_COMPLETE.name(), //待核保
                APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name(),//核保中
                APPLY_STATUS_PAID_PENDING_ON_UW.name(),//已交费待核保
                APPLY_STATUS_APPROVE_SUCCESS.name(),//承保成功
                APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE.name(),//已交费待核保
                APPLY_STATUS_PAID_PENDING_ON_UW.name(),//已交费待核保
                APPLY_STATUS_UNDERWRITE_PASS.name());//待支付
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY.fields())
                .from(APPLY)
                .innerJoin(APPLY_APPLICANT).on(APPLY.APPLY_ID.eq(APPLY_APPLICANT.APPLY_ID), APPLY_APPLICANT.CUSTOMER_ID.in(customerAgentIds), APPLY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .where(APPLY.APPLY_ID.ne(applyId), APPLY.APPLY_STATUS.in(effectiveApply), APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        return selectConditionStep.fetchInto(ApplyPo.class);
    }

    @Override
    public List<ApplyPo> queryOtherApplyByInsured(String applyId, List<String> customerAgentIds) {
        List<String> effectiveApply = Arrays.asList(
                APPLY_STATUS_INITIAL_COMPLETE.name(),//待复核
                APPLY_STATUS_INPUT_REVIEW.name(), //复核中
                APPLY_STATUS_INPUT_REVIEW_COMPLETE.name(), //待核保
                APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name(),//核保中
                APPLY_STATUS_PAID_PENDING_ON_UW.name(),//已交费待核保
                APPLY_STATUS_APPROVE_SUCCESS.name(),//承保成功
                APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE.name(),//已交费待核保
                APPLY_STATUS_PAID_PENDING_ON_UW.name(),//已交费待核保
                APPLY_STATUS_UNDERWRITE_PASS.name());//待支付
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(APPLY.fields())
                .from(APPLY)
                .innerJoin(APPLY_INSURED).on(APPLY.APPLY_ID.eq(APPLY_INSURED.APPLY_ID), APPLY_INSURED.CUSTOMER_ID.in(customerAgentIds), APPLY_INSURED.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                .where(APPLY.APPLY_ID.ne(applyId), APPLY.APPLY_STATUS.in(effectiveApply), APPLY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()));
        return selectConditionStep.fetchInto(ApplyPo.class);
    }
}
