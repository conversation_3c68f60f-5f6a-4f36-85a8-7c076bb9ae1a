package com.gclife.apply.dao;

import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.group.ApplyApplicantListBo;
import com.gclife.apply.model.vo.ApplyInsuredPrintVo;
import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.BasePageRequest;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 投保单基础DAO
 * @date 18-4-27
 */
public interface ApplyBaseDao extends BaseDao {

    /**
     * 查询投保单基本新
     *
     * @param applyId 投保单号
     * @return
     */
    ApplyPo getApply(String applyId);

    /**
     * 查询投保单基本新
     *
     * @param applyNo 投保单号
     * @return
     */
    ApplyPo getApplyByApplyNo(String applyNo);

    /**
     * 根据代理人ID查询投保单数据
     *
     * @param agentIds    代理人ID
     * @param applyStatus 投保单状态
     * @return 投保单list
     */
    List<ApplyPo> getApplyByAgentId(List<String> agentIds, String applyStatus);

    /**
     * 根据投保单ID查询投保单数据
     *
     * @param applyIds 投保单ID
     * @return
     */
    List<ApplyPo> getApplyByApplyId(List<String> applyIds);

    /**
     * 根据投保单ID查询投保人信息
     *
     * @param applyId 投保单ID
     * @return ApplyApplicantPo
     */
    ApplyApplicantPo getApplyApplicant(String applyId);

    /**
     * 查询投保单附件信息列表
     *
     * @param applyId            投保单ID
     * @param attachmentTypeCode 投保单附件类形编码
     * @return list
     */
    List<ApplyAttachmentPo> getApplyAttachmentList(String applyId, String attachmentTypeCode);

    /**
     * 查询投保单附件信息列表
     *
     * @param applyId             投保单ID
     * @param attachmentTypeCodes 投保单附件类形编码
     * @return list
     */
    List<ApplyAttachmentPo> getApplyAttachmentList(String applyId, List<String> attachmentTypeCodes);

    /**
     * 查询有效的投保单支付附件
     *
     * @param applyId            投保单ID
     * @param attachmentTypeCode 附件类型
     * @return list
     */
    List<ApplyPaymentAttachmentPo> getApplyPaymentAttachmentList(String applyId, String attachmentTypeCode);

    /**
     * 查询团险投保单险种列表(无被保人)
     *
     * @param applyId 投保单ID
     * @return list
     */
    List<ApplyCoveragePo> getApplyCoverageList(String applyId);

    /**
     * 团险查询投保单险种信息列表
     *
     * @param applyId   投保单ID
     * @param insuredId 被保人ID
     * @return list
     */
    List<ApplyCoveragePo> getGroupApplyCoverageList(String applyId, String insuredId);

    /**
     * 仅仅根据applyId查询投保单主险险种信息
     *
     * @param applyId 投保单ID
     * @return list
     */
    List<ApplyCoveragePo> getOnlyApplyCoverageList(String applyId);

    /**
     * 查询被保人险种信息列表
     *
     * @param applyId   投保单ID
     * @param insuredId 被保人ID
     * @return list
     */
    List<ApplyCoveragePo> getApplyCoverageList(String applyId, String insuredId);

    /**
     * 查询主险数量
     *
     * @param applyId
     * @return
     */
    int getApplyCoverageNum(String applyId);

    /**
     * 查询投保单被保人列表
     *
     * @param applyId 投保单ID
     * @param keyword 搜索关键字
     * @return list
     */
    List<ApplyInsuredBo> getApplyInsuredList(String applyId, String keyword);

    /**
     * 查询被保人拓展信息列表
     *
     * @param insuredIds 被保人IDS
     * @return list
     */
    List<ApplyInsuredExtendPo> getApplyInsuredExtendList(List<String> insuredIds);

    /**
     * 查询投保单代理人信息
     *
     * @param applyId 投保单ID
     * @return ApplyAgentPo
     */
    ApplyAgentPo getApplyAgent(String applyId);

    /**
     * 根据投保单ID查询被保人统计信息
     *
     * @param applyId 投保单ID
     * @return ApplyInsuredCollectPo
     */
    ApplyInsuredCollectPo getApplyInsuredCollect(String applyId);

    /**
     * 根据投保单ID查询投保单联系信息
     *
     * @param applyId 投保单ID
     * @return ApplyContactInfoPo
     */
    ApplyContactInfoPo getApplyContactInfo(String applyId);

    /**
     * 根据投保单ID和操作状态查询投保单问题件信息
     *
     * @param applyId      投保单ID
     * @param optionStatus 问题件操作状态(INIT"初始化",COMPLETE"已经提交") 为空查所有
     * @return List<ApplyQuestionFlowPo>
     */
    List<ApplyQuestionFlowPo> getApplyQuestionFlowBos(String applyId, String optionStatus);

    /**
     * 根据投保单ID查询投保单特别约定信息
     *
     * @param applyId 投保单ID
     * @return List<ApplySpecialContractPo>
     */
    List<ApplySpecialContractPo> getApplySpecialContract(String applyId);

    /**
     * 根据投保单ID查询投保单特别约定信息
     *
     * @param applyId 投保单ID
     * @return ApplySpecialContractPo
     */
    ApplySpecialContractPo queryApplySpecialContract(String applyId);

    /**
     * 根据投保单ID查询投保单备注信息
     *
     * @param applyId 投保单ID
     * @return List<ApplyRemarkBo>
     */
    List<ApplyRemarkBo> getApplyRemark(String applyId);

    /**
     * 查询被保人最新批次信息
     *
     * @param applyId
     * @return
     */
    ApplyInsuredBatchPo getApplyInsuredBatch(String applyId);

    /**
     * 根据批次ID查询被保人上传信息
     *
     * @param insuredBatchId 批次ID
     * @return ApplyInsuredUploadBo
     */
    List<ApplyInsuredUploadBo> getApplyInsuredUpload(String insuredBatchId);

    /**
     * 获取缴费信息
     *
     * @param applyId
     * @return
     */
    ApplyPremiumBo getApplyPremium(String applyId);

    /**
     * 获取投保单账户列表
     *
     * @param applyId 投保单id
     * @return list
     */
    List<ApplyAccountBo> getApplyAccountList(String applyId);

    /**
     * 根据投保单ID查询账户信息
     *
     * @param applyId 投保单ID
     * @return
     */
    ApplyAccountPo queryApplyAccount(String applyId);

    /**
     * 获取投保单加费信息
     *
     * @param applyId 投保单id
     * @return ApplyAddPremiumPo
     */
    List<ApplyAddPremiumPo> getApplyAddPremium(String applyId);

    /**
     * 查询指定加费信息
     *
     * @param applyId    投保单Id
     * @param insuredId  被保人Id
     * @param coverageId 险种Id
     * @return ApplyAddPremiumPo
     */
    List<ApplyAddPremiumPo> getApplyAddPremium(String applyId, String insuredId, String coverageId);

    /**
     * 查询核保任务
     *
     * @param applyId 投保单ID
     * @return ApplyUnderwriteTaskPo
     */
    ApplyUnderwriteTaskPo getApplyUnderwriteTask(String applyId);

    /**
     * 查询核保决定
     *
     * @param underwriteTaskId     核保任务id
     * @param underwriteDecisionId 基础决定编码id
     * @return ApplyUnderwriteDecisionPo
     */
    ApplyUnderwriteDecisionPo getApplyUnderwriteDecision(String underwriteTaskId, String underwriteDecisionId);

    /**
     * 根据投保单Id查询投保单的核保决定
     *
     * @param applyId 投保单ID
     * @return ApplyUnderwriteDecisionPo
     */
    ApplyUnderwriteDecisionPo getApplyUnderwriteDecision(String applyId);

    /**
     * 基本核保决定编码
     *
     * @param underwriteDecisionCode 核保决定编码
     * @return BaseUnderwriteDecisionPo
     */
    BaseUnderwriteDecisionPo getBaseUnderwriteDecisionPo(String underwriteDecisionCode);

    /**
     * 所有基本核保决定编码
     *
     * @return BaseUnderwriteDecisionPo
     */
    List<BaseUnderwriteDecisionPo> getBaseUnderwriteDecisionPo();

    /**
     * 查询投保单支付事务
     *
     * @param applyId       投保单ID
     * @param paymentStatus 支付状态
     * @param paymentType
     * @param feeType
     * @return ApplyPaymentTransactionBo
     */
    ApplyPaymentTransactionBo getApplyPaymentTransaction(String applyId, String paymentStatus, String paymentType, String feeType);

    /**
     * 查询投保单支付事务项目
     *
     * @param paymentTransactionId 支付事务ID
     * @param paymentStatus        支付状态
     * @return list
     */
    List<ApplyPaymentTransactionItemPo> getApplyPaymentTransactionItemList(String paymentTransactionId, String paymentStatus);

    /**
     * 查询受理险种
     *
     * @param applyId 投保单ID
     * @return ApplyCoverageAcceptPo
     */
    List<ApplyCoverageAcceptPo> getApplyCoverageAccepts(String applyId);

    /**
     * 查询投保单作废
     *
     * @param applyId 投保单ID
     * @return ApplyAbandonedPo
     */
    ApplyAbandonedPo queryApplyAbandoned(String applyId);

    ApplyInsuredPo querySokSanCustomer(String applyId, String name, String sex, Long birthday, String idNo);

    /**
     * 查询健康告知备注
     *
     * @param applyId      投保单ID
     * @param customerType 客户类型
     * @return ApplyHealthQuestionnaireRemarkPo
     */
    ApplyHealthQuestionnaireRemarkPo getApplyHealthQuestionnaireRemark(String applyId, String customerType);

    /**
     * 查询健康告知备注
     *
     * @param applyId 投保单ID
     * @return
     */
    List<ApplyHealthQuestionnaireRemarkPo> listApplyHealthQuestionnaireRemark(String applyId);

    /**
     * 查询投保单信息跟投保人信息（收付费明细报表）
     *
     * @param businessId payment关联投保单id
     */
    public List<ApplyReportBo> queryApplyReport(List<String> businessId);


    List<ApplyBeneficiaryInfoBo> queryApplyBeneficiaryListByApplyId(String applyId);

    List<ApplyBeneficiaryAttachmentBo> queryApplyBeneficiaryAttachment(String beneficiaryId);

    /**
     * 查询计划书
     *
     * @param applyId
     * @return
     */
    ApplyPlanPo queryApplyPlan(String applyId);

    /**
     * 查询计划书追踪信息
     *
     * @param applyPlanId 计划书id
     * @return ApplyPlanTracePos
     */
    List<ApplyPlanTracePo> queryApplyPlanTracePo(String applyPlanId);

    /**
     * 查询计划书追踪信息
     *
     * @param applyPlanId     计划书id
     * @param basePageRequest
     * @return ApplyPlanTracePos
     */
    List<ApplyPlanTraceBo> queryApplyPlanTraceByPage(String applyPlanId, BasePageRequest basePageRequest);

    /**
     * 被保人下的团险健康告知
     *
     * @param applyId
     * @param insuredId
     * @return
     */
    List<ApplyGroupHealthQuestionnaireAnswerPo> queryApplyGroupHealthQuestionnaireAnswer(String applyId, String insuredId);

    /**
     * 被保人下的团险健康告知
     *
     * @param insuredIds 被保人ID集
     * @return
     */
    List<ApplyGroupHealthQuestionnaireAnswerPo> listApplyGroupHealthQuestionnaireAnswer(List<String> insuredIds);

    /**
     * 查询投保单ID对应的险种责任
     *
     * @param applyId 投保单ID
     * @return list
     */
    List<ApplyCoverageDutyBo> getApplyCoverageDutyList(String applyId);

    /**
     * 根据投保单ID查询险种责任
     *
     * @param applyId 投保单ID
     * @return list
     */
    List<ApplyCoverageDutyPo> listApplyCoverageDuty(String applyId);

    List<ApplyApplicantBo> queryApplyApplicantList(String applyType);

    List<ApplyInsuredBo> queryApplyInsuredList(String applyType);

    /**
     * 查询被保险人清单打印数据
     *
     * @param applyId
     * @return
     */
    PrintApplyInsuredBo queryPrintApplyData(String applyId);

    /**
     * 查询被保险人清单打印数据
     *
     * @param applyId
     * @return
     */
    PrintApplyInsuredBo queryPrintApplyDataNew(String applyId);

    /**
     * 查询被保人打印列表
     *
     * @return
     */
    List<ApplyInsuredPrintBo> queryApplyInsuredPrint(ApplyInsuredPrintVo applyInsuredPrintVo);

    /**
     * 查询被保人打印列表
     *
     * @return
     */
    List<ApplyInsuredPrintBo> queryApplyInsuredPrintManage(ApplyInsuredPrintVo applyInsuredPrintVo);

    /**
     * 查询被保人打印信息
     *
     * @param applyInsuredPrintId
     * @return
     */
    ApplyInsuredPrintPo queryApplyInsuredPrintPo(String applyInsuredPrintId);

    /**
     * 查询被保人打印信息
     *
     * @param applyInsuredPrintId
     * @return
     */
    ApplyInsuredPrintDetailPo queryApplyInsuredPrintPoByLanguage(String applyInsuredPrintId,String languageCode);

    /**
     * 根据applyId查询被保人打印信息
     *
     * @param applyId
     * @return
     */
    ApplyInsuredPrintPo queryApplyInsuredPrintByApplyId(String applyId);

    /**
     * 投保单ID查询被保人信息
     *
     * @param applyId
     * @return
     */
    List<ApplyInsuredBo> queryApplyInsured(String applyId);

    /**
     * 投保单ID查询受益人信息
     *
     * @param applyId
     * @return
     */
    List<ApplyBeneficiaryBo> queryApplyBeneficiaryByApplyId(String applyId);

    /**
     * 需要设置的投保人代表数据
     *
     * @return
     */
    List<ApplyGroupReportSyncApplicantBo> querySyncApplicantCustomer();

    /**
     * 需要设置的投保人代表Po数据
     *
     * @return
     */
    List<ApplyApplicantPo> queryApplicantCustomer();

    /**
     * 查询业绩报表统计数据
     *
     * @param policyIdList
     * @return
     */
    List<ActualPerformanceReportBo> queryActualPerformance(List<String> policyIdList);

    /**
     * 投保单ID查询受益人信息
     *
     * @param applyId
     * @param modifyFlag
     * @return
     */
    List<ApplyBeneficiaryInfoBo> queryApplyLoanBeneficiary(String applyId, String modifyFlag);

    /**
     * 执行同步投保单银保渠道明细表
     *
     * @param basePageRequest
     * @param syncDate
     * @return
     */
    List<ServiceChargeBankChannelBo> syncApplyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate);

    /**
     * 执行同步销售报表
     *
     * @param basePageRequest
     * @param syncDate
     * @return
     */
    List<SaleApplyPolicyBo> syncSaleApplyPolicy(BasePageRequest basePageRequest, String syncDate);

    /**
     * 执行同步销售报表
     *
     * @param applyIdList
     * @return
     */
    List<SaleApplyPolicyBo> syncSaleApplyPolicyCoverage(List<String> applyIdList);

    /**
     * @param applyId
     * @return
     */
    List<ApplyUnderwriteProblemPo> queryApplyUnderwriteProblemPo(String applyId);

    List<ApplyPaymentTransactionBo> queryApplyPaymentTransactions(String applyId);

    /**
     * 查询支付数据
     *
     * @param applyIdList
     * @return
     */
    List<ApplyPaymentTransactionBo> queryApplyPaymentTransactionList(List<String> applyIdList);

    /**
     * 查询合同信息上传表
     *
     * @param insuredBatchId 批次ID
     * @return ApplyContactUploadPo
     */
    ApplyContactUploadPo getApplyContactUploadPo(String insuredBatchId);

    /**
     * 查询合同信息完成表
     *
     * @param insuredBatchId 批次ID
     * @return ApplyContactDonePo
     */
    ApplyContactDonePo getApplyContactDonePo(String insuredBatchId);

    /**
     * 获取投保单存在客户列表
     *
     * @param applyId        投保单ID
     * @param allCustomerIds 客户ID
     * @param notApproveFlag
     * @return ApplyRealClientListBos
     */
    List<ApplyRealClientListBo> getApplyRealClient(String applyId, List<String> allCustomerIds, boolean notApproveFlag);

    /**
     * 查询网销重复的投保单
     *
     * @param applyId    投保单ID
     * @param customerId 客户ID
     * @return ApplyPos
     */
    List<ApplyPo> getRepeatOnlineApply(String applyId, String customerId);

    List<ApplyApplicantListBo> applyApplicantList(List<String> applyIds);
}
