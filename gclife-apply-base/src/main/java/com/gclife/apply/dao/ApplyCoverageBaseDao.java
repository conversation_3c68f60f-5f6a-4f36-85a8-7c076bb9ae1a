package com.gclife.apply.dao;

import com.gclife.apply.core.jooq.tables.pojos.ApplyCoverageLevelPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.attachment.model.policy.policy.PolicyCoverageBo;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * Description:
 * @date 19-8-27
 */
public interface ApplyCoverageBaseDao {

    /**
     * 查询险种档次信息
     *
     * @param applyId        投保单ID
     * @param coverageDutyId 责任ID
     * @return
     */
    List<ApplyCoverageLevelPo> listApplyCoverageLevel(String applyId, String coverageDutyId);

    /**
     * 根据险种ID集查询险种档次信息
     *
     * @param coverageIds 险种ID集
     * @return
     */
    List<ApplyCoverageLevelPo> listApplyCoverageLevel(List<String> coverageIds);

    /**
     *
     * @param applyId
     * @return
     */
    List<PolicyCoverageBo> queryApplyCoverageBo(String applyId);

    List<PolicyCoverageBo> queryApplyCoverageByInsured(String applyId, String customerAgentId, boolean applicantFlag);

    List<PolicyCoverageBo> queryOtherApplyCoverageByInsured(String applyId, List<String> customerAgentIds, boolean applicantFlag);
    List<ApplyPo> queryOtherApplyByApplicant(String applyId, List<String> customerAgentIds);
    List<ApplyPo> queryOtherApplyByInsured(String applyId, List<String> customerAgentIds);
}
