package com.gclife.apply.dao;

import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.model.bo.ApplyHcBo;
import com.gclife.apply.model.bo.ApplyListBo;
import com.gclife.apply.model.bo.ApplyUnderwriteNoticeListBo;
import com.gclife.apply.model.vo.ApplyListVo;
import com.gclife.common.dao.base.BaseDao;
import com.gclife.common.model.BasePageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
public interface ApplyBusinessDao extends BaseDao {
    /**
     * 查询当前用户所管理对的销售机构的投保单列表
     *
     * @param branchIds   用户管理的销售机构集合
     * @param applyListVo 列表请求参数
     * @param applyType   投保单类型
     * @return 列表
     */
    List<ApplyListBo> loadApplyListByBranchIds(List<String> branchIds, ApplyListVo applyListVo, String applyType);

    /**
     * 查询当前用户所属的投保单列表
     *
     * @param agentId     代理人id
     * @param applyListVo 列表请求参数
     * @param applyType   投保单类型
     * @param applyStatus 投保的状态
     * @return 列表
     */
    List<ApplyListBo> loadApplyListByAgentId(String agentId, ApplyListVo applyListVo, String applyType, String applyStatus);

    /**
     * 查询用户能处理的投保单列表(待办任务)
     *
     * @param applyIds    工作流筛选的投保单集合
     * @param applyListVo 列表请求参数
     * @param applyType   投保单类型
     * @return 列表
     */
    List<ApplyListBo> loadApplyListByApplyIds(List<String> applyIds, ApplyListVo applyListVo, String applyType);

    /**
     * 查询当前用户所属的团险投保单列表
     *
     * @param applyListVo 列表请求参数
     * @param applyType   投保单类型
     * @return 列表
     */
    List<ApplyListBo> loadGroupApplyList(ApplyListVo applyListVo, String applyType);

    /**
     * 分页查询核保通知书打印列表
     *
     * @param applyListVo 列表请求参数
     * @return ApplyUnderwriteNoticeListBo
     */
    List<ApplyUnderwriteNoticeListBo> getApplyUnderwriteNoticeList(ApplyListVo applyListVo);

    /**
     * 查询暂予承保待生效的单
     * @param basePageRequest 分页
     * @return ApplyPos
     */
    List<ApplyHcBo> queryApprovedHcEffective(BasePageRequest basePageRequest);
}
