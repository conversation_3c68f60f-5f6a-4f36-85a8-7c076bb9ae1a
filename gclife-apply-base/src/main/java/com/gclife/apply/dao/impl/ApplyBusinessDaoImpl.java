package com.gclife.apply.dao.impl;

import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.dao.ApplyBusinessDao;
import com.gclife.apply.model.bo.ApplyHcBo;
import com.gclife.apply.model.bo.ApplyListBo;
import com.gclife.apply.model.bo.ApplyUnderwriteNoticeListBo;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.vo.ApplyListVo;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.jooq.Condition;
import org.jooq.SelectJoinStep;
import org.jooq.Table;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.apply.core.jooq.Tables.*;
import static com.gclife.apply.core.jooq.tables.Apply.APPLY;

/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
@Component
public class ApplyBusinessDaoImpl extends BaseDaoImpl implements ApplyBusinessDao {


    /**
     * 查询用户所属的投保单列表
     *
     * @param branchIds   用户管理的销售机构集合
     * @param applyListVo 列表请求参数
     * @param applyType   投保单类型
     * @return
     */
    @Override
    public List<ApplyListBo> loadApplyListByBranchIds(List<String> branchIds, ApplyListVo applyListVo, String applyType) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(APPLY.fields())
                .select(APPLY_APPLICANT.NAME.as("applicantName"), APPLY_APPLICANT.MOBILE.as("applicantMobile"), APPLY_APPLICANT.ID_NO.as("applicantIdNo"), APPLY_APPLICANT.COMPANY_CONTRACT_NAME, APPLY_APPLICANT.COMPANY_CONTRACT_MOBILE,
                        APPLY_APPLICANT.DELEGATE_NAME, APPLY_APPLICANT.DELEGATE_MOBILE)
//                .select(APPLY_COVERAGE.fields())
                .select(APPLY_AGENT.fields())
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .from(APPLY)
                .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
//                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID));

        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY.SALES_BRANCH_ID.in(branchIds)
                        .and(APPLY.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name()))
//                .and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()))
        );

        if (AssertUtils.isNotEmpty(applyType)) {
            conditions.add(APPLY.APPLY_TYPE.eq(applyType));
        }
        if (AssertUtils.isNotEmpty(applyListVo.getApplyStatus())) {
            conditions.add(APPLY.APPLY_STATUS.eq(applyListVo.getApplyStatus()));
        }
        if (AssertUtils.isNotEmpty(applyListVo.getKeyword())) {
            conditions.add(
                    APPLY.APPLY_NO.like("%" + applyListVo.getKeyword() + "%")
                            .or(APPLY_APPLICANT.NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.DELEGATE_NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.DELEGATE_MOBILE.like("%" + applyListVo.getKeyword() + "%"))
            );
        }
        selectJoinStep.where(conditions);
        selectJoinStep.offset(applyListVo.getOffset()).limit(applyListVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyListBo.class);
    }

    @Override
    public List<ApplyListBo> loadApplyListByAgentId(String agentId, ApplyListVo applyListVo, String applyType, String applyStatus) {
        //基表
        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(APPLY.fields())
                .select(APPLY_APPLICANT.NAME.as("applicantName"), APPLY_APPLICANT.MOBILE.as("applicantMobile"), APPLY_APPLICANT.ID_NO.as("applicantIdNo"), APPLY_APPLICANT.COMPANY_CONTRACT_NAME, APPLY_APPLICANT.COMPANY_CONTRACT_MOBILE,
                        APPLY_APPLICANT.DELEGATE_NAME, APPLY_APPLICANT.DELEGATE_MOBILE)
                .select(APPLY_AGENT.AGENT_CODE, APPLY_AGENT.AGENT_ID)
                .select(APPLY_CHANGE.APPLY_CHANGE_ID)
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_NAME)
                .select(APPLY_PREMIUM.TOTAL_ACTUAL_PREMIUM.as("totalPremium"))
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .from(APPLY)
                .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_CHANGE).on(APPLY_CHANGE.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_PREMIUM).on(APPLY.APPLY_ID.eq(APPLY_PREMIUM.APPLY_ID))
                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())));

        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY_AGENT.AGENT_ID.eq(agentId)
                .and(APPLY.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name()))
        );
        if (AssertUtils.isNotEmpty(applyStatus)) {
            if (ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name().equals(applyStatus)) {
                conditions.add(APPLY.APPLY_STATUS.ne(applyStatus));
            } else {
                conditions.add(APPLY.APPLY_STATUS.eq(applyStatus));
            }
        }
        // 支付状态筛选
        if (AssertUtils.isNotEmpty(applyListVo.getPremiumStatusList())) {
            conditions.add(APPLY_PREMIUM.PREMIUM_STATUS.in(applyListVo.getPremiumStatusList()));
        }

        if (AssertUtils.isNotEmpty(applyType)) {
            conditions.add(APPLY.APPLY_TYPE.eq(applyType));
        }
        if (AssertUtils.isNotEmpty(applyListVo.getApplyStatusList())) {
            conditions.add(APPLY.APPLY_STATUS.in(applyListVo.getApplyStatusList()));
        }
        if (AssertUtils.isNotEmpty(applyListVo.getKeyword())) {
            conditions.add(
                    APPLY.APPLY_NO.like("%" + applyListVo.getKeyword() + "%")
                            .or(APPLY_APPLICANT.COMPANY_NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.MOBILE.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.DELEGATE_NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.DELEGATE_MOBILE.like("%" + applyListVo.getKeyword() + "%"))
            );
        }
        //起期  大于等于
        String applyDateStartFormat = applyListVo.getApplyDateStartFormat();
        if (AssertUtils.isNotEmpty(applyDateStartFormat) && AssertUtils.isDateFormat(applyDateStartFormat)) {
            applyListVo.setApplyDateStartTime(DateUtils.stringToTime(applyDateStartFormat));
        }
        if (AssertUtils.isNotNull(applyListVo.getApplyDateStartTime())) {
            conditions.add(APPLY.APPLY_DATE.ge(DateUtils.timeToTimeLow(applyListVo.getApplyDateStartTime())));
        }
        //止期  小于等于
        String applyDateEndFormat = applyListVo.getApplyDateEndFormat();
        if (AssertUtils.isNotEmpty(applyDateEndFormat) && AssertUtils.isDateFormat(applyDateEndFormat)) {
            applyListVo.setApplyDateEndTime(DateUtils.stringToTime(applyDateEndFormat));
        }
        if (AssertUtils.isNotNull(applyListVo.getApplyDateEndTime())) {
            conditions.add(APPLY.APPLY_DATE.le(DateUtils.timeToTimeTop(applyListVo.getApplyDateEndTime())));
        }

        selectJoinStep.where(conditions);
        Table table = selectJoinStep.asTable();

        //查询产品名称
        SelectJoinStep applyCoverage = this.getDslContext()
                .select(APPLY_COVERAGE.PRODUCT_NAME.as("productName"))
                .from(APPLY_AGENT)
                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY_AGENT.APPLY_ID));

        List<Condition> coverageConditions = new ArrayList<Condition>();
        coverageConditions.add(APPLY_AGENT.AGENT_ID.eq(agentId));
        coverageConditions.add(APPLY_AGENT.APPLY_ID.eq(table.field(0)));
        coverageConditions.add(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        applyCoverage.where(coverageConditions).limit(1);

        //查询被保人人数
        SelectJoinStep applyInsured = this.getDslContext()
                .select(APPLY_INSURED.INSURED_ID.count().as("insuredSum"))
                .from(APPLY_INSURED);

        List<Condition> applyInsuredConditions = new ArrayList<Condition>();
        applyInsuredConditions.add(APPLY_INSURED.APPLY_ID.eq(table.field(0)));
        applyInsured.where(applyInsuredConditions);

        SelectJoinStep select = this.getDslContext()
                .select(table.fields())
                .select(DSL.field("(" + applyCoverage.toString() + ")").as("productName"))
                .select(DSL.field("(" + applyInsured.toString() + ")").as("insuredSum"))
                .from(table);

//        select.orderBy(table.field(APPLY.APPLY_STATUS.getName()));
        select.orderBy(table.field(APPLY.APPLY_DATE.getName()).desc());
        select.offset(applyListVo.getOffset()).limit(applyListVo.getPageSize());
        System.out.println(select.toString());
        return select.fetchInto(ApplyListBo.class);
    }

    @Override
    public List<ApplyListBo> loadApplyListByApplyIds(List<String> applyIds, ApplyListVo applyListVo, String applyType) {

        SelectJoinStep applyCoverage = this.getDslContext()
                .selectDistinct(APPLY_COVERAGE.PRODUCT_NAME.as("productName"))
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.APPLY_ID.as("applyId"))
                .from(APPLY_COVERAGE);
        List<Condition> coverageConditions = new ArrayList<Condition>();
        coverageConditions.add(APPLY_COVERAGE.APPLY_ID.in(applyIds));
        coverageConditions.add(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        applyCoverage.where(coverageConditions);
        Table applyCoverageTable = applyCoverage.asTable();

        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(APPLY.fields())
                .select(APPLY_APPLICANT.NAME.as("applicantName"), APPLY_APPLICANT.MOBILE.as("applicantMobile"), APPLY_APPLICANT.ID_NO.as("applicantIdNo"), APPLY_APPLICANT.COMPANY_CONTRACT_NAME, APPLY_APPLICANT.COMPANY_CONTRACT_MOBILE,
                        APPLY_APPLICANT.DELEGATE_NAME, APPLY_APPLICANT.DELEGATE_MOBILE)
                .select(applyCoverageTable.fields())
                .select(APPLY_AGENT.fields())
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .from(APPLY)
                .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(applyCoverageTable).on(applyCoverageTable.field("applyId").eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID));
        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY.APPLY_ID.in(applyIds)
                .and(APPLY.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name()))
                .and(APPLY.APPLY_TYPE.eq(applyType))
        );
        if (AssertUtils.isNotEmpty(applyListVo.getApplyStatus())) {
            conditions.add(APPLY.APPLY_STATUS.eq(applyListVo.getApplyStatus()));
        }
        if (AssertUtils.isNotEmpty(applyListVo.getKeyword())) {
            conditions.add(
                    APPLY.APPLY_NO.like("%" + applyListVo.getKeyword() + "%")
                            .or(APPLY_APPLICANT.NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.DELEGATE_NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_APPLICANT.DELEGATE_MOBILE.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_AGENT.AGENT_CODE.like("%" + applyListVo.getKeyword() + "%"))
            );
        }

        //打印起期  大于等于
        String applyDateStartFormat = applyListVo.getApplyDateStartFormat();
        if (AssertUtils.isNotEmpty(applyDateStartFormat) && AssertUtils.isDateFormat(applyDateStartFormat)) {
            applyListVo.setApplyDateStartTime(DateUtils.stringToTime(applyDateStartFormat));
        }
        if (AssertUtils.isNotNull(applyListVo.getApplyDateStartTime())) {
            conditions.add(APPLY.APPLY_DATE.ge(DateUtils.timeToTimeLow(applyListVo.getApplyDateStartTime())));
        }
        //打印止期  小于等于
        String applyDateEndFormat = applyListVo.getApplyDateEndFormat();
        if (AssertUtils.isNotEmpty(applyDateEndFormat) && AssertUtils.isDateFormat(applyDateEndFormat)) {
            applyListVo.setApplyDateEndTime(DateUtils.stringToTime(applyDateEndFormat));
        }
        if (AssertUtils.isNotNull(applyListVo.getApplyDateEndTime())) {
            conditions.add(APPLY.APPLY_DATE.le(DateUtils.timeToTimeTop(applyListVo.getApplyDateEndTime())));
        }

        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(APPLY.APPLY_ID.sortAsc(applyIds));
        selectJoinStep.offset(applyListVo.getOffset()).limit(applyListVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyListBo.class);
    }

    @Override
    public List<ApplyListBo> loadGroupApplyList(ApplyListVo applyListVo, String applyType) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(APPLY.fields())
                .select(APPLY_APPLICANT.NAME.as("applicantName"), APPLY_APPLICANT.DELEGATE_NAME, APPLY_APPLICANT.DELEGATE_MOBILE)
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_NAME.as("productName"))
                .select(APPLY_CHANGE.APPLY_CHANGE_ID)
                .from(APPLY)
                .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_CHANGE).on(APPLY_CHANGE.APPLY_ID.eq(APPLY.APPLY_ID), APPLY_CHANGE.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name()))
                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID)).and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()).and(APPLY_COVERAGE.INSURED_ID.isNull()));
        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY.SALES_BRANCH_ID.in(applyListVo.getBranchIds())
                .and(APPLY.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name()))
        );
        if (AssertUtils.isNotEmpty(applyType)) {
            conditions.add(APPLY.APPLY_TYPE.eq(applyType));
        }
        if (AssertUtils.isNotEmpty(applyListVo.getApplyStatus())) {
            conditions.add(APPLY.APPLY_STATUS.eq(applyListVo.getApplyStatus()));
        }
        if (AssertUtils.isNotEmpty(applyListVo.getKeyword())) {
            conditions.add(
                    APPLY.APPLY_NO.like("%" + applyListVo.getKeyword() + "%")
                            .or(APPLY_APPLICANT.NAME.like("%" + applyListVo.getKeyword() + "%"))
            );
        }
        selectJoinStep.orderBy(APPLY.APPLY_DATE.desc());
        selectJoinStep.offset(applyListVo.getOffset()).limit(applyListVo.getPageSize());
        selectJoinStep.where(conditions);
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyListBo.class);
    }

    /**
     * 分页查询核保通知书打印列表
     *
     * @param applyListVo 列表请求参数
     * @return ApplyUnderwriteNoticeListBo
     */
    @Override
    public List<ApplyUnderwriteNoticeListBo> getApplyUnderwriteNoticeList(ApplyListVo applyListVo) {
        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(APPLY.fields())
                .select(APPLY_APPLICANT.NAME.as("applicantName"))
                .select(APPLY_INSURED.NAME.as("insuredName"))
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.PRODUCT_NAME)
                .select(APPLY_AGENT.AGENT_ID)
                .select(APPLY_UNDERWRITE_NOTICE.APPLY_UNDERWRITE_NOTICE_ID)
                .select(APPLY_UNDERWRITE_NOTICE.ENGLISH_FLAG)
                .select(APPLY_UNDERWRITE_NOTICE.CAMBODIAN_FLAG)
                .select(APPLY_PREMIUM.RECEIVABLE_PREMIUM)
                .select(APPLY.APPLY_ID.countOver().as("totalLine"))
                .from(APPLY)
                .innerJoin(APPLY_UNDERWRITE_NOTICE).on(APPLY_UNDERWRITE_NOTICE.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_INSURED).on(APPLY_INSURED.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_PREMIUM).on(APPLY_PREMIUM.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID));

        List<Condition> conditions = new ArrayList<>();
        conditions.add(APPLY.SALES_BRANCH_ID.in(applyListVo.getBranchIds()).and(APPLY.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name())));
        conditions.add(APPLY_UNDERWRITE_NOTICE.PRINT_STATUS.ne(TerminologyConfigEnum.WHETHER.YES.name()));

        if (AssertUtils.isNotEmpty(applyListVo.getKeyword())) {
            conditions.add(
                    APPLY.APPLY_NO.like("%" + applyListVo.getKeyword() + "%")
                            .or(APPLY_APPLICANT.NAME.like("%" + applyListVo.getKeyword() + "%"))
                            .or(APPLY_INSURED.NAME.like("%" + applyListVo.getKeyword() + "%"))
            );
        }
        selectJoinStep.where(conditions);
        selectJoinStep.offset(applyListVo.getOffset()).limit(applyListVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyUnderwriteNoticeListBo.class);
    }

    /**
     * 查询暂予承保待生效的单
     *
     * @param basePageRequest 分页
     * @return ApplyPos
     */
    @Override
    public List<ApplyHcBo> queryApprovedHcEffective(BasePageRequest basePageRequest) {
        return this.getDslContext()
                .select(APPLY.fields())
                .select(APPLY_PREMIUM.PREMIUM_STATUS)
                .from(APPLY)
                .innerJoin(APPLY_PREMIUM).on(APPLY.APPLY_ID.eq(APPLY_PREMIUM.APPLY_ID),APPLY_PREMIUM.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name()))
                .where(APPLY.APPLY_STATUS.eq(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name()))
                .and(APPLY.VALID_FLAG.eq(ApplyTermEnum.VALID_FLAG.effective.name()))
                .and(APPLY.PRE_UNDERWRITING_FLAG.eq(TerminologyConfigEnum.WHETHER.YES.name()))
                .and(APPLY.PRE_UNDERWRITING_START_DATE.isNotNull())
                .and(APPLY.PRE_UNDERWRITING_START_DATE.le(DateUtils.timeToTimeTop(DateUtils.getCurrentTime())))
                .orderBy(APPLY.PRE_UNDERWRITING_START_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize())
                .fetchInto(ApplyHcBo.class);
    }
}
