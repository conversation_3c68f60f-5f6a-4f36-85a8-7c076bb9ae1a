package com.gclife.apply.api;

import com.gclife.apply.model.respone.ApplyListBo;
import com.gclife.apply.model.respone.ApplyReportResponse;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.model.ResultObject;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * create 19-7-19
 * description:
 */
@FeignClient(name = "gclife-apply-service")
public interface ApplyReportApi {
    /**
     * 查询投保单信息跟投保人信息（收付费明细报表）
     *
     * @param businessId payment关联投保单id
     * @return ApplyReportResponse
     */
    @PostMapping(value = "v1/base/apply/applys")
    ResultObject<List<ApplyReportResponse>> queryListPaymentBo(@RequestBody List<String> businessId);


    /**
     * 实收业绩明细报表
     */
    @ApiOperation(value = "实收业绩明细表统计", notes = "实收业绩明细表统计")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "v1/report/actual/performance")
    ResultObject<List<ActualPerformanceReportBo>> queryActualPerformance(@RequestBody List<String> policyIdList);



    @ApiOperation(value = "执行同步投保单银保渠道明细表", notes = "执行同步投保单银保渠道明细表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "v1/base/apply/sync/apply/service/charge/bank/channel")
    ResultObject<List<ServiceChargeBankChannelBo>> syncApplyServiceChargeBankChannel(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) ;

    @ApiOperation(value = "执行同步销售报表数据", notes = "执行同步销售报表数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "v1/base/apply/sync/sale/apply/policy")
    ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) ;

    /**
     * 系统预警查询已承保的投保单列表
     */
    @GetMapping(value = "v1/system/warning/query/applies")
    ResultObject<List<ApplyListBo>> getSystemWarningApplyQueryList();

    /**
     * 系统预警查询支付的投保单列表
     */
    @GetMapping(value = "v1/system/warning/query/pay/applies")
    ResultObject<List<ApplyListBo>> getSystemWarningApplyPaymentList();

    /**
     * 系统预警查询支付的投保单
     */
    @GetMapping(value = "v1/system/warning/query/pay/apply")
    ResultObject<ApplyListBo> getSystemWarningApplyPayment(@RequestParam(name = "applyId") String applyId);

}
