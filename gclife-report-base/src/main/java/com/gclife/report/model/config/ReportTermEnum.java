package com.gclife.report.model.config;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 术语类型枚举
 */
public class ReportTermEnum {

    private ReportTermEnum() {
        throw new AssertionError();
    }

    public interface ConstantType {
    }

    public enum PRODUCT_PRIMARY_FLAG implements ConstantType {

        MAIN("主险"),
        ADDITIONAL("附加险");

        private String code;
        private String desc;

        private PRODUCT_PRIMARY_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum CUSTOMER_TYPE implements ConstantType {

        APPLICANT("投保人"),
        INSURED("被保人"),
        GROUP_DELEGATE("团队"),
        PERSONAL("个人"),
        ;

        private String code;
        private String desc;

        private CUSTOMER_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum RENEWAL_DATE_PROPERTY implements ConstantType {
        /**
         * 续期日期属性
         */
        GRACE_PERIOD("宽限期"),
        INVALID_PERIOD_YEAR("永久失效期(年)"),
        ;

        private String desc;

        RENEWAL_DATE_PROPERTY(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum AGENT_TYPE_CODE implements ConstantType {
        /**
         * 业务员类型
         */
        LIFE_CONSULTANT("寿险顾问"),
        EMPLOYEE("销售经理"),
        ;

        private String desc;

        AGENT_TYPE_CODE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 支付方式
     */
    public enum PAYMENT_METHODS implements ConstantType {
        PAYMENT_METHODS("支付方式"),
        CASH("现金支付"),
        BANK_TRANSFER("银行转账"),
        WING_H5("WING在线支付"),
        WING_OFFLINE("WING线下支付");
        private String code;
        private String desc;

        private PAYMENT_METHODS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 收付费
     */
    public enum PAYMENT_TYPE1 implements ConstantType {
        PAYMENT_TYPE1("收付费"),
        PAYMENT("收费"),
        RECEIPT("付费");
        private String code;
        private String desc;

        private PAYMENT_TYPE1(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 收付费
     */
    public enum PRODUCT_TYPE implements ConstantType {
        PRODUCT_TYPE("长期险/短期险"),
        LONG("长期险"),
        SHORT("短期险");
        private String code;
        private String desc;

        private PRODUCT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * excel 导入导出模版
     */
    public enum IMPORT_EXPORT_REPORT implements ConstantType {
        GROUP_INSURED_LIST_TEMPLATE("团险被保人清单"),
        REVENUE_REPORT_TEMPLATE("实收业绩明细表"),
        REPORT_PAYMENT_TEMPLATE("财务报表模板"),
        REPORT_CUSTOMER_TEMPLATE("业务报表-投保人信息"),
        REPORT_POLICY_TEMPLATE("业务报表-个险承保清单"),
        LONG_TERM_INSURANCE_TEMPLATE("长期险报表模板-承保清单"),
        SHORT_TERM_INSURANCE_TEMPLATE("短期险报表模板-承保清单"),
        REPORT_RENEWAL_INSURANCE_TEMPLATE("续保清单模板"),
        REPORT_GROUP_RENEWAL_TEMPLATE("团险续保清单模板"),
        REPORT_OPERATION_CRITICAL_TEMPLATE("营运清单模板"),
        REPORT_RENEWAL_TEMPLATE("续期清单模板"),
        REPORT_GROUP_POLICY_TEMPLATE("业务报表-团险承保清单"),
        REPORT_RESERVE_WITHDRAWAL_TEMPLATE("准备金数据提取报表"),
        SUSPENSE_REPORT_TEMPLATE("预缴保费报表"),
        SERVICE_CHARGE_BANK_CHANNEL_TEMPLATE("银保渠道手续费费用明细表"),
        SERVICE_CHARGE_BROKER_CHANNEL_TEMPLATE("中介保渠道手续费费用明细表"),
        EMONEY_REPORTING_TEMPLATE("eMoney服务报表"),
        MD_REPORTING_TEMPLATE("巨量代销服务报表"),
        SDF_REPORT_TEMPLATE("学校发展基金报表"),
        STAFF_IN_SURANCE_REPORT("员工团队报表"),
        ENDORSE_REPORT_TEMPLATE("保全报表"),
        CLAIM_REPORT_TEMPLATE("理赔报表"),
        SALE_APPLY_POLICY_REPORT_TEMPLATE("销售报表"),
        REPORT_RECEIPT_REPORT_TEMPLATE("销售报表"),
        REFERRAL_ACTIVITY_REPORT_TEMPLATE("推荐活动报表"),
        PENDING_RENEWAL_INSURANCE_TEMPLATE("个险待续保清单报表"),
        PENDING_GROUP_RENEWAL_INSURANCE_TEMPLATE("团险待续保清单报表"),

        CASH_TRANSACTION_TEMPLATE("现金交易报表"),
        /**
         * 操作日志报表
         */
        REPORT_OPERATION_LOG_REPORT("操作日志报表"),
        EXPORT_SWAGGER("swagger导出模板"),
        ;
        private String code;
        private String desc;

        private IMPORT_EXPORT_REPORT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum RENEWAL_STATUS implements ConstantType {
        /**
         * 续期状态
         */
        INITIAL("初始化"),
        SEPERATE("分单"),
        PAYMENT("待缴费"),
        INVALID("失效"),
        ACTUAL_PAY("实收"),
        CANCEL("取消"),
        INVALID_THOROUGH("永久失效"),
        REFUND("退费"),
        ARCHIVE("归档"),

        /**
         * 续保特有状态
         */
        APPLYING("待申请"),
        AUDITING("待审核"),
        RENEWALED("已续保"),
        EFFECTIVE("已生效"),
        ;

        private String code;
        private String desc;

        private RENEWAL_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum REPORT_TYPE implements ConstantType {
        /**
         * 报表类型
         */
        POLICY("业务报表-承保清单"),
        ACTUAL_PERFORMANCE("实收业绩明细报表"),
        REGULATORY_POLICY("监管报表-承保清单"),
        PAYMENT("财务报表"),
        CUSTOMER("投保人资料报表"),
        INSURED("被保人资料"),
        RENEWAL("续期报表"),
        GROUP_INSTALLMENT("团险续期报表"),
        ENDORSE("保全报表"),
        POLICY_GROUP("业务报表-团险承保清单"),
        CLAIM("理赔报表"),
        AGENT("业务员报表"),
        RETURN_VISIT("客户回访报表"),
        MONTHLY_STATISTICS("月度统计报表"),
        SERVICE_CHARGE_BANK_CHANNEL("银保渠道手续费数据同步"),
        SERVICE_CHARGE_BANK_CHANNEL_PAYMENT("银保渠道手续费数据同步"),
        SALE_APPLY_POLICY(""),
        ;
        private String code;
        private String desc;

        private REPORT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 支付状态
     */
    public enum PAYMENT_STATUS implements ConstantType {

        PAYMENT_INITIAL("初始化"),
        PAYMENT_AUDIT("待审核"),
        PAYMENT_AUDIT_PASS("支付方式审核通过"),
        PAYMENT_AUDIT_NOPASS("支付方式审核不通过"),
        PAYMENT_WAITTING("等待支付"),
        PAYMENT_SUCCESS("支付成功"),
        PAYMENT_FINISHED_ADJUST("支付完成调整"),
        PAYMENT_FINISHED("支付完成"),
        PAYMENT_CONFIRM_DATE_AUDIT("收费审核"),
        PAYMENT_TIMEOUT("支付超时"),
        PAYMENT_INVALID("支付作废"),
        //PAYMENT_FINISHED("支付完成"),
        PAYMENT_FAILED("支付失败");
        private String code;
        private String desc;

        PAYMENT_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum PAYMENT_BUSINESS_TYPE implements ConstantType {
        /**
         * 支付业务类型
         */
        APPLY("新契约-个险"),
        APPLY_GROUP("新契约-团险"),

        POLICY_ENDORSE("保全"),
        GROUP_ADD_INSURED("团险-增员"),
        GROUP_SUBTRACT_INSURED("团险-减员"),
        GROUP_ADD_ADDITIONAL("团险-增加附加险"),
        GROUP_ADD_SUBTRACT_INSURED("团险-增减员"),

        POLICY_CLAIM("理赔"),
        POLICY_RENEWAL_INSURANCE("续保"),
        POLICY_RENEWAL_PAYMENT("续期"),
        GROUP_RENEWAL("续保"),

        ;

        private String code;
        private String desc;

        private PAYMENT_BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum POLICY_STATUS_FLAG implements ConstantType {

        POLICY_STATUS("保单状态"),
        NEW_POLICY_STATUS("保单状态"),

        //以下状态为终止
        POLICY_STATUS_INDEMNITY_TERMINATION("赔付终止"),
        POLICY_STATUS_HESITATION_REVOKE("犹豫期撤单"),
        POLICY_STATUS_INVALID_THOROUGH("永久失效"),
        POLICY_STATUS_EFFECT_TERMINATION("效力终止"),
        POLICY_STATUS_SURRENDER("退保"),

        //以下状态未终止
        POLICY_STATUS_WAIT_RENEWAL("待续保"),
        POLICY_STATUS_IEXPIRE("保单满期"),
        POLICY_STATUS_INVALID("失效"),
        POLICY_STATUS_EFFECTIVE("有效"),
        POLICY_STATUS_REINSTATEMENT("保单复效"),
        POLICY_STATUS_EXTEND("展期"),
        POLICY_STATUS_WAIVER_PREMIUM("豁免保费"),
        ;
        private String code;
        private String desc;

        private POLICY_STATUS_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    public enum RENEWAL_TYPE implements ConstantType {
        /**
         * 续期 续保类型
         */
        RENEWAL("续期业务"),
        RENEWAL_INSURANCE("续保业务"),
        ;

        private String code;
        private String desc;

        private RENEWAL_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum RENEWAL_CHANGE_FLAG implements ConstantType {
        //续保前后标识
        BEFORE("续保前"),
        AFTER("续保后"),
        ;

        private String code;
        private String desc;

        private RENEWAL_CHANGE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费周期
     */
    public enum PRODUCT_PREMIUM_FREQUENCY implements ConstantType {

        MONTH("月缴"),
        SEASON("季缴"),
        SEMIANNUAL("半年缴"),
        YEAR("年缴"),
        SINGLE("趸缴"),
        MAIN("同主险");
        private String desc;

        PRODUCT_PREMIUM_FREQUENCY(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 报表打印类型
     */
    public enum REPORT_PRINT_TYPE implements ConstantType {

        REPORT_OPERATE("运营月度统计报表"),
        REPORT_MARKET("市场月度统计报表"),
        ;
        private String desc;

        REPORT_PRINT_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 国家编码
     */
    public enum COUNTRY_CODE implements ConstantType {

        CAMBODIA("柬埔寨"),
        ;
        private String desc;

        COUNTRY_CODE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 理赔状态
     */
    public enum CLAIM_STATUS implements ConstantType {
        CLAIM_STATUS("理赔状态"),
        CLAIM_STATUS_INITIAL("初始化"),
        CLAIM_STATUS_REPORTED("报案中"),
        CLAIM_STATUS_WAIT_CASE_ACCEPT("待案件受理"),
        CLAIM_STATUS_CASE_ACCEPTANCE("案件受理中"),
        CLAIM_STATUS_PENDING_CASE("待立案"),
        CLAIM_STATUS_REGISTER_CASE("立案中"),
        CLAIM_STATUS_WAIT_CALCULATION("待理算"),
        CLAIM_STATUS_CALCULATION("理算中"),
        CLAIM_STATUS_UNSIGNED("未签批"),
        CLAIM_STATUS_SIGNING("签批中"),
        CLAIM_STATUS_SIGN_COMPLETED("签批完成"),
        CLAIM_STATUS_CLOSING_CASE("已结案"),
        CLAIM_STATUS_NOT_REGISTER("不予立案"),
        CLAIM_STATUS_CUSTOMER_CANCEL("客户撤案"),
        ;
        private String code;
        private String desc;

        private CLAIM_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 保全项目
     */
    public enum ENDORSE_PROJECT implements ConstantType {
        GROUP_ENDORSE_PROJECT("团险保全类型"),
        ENDORSE_PROJECT("个险保全类型"),
        CUSTOMER_CONTACT_CHANGE("客户联系方式变更"),
        HESITATION_REVOKE("撤单"),
        SURRENDER("退保"),
        REINSTATEMENT("复效"),
        MODE_OF_PAYMENT_MODIFY("缴费周期变更"),
        ADD_ADDITIONAL("增加附加险"),
        ADD_INSURED("增加被保人"),
        SUBTRACT_INSURED("减少被保人"),
        ADD_SUBTRACT_INSURED("增加或减少被保人"),
        GROUP_ADD_ADDITIONAL("增加附加险"),
        ;

        private String desc;

        ENDORSE_PROJECT(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 费用类型
     */
    public enum FEE_TYPE implements ConstantType {

        PAY("缴费"),
        GET("给付"),

        ;

        private String desc;

        FEE_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 保全状态
     */
    public enum ENDORSE_STATUS implements ConstantType {

        ENDORSE_STATUS_ACCEPT("受理"),
        ENDORSE_STATUS_ACCEPT_COMPLETE("受理完成"),
        ENDORSE_STATUS_ACCEPT_PROBLEM("受理问题件"),
        ENDORSE_STATUS_INPUT("录入"),
        ENDORSE_STATUS_INPUT_COMPLETE("录入完成"),
        ENDORSE_STATUS_INPUT_PROBLEM("录入问题件"),
        ENDORSE_STATUS_AUDIT("审核"),
        ENDORSE_STATUS_AUDIT_COMPLETE("审核完成"),
        ENDORSE_STATUS_AUDIT_PROBLEM("审核问题件"),
        ENDORSE_STATUS_UNDERWRITE_AUTO("自动核保"),
        ENDORSE_STATUS_UNDERWRITE_ARTIFICIAL("人工核保"),
        ENDORSE_STATUS_UNDERWRITE_NOTE("照会"),
        ENDORSE_STATUS_UNDERWRITE_WAIT_NOTE_WRITE_OFF("照会待回销"),
        ENDORSE_STATUS_UNDERWRITE_NOTE_WRITE_OFF_COMPLETE("回销完成"),
        ENDORSE_STATUS_UNDERWRITE_PASS("核保通过"),
        ENDORSE_STATUS_PAYMENT_COMPLETE("缴费完成"),
        ENDORSE_STATUS_PAYMENT_SUCCESS("缴费成功"),
        ENDORSE_STATUS_PAYMENT_FAILED("缴费失败"),
        ENDORSE_STATUS_GET_COMPLETE("给付完成"),
        ENDORSE_STATUS_GET_SUCCESS("给付成功"),
        ENDORSE_STATUS_GET_FAILED("给付失败"),
        ENDORSE_STATUS_PENDING_EFFETIVE("待生效"),
        ENDORSE_STATUS_EFFETIVE("生效"),
        ENDORSE_STATUS_ABANDONED("废弃"),
        ENDORSE_STATUS_REFUND("拒绝"),
        ENDORSE_STATUS_REVOKE("撤销"),
        ENDORSE_STATUS_INVALID("失效"),
        ;

        private String desc;

        ENDORSE_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 佣金业务类型
     */
    public enum COMMISSION_BUSINESS_TYPE implements ConstantType {

        BUSINESS_TYPE_NEW_CONTRACT("新契约"),
        BUSINESS_TYPE_RENEWAL("续期"),
        BUSINESS_TYPE_RENEWAL_INSURANCE("续保"),
        BUSINESS_TYPE_ENDORSE("保全"),
        BUSINESS_TYPE_GROUP_RENEWAL("团险续保"),
        BUSINESS_TYPE_ENDORSE_MODE_OF_PAYMENT_MODIFY("缴费周期变更"),
        BUSINESS_TYPE_ENDORSE_ADD_ADDITIONAL("增加附加险"),
        ;

        private String code;
        private String desc;

        COMMISSION_BUSINESS_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum POLICY_TYPE implements ConstantType {
        /**
         * 投保单类型
         */
        POLICY_TYPE("保单类型"),
        LIFE_INSURANCE_GROUP("新契约团险"),
        LIFE_INSURANCE_PERSONAL("新契约个险"),
        ;

        private String code;
        private String desc;

        private POLICY_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum REFERRAL_SOURCES implements ConstantType {
        BANK("银行"),
        PERSONAL("个人"),
        CUSTOMER("客户"),
        ;
        private String code;
        private String desc;

        private REFERRAL_SOURCES(String desc) {
            this.code = code;
            this.desc = desc;
        }
        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    public enum GROUP_ENDORSE_STATUS implements ConstantType {
        /**
         * 投保单类型
         */
        GROUP_ENDORSE_STATUS("新契约团险"),
        ENDORSE_STATUS("新契约团险"),
        ;

        private String code;
        private String desc;

        private GROUP_ENDORSE_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 保全类型
     */
    public enum ENDORSE_TYPE implements ConstantType {

        GROUP_POLICY("团险保单变更"),
        POLICY("保单变更"),
        CUSTOMER("客户资料变更"),

        ;

        private String desc;

        ENDORSE_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * ANNUAL_EARNED_INCOME
     */
    public enum ANNUAL_EARNED_INCOME implements ConstantType {

        ANNUAL_EARNED_INCOME("收入国际化"),

        ;
        private String code;
        private String desc;

        private ANNUAL_EARNED_INCOME(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }
    /**
     * 事件业务类型
     */
    public enum CHANNEL_TYPE implements ConstantType {

        BANK("BANK", "银保渠道"),
        BROKER("BROKER", "中介渠道"),
        PARTNER("PARTNER", "合作伙伴"),
        MANAGER("MANAGER", "行政管理机构"),
        CUSTOMS("CUSTOMS", "第三方合作机构"),
        AGENT("AGENT", "个险渠道"),
        GMCE("GMCE", "中企渠道"),
        GMCP("GMCP", "合作伙伴渠道"),
        ;
        private String code;
        private String desc;

        CHANNEL_TYPE(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * eMoney手续费率
     */
    public enum EMONEY_SERVICE_FEE implements ConstantType {
        EMONEY_5000("Plan 1", new BigDecimal("14")),
        EMONEY_7500("Plan 2", new BigDecimal("17")),
        EMONEY_10000("Plan 3", new BigDecimal("17")),
        MD_5000("Plan 1", new BigDecimal("3")),
        MD_7500("Plan 2", new BigDecimal("5")),
        MD_10000("Plan 3", new BigDecimal("5")),
        ;
        private String plan;
        private BigDecimal rate;

        EMONEY_SERVICE_FEE(String plan, BigDecimal rate) {
            this.plan = plan;
            this.rate = rate;
        }

        public String plan() {
            return plan;
        }

        public BigDecimal rate() {
            return rate;
        }
    }

    /**
     * 缴费周期
     */
    public enum PREMIUM_FREQUENCY_CONVERSION_MONTH implements ConstantType {
        YEAR(1),
        SEMIANNUAL(2),
        SEASON(4),
        MONTH(12),
        SINGLE(1);

        private String code;
        private int value;


        private PREMIUM_FREQUENCY_CONVERSION_MONTH(int value) {
            this.value = value;
        }

        public String code() {
            return code;
        }

        public int value() {
            return value;
        }
    }

}
