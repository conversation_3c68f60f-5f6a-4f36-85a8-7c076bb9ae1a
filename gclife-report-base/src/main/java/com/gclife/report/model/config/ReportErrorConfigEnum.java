package com.gclife.report.model.config;

import com.gclife.common.model.inter.IEnum;

/**
 * <AUTHOR>
 * create 18-5-16
 * description:
 */
public enum ReportErrorConfigEnum implements IEnum {
    /**
     * 编码规则
     * 参数错误: 模块名称_PARAMETER_表名称_字段名称_验证结果{IS_NOT_NULL:不能为空,FORMAT_ERROR:格式错误,IS_NOT_FOUND_VALUE:未找到值}
     * 业务错误: 模块名称_BUSINESS_规则描述_验证结果{IS_NOT_FOUND_OBJECT:未找到对象}
     * 数据转换错误: 模块名称_CONVERSION_DTO对象名称_ERROR
     * 保存错误: 模块名称_SAVE_表名称_ERROR
     * 查询错误: 模块名称_QUERY_表名称_查询结果(ERROR:查询出错);
     * 通用错误: 模块名称_FAIL
     */
    REPORT_UNDERWRITING_REPORT_IS_NULL("20", "沒有导出承保清单", "REPORT"),
    REPORT_PAYMENT_IS_NULL("21", "沒有导出财务报表", "REPORT"),
    REPORT_RENEWAL_IS_NULL("21", "沒有导出续期报表", "REPORT"),
    REPORT_POLICY_RENEWAL_IS_NULL("22", "沒有导出业务报表-投保人信息", "REPORT"),
    REPORT_POLICY_RENEWAL_ERROR("23", "承保清单导出异常", "REPORT"),
    REPORT_LONG_REPORT_POLICY_ERROR("24", "长期险承保清单导出异常", "REPORT"),
    REPORT_SHORT_REPORT_POLICY_ERROR("25", "短期险承保清单导出异常", "REPORT"),
    REPORT_REPORT_CUSTOMER_ERROR("26", "业务报表投保人资料导出异常", "REPORT"),
    REPORT_REPORT_PAYMENT_ERROR("26", "财务报表导出异常", "REPORT"),
    REPORT_QUERY_REPORT_PAYMENT_DATA_ERROR("27", "查询财务列表异常", "REPORT"),
    REPORT_QUERY_REPORT_PAYMENT_INIT_DATA_ERROR("27", "查询财务列表数据字典异常", "REPORT"),
    REPORT_QUERY_REPORT_CUSTOMER_DATA_ERROR("27", "查询业务报表投保人资料列表异常", "REPORT"),
    REPORT_QUERY_REPORT_CUSTOMER_INIT_DATA_ERROR("27", "查询业务报表投保人资料列表数据字典异常", "REPORT"),
    REPORT_QUERY_REPORT_POLICY_DATA_ERROR("27", "查询承保清单列表异常", "REPORT"),
    REPORT_QUERY_REPORT_POLICY_INIT_DATA_ERROR("27", "查询承保清单列表数据字典异常", "REPORT"),
    REPORT_BASE_FAIL("226000001", "报表基础服务异常", "REPORT_BASE"),
    REPORT_DATE_FORMAT_ERROR("226000001", "请输入合法的日期格式", "REPORT"),
    REPORT_DATE_FORMAT_CANT_CROSS_YEAR_ERROR("226000001", "不支持跨年份搜索，请重新输入", "REPORT"),
    REPORT_REMAINING_DAYS_FORMAT_ERROR("226000001", "天数格式有误,请输入正整数", "REPORT"),
    REPORT_APPROVE_DATE_FORMAT_ERROR("226000001", "承保天数格式有误", "REPORT"),
    REPORT_APPROVE_DATE_AND_APPROVE_DAYS_IS_NULL("226000001", "承保时间或承保天数必须输入其中一项", "REPORT"),
    REPORT_RENEWAL_DATE_OR_PAYMENT_DATE_OR_REMAINING_DAYS_OR_PASSED_DAYS_IS_NOT_NULL("226000001", "应收日期,缴费日期,应收剩余天数或应收已过天数必须输入其中一项", "REPORT"),
    REPORT_REMAINING_DAYS_OR_PASSED_DAYS_IS_NOT_NULL("226000001", "应收剩余天数或应收已过天数必须输入其中一项,且参数完整", "REPORT"),
    REPORT_PAYMENT_END_DATE_AND_REMAINING_DAYS_IS_NULL("226000001", "缴费截止日期或缴费截止剩余天数必须输入其中一项", "REPORT"),
    REPORT_REMAINING_DAYS_IS_NULL("226000001", "缴费截止剩余天数条件必须完整", "REPORT"),
    REPORT_EXPORT_REPORT_RENEWAL_ERROR("26", "续期清单报表导出异常", "REPORT"),
    REPORT_EXPORT_REPORT_REFERRAL_ERROR("26", "推荐费报表导出异常", "REPORT"),
    REPORT_EXPORT_REPORT_RENEWAL_INSURANCE_ERROR("26", "续保清单报表导出异常", "REPORT"),

    REPORT_REMAINING_DAYS_LESS_EQUALS_IS_NOT_NULL("226000001", "请输入应收剩余天数(<=)", "REPORT"),
    REPORT_REMAINING_DAYS_THAN_EQUALS_IS_NOT_NULL("226000001", "请输入应收剩余天数(>=)", "REPORT"),
    REPORT_RENEWAL_PASSED_DAYS_LESS_EQUALS_IS_NOT_NULL("226000001", "请输入应收已过天数(<=)", "REPORT"),
    REPORT_RENEWAL_PASSED_DAYS_THAN_EQUALS_IS_NOT_NULL("226000001", "请输入应收已过天数(>=)", "REPORT"),
    REPORT_PAYMENT_END_DATE_REMAINING_DAYS_LESS_EQUALS_IS_NOT_NULL("226000001", "请输入缴费截止剩余天数(<=)", "REPORT"),
    REPORT_PAYMENT_END_DATE_REMAINING_DAYS_THAN_EQUALS_IS_NOT_NULL("226000001", "请输入缴费截止剩余天数(>=)", "REPORT"),
    REPORT_REPORT_OPERATION_ERROR("22", "导出营运数据异常", "REPORT"),
    REPORT_QUERY_OPERATION_ERROR("22", "查询营运数据异常", "REPORT"),
    REPORT_QUERY_OPERATION_DATE_IS_NOT_NULL("22", "统计日期不为空", "REPORT"),
    REPORT_QUERY_OPERATION_START_DATE_IS_ERROR("22", "统计结束日期要小于当月", "REPORT"),

    REPORT_EXPORT_BRANCH_ID_IS_NOT_NULL("226000001", "机构ID不能为空", "REPORT"),
    REPORT_EXPORT_QUARTER_DATE_IS_NOT_NULL("226000001", "统计日期不能为空", "REPORT"),
    REPORT_EXPORT_CLAIM_DATE_IS_NOT_NULL("22", "报案日期和签批日期必须输入其中一个", "REPORT"),
    /**
     * 导出操作日志报表操作时间不能为空
     */
    REPORT_EXPORT_OPERATION_LOG_OPERATION_DATE_IS_NULL("226000024", "操作时间不能为空", "REPORT"),
    /**
     * 导出操作日志报表 数据太多
     */
    REPORT_EXPORT_OPERATION_LOG_OPERATION_DATE_TOO_MUCH_ERROR("226000025", "所选操作时间段导出的数据太庞大，请缩小时间范围", "REPORT"),
    /**
     * 导出操作日志报表异常
     */
    REPORT_EXPORT_OPERATION_LOG_ERROR("226000026", "导出操作日志报表异常", "REPORT"),
    /**
     * 不能同时选中操作人和角色
     */
    REPORT_EXPORT_OPERATION_LOG_USER_OR_ROLE_CHECK_ERROR("226000027", "不能同时选中操作人和角色", "REPORT"),
    /**
     * 导出操作日志报表操作时间不能为空
     */
    REPORT_EXPORT_OPERATION_LOG_FILE_URL_IS_NULL("226000024", "请求文件路径不能为空", "REPORT"),
    REPORT_POLICY_TYPE_IS_NOT_NULL("226000001", "险种类型不能为空", "REPORT"),
    REPORT_PAYMENT_END_DATE_IS_NOT_NULL("226000001", "缴费截止日期不能为空", "REPORT"),
    REPORT_QUERY_REPORT_PENDING_RENEWAL_POLICY_DATA_ERROR("27", "查询个险待续保清单列表异常", "REPORT"),
    REPORT_QUERY_REPORT_PENDING_GROUP_RENEWAL_POLICY_DATA_ERROR("27", "查询团险待续保清单列表异常", "REPORT"),
    REPORT_EXPORT_PENDING_GROUP_RENEWAL_ERROR("24", "待续保清单导出异常", "REPORT"),
    REPORT_EXPORT_CASH_TRANSACTION_ERROR("66","现金交易报表导出异常", "REPORT")

    ;
    private String code;

    private String value;

    private String group;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getGroup() {
        return group;
    }

    @Override
    public String getCode(String value, String group) {
        return null;
    }

    @Override
    public String getValue(String code, String group) {
        return null;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    ReportErrorConfigEnum(String code, String value, String group) {
        this.code = code;
        this.value = value;
        this.group = group;
    }
}
