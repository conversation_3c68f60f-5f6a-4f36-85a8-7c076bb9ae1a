package com.gclife.report.dao;

import com.gclife.report.core.jooq.tables.pojos.ReportSaleApplyPolicyPo;
import com.gclife.report.core.jooq.tables.pojos.ReportSuspensePo;
import com.gclife.report.model.bo.ReportSaleApplyPolicyBo;
import com.gclife.report.model.bo.ReportSalePolicyBo;
import com.gclife.report.model.vo.ReportSaleApplyPolicyVo;

import java.util.List;

/**
 * @program: gclife-report-service
 * @description: 销售报表数据层
 * @author: baizhongying
 * @create: 2021-02-03 10:24
 **/
public interface ReportSaleApplyPolicyBaseDao {


    /**
     *
     * @param applyIds
     * @return
     */
    List<ReportSaleApplyPolicyPo> queryByApplyIds(List<String> applyIds);

    /**
     *
     * @param applyNos
     * @return
     */
    List<ReportSaleApplyPolicyPo> queryByApplyNos(List<String> applyNos);

    /**
     *
     * @param applyIds
     */
    void deleteByApplyIds(List<String> applyIds);

    /**
     * 销售列表查询
     * @param reportSaleApplyPolicyVo
     * @return
     */
    List<ReportSaleApplyPolicyBo> querySaleApplyPolicyPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    /**
     *
     * @param reportSaleApplyPolicyVo
     * @return
     */
    List<ReportSaleApplyPolicyBo> queryReceiptPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    /**
     * 查询预缴保费数据
     * @param reportSaleApplyPolicyVo
     * @return
     */
    List<ReportSaleApplyPolicyBo> querySuspensePageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    /**查询预缴保费数据
     *
     * @return
     */
    List<ReportSaleApplyPolicyBo> querySyncSuspense(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    ReportSuspensePo getReportSuspensePo(String applyId, String applyStatus);

    List<ReportSalePolicyBo> getSaleApplyPolicyList(List<String> policyNos);
}
