package com.gclife.report.dao.impl;

import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.dao.ReportActualPerformanceBaseDao;
import com.gclife.report.model.bo.ReportCashTransactionBo;
import com.gclife.report.model.config.ReportTermEnum;
import com.gclife.report.model.vo.ReportCashTransctionVO;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.gclife.report.core.jooq.Tables.*;
import static com.gclife.report.model.config.ReportTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS;

/**
 * <AUTHOR>
 * @description
 * @date 2020/6/19 3:32 下午
 */
@Repository
public class ReportActualPerformanceBaseDaoImpl extends BaseDaoImpl implements ReportActualPerformanceBaseDao {

    @Override
    public List<ActualPerformanceReportBo> queryByDate(long startTime, long endTime) {

        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(REPORT_ACTUAL_PERFORMANCE.fields())
                .select(REPORT_ACTUAL_PERFORMANCE.REPORT_ACTUAL_PERFORMANCE_ID.countOver().as("totalLine"))
                .select(REPORT_PAYMENT.PAYMENT_CODE_NO)
                .select(REPORT_PAYMENT.BANK_CODE)
                .select(REPORT_SALE_APPLY_POLICY.CHANNEL_TYPE_CODE)
                .from(REPORT_ACTUAL_PERFORMANCE)
                .leftJoin(REPORT_PAYMENT).on(REPORT_PAYMENT.REPORT_PAYMENT_ID.eq(REPORT_ACTUAL_PERFORMANCE.REPORT_PAYMENT_ID))
                .leftJoin(REPORT_SALE_APPLY_POLICY).on(REPORT_SALE_APPLY_POLICY.POLICY_NO.eq(REPORT_ACTUAL_PERFORMANCE.POLICY_NO),
                        REPORT_SALE_APPLY_POLICY.PRIMARY_FLAG.eq(ReportTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())
                )
                .where(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.ge(startTime),
                        REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.le(endTime),
                        //预缴保费支付会产生支付数据同同步实收业绩报表 ，但是不需要展示
                        REPORT_ACTUAL_PERFORMANCE.POLICY_NO.isNotNull());
        selectConditionStep.orderBy(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.asc(),
                REPORT_ACTUAL_PERFORMANCE.POLICY_ID.asc(),
                REPORT_ACTUAL_PERFORMANCE.RECEIPT_NO.asc(),
                REPORT_ACTUAL_PERFORMANCE.REFUND_AMOUNT.decode(new BigDecimal(0.00), 0, 1).asc(),
                REPORT_ACTUAL_PERFORMANCE.FREQUENCY.asc(),
                REPORT_ACTUAL_PERFORMANCE.PRIMARY_FLAG.desc(),
                REPORT_ACTUAL_PERFORMANCE.PRODUCT_ID.asc(),
                REPORT_ACTUAL_PERFORMANCE.DUTY_ID.asc(),
                REPORT_ACTUAL_PERFORMANCE.PRODUCT_LEVEL.asc());
        System.out.println(selectConditionStep.toString());
        return selectConditionStep.fetchInto(ActualPerformanceReportBo.class);
    }

    @Override
    public void deleteByPaymentId(List<String> paymentIdList) {
        this.getDslContext().deleteFrom(REPORT_ACTUAL_PERFORMANCE).where(
                REPORT_ACTUAL_PERFORMANCE.PAYMENT_ID.in(paymentIdList)
        ).execute();
    }

    @Override
    public List<ReportCashTransactionBo> queryCashTransactionPageList(ReportCashTransctionVO reportCashTransctionVO) {
        String startDate = reportCashTransctionVO.getStartDate();
        String endDate = reportCashTransctionVO.getEndDate();
        String keyword = reportCashTransctionVO.getKeyword();
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(keyword)) {
            conditions.add(REPORT_ACTUAL_PERFORMANCE.POLICY_NO.eq(keyword).or(REPORT_PAYMENT.APPLICANT_NAME.likeRegex(keyword)));
        }
        if (AssertUtils.isNotEmpty(startDate)) {
            long actualPayStartDate = DateUtils.timeToTimeLow(DateUtils.stringToTime(startDate, DateUtils.FORMATE3));
            conditions.add(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.ge(actualPayStartDate));
        }
        if (AssertUtils.isNotEmpty(endDate)) {
            long actualPayEndDate = DateUtils.timeToTimeTop(DateUtils.stringToTime(endDate, DateUtils.FORMATE3));
            conditions.add(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.le(actualPayEndDate));
        }
        conditions.add(REPORT_PAYMENT.STATUS.eq(PAYMENT_SUCCESS.name()));
        conditions.add(REPORT_PAYMENT.PAYMENT_TYPE.in(ReportTermEnum.PAYMENT_TYPE1.PAYMENT.name(),"SUSPENSE_PREMIUM"));
        String[] strings = this.getDslContext()
                .select(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .from(REPORT_PAYMENT)
                .where(REPORT_PAYMENT.STATUS.eq(PAYMENT_SUCCESS.name()))
                .and(REPORT_PAYMENT.PAYMENT_TYPE.in(ReportTermEnum.PAYMENT_TYPE1.PAYMENT.name(),"SUSPENSE_PREMIUM"))
                .and(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO.isNotNull())
                .groupBy(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .having(REPORT_PAYMENT.ACTUAL_PAY_AMOUNT.sum().ge(new BigDecimal(10000)))
                .fetchArray(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO, String.class);
        if (!AssertUtils.isNotEmpty(Arrays.asList(strings))) {
            return new ArrayList<ReportCashTransactionBo>();
        }
        conditions.add(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO.in(strings));
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(REPORT_PAYMENT.REPORT_PAYMENT_ID)
                .select(REPORT_PAYMENT.APPLY_NO)
                .select(REPORT_PAYMENT.POLICY_NO)
                .select(REPORT_PAYMENT.POLICY_ID)
                .select(REPORT_PAYMENT.APPLICANT_NAME)
                .select(REPORT_PAYMENT.PAYMENT_METHOD_CODE)
                .select(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE)
                .select(REPORT_ACTUAL_PERFORMANCE.PREMIUM_FREQUENCY)
                .select(REPORT_ACTUAL_PERFORMANCE.PREMIUM_PERIOD)
                .select(REPORT_PAYMENT.REPORT_PAYMENT_ID.countOver().as("totalLine"))
                .select(REPORT_PAYMENT.PAYMENT_CODE_NO)
                .select(REPORT_PAYMENT.BANK_CODE)
                .select(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .select(REPORT_PAYMENT.APPLY_ID)
                .select(REPORT_PAYMENT.INSURED_NAME)
                .select(REPORT_PAYMENT.ACTUAL_PAY_AMOUNT)
                .select(REPORT_PAYMENT.DUE_PAY_AMOUNT)
                .select(REPORT_PAYMENT.BANK_ACCOUNT_NO)
                .select(REPORT_PAYMENT.CASH_TRANSACTION_NO)
                .select(REPORT_PAYMENT.BUSINESS_TYPE)
                .from(REPORT_PAYMENT)
                .leftJoin(REPORT_ACTUAL_PERFORMANCE).on(REPORT_PAYMENT.REPORT_PAYMENT_ID.eq(REPORT_ACTUAL_PERFORMANCE.REPORT_PAYMENT_ID))
                .where(conditions);
        selectConditionStep.offset(reportCashTransctionVO.getOffset()).limit(reportCashTransctionVO.getPageSize());
        selectConditionStep.groupBy(
                REPORT_PAYMENT.REPORT_PAYMENT_ID,
                REPORT_PAYMENT.APPLY_NO,
                REPORT_PAYMENT.POLICY_NO,
                REPORT_PAYMENT.POLICY_ID,
                REPORT_PAYMENT.PAYMENT_METHOD_CODE,
                REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE,
                REPORT_ACTUAL_PERFORMANCE.PREMIUM_FREQUENCY,
                REPORT_ACTUAL_PERFORMANCE.PREMIUM_PERIOD,
                REPORT_PAYMENT.PAYMENT_CODE_NO,
                REPORT_PAYMENT.BANK_CODE,
                REPORT_PAYMENT.APPLICANT_CUSTOMER_NO,
                REPORT_PAYMENT.APPLY_ID,
                REPORT_PAYMENT.INSURED_NAME,
                REPORT_PAYMENT.ACTUAL_PAY_AMOUNT,
                REPORT_PAYMENT.DUE_PAY_AMOUNT,
                REPORT_PAYMENT.BANK_ACCOUNT_NO,
                REPORT_PAYMENT.CASH_TRANSACTION_NO,
                REPORT_PAYMENT.BUSINESS_TYPE,
                REPORT_PAYMENT.APPLICANT_NAME
        );
        getLogger().info("sql" + selectConditionStep);
        return selectConditionStep.fetchInto(ReportCashTransactionBo.class);
    }

    @Override
    public List<ReportCashTransactionBo> exportCashTransaction(ReportCashTransctionVO reportCashTransctionVO) {
        String startDate = reportCashTransctionVO.getStartDate();
        String endDate = reportCashTransctionVO.getEndDate();
        List<Condition> conditions = new ArrayList<>();
        if (AssertUtils.isNotEmpty(startDate)) {
            long actualPayStartDate = DateUtils.timeToTimeLow(DateUtils.stringToTime(startDate, DateUtils.FORMATE3));
            conditions.add(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.ge(actualPayStartDate));
        }
        if (AssertUtils.isNotEmpty(endDate)) {
            long actualPayEndDate = DateUtils.timeToTimeTop(DateUtils.stringToTime(endDate, DateUtils.FORMATE3));
            conditions.add(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.le(actualPayEndDate));
        }
        conditions.add(REPORT_PAYMENT.STATUS.eq(PAYMENT_SUCCESS.name()));
        conditions.add(REPORT_PAYMENT.PAYMENT_TYPE.in(ReportTermEnum.PAYMENT_TYPE1.PAYMENT.name(),"SUSPENSE_PREMIUM"));
        String[] strings = this.getDslContext()
                .select(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .from(REPORT_PAYMENT)
                .where(REPORT_PAYMENT.STATUS.eq(PAYMENT_SUCCESS.name()))
                .and(REPORT_PAYMENT.PAYMENT_TYPE.in(ReportTermEnum.PAYMENT_TYPE1.PAYMENT.name(),"SUSPENSE_PREMIUM"))
                .and(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO.isNotNull())
                .groupBy(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .having(REPORT_PAYMENT.ACTUAL_PAY_AMOUNT.sum().ge(new BigDecimal(10000)))
                .fetchArray(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO, String.class);


        conditions.add(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO.in(strings));
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(REPORT_PAYMENT.REPORT_PAYMENT_ID)
                .select(REPORT_PAYMENT.APPLY_NO)
                .select(REPORT_PAYMENT.POLICY_NO)
                .select(REPORT_PAYMENT.POLICY_ID)
                .select(REPORT_PAYMENT.APPLICANT_NAME)
                .select(REPORT_PAYMENT.PAYMENT_METHOD_CODE)
                .select(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE)
                .select(REPORT_ACTUAL_PERFORMANCE.PREMIUM_FREQUENCY)
                .select(REPORT_ACTUAL_PERFORMANCE.PREMIUM_PERIOD)
                .select(REPORT_PAYMENT.REPORT_PAYMENT_ID.countOver().as("totalLine"))
                .select(REPORT_PAYMENT.PAYMENT_CODE_NO)
                .select(REPORT_PAYMENT.BANK_CODE)
                .select(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .select(REPORT_PAYMENT.APPLY_ID)
                .select(REPORT_PAYMENT.INSURED_NAME)
                .select(REPORT_PAYMENT.ACTUAL_PAY_AMOUNT)
                .select(REPORT_PAYMENT.DUE_PAY_AMOUNT)
                .select(REPORT_PAYMENT.BANK_ACCOUNT_NO)
                .select(REPORT_PAYMENT.CASH_TRANSACTION_NO)
                .select(REPORT_PAYMENT.BUSINESS_TYPE)
                .from(REPORT_PAYMENT)
                .leftJoin(REPORT_ACTUAL_PERFORMANCE).on(REPORT_PAYMENT.REPORT_PAYMENT_ID.eq(REPORT_ACTUAL_PERFORMANCE.REPORT_PAYMENT_ID))
                .where(conditions);
        selectConditionStep.groupBy(
                REPORT_PAYMENT.REPORT_PAYMENT_ID,
                REPORT_PAYMENT.APPLY_NO,
                REPORT_PAYMENT.POLICY_NO,
                REPORT_PAYMENT.POLICY_ID,
                REPORT_PAYMENT.PAYMENT_METHOD_CODE,
                REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE,
                REPORT_ACTUAL_PERFORMANCE.PREMIUM_FREQUENCY,
                REPORT_ACTUAL_PERFORMANCE.PREMIUM_PERIOD,
                REPORT_PAYMENT.PAYMENT_CODE_NO,
                REPORT_PAYMENT.BANK_CODE,
                REPORT_PAYMENT.APPLICANT_CUSTOMER_NO,
                REPORT_PAYMENT.APPLY_ID,
                REPORT_PAYMENT.INSURED_NAME,
                REPORT_PAYMENT.ACTUAL_PAY_AMOUNT,
                REPORT_PAYMENT.DUE_PAY_AMOUNT,
                REPORT_PAYMENT.BANK_ACCOUNT_NO,
                REPORT_PAYMENT.CASH_TRANSACTION_NO,
                REPORT_PAYMENT.BUSINESS_TYPE,
                REPORT_PAYMENT.APPLICANT_NAME
        );
        System.out.println("sql" + selectConditionStep);
        return selectConditionStep.fetchInto(ReportCashTransactionBo.class);
    }

    @Override
    public List<ReportCashTransactionBo> getCashTransactionMsg(long startDate, long endDate) {
        List<Condition> conditions = new ArrayList<>();
        conditions.add(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.ge(startDate));
        conditions.add(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE.le(endDate));
        conditions.add(REPORT_PAYMENT.STATUS.eq(PAYMENT_SUCCESS.name()));
        conditions.add(REPORT_PAYMENT.PAYMENT_TYPE.in(ReportTermEnum.PAYMENT_TYPE1.PAYMENT.name(), "SUSPENSE_PREMIUM"));
        String[] strings = this.getDslContext()
                .select(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .from(REPORT_PAYMENT)
                .where(REPORT_PAYMENT.STATUS.eq(PAYMENT_SUCCESS.name()))
                .and(REPORT_PAYMENT.PAYMENT_TYPE.in(ReportTermEnum.PAYMENT_TYPE1.PAYMENT.name(),"SUSPENSE_PREMIUM"))
                .and(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO.isNotNull())
                .groupBy(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .having(REPORT_PAYMENT.ACTUAL_PAY_AMOUNT.sum().ge(new BigDecimal(10000)))
                .fetchArray(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO, String.class);
        if (strings.length > 0) {
            conditions.add(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO.in(strings));
        }
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(REPORT_PAYMENT.REPORT_PAYMENT_ID)
                .select(REPORT_ACTUAL_PERFORMANCE.APPLY_NO)
                .select(REPORT_ACTUAL_PERFORMANCE.POLICY_NO)
                .select(REPORT_ACTUAL_PERFORMANCE.POLICY_ID)
                .select(REPORT_ACTUAL_PERFORMANCE.APPLICANT_NAME)
                .select(REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE)
                .select(REPORT_ACTUAL_PERFORMANCE.PREMIUM_FREQUENCY)
                .select(REPORT_ACTUAL_PERFORMANCE.PAYMENT_METHOD_CODE)
                .select(REPORT_ACTUAL_PERFORMANCE.PREMIUM_PERIOD)
                .select(REPORT_PAYMENT.PAYMENT_CODE_NO)
                .select(REPORT_PAYMENT.BANK_CODE)
                .select(REPORT_PAYMENT.BRANCH_ID)
                .select(REPORT_PAYMENT.APPLICANT_CUSTOMER_NO)
                .select(REPORT_PAYMENT.APPLY_ID)
                .select(REPORT_PAYMENT.INSURED_NAME)
                .select(REPORT_PAYMENT.ACTUAL_PAY_AMOUNT)
                .select(REPORT_PAYMENT.DUE_PAY_AMOUNT)
                .from(REPORT_PAYMENT)
                .leftJoin(REPORT_ACTUAL_PERFORMANCE).on(REPORT_PAYMENT.REPORT_PAYMENT_ID.eq(REPORT_ACTUAL_PERFORMANCE.REPORT_PAYMENT_ID))
                .where(conditions);
        selectConditionStep.groupBy(
                REPORT_PAYMENT.REPORT_PAYMENT_ID,
                REPORT_ACTUAL_PERFORMANCE.APPLY_NO,
                REPORT_ACTUAL_PERFORMANCE.POLICY_NO,
                REPORT_ACTUAL_PERFORMANCE.POLICY_ID,
                REPORT_ACTUAL_PERFORMANCE.APPLICANT_NAME,
                REPORT_ACTUAL_PERFORMANCE.ACTUAL_PAY_DATE,
                REPORT_ACTUAL_PERFORMANCE.PREMIUM_FREQUENCY,
                REPORT_ACTUAL_PERFORMANCE.PAYMENT_METHOD_CODE,
                REPORT_ACTUAL_PERFORMANCE.PREMIUM_PERIOD,
                REPORT_PAYMENT.PAYMENT_CODE_NO,
                REPORT_PAYMENT.BANK_CODE,
                REPORT_PAYMENT.APPLICANT_CUSTOMER_NO,
                REPORT_PAYMENT.APPLY_ID,
                REPORT_PAYMENT.INSURED_NAME,
                REPORT_PAYMENT.ACTUAL_PAY_AMOUNT,
                REPORT_PAYMENT.DUE_PAY_AMOUNT
        );
        System.out.println("sql" + selectConditionStep);
        return selectConditionStep.fetchInto(ReportCashTransactionBo.class);
    }
}
