package com.gclife.report.dao;

import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.model.bo.ReportCashTransactionBo;
import com.gclife.report.model.vo.ReportCashTransctionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/6/19 3:32 下午
 */
public interface ReportActualPerformanceBaseDao {
    /**
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<ActualPerformanceReportBo> queryByDate(long startTime, long endTime);

    /**
     *
     * @param paymentIdList
     */
    void deleteByPaymentId(List<String> paymentIdList);

    List<ReportCashTransactionBo> queryCashTransactionPageList(ReportCashTransctionVO reportCashTransctionVO);

    List<ReportCashTransactionBo> exportCashTransaction(ReportCashTransctionVO reportCashTransctionVO);

    List<ReportCashTransactionBo> getCashTransactionMsg(long startDate, long endDate);
}
