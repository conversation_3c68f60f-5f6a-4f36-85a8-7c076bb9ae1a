package com.gclife.report.dao.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.ResultObject;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.report.core.jooq.tables.ReportSaleApplyPolicy;
import com.gclife.report.core.jooq.tables.pojos.ReportSaleApplyPolicyPo;
import com.gclife.report.core.jooq.tables.pojos.ReportSuspensePo;
import com.gclife.report.core.jooq.tables.records.ReportSaleApplyPolicyRecord;
import com.gclife.report.dao.ReportSaleApplyPolicyBaseDao;
import com.gclife.report.model.bo.ReportSaleApplyPolicyBo;
import com.gclife.report.model.bo.ReportSalePolicyBo;
import com.gclife.report.model.vo.ReportSaleApplyPolicyVo;
import org.jooq.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.gclife.report.core.jooq.Tables.*;
import static com.gclife.report.model.config.ReportTermEnum.PRODUCT_PRIMARY_FLAG.MAIN;

/**
 * @program: gclife-report-service
 * @description: 销售报表数据层
 * @author: baizhongying
 * @create: 2021-02-03 10:25
 **/
@Repository
public class ReportSaleApplyPolicyBaseDaoImpl extends BaseDaoImpl implements ReportSaleApplyPolicyBaseDao {

    @Override
    public List<ReportSaleApplyPolicyPo> queryByApplyIds(List<String> applyIds) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(REPORT_SALE_APPLY_POLICY.fields())
                .from(REPORT_SALE_APPLY_POLICY)
                .where(REPORT_SALE_APPLY_POLICY.APPLY_ID.in(applyIds));
        return selectConditionStep.fetchInto(ReportSaleApplyPolicyPo.class);
    }

    @Override
    public List<ReportSaleApplyPolicyPo> queryByApplyNos(List<String> applyNos) {
        SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                .select(REPORT_SALE_APPLY_POLICY.fields())
                .from(REPORT_SALE_APPLY_POLICY)
                .where(REPORT_SALE_APPLY_POLICY.APPLY_NO.in(applyNos));
        return selectConditionStep.fetchInto(ReportSaleApplyPolicyPo.class);
    }

    @Override
    public void deleteByApplyIds(List<String> applyIds) {
        DeleteConditionStep<ReportSaleApplyPolicyRecord> deleteConditionStep = this.getDslContext()
                .delete(REPORT_SALE_APPLY_POLICY)
                .where(REPORT_SALE_APPLY_POLICY.APPLY_ID.in(applyIds));
        deleteConditionStep.execute();
    }

    @Override
    public List<ReportSaleApplyPolicyBo> querySaleApplyPolicyPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        ReportSaleApplyPolicy t1 = REPORT_SALE_APPLY_POLICY.as("t1");
        SelectJoinStep<Record> selectJoinStep = this.getDslContext()
                .select(REPORT_SALE_APPLY_POLICY.fields())
                .select(t1.PRODUCT_ID.as("mainProductId"))
                .select(t1.REPORT_SALE_APPLY_POLICY_ID.as("saleId"))
                .select(REPORT_SALE_APPLY_POLICY.REPORT_SALE_APPLY_POLICY_ID.countOver().as("totalLine"))
                .from(REPORT_SALE_APPLY_POLICY)
                .leftJoin(t1)
                .on(t1.APPLY_ID.eq(REPORT_SALE_APPLY_POLICY.APPLY_ID),t1.PRIMARY_FLAG.eq(MAIN.name()));
        List<Condition> conditionList = new ArrayList<>();
        String keyword = reportSaleApplyPolicyVo.getKeyword();
        List<String> agentIdList = reportSaleApplyPolicyVo.getAgentIdList();
        List<String> policyIds = reportSaleApplyPolicyVo.getPolicyIds();
        if (AssertUtils.isNotEmpty(keyword)) {
            Condition condition = REPORT_SALE_APPLY_POLICY.APPLY_NO.like("%" + keyword + "%")
                    .or(REPORT_SALE_APPLY_POLICY.POLICY_NO.like("%" + keyword + "%"));
            if (AssertUtils.isNotEmpty(agentIdList)) {
                condition = condition.or(REPORT_SALE_APPLY_POLICY.AGENT_ID.in(agentIdList));
            }
            if (AssertUtils.isNotEmpty(policyIds)) {
                condition = condition.or(REPORT_SALE_APPLY_POLICY.POLICY_ID.in(reportSaleApplyPolicyVo.getPolicyIds()));
            }
            conditionList.add(condition);
        }
        String channelTypeCode = reportSaleApplyPolicyVo.getChannelTypeCode();
        if (AssertUtils.isNotEmpty(channelTypeCode)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.CHANNEL_TYPE_CODE.eq(channelTypeCode));
        }
        String appSubmitUnderwritingStartDateFormat = reportSaleApplyPolicyVo.getAppSubmitUnderwritingStartDateFormat();
        String appSubmitUnderwritingEndDateFormat = reportSaleApplyPolicyVo.getAppSubmitUnderwritingEndDateFormat();
        if (AssertUtils.isNotEmpty(appSubmitUnderwritingStartDateFormat) && AssertUtils.isNotEmpty(appSubmitUnderwritingEndDateFormat)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.APP_SUBMIT_UNDERWRITING_DATE.between(
                    DateUtils.timeToTimeLow(DateUtils.stringToTime(appSubmitUnderwritingStartDateFormat, DateUtils.FORMATE3)),
                    DateUtils.timeToTimeTop(DateUtils.stringToTime(appSubmitUnderwritingEndDateFormat, DateUtils.FORMATE3)))
            );
        }
        String approveStartDateFormat = reportSaleApplyPolicyVo.getApproveStartDateFormat();
        String approveEndDateFormat = reportSaleApplyPolicyVo.getApproveEndDateFormat();
        if (AssertUtils.isNotEmpty(approveStartDateFormat) && AssertUtils.isNotEmpty(approveEndDateFormat)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.APPROVE_DATE.between(
                    DateUtils.timeToTimeLow(DateUtils.stringToTime(approveStartDateFormat, DateUtils.FORMATE3)),
                    DateUtils.timeToTimeTop(DateUtils.stringToTime(approveEndDateFormat, DateUtils.FORMATE3)))
            );
        }

        String riskCommencementStartDateFormat = reportSaleApplyPolicyVo.getRiskCommencementStartDateFormat();
        String riskCommencementEndDateFormat = reportSaleApplyPolicyVo.getRiskCommencementEndDateFormat();
        if (AssertUtils.isNotEmpty(riskCommencementStartDateFormat) && AssertUtils.isNotEmpty(riskCommencementEndDateFormat)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.RISK_COMMENCEMENT_DATE.between(
                    DateUtils.timeToTimeLow(DateUtils.stringToTime(riskCommencementStartDateFormat, DateUtils.FORMATE3)),
                    DateUtils.timeToTimeTop(DateUtils.stringToTime(riskCommencementEndDateFormat, DateUtils.FORMATE3)))
            );
        }

        String agentId = reportSaleApplyPolicyVo.getAgentId();
        if (AssertUtils.isNotEmpty(agentId)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.AGENT_ID.eq(agentId));
        }
        String applyStatus = reportSaleApplyPolicyVo.getApplyStatus();
        if (AssertUtils.isNotEmpty(applyStatus)) {
            String[] applyStatuss = applyStatus.split(",");
            conditionList.add(REPORT_SALE_APPLY_POLICY.APPLY_STATUS.in(applyStatuss));
        }
        String noApplyStatus = reportSaleApplyPolicyVo.getNotApplyStatus();
        if (AssertUtils.isNotEmpty(noApplyStatus)) {
            String[] noApplyStatuss = noApplyStatus.split(",");
            conditionList.add(REPORT_SALE_APPLY_POLICY.APPLY_STATUS.notIn(noApplyStatuss));
        }
        String policyStatus = reportSaleApplyPolicyVo.getPolicyStatus();
        if (AssertUtils.isNotEmpty(policyStatus)) {
            String[] policyStatuss = policyStatus.split(",");
            conditionList.add(REPORT_SALE_APPLY_POLICY.POLICY_STATUS.in(policyStatuss));
        }
        String noPolicyStatus = reportSaleApplyPolicyVo.getNotPolicyStatus();
        if (AssertUtils.isNotEmpty(noPolicyStatus)) {
            String[] noPolicyStatuss = noPolicyStatus.split(",");
            conditionList.add(REPORT_SALE_APPLY_POLICY.POLICY_STATUS.notIn(noPolicyStatuss));
        }
        String applyPolicyStatusList = reportSaleApplyPolicyVo.getApplyPolicyStatusList();
        if (AssertUtils.isNotEmpty(applyPolicyStatusList)) {
            String[] applyPolicyStatuss = applyPolicyStatusList.split(",");
            conditionList.add(REPORT_SALE_APPLY_POLICY.POLICY_STATUS.in(applyPolicyStatuss).or(REPORT_SALE_APPLY_POLICY.APPLY_STATUS.in(applyPolicyStatuss)));
        }
        //查询非保单/投保单状态(弃用,现将投保单和保单分开查询)
        String noApplyPolicyStatusList = reportSaleApplyPolicyVo.getNoApplyPolicyStatusList();
        if (AssertUtils.isNotEmpty(noApplyPolicyStatusList)) {
            String[] noApplyPolicyStatuss = noApplyPolicyStatusList.split(",");
            conditionList.add(REPORT_SALE_APPLY_POLICY.POLICY_STATUS.notIn(noApplyPolicyStatuss).or(REPORT_SALE_APPLY_POLICY.APPLY_STATUS.notIn(noApplyPolicyStatuss)));
        }
        //新旧保单关联查询
        if (AssertUtils.isNotEmpty(reportSaleApplyPolicyVo.getPolicyIds())) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.POLICY_ID.in(reportSaleApplyPolicyVo.getPolicyIds()));
        }
        selectJoinStep.where(conditionList);
        selectJoinStep.orderBy(
                REPORT_SALE_APPLY_POLICY.APPLY_DATE.desc(),
                REPORT_SALE_APPLY_POLICY.APPROVE_DATE.desc(),
                REPORT_SALE_APPLY_POLICY.POLICY_NO.desc(),
                REPORT_SALE_APPLY_POLICY.APPLY_NO.desc(),
                REPORT_SALE_APPLY_POLICY.PRIMARY_FLAG.desc(),
                REPORT_SALE_APPLY_POLICY.REPORT_SALE_APPLY_POLICY_ID.desc()
        );
        selectJoinStep.offset(reportSaleApplyPolicyVo.getOffset()).limit(reportSaleApplyPolicyVo.getPageSize());
        getLogger().info("Generated SQL: \n{}", selectJoinStep.toString());
        return selectJoinStep.fetchInto(ReportSaleApplyPolicyBo.class);
    }

    @Override
    public List<ReportSaleApplyPolicyBo> queryReceiptPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {

        SelectJoinStep<Record> selectJoinStep = this.getDslContext()
                .select(REPORT_SALE_APPLY_POLICY.fields())
                .select(REPORT_SALE_APPLY_POLICY.REPORT_SALE_APPLY_POLICY_ID.countOver().as("totalLine"))
                .from(REPORT_SALE_APPLY_POLICY);
        List<Condition> conditionList = new ArrayList<>();
        String keyword = reportSaleApplyPolicyVo.getKeyword();
        List<String> agentIdList = reportSaleApplyPolicyVo.getAgentIdList();
        List<String> policyIds = reportSaleApplyPolicyVo.getPolicyIds();
        if (AssertUtils.isNotEmpty(keyword)) {
            Condition condition = REPORT_SALE_APPLY_POLICY.POLICY_NO.like("%" + keyword + "%");
            if (AssertUtils.isNotEmpty(agentIdList)) {
                condition = condition.or(REPORT_SALE_APPLY_POLICY.AGENT_ID.in(agentIdList));
            }
            if (AssertUtils.isNotEmpty(policyIds)) {
                condition = condition.or(REPORT_SALE_APPLY_POLICY.POLICY_ID.in(reportSaleApplyPolicyVo.getPolicyIds()));
            }
            conditionList.add(condition);
        }
        if (AssertUtils.isNotEmpty(reportSaleApplyPolicyVo.getChannelTypeCode())) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.CHANNEL_TYPE_CODE.eq(reportSaleApplyPolicyVo.getChannelTypeCode()));
        }

        String receiptDateStartFormat = reportSaleApplyPolicyVo.getReceiptDateStartFormat();
        String receiptDateEndFormat = reportSaleApplyPolicyVo.getReceiptDateEndFormat();
        if (AssertUtils.isNotEmpty(receiptDateStartFormat) && AssertUtils.isNotEmpty(receiptDateEndFormat)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.RECEIPT_DATE.between(
                    DateUtils.timeToTimeLow(DateUtils.stringToTime(receiptDateStartFormat, DateUtils.FORMATE3)),
                    DateUtils.timeToTimeTop(DateUtils.stringToTime(receiptDateEndFormat, DateUtils.FORMATE3)))
            );
        }

        String receiptReturnDateStartFormat = reportSaleApplyPolicyVo.getReceiptReturnDateStartFormat();
        String receiptReturnDateEndFormat = reportSaleApplyPolicyVo.getReceiptReturnDateEndFormat();
        if (AssertUtils.isNotEmpty(receiptReturnDateStartFormat) && AssertUtils.isNotEmpty(receiptReturnDateEndFormat)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.RECEIPT_RETURN_DATE.between(
                    DateUtils.timeToTimeLow(DateUtils.stringToTime(receiptReturnDateStartFormat, DateUtils.FORMATE3)),
                    DateUtils.timeToTimeTop(DateUtils.stringToTime(receiptReturnDateEndFormat, DateUtils.FORMATE3)))
            );
        }

        String receiptSubmitDateStartFormat = reportSaleApplyPolicyVo.getReceiptSubmitDateStartFormat();
        String receiptSubmitDateEndFormat = reportSaleApplyPolicyVo.getReceiptSubmitDateEndFormat();
        if (AssertUtils.isNotEmpty(receiptSubmitDateStartFormat) && AssertUtils.isNotEmpty(receiptSubmitDateEndFormat)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.RECEIPT_SUBMIT_DATE.between(
                    DateUtils.timeToTimeLow(DateUtils.stringToTime(receiptSubmitDateStartFormat, DateUtils.FORMATE3)),
                    DateUtils.timeToTimeTop(DateUtils.stringToTime(receiptSubmitDateEndFormat, DateUtils.FORMATE3)))
            );
        }
        String approveStartDateFormat = reportSaleApplyPolicyVo.getApproveStartDateFormat();
        String approveEndDateFormat = reportSaleApplyPolicyVo.getApproveEndDateFormat();
        if (AssertUtils.isNotEmpty(approveStartDateFormat) && AssertUtils.isNotEmpty(approveEndDateFormat)) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.APPROVE_DATE.between(
                    DateUtils.timeToTimeLow(DateUtils.stringToTime(approveStartDateFormat, DateUtils.FORMATE3)),
                    DateUtils.timeToTimeTop(DateUtils.stringToTime(approveEndDateFormat, DateUtils.FORMATE3)))
            );
        }
        conditionList.add(REPORT_SALE_APPLY_POLICY.PRIMARY_FLAG.eq(MAIN.name()));
        conditionList.add(REPORT_SALE_APPLY_POLICY.POLICY_NO.isNotNull());

        //新旧保单关联查询
        if (AssertUtils.isNotEmpty(reportSaleApplyPolicyVo.getPolicyIds())) {
            conditionList.add(REPORT_SALE_APPLY_POLICY.POLICY_ID.in(reportSaleApplyPolicyVo.getPolicyIds()));
        }

        selectJoinStep.where(conditionList);
        selectJoinStep.orderBy(
                REPORT_SALE_APPLY_POLICY.APPLY_DATE.desc(),
                REPORT_SALE_APPLY_POLICY.APPROVE_DATE.desc(),
                REPORT_SALE_APPLY_POLICY.POLICY_NO.desc(),
                REPORT_SALE_APPLY_POLICY.APPLY_NO.desc(),
                REPORT_SALE_APPLY_POLICY.PRIMARY_FLAG.desc(),
                REPORT_SALE_APPLY_POLICY.REPORT_SALE_APPLY_POLICY_ID.desc()
        );
        selectJoinStep.offset(reportSaleApplyPolicyVo.getOffset()).limit(reportSaleApplyPolicyVo.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ReportSaleApplyPolicyBo.class);
    }

    @Override
    public List<ReportSaleApplyPolicyBo> querySuspensePageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        SelectJoinStep<Record> selectJoinStep = this.getDslContext()
                .select(REPORT_SUSPENSE.fields())
                .select(REPORT_SUSPENSE.REPORT_SUSPENSE_ID.countOver().as("totalLine"))
                .from(REPORT_SUSPENSE);

        List<Condition> conditionList = new ArrayList<>();
        conditionList.add(REPORT_SUSPENSE.QUARTER_DATE.eq(reportSaleApplyPolicyVo.getQuarterDate()));
        if (AssertUtils.isNotEmpty(reportSaleApplyPolicyVo.getBranchIdList())) {
            conditionList.add(REPORT_SUSPENSE.SALES_BRANCH_ID.in(reportSaleApplyPolicyVo.getBranchIdList()));
        }
        selectJoinStep.where(conditionList);
        selectJoinStep.orderBy(REPORT_SUSPENSE.APPLY_DATE);
        selectJoinStep.orderBy(REPORT_SUSPENSE.REPORT_SUSPENSE_ID);
        selectJoinStep.offset(reportSaleApplyPolicyVo.getOffset()).limit(reportSaleApplyPolicyVo.getPageSize());
        System.out.println(selectJoinStep);
        return selectJoinStep.fetchInto(ReportSaleApplyPolicyBo.class);
    }

    @Override
    public List<ReportSaleApplyPolicyBo> querySyncSuspense(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        SelectJoinStep<Record> selectJoinStep = this.getDslContext()
                .select(REPORT_SALE_APPLY_POLICY.fields())
                .select(REPORT_SALE_APPLY_POLICY.REPORT_SALE_APPLY_POLICY_ID.countOver().as("totalLine"))
                .from(REPORT_SALE_APPLY_POLICY);

        List<Condition> conditionList = new ArrayList<>();

        conditionList.add(REPORT_SALE_APPLY_POLICY.PRIMARY_FLAG.eq(MAIN.name()));
        conditionList.add(REPORT_SALE_APPLY_POLICY.SUSPENSE_PREMIUM_DATE.isNotNull());
        // (承保日期，拒保日期，作废日期)在当月  解释：单月结束的单
        // 或
        // (承保日期，拒保日期，作废日期)为空   解释；未结束的单
        long thisMonthFirstDay = DateUtils.getThisMonthFirstDay(DateUtils.stringToTime(reportSaleApplyPolicyVo.getQuarterDate(), DateUtils.FORMATE2));
        long thisMonthLastDay = DateUtils.getThisMonthLastDay(DateUtils.stringToTime(reportSaleApplyPolicyVo.getQuarterDate(), DateUtils.FORMATE2));
        conditionList.add(
                REPORT_SALE_APPLY_POLICY.APPROVE_DATE.between(thisMonthFirstDay, thisMonthLastDay).or(
                        REPORT_SALE_APPLY_POLICY.APPLY_INVALID_DATE.between(thisMonthFirstDay, thisMonthLastDay)
                ).or((REPORT_SALE_APPLY_POLICY.APPROVE_DATE.isNull().and(REPORT_SALE_APPLY_POLICY.APPLY_INVALID_DATE.isNull())))
        );


        selectJoinStep.where(conditionList);

        selectJoinStep.offset(reportSaleApplyPolicyVo.getOffset()).limit(reportSaleApplyPolicyVo.getPageSize());
        selectJoinStep.orderBy(REPORT_SALE_APPLY_POLICY.REPORT_SALE_APPLY_POLICY_ID);
        System.out.println(selectJoinStep);
        return selectJoinStep.fetchInto(ReportSaleApplyPolicyBo.class);
    }

    @Override
    public ReportSuspensePo getReportSuspensePo(String applyId, String applyStatus) {
        return this.getDslContext().selectFrom(REPORT_SUSPENSE)
                .where(REPORT_SUSPENSE.APPLY_ID.eq(applyId), REPORT_SUSPENSE.APPLY_STATUS.eq(applyStatus))
                .orderBy(REPORT_SUSPENSE.QUARTER_DATE.desc())
                .limit(1)
                .fetchOneInto(ReportSuspensePo.class);
    }

    @Override
    public List<ReportSalePolicyBo> getSaleApplyPolicyList(List<String> policyNos) {
        return this.getDslContext()
                .select(REPORT_SALE_APPLY_POLICY.POLICY_NO)
                .select(REPORT_SALE_APPLY_POLICY.ACTUAL_PREMIUM.as("actualPremium"))
                .select(REPORT_SALE_APPLY_POLICY.TOTAL_ACTUAL_PREMIUM.as("totalActualPremium"))
                .select(REPORT_SALE_APPLY_POLICY.AMOUNT.as("amount"))
                .select(REPORT_SALE_APPLY_POLICY.UNDERWRITE_END_DATE.as("underwriteEndDate"))
                .from(REPORT_SALE_APPLY_POLICY)
                .where(REPORT_SALE_APPLY_POLICY.POLICY_NO.in(policyNos))
                .fetchInto(ReportSalePolicyBo.class);
    }
}
