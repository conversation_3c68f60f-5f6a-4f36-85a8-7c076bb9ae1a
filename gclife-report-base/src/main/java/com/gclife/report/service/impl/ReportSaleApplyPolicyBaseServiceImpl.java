package com.gclife.report.service.impl;

import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.model.vo.PolicyApplicantVo;


import com.gclife.report.core.jooq.tables.daos.ReportSaleApplyPolicyDao;

import com.gclife.report.core.jooq.tables.pojos.ReportSaleApplyPolicyPo;
import com.gclife.report.core.jooq.tables.pojos.ReportSuspensePo;
import com.gclife.report.dao.ReportSaleApplyPolicyBaseDao;
import com.gclife.report.model.bo.ReportGroupRenewalBo;
import com.gclife.report.model.bo.ReportSaleApplyPolicyBo;
import com.gclife.report.model.bo.ReportSalePolicyBo;
import com.gclife.report.model.vo.ReportSaleApplyPolicyVo;
import com.gclife.report.service.ReportSaleApplyPolicyBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.EN_US;
import static com.gclife.common.model.config.TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY;
import static com.gclife.common.model.config.TerminologyTypeEnum.PRODUCT_PRODUCT_LEVEL;

/**
 * @program: gclife-report-service
 * @description:
 * @author: baizhongying
 * @create: 2021-02-03 13:59
 **/
@Slf4j
@Service
public class ReportSaleApplyPolicyBaseServiceImpl implements ReportSaleApplyPolicyBaseService {

    @Autowired
    private ReportSaleApplyPolicyBaseDao reportSaleApplyPolicyBaseDao;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private AgentApi agentApi;

    @Autowired
    private ReportSaleApplyPolicyDao reportSaleApplyPolicyDao;

    @Override
    public List<ReportSaleApplyPolicyBo> querySaleApplyPolicyPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        String keyword = reportSaleApplyPolicyVo.getKeyword();
        if (AssertUtils.isNotEmpty(keyword)) {
            ResultObject<List<AgentKeyWordResponse>> agentListResultObject = agentApi.agentsVagueGet(keyword);
            List<AgentKeyWordResponse> agentKeyWordResponseList = agentListResultObject.getData();
            if (AssertUtils.isNotNull(agentKeyWordResponseList)) {
                List<String> agentIdList = agentKeyWordResponseList.stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
                reportSaleApplyPolicyVo.setAgentIdList(agentIdList);
            }
        }
        List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBoList = reportSaleApplyPolicyBaseDao.querySaleApplyPolicyPageList(reportSaleApplyPolicyVo);

        return reportSaleApplyPolicyBoList;
    }

    @Override
    public List<ReportSaleApplyPolicyBo> queryReceiptPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        String keyword = reportSaleApplyPolicyVo.getKeyword();
        if (AssertUtils.isNotEmpty(keyword)) {
            ResultObject<List<AgentKeyWordResponse>> agentListResultObject = agentApi.agentsVagueGet(keyword);
            List<AgentKeyWordResponse> agentKeyWordResponseList = agentListResultObject.getData();
            if (AssertUtils.isNotNull(agentKeyWordResponseList)) {
                List<String> agentIdList = agentKeyWordResponseList.stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
                reportSaleApplyPolicyVo.setAgentIdList(agentIdList);
            }
        }
        List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBoList = reportSaleApplyPolicyBaseDao.queryReceiptPageList(reportSaleApplyPolicyVo);
        List<String> agentIdList = reportSaleApplyPolicyBoList.stream().map(ReportSaleApplyPolicyBo::getAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(agentIdList);
        List<AgentResponse> agentResponseList = agentApi.agentsGet(agentApplyQueryRequest).getData();
        if (AssertUtils.isNotEmpty(agentResponseList)) {
            for (ReportSaleApplyPolicyBo reportSaleApplyPolicyBo : reportSaleApplyPolicyBoList) {
                Optional<AgentResponse> agentOptional = agentResponseList.stream().filter(agentResponse -> agentResponse.getAgentId().equals(reportSaleApplyPolicyBo.getAgentId())).findFirst();
                if (agentOptional.isPresent()) {
                    AgentResponse agent = agentOptional.get();
                    reportSaleApplyPolicyBo.setAgentCode(agent.getAgentCode());
                    reportSaleApplyPolicyBo.setAgentName(agent.getAgentName());
                    reportSaleApplyPolicyBo.setAgentMobile(agent.getMobile());
                }
            }
        }
        return reportSaleApplyPolicyBoList;
    }

    @Override
    public List<ReportSaleApplyPolicyBo> querySuspensePageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {
        List<ReportSaleApplyPolicyBo> reportSaleApplyPolicyBoList = reportSaleApplyPolicyBaseDao.querySuspensePageList(reportSaleApplyPolicyVo);

        if(!AssertUtils.isNotEmpty(reportSaleApplyPolicyBoList)){
            return reportSaleApplyPolicyBoList;
        }
        List<String> salesBranchIdList = reportSaleApplyPolicyBoList.stream().map(ReportSaleApplyPolicyBo::getSalesBranchId).distinct().collect(Collectors.toList());
        ResultObject<List<BranchResponse>> branchResultObject = platformBranchApi.branchsPost(salesBranchIdList);
        AssertUtils.isResultObjectError(log, branchResultObject);
        List<BranchResponse> branchResponseList = branchResultObject.getData();

        List<String> agentIdList = reportSaleApplyPolicyBoList.stream().map(ReportSaleApplyPolicyBo::getAgentId).distinct().collect(Collectors.toList());
        AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
        agentApplyQueryRequest.setListAgentId(agentIdList);
        List<AgentResponse> agentResponseList = agentApi.agentsGet(agentApplyQueryRequest).getData();

        if (AssertUtils.isNotEmpty(agentResponseList)||AssertUtils.isNotEmpty(branchResponseList)) {
            for (ReportSaleApplyPolicyBo reportSaleApplyPolicyBo : reportSaleApplyPolicyBoList) {
                if(AssertUtils.isNotEmpty(agentResponseList)){
                    Optional<AgentResponse> agentOptional = agentResponseList.stream().filter(agentResponse -> agentResponse.getAgentId().equals(reportSaleApplyPolicyBo.getAgentId())).findFirst();
                    if (agentOptional.isPresent()) {
                        AgentResponse agent = agentOptional.get();
                        reportSaleApplyPolicyBo.setAgentCode(agent.getAgentCode());
                        reportSaleApplyPolicyBo.setAgentName(agent.getAgentName());
                        reportSaleApplyPolicyBo.setAgentMobile(agent.getMobile());
                    }
                }
                if(AssertUtils.isNotEmpty(branchResponseList)){
                    branchResponseList.stream().filter(branchResponse -> branchResponse.getBranchId().equals(reportSaleApplyPolicyBo.getSalesBranchId())).findFirst().ifPresent(branchResponse -> {
                        if(AssertUtils.isNotNull(branchResponse.getBranchBank())){
                            reportSaleApplyPolicyBo.setSalesBranchCode(branchResponse.getBranchBank().getBankExtNo());
                        }
                        reportSaleApplyPolicyBo.setSalesBranchName(branchResponse.getBranchName());
                    });
                }
            }
        }
        return reportSaleApplyPolicyBoList;
    }

    @Override
    public List<ReportSaleApplyPolicyBo> querySyncSuspense(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo) {

        return reportSaleApplyPolicyBaseDao.querySyncSuspense( reportSaleApplyPolicyVo);
    }

    @Override
    public ReportSuspensePo getReportSuspensePo(String applyId, String applyStatus) {
        return reportSaleApplyPolicyBaseDao.getReportSuspensePo(applyId, applyStatus);
    }

    @Override
    public List<ReportSalePolicyBo> getSaleApplyPolicyList(List<String> policyNos) {
        return reportSaleApplyPolicyBaseDao.getSaleApplyPolicyList(policyNos);
    }
    public ReportSaleApplyPolicyPo getRemarkList(String saleId) {
        return reportSaleApplyPolicyDao.findById(saleId);

    }
}
