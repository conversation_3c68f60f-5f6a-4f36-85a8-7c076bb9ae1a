package com.gclife.report.service.impl;

import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.report.core.jooq.tables.daos.*;
import com.gclife.report.core.jooq.tables.pojos.*;
import com.gclife.report.dao.ReportBaseDao;
import com.gclife.report.model.bo.ReportPaymentBo;
import com.gclife.report.service.ReportBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 18-11-7
 * description:
 */
@Service
public class ReportBaseServiceImpl extends BaseBusinessServiceImpl implements ReportBaseService {
    @Autowired
    private ReportPaymentDao reportPaymentDao;
    @Autowired
    private ReportCollectDao reportCollectDao;
    @Autowired
    private ReportCustomerDao reportCustomerDao;
    @Autowired
    private ReportInsuredDao reportInsuredDao;
    @Autowired
    private ReportPolicyDao reportPolicyDao;
    @Autowired
    private ReportRegulatoryPolicyDao reportRegulatoryPolicyDao;
    @Autowired
    private ReportBaseDao reportBaseDao;
    @Autowired
    private ReportBatchDao reportBatchDao;
    @Autowired
    private ReportRenewalDao reportRenewalDao;
    @Autowired
    private ReportRenewalCoverageDao reportRenewalCoverageDao;
    @Autowired
    private ReportCustomerAgentDao reportCustomerAgentDao;
    @Autowired
    private ReportGroupPolicyDao reportGroupPolicyDao;
    @Autowired
    private ReportPolicyCoverageDao reportPolicyCoverageDao;
    @Autowired
    private ReportPolicyCoverageLevelDao reportPolicyCoverageLevelDao;
    @Autowired
    private ReportPolicyInsuredDao reportPolicyInsuredDao;
    @Autowired
    private ReportPolicyCoverageDutyDao reportPolicyCoverageDutyDao;
    @Autowired
    private ReportClaimDao reportClaimDao;
    @Autowired
    private ReportAgentDao reportAgentDao;
    @Autowired
    private ReportReturnVisitDao reportReturnVisitDao;
    @Autowired
    private ReportEndorseDao reportEndorseDao;
    @Autowired
    private ReportEndorsePaymentDao reportEndorsePaymentDao;
    @Autowired
    private ReportGroupInstallmentDao reportGroupInstallmentDao;
    @Autowired
    private SystemWarningDao systemWarningDao;

    /**
     * 保存财务报表
     *
     * @param reportPaymentPos 财务报表
     * @param userId           用户ID
     */
    @Override
    public void saveReportPayment(List<ReportPaymentBo> reportPaymentPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportPaymentPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<ReportPaymentPo> insertData = new ArrayList<>();
        List<ReportPaymentPo> updateData = new ArrayList<>();
        reportPaymentPos.forEach(reportPaymentPo -> {
            if (AssertUtils.isNotEmpty(reportPaymentPo.getReportPaymentId())) {
                reportPaymentPo.setUpdateUserId(userId);
                reportPaymentPo.setUpdateDate(currentTime);
                updateData.add(reportPaymentPo);
            } else {
                reportPaymentPo.setReportPaymentId(UUIDUtils.getUUIDShort());
                reportPaymentPo.setCashTransactionNo("CH" + UUIDUtils.getUUIDShort());
                reportPaymentPo.setCreateUserId(userId);
                reportPaymentPo.setCreateDate(currentTime);
                reportPaymentPo.setUpdateUserId(userId);
                reportPaymentPo.setUpdateDate(currentTime);
                reportPaymentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportPaymentPo);
            }
        });
        reportPaymentDao.insert(insertData);
        reportPaymentDao.update(updateData);
    }

    /**
     * 保存报表统计表
     *
     * @param reportCollectPo 报表统计
     * @param userId          用户ID
     */
    @Override
    public void saveReportCollect(ReportCollectPo reportCollectPo, String userId) {
        Long currentTime = DateUtils.getCurrentTime();
        if (!AssertUtils.isNotEmpty(reportCollectPo.getReportCollectId())) {
            reportCollectPo.setReportCollectId(UUIDUtils.getUUIDShort());
            reportCollectPo.setCreateDate(currentTime);
            reportCollectPo.setCreateUserId(userId);
            reportCollectPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            reportCollectDao.insert(reportCollectPo);
        } else {
            reportCollectPo.setUpdateDate(currentTime);
            reportCollectPo.setUpdateUserId(userId);
            reportCollectDao.update(reportCollectPo);
        }
    }

    /**
     * 保存客户报表
     *
     * @param reportCustomerPos 客户报表
     * @param userId            用户ID
     */
    @Override
    public void saveReportCustomer(List<ReportCustomerPo> reportCustomerPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportCustomerPos)) {
            return;
        }
        //此处不设置创建时间更新时间和有效标识，沿用保单投保人的字段
        List<ReportCustomerPo> insertData = new ArrayList<>();
        List<ReportCustomerPo> updateData = new ArrayList<>();
        reportCustomerPos.forEach(reportCustomerPo -> {
            if (AssertUtils.isNotEmpty(reportCustomerPo.getReportCustomerId())) {
                updateData.add(reportCustomerPo);
            } else {
                reportCustomerPo.setReportCustomerId(UUIDUtils.getUUIDShort());
                insertData.add(reportCustomerPo);
            }
        });
        reportCustomerDao.insert(insertData);
        reportCustomerDao.update(updateData);
    }

    /**
     * 保存被保人资料
     *
     * @param reportInsuredPos 被保人资料
     */
    @Override
    public void saveReportInsured(List<ReportInsuredPo> reportInsuredPos) {
        if (!AssertUtils.isNotEmpty(reportInsuredPos)) {
            return;
        }
        //此处不设置创建时间更新时间和有效标识，沿用保单被保人的字段
        List<ReportInsuredPo> insertData = new ArrayList<>();
        List<ReportInsuredPo> updateData = new ArrayList<>();
        reportInsuredPos.forEach(reportInsuredPo -> {
            if (AssertUtils.isNotEmpty(reportInsuredPo.getReportInsuredId())) {
                updateData.add(reportInsuredPo);
            } else {
                reportInsuredPo.setReportInsuredId(UUIDUtils.getUUIDShort());
                insertData.add(reportInsuredPo);
            }
        });
        reportInsuredDao.insert(insertData);
        reportInsuredDao.update(updateData);
    }

    /**
     * 保存续期报表
     *
     * @param reportRenewalPos 续期报表
     * @param userId           用户ID
     */
    @Override
    public void saveReportRenewal(List<ReportRenewalPo> reportRenewalPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportRenewalPos)) {
            return;
        }
        List<ReportRenewalPo> insertData = new ArrayList<>();
        List<ReportRenewalPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportRenewalPos.forEach(reportRenewalPo -> {
            if (AssertUtils.isNotEmpty(reportRenewalPo.getReportRenewalId())) {
                reportRenewalPo.setUpdateDate(currentTime);
                reportRenewalPo.setUpdateUserId(userId);
                updateData.add(reportRenewalPo);

            } else {
                reportRenewalPo.setReportRenewalId(UUIDUtils.getUUIDShort());
                reportRenewalPo.setCreateUserId(userId);
                reportRenewalPo.setCreateDate(currentTime);
                reportRenewalPo.setUpdateDate(currentTime);
                reportRenewalPo.setUpdateUserId(userId);
                insertData.add(reportRenewalPo);
            }
        });
        reportRenewalDao.insert(insertData);
        reportRenewalDao.update(updateData);
    }

    /**
     * 保存保单报表
     *
     * @param reportPolicyPos 保单报表
     * @param userId          用户ID
     */
    @Override
    public void saveReportPolicy(List<ReportPolicyPo> reportPolicyPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportPolicyPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<ReportPolicyPo> insertData = new ArrayList<>();
        List<ReportPolicyPo> updateData = new ArrayList<>();
        reportPolicyPos.forEach(reportPolicyPo -> {
            if (AssertUtils.isNotEmpty(reportPolicyPo.getReportPolicyId())) {
                reportPolicyPo.setUpdateDate(currentTime);
                reportPolicyPo.setUpdateUserId(userId);
                updateData.add(reportPolicyPo);
            } else {
                reportPolicyPo.setReportPolicyId(UUIDUtils.getUUIDShort());
                reportPolicyPo.setCreateUserId(userId);
                reportPolicyPo.setCreateDate(currentTime);
                reportPolicyPo.setUpdateDate(currentTime);
                reportPolicyPo.setUpdateUserId(userId);
                reportPolicyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportPolicyPo);
            }
        });
        reportPolicyDao.insert(insertData);
        reportPolicyDao.update(updateData);
    }

    /**
     * 保存监管保单报表
     *
     * @param reportRegulatoryPolicyPos 监管保单报表
     * @param userId                    用户ID
     */
    @Override
    public void saveReportRegulatoryPolicy(List<ReportRegulatoryPolicyPo> reportRegulatoryPolicyPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportRegulatoryPolicyPos)) {
            return;
        }
        Long currentTime = DateUtils.getCurrentTime();
        List<ReportRegulatoryPolicyPo> insertData = new ArrayList<>();
        List<ReportRegulatoryPolicyPo> updateData = new ArrayList<>();

        reportRegulatoryPolicyPos.forEach(reportRegulatoryPolicyPo -> {
            if (AssertUtils.isNotEmpty(reportRegulatoryPolicyPo.getReportRegulatoryPolicyId())) {
                reportRegulatoryPolicyPo.setUpdateDate(currentTime);
                reportRegulatoryPolicyPo.setUpdateUserId(userId);
                updateData.add(reportRegulatoryPolicyPo);
            } else {
                reportRegulatoryPolicyPo.setReportRegulatoryPolicyId(UUIDUtils.getUUIDShort());
                reportRegulatoryPolicyPo.setCreateUserId(userId);
                reportRegulatoryPolicyPo.setCreateDate(currentTime);
                reportRegulatoryPolicyPo.setUpdateDate(currentTime);
                reportRegulatoryPolicyPo.setUpdateUserId(userId);
                reportRegulatoryPolicyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportRegulatoryPolicyPo);
            }
        });
        reportRegulatoryPolicyDao.insert(insertData);
        reportRegulatoryPolicyDao.update(updateData);
    }

    @Override
    public void deleteReport(String reportType, String startDate, Long currentTime, List<String> businessIds) {
        reportBaseDao.deleteReport(reportType, startDate, currentTime, businessIds);
    }

    /**
     * 保存报表批处理表
     *
     * @param reportBatchPo 报表批处理
     */
    @Override
    public void saveReportBatch(ReportBatchPo reportBatchPo) {
        if (AssertUtils.isNotEmpty(reportBatchPo.getReportBatchId())) {
            reportBatchDao.update(reportBatchPo);
        }
    }

    @Override
    public ReportBatchPo queryOneReportBatchByType(String reportType) {
        return reportBaseDao.queryOneReportBatch(reportType);
    }

    @Override
    public void saveReportCustomerAgent(ReportCustomerAgentPo reportCustomerAgentPo) {
        Long currentTime = DateUtils.getCurrentTime();
        if (!AssertUtils.isNotEmpty(reportCustomerAgentPo.getCustomerAgentId())) {
            reportCustomerAgentPo.setCustomerAgentId(UUIDUtils.getUUIDShort());
            reportCustomerAgentPo.setCreatedDate(currentTime);
            reportCustomerAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
            reportCustomerAgentDao.insert(reportCustomerAgentPo);
        } else {
            reportCustomerAgentPo.setUpdatedDate(currentTime);
            reportCustomerAgentDao.update(reportCustomerAgentPo);
        }
    }

    @Override
    public ReportCustomerAgentPo queryReportCustomerAgentByPolicyId(String policyId, String customerType) {
        return reportBaseDao.queryReportCustomerAgentByPolicyId(policyId, customerType);
    }

    @Override
    public void saveReportGroupPolicy(List<ReportGroupPolicyPo> reportGroupPolicyPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportGroupPolicyPos)) {
            return;
        }
        List<ReportGroupPolicyPo> insertData = new ArrayList<>();
        List<ReportGroupPolicyPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportGroupPolicyPos.forEach(reportGroupPolicyPo -> {
            if (AssertUtils.isNotEmpty(reportGroupPolicyPo.getReportGroupPolicyId())) {
                reportGroupPolicyPo.setUpdatedDate(currentTime);
                reportGroupPolicyPo.setUpdatedUserId(userId);
                updateData.add(reportGroupPolicyPo);
            } else {
                reportGroupPolicyPo.setReportGroupPolicyId(UUIDUtils.getUUIDShort());
                reportGroupPolicyPo.setCreatedUserId(userId);
                reportGroupPolicyPo.setCreatedDate(currentTime);
                reportGroupPolicyPo.setUpdatedDate(currentTime);
                reportGroupPolicyPo.setUpdatedUserId(userId);
                reportGroupPolicyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportGroupPolicyPo);
            }
        });
        reportGroupPolicyDao.insert(insertData);
        reportGroupPolicyDao.update(updateData);
    }

    @Override
    public void saveReportPolicyCoverage(List<ReportPolicyCoveragePo> reportPolicyCoveragePos, String userId) {
        if (!AssertUtils.isNotEmpty(reportPolicyCoveragePos)) {
            return;
        }
        List<ReportPolicyCoveragePo> insertData = new ArrayList<>();
        List<ReportPolicyCoveragePo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportPolicyCoveragePos.forEach(reportPolicyCoveragePo -> {
            if (AssertUtils.isNotEmpty(reportPolicyCoveragePo.getReportPolicyCoverageId())) {
                reportPolicyCoveragePo.setUpdatedDate(currentTime);
                reportPolicyCoveragePo.setUpdatedUserId(userId);
                updateData.add(reportPolicyCoveragePo);
            } else {
                reportPolicyCoveragePo.setReportPolicyCoverageId(UUIDUtils.getUUIDShort());
                reportPolicyCoveragePo.setCreatedUserId(userId);
                reportPolicyCoveragePo.setCreatedDate(currentTime);
                reportPolicyCoveragePo.setUpdatedDate(currentTime);
                reportPolicyCoveragePo.setUpdatedUserId(userId);
                reportPolicyCoveragePo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportPolicyCoveragePo);
            }
        });
        reportPolicyCoverageDao.insert(insertData);
        reportPolicyCoverageDao.update(updateData);
    }

    /**
     * 保存理赔报表
     *
     * @param reportClaimPos 理赔报表
     */
    @Override
    public void saveReportClaim(List<ReportClaimPo> reportClaimPos) {
        if (!AssertUtils.isNotEmpty(reportClaimPos)) {
            return;
        }
        List<ReportClaimPo> insertData = new ArrayList<>();
        List<ReportClaimPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportClaimPos.forEach(reportClaimPo -> {
            if (AssertUtils.isNotEmpty(reportClaimPo.getReportClaimId())) {
                reportClaimPo.setUpdatedDate(currentTime);
                updateData.add(reportClaimPo);
            } else {
                reportClaimPo.setReportClaimId(UUIDUtils.getUUIDShort());
                reportClaimPo.setCreatedDate(currentTime);
                reportClaimPo.setUpdatedDate(currentTime);
                reportClaimPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportClaimPo);
            }
        });
        reportClaimDao.insert(insertData);
        reportClaimDao.update(updateData);
    }

    /**
     * 保存业务员报表
     *
     * @param reportAgentPos 业务员报表
     */
    @Override
    public void saveReportAgent(List<ReportAgentPo> reportAgentPos) {
        if (!AssertUtils.isNotEmpty(reportAgentPos)) {
            return;
        }
        List<ReportAgentPo> insertData = new ArrayList<>();
        List<ReportAgentPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportAgentPos.forEach(reportAgentPo -> {
            if (AssertUtils.isNotEmpty(reportAgentPo.getReportAgentId())) {
                reportAgentPo.setUpdatedDate(currentTime);
                updateData.add(reportAgentPo);
            } else {
                reportAgentPo.setReportAgentId(UUIDUtils.getUUIDShort());
                reportAgentPo.setCreatedDate(currentTime);
                reportAgentPo.setUpdatedDate(currentTime);
                reportAgentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportAgentPo);
            }
        });
        reportAgentDao.insert(insertData);
        reportAgentDao.update(updateData);
    }

    /**
     * 保存客户回访报表
     *
     * @param reportReturnVisitPos 客户回访报表
     */
    @Override
    public void saveReportReturnVisit(List<ReportReturnVisitPo> reportReturnVisitPos) {
        if (!AssertUtils.isNotEmpty(reportReturnVisitPos)) {
            return;
        }
        List<ReportReturnVisitPo> insertData = new ArrayList<>();
        List<ReportReturnVisitPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportReturnVisitPos.forEach(reportReturnVisitPo -> {
            if (AssertUtils.isNotEmpty(reportReturnVisitPo.getReportReturnVisitId())) {
                reportReturnVisitPo.setUpdatedDate(currentTime);
                updateData.add(reportReturnVisitPo);
            } else {
                reportReturnVisitPo.setReportReturnVisitId(UUIDUtils.getUUIDShort());
                reportReturnVisitPo.setCreatedDate(currentTime);
                reportReturnVisitPo.setUpdatedDate(currentTime);
                reportReturnVisitPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportReturnVisitPo);
            }
        });
        reportReturnVisitDao.insert(insertData);
        reportReturnVisitDao.update(updateData);
    }

    /**
     * 保存保全报表
     *
     * @param reportEndorsePos 保全报表
     */
    @Override
    public void saveReportEndorse(List<ReportEndorsePo> reportEndorsePos) {
        if (!AssertUtils.isNotEmpty(reportEndorsePos)) {
            return;
        }
        List<ReportEndorsePo> insertData = new ArrayList<>();
        List<ReportEndorsePo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportEndorsePos.forEach(reportEndorsePo -> {
            if (AssertUtils.isNotEmpty(reportEndorsePo.getReportEndorseId())) {
                reportEndorsePo.setUpdatedDate(currentTime);
                updateData.add(reportEndorsePo);
            } else {
                reportEndorsePo.setReportEndorseId(UUIDUtils.getUUIDShort());
                reportEndorsePo.setCreatedDate(currentTime);
                reportEndorsePo.setUpdatedDate(currentTime);
                reportEndorsePo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportEndorsePo);
            }
        });
        reportEndorseDao.insert(insertData);
        reportEndorseDao.update(updateData);
    }

    /**
     * 保存保全缴费信息
     *
     * @param reportEndorsePaymentPos 保全缴费信息
     */
    @Override
    public void saveReportEndorsePayment(List<ReportEndorsePaymentPo> reportEndorsePaymentPos) {
        if (!AssertUtils.isNotEmpty(reportEndorsePaymentPos)) {
            return;
        }
        List<ReportEndorsePaymentPo> insertData = new ArrayList<>();
        List<ReportEndorsePaymentPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportEndorsePaymentPos.forEach(reportEndorsePaymentPo -> {
            if (AssertUtils.isNotEmpty(reportEndorsePaymentPo.getReportEndorsePaymentId())) {
                reportEndorsePaymentPo.setUpdatedDate(currentTime);
                updateData.add(reportEndorsePaymentPo);
            } else {
                reportEndorsePaymentPo.setReportEndorsePaymentId(UUIDUtils.getUUIDShort());
                reportEndorsePaymentPo.setCreatedDate(currentTime);
                reportEndorsePaymentPo.setUpdatedDate(currentTime);
                reportEndorsePaymentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportEndorsePaymentPo);
            }
        });
        reportEndorsePaymentDao.insert(insertData);
        reportEndorsePaymentDao.update(updateData);
    }

    @Override
    public void saveReportRenewalCoverage(List<ReportRenewalCoveragePo> reportRenewalCoveragePos, String userId) {
        if (!AssertUtils.isNotEmpty(reportRenewalCoveragePos)) {
            return;
        }
        List<ReportRenewalCoveragePo> insertData = new ArrayList<>();
        List<ReportRenewalCoveragePo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportRenewalCoveragePos.forEach(reportRenewalCoverage -> {
            if (AssertUtils.isNotEmpty(reportRenewalCoverage.getReportRenewalCoverageId())) {
                reportRenewalCoverage.setUpdatedDate(currentTime);
                reportRenewalCoverage.setUpdatedUserId(userId);
                updateData.add(reportRenewalCoverage);

            } else {
                reportRenewalCoverage.setReportRenewalCoverageId(UUIDUtils.getUUIDShort());
                reportRenewalCoverage.setCreatedUserId(userId);
                reportRenewalCoverage.setCreatedDate(currentTime);
                reportRenewalCoverage.setUpdatedDate(currentTime);
                reportRenewalCoverage.setUpdatedUserId(userId);
                reportRenewalCoverage.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(reportRenewalCoverage);
            }
        });
        reportRenewalCoverageDao.insert(insertData);
        reportRenewalCoverageDao.update(updateData);
    }
    @Override
    public void saveReportPolicyCoverageDuty(List<ReportPolicyCoverageDutyPo> reportPolicyCoverageDutyPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportPolicyCoverageDutyPos)) {
            return;
        }
        List<ReportPolicyCoverageDutyPo> insertData = new ArrayList<>();
        List<ReportPolicyCoverageDutyPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportPolicyCoverageDutyPos.forEach(coverageDutyPo -> {
            if (AssertUtils.isNotEmpty(coverageDutyPo.getReportCoverageDutyId())) {
                coverageDutyPo.setUpdatedDate(currentTime);
                coverageDutyPo.setUpdatedUserId(userId);
                updateData.add(coverageDutyPo);
            } else {
                coverageDutyPo.setReportCoverageDutyId(UUIDUtils.getUUIDShort());
                coverageDutyPo.setCreatedUserId(userId);
                coverageDutyPo.setCreatedDate(currentTime);
                coverageDutyPo.setUpdatedDate(currentTime);
                coverageDutyPo.setUpdatedUserId(userId);
                coverageDutyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(coverageDutyPo);
            }
        });
        reportPolicyCoverageDutyDao.insert(insertData);
        reportPolicyCoverageDutyDao.update(updateData);
    }

    @Override
    public void saveReportPolicyCoverageLevel(List<ReportPolicyCoverageLevelPo> reportPolicyCoverageLevelPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportPolicyCoverageLevelPos)) {
            return;
        }
        List<ReportPolicyCoverageLevelPo> insertData = new ArrayList<>();
        List<ReportPolicyCoverageLevelPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportPolicyCoverageLevelPos.forEach(coverageLevelPo -> {
            if (AssertUtils.isNotEmpty(coverageLevelPo.getReportCoverageLevelId())) {
                coverageLevelPo.setUpdatedDate(currentTime);
                coverageLevelPo.setUpdatedUserId(userId);
                updateData.add(coverageLevelPo);
            } else {
                coverageLevelPo.setReportCoverageLevelId(UUIDUtils.getUUIDShort());
                coverageLevelPo.setCreatedUserId(userId);
                coverageLevelPo.setCreatedDate(currentTime);
                coverageLevelPo.setUpdatedDate(currentTime);
                coverageLevelPo.setUpdatedUserId(userId);
                coverageLevelPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(coverageLevelPo);
            }
        });
        reportPolicyCoverageLevelDao.insert(insertData);
        reportPolicyCoverageLevelDao.update(updateData);
    }

    @Override
    public void saveReportPolicyInsured(List<ReportPolicyInsuredPo> policyInsuredPoList, String userId) {
        if (!AssertUtils.isNotEmpty(policyInsuredPoList)) {
            return;
        }
        List<ReportPolicyInsuredPo> insertData = new ArrayList<>();
        List<ReportPolicyInsuredPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        policyInsuredPoList.forEach(coverageLevelPo -> {
            if (AssertUtils.isNotEmpty(coverageLevelPo.getReportInsuredId())) {
                coverageLevelPo.setUpdatedDate(currentTime);
                coverageLevelPo.setUpdatedUserId(userId);
                updateData.add(coverageLevelPo);
            } else {
                coverageLevelPo.setReportInsuredId(UUIDUtils.getUUIDShort());
                coverageLevelPo.setCreatedUserId(userId);
                coverageLevelPo.setCreatedDate(currentTime);
                coverageLevelPo.setUpdatedDate(currentTime);
                coverageLevelPo.setUpdatedUserId(userId);
                coverageLevelPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                insertData.add(coverageLevelPo);
            }
        });
        reportPolicyInsuredDao.insert(insertData);
        reportPolicyInsuredDao.update(updateData);
    }

    @Override
    public void saveReportGroupInstall(List<ReportGroupInstallmentPo> reportGroupInstallmentPos, String userId) {
        if (!AssertUtils.isNotEmpty(reportGroupInstallmentPos)) {
            return;
        }
        List<ReportGroupInstallmentPo> insertData = new ArrayList<>();
        List<ReportGroupInstallmentPo> updateData = new ArrayList<>();
        Long currentTime = DateUtils.getCurrentTime();
        reportGroupInstallmentPos.forEach(reportGroupInstallmentPo -> {
            if (AssertUtils.isNotEmpty(reportGroupInstallmentPo.getReportGroupRenewalId())) {
                reportGroupInstallmentPo.setUpdatedDate(currentTime);
                reportGroupInstallmentPo.setUpdatedUserId(userId);
                updateData.add(reportGroupInstallmentPo);

            } else {
                reportGroupInstallmentPo.setReportGroupRenewalId(UUIDUtils.getUUIDShort());
                reportGroupInstallmentPo.setCreatedUserId(userId);
                reportGroupInstallmentPo.setCreatedDate(currentTime);
                reportGroupInstallmentPo.setUpdatedDate(currentTime);
                reportGroupInstallmentPo.setUpdatedUserId(userId);
                insertData.add(reportGroupInstallmentPo);
            }
        });
        reportGroupInstallmentDao.insert(insertData);
        reportGroupInstallmentDao.update(updateData);
    }

    @Override
    public void saveSystemWarning(SystemWarningPo systemWarningPo, String userId) {
        if (!AssertUtils.isNotNull(systemWarningPo)) {
            return;
        }

        Long currentTime = DateUtils.getCurrentTime();
        systemWarningPo.setSystemWarningId(UUIDUtils.getUUIDShort());
        systemWarningPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        systemWarningPo.setCreatedUserId(userId);
        systemWarningPo.setCreatedDate(currentTime);
        systemWarningPo.setUpdatedDate(currentTime);
        systemWarningPo.setUpdatedUserId(userId);
        systemWarningDao.insert(systemWarningPo);

    }
}
