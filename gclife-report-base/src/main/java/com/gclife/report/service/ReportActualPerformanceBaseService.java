package com.gclife.report.service;

import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.model.bo.ReportCashTransactionBo;
import com.gclife.report.model.vo.ReportCashTransctionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/6/19 3:31 下午
 */
public interface ReportActualPerformanceBaseService {
    /**
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<ActualPerformanceReportBo> queryByDate(long startTime, long endTime);

    List<ReportCashTransactionBo> queryCashTransactionPageList(ReportCashTransctionVO reportCashTransctionVO);

    List<ReportCashTransactionBo> exportCashTransaction(ReportCashTransctionVO reportCashTransctionVO);

    List<ReportCashTransactionBo> getCashTransactionMsg(long lastMonthStr, long lastMonthStr1);
}
