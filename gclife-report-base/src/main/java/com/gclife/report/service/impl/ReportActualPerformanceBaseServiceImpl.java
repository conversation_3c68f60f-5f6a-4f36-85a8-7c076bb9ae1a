package com.gclife.report.service.impl;

import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.dao.ReportActualPerformanceBaseDao;
import com.gclife.report.model.bo.ReportCashTransactionBo;
import com.gclife.report.model.vo.ReportCashTransctionVO;
import com.gclife.report.service.ReportActualPerformanceBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2020/6/19 3:31 下午
 */
@Service
public class ReportActualPerformanceBaseServiceImpl implements ReportActualPerformanceBaseService {

    @Autowired
    private ReportActualPerformanceBaseDao reportActualPerformanceBaseDao;
    @Override
    public List<ActualPerformanceReportBo> queryByDate(long startTime, long endTime) {

        return reportActualPerformanceBaseDao.queryByDate(startTime,endTime);
    }

    @Override
    public List<ReportCashTransactionBo> queryCashTransactionPageList(ReportCashTransctionVO reportCashTransctionVO) {
        return reportActualPerformanceBaseDao.queryCashTransactionPageList(reportCashTransctionVO);
    }

    @Override
    public List<ReportCashTransactionBo> exportCashTransaction(ReportCashTransctionVO reportCashTransctionVO) {
        return reportActualPerformanceBaseDao.exportCashTransaction(reportCashTransctionVO);
    }

    @Override
    public List<ReportCashTransactionBo> getCashTransactionMsg(long startDate, long endDate) {
        return reportActualPerformanceBaseDao.getCashTransactionMsg(startDate, endDate);
    }
}
