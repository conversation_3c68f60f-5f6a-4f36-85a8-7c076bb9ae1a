package com.gclife.report.service;

import com.gclife.report.core.jooq.tables.pojos.ReportSaleApplyPolicyPo;
import com.gclife.report.core.jooq.tables.pojos.ReportSuspensePo;
import com.gclife.report.model.bo.ReportSaleApplyPolicyBo;
import com.gclife.report.model.bo.ReportSalePolicyBo;
import com.gclife.report.model.vo.ReportSaleApplyPolicyVo;

import java.util.List;

/**
 * @program: gclife-report-service
 * @description:
 * @author: baizhongying
 * @create: 2021-02-03 13:58
 **/
public interface ReportSaleApplyPolicyBaseService {
    /**
     * 查询销售报表列表
     *
     * @param reportSaleApplyPolicyVo
     * @return
     */
    List<ReportSaleApplyPolicyBo> querySaleApplyPolicyPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    /**
     * 查询回执追踪报表
     *
     * @param reportSaleApplyPolicyVo
     * @return
     */
    List<ReportSaleApplyPolicyBo> queryReceiptPageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    /**
     * 查询预缴保费数据
     * @param reportSaleApplyPolicyVo
     * @return
     */
    List<ReportSaleApplyPolicyBo> querySuspensePageList(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    /**
     * 查询预缴保费数据
     * @return
     */
    List<ReportSaleApplyPolicyBo> querySyncSuspense(ReportSaleApplyPolicyVo reportSaleApplyPolicyVo);

    ReportSuspensePo getReportSuspensePo(String applyId, String applyStatus);


    List<ReportSalePolicyBo> getSaleApplyPolicyList(List<String> policyNos);
    /**
     * 获取备注列表
     * @param saleId
     * @return
     */
    ReportSaleApplyPolicyPo getRemarkList(String saleId);
}
