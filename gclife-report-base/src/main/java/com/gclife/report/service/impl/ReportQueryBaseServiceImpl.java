package com.gclife.report.service.impl;

import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.report.core.jooq.tables.pojos.*;
import com.gclife.report.dao.ReportQueryBaseDao;
import com.gclife.report.model.bo.*;
import com.gclife.report.model.vo.*;
import com.gclife.report.service.ReportQueryBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * create 19-1-7
 * description:
 */
@Service
public class ReportQueryBaseServiceImpl extends BaseBusinessServiceImpl implements ReportQueryBaseService {
    @Autowired
    private ReportQueryBaseDao reportQueryBaseDao;

    /**
     * 查询承保清单
     *
     * @param reportPolicyVo 列表参数
     * @param type           是否来自列表
     * @return ReportPolicyBos
     */
    @Override
    public List<ReportPolicyBo> queryListReportPolicy(ReportPolicyVo reportPolicyVo, String type) {
        System.out.println("查询承保清单");
        return reportQueryBaseDao.queryListReportPolicy(reportPolicyVo, type);
    }

    /**
     * 查询监管报表-承保清单
     *
     * @param reportPolicyVo 列表参数
     * @param type           是否来自列表
     * @return ReportPolicyBos
     */
    @Override
    public List<ReportPolicyBo> queryListReportRegulatoryPolicy(ReportPolicyVo reportPolicyVo, String type) {
        return reportQueryBaseDao.queryListReportRegulatoryPolicy(reportPolicyVo, type);
    }

    /**
     * 查询财务报表
     *
     * @param reportPaymentVo 列表参数
     * @param type            是否来自列表
     * @return ReportPaymentBos
     */
    @Override
    public List<ReportPaymentBo> queryListReportPayment(ReportPaymentVo reportPaymentVo, String type) {
        return reportQueryBaseDao.queryListReportPayment(reportPaymentVo, type);
    }

    /**
     * 查询客户报表
     *
     * @param reportCustomerVo 列表参数
     * @param type             是否来自列表
     * @return ReportCustomerBo
     */
    @Override
    public List<ReportCustomerBo> queryListReportCustomer(ReportCustomerVo reportCustomerVo, String type) {
        return reportQueryBaseDao.queryListReportCustomer(reportCustomerVo, type);
    }

    /**
     * 查询续期报表
     *
     * @param reportRenewalVo 列表参数
     * @param type            是否来自列表
     * @return ReportRenewalBos
     */
    @Override
    public List<ReportRenewalBo> queryListReportRenewal(ReportRenewalVo reportRenewalVo, String type) {
        return reportQueryBaseDao.queryListReportRenewal(reportRenewalVo, type);
    }

    /**
     * 查询续保报表
     *
     * @param reportRenewalVo 列表参数
     * @param type            是否来自列表
     * @return ReportRenewalBos
     */
    @Override
    public List<ReportRenewalBo> queryListReportRenewalInsurance(ReportRenewalVo reportRenewalVo, String type) {
        return reportQueryBaseDao.queryListReportRenewalInsurance(reportRenewalVo, type);
    }

    /**
     * 根据续期ID集合查询续期报表
     *
     * @param renewalIds 续期ID集合
     * @return ReportRenewalPo
     */
    @Override
    public List<ReportRenewalPo> queryReportRenewalByRenewalId(List<String> renewalIds) {
        return reportQueryBaseDao.queryReportRenewalByRenewalId(renewalIds);
    }

    /**
     * 根据险种ID集合查询承保报表
     *
     * @param policyIds 险种ID集合
     * @return ReportPolicyPo
     */
    @Override
    public List<ReportPolicyPo> queryReportPolicyByPolicyId(List<String> policyIds) {
        return reportQueryBaseDao.queryReportPolicyByPolicyId(policyIds);
    }

    /**
     * 根据险种ID集合查询监管报表承保报表
     *
     * @param coverageIds 险种ID集合
     * @return ReportRegulatoryPolicyPos
     */
    @Override
    public List<ReportRegulatoryPolicyPo> queryReportRegulatoryPolicyByCoverageId(List<String> coverageIds) {
        return reportQueryBaseDao.queryReportRegulatoryPolicyByCoverageId(coverageIds);
    }

    /**
     * 根据客户ID集合查询投保人资料报表
     *
     * @param customerIds 客户id
     * @return ReportCustomerPos
     */
    @Override
    public List<ReportCustomerPo> queryReportCustomerByCustomerId(List<String> customerIds) {
        return reportQueryBaseDao.queryReportCustomerByCustomerId(customerIds);
    }

    /**
     * 根据客户ID集合查询被保人资料报表
     *
     * @param customerIds 客户id
     * @return ReportInsuredPos
     */
    @Override
    public List<ReportInsuredPo> queryReportInsuredByCustomerId(List<String> customerIds) {
        return reportQueryBaseDao.queryReportInsuredByCustomerId(customerIds);
    }

    /**
     * 根据支付ID集合查询投保人资料报表
     *
     * @param paymentIds 支付id
     * @return ReportPaymentPos
     */
    @Override
    public List<ReportPaymentPo> queryReportPaymentByPaymentId(List<String> paymentIds) {
        return reportQueryBaseDao.queryReportPaymentByPaymentId(paymentIds);
    }

    /**
     * 根据客户ID集合查询投保人资料报表
     *
     * @param receiptIds 收费id
     * @return ReportPaymentPos
     */
    @Override
    public List<ReportPaymentPo> queryReportPaymentByReceiptId(List<String> receiptIds) {
        return reportQueryBaseDao.queryReportPaymentByReceiptId(receiptIds);
    }

    @Override
    public List<ReportPolicyBo> queryReportPolicys(ReportOperationVo reportOperationVo, String agentTypeCode, String policyStatus) {
        return reportQueryBaseDao.queryReportPolicys(reportOperationVo, agentTypeCode, policyStatus);
    }

    @Override
    public List<ReportRenewalBo> queryReportRenewal(ReportOperationVo reportOperationVo, String agentTypeCode, String renewalType, List<String> renewalStatus) {
        return reportQueryBaseDao.queryReportRenewal(reportOperationVo, agentTypeCode, renewalType, renewalStatus);
    }

    @Override
    public List<ReportGroupPolicyPo> queryReportGroupPolicyByPolicyId(List<String> policyIds) {
        return reportQueryBaseDao.queryReportGroupPolicyByPolicyId(policyIds);
    }

    @Override
    public List<ReportPolicyCoveragePo> queryReportPolicyCoveragePo(List<String> policyIds) {
        return reportQueryBaseDao.queryReportPolicyCoveragePo(policyIds);
    }

    @Override
    public List<ReportGroupPolicyBo> queryReportGroupPolicys(ReportOperationVo reportOperationVo, String agentTypeCode, String policyStatus) {
        return reportQueryBaseDao.queryReportGroupPolicys(reportOperationVo, agentTypeCode, policyStatus);
    }

    /**
     * 根据理赔ID集合查询理赔报表
     *
     * @param claimIds 理赔IDs
     * @return ReportClaimPos
     */
    @Override
    public List<ReportClaimPo> queryReportClaimPoByClaimId(List<String> claimIds) {
        return reportQueryBaseDao.queryReportClaimPoByClaimId(claimIds);
    }

    /**
     * 根据业务员ID集合查询业务员报表
     *
     * @param agentIds 业务员IDs
     * @return ReportAgentPos
     */
    @Override
    public List<ReportAgentPo> queryReportAgentPoByAgentId(List<String> agentIds) {
        return reportQueryBaseDao.queryReportAgentPoByAgentId(agentIds);
    }

    /**
     * 根据回访ID集合查询回访报表
     *
     * @param returnVisitIds 回访ID
     * @return ReportReturnVisitPos
     */
    @Override
    public List<ReportReturnVisitPo> queryReportReturnVisitPoById(List<String> returnVisitIds) {
        return reportQueryBaseDao.queryReportReturnVisitPoById(returnVisitIds);
    }

    /**
     * 根据保全ID查询保全报表
     *
     * @param endorseIds 保全ID
     * @return ReportEndorsePos
     */
    @Override
    public List<ReportEndorsePo> queryReportEndorsePoById(List<String> endorseIds) {
        return reportQueryBaseDao.queryReportEndorsePoById(endorseIds);
    }

    /**
     * 查询团险承保清单
     *
     * @param reportPolicyVo 列表参数
     * @return ReportGroupPolicyListBos
     */
    @Override
    public List<ReportGroupPolicyListBo> queryGroupPolicyList(ReportPolicyVo reportPolicyVo) {
        return reportQueryBaseDao.queryGroupPolicyList(reportPolicyVo);
    }

    @Override
    public List<ReportGroupInstallmentPo> listReportGroupInstallByRenewalId(List<String> groupRenewalIds) {
        return reportQueryBaseDao.listReportGroupInstallByRenewalId(groupRenewalIds);
    }

    @Override
    public List<ReportPendingPolicyBo> queryListReportPendingRenewal(ReportPendingRenewalVo pendingRenewalVo,boolean isPaging) {
        return reportQueryBaseDao.queryListReportPendingRenewal(pendingRenewalVo,isPaging);
    }

    @Override
    public List<ReportPendingGroupPolicyBo> queryListReportPendingRenewalGroup(ReportPendingRenewalVo pendingRenewalVo, boolean isPaging) {
        return reportQueryBaseDao.queryListReportPendingRenewalGroup(pendingRenewalVo,isPaging);
    }

    /**
     * 查询保单最后一次缴费（新契约、续期）的实收业绩报表数据
     *
     * @param policyNos 保单ID
     * @return ReportActualPerformancePos
     */
    @Override
    public List<ReportActualPerformancePo> queryPolicyLastPaymentData(List<String> policyNos) {
        return reportQueryBaseDao.queryPolicyLastPaymentData(policyNos);
    }


}
