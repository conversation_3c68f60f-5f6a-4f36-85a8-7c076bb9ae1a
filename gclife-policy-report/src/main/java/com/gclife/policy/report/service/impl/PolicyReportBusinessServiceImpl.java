package com.gclife.policy.report.service.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.api.ApplyPlanApi;
import com.gclife.apply.model.respone.ScratchCardCoverageResponse;
import com.gclife.claim.api.ClaimCoverageApi;
import com.gclife.claim.model.respone.ClaimCoverageAmountBo;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.endorse.api.EndorseApi;
import com.gclife.endorse.api.EndorseReportApi;
import com.gclife.endorse.model.response.ReportEndorseResponse;
import com.gclife.endorse.model.response.ReportInsuredEndorseDetailResponse;
import com.gclife.party.api.CustomerAgentBaseApi;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.request.CustomerBusinessRequest;
import com.gclife.party.model.response.CustomerAgentsResponse;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.policy.core.jooq.tables.daos.PolicyApplicantDao;
import com.gclife.policy.core.jooq.tables.daos.PolicyDao;
import com.gclife.policy.core.jooq.tables.pojos.*;
import com.gclife.policy.dao.PolicyAgentHistoryBaseDao;
import com.gclife.policy.dao.PolicyCoverageHistoryBaseDao;
import com.gclife.policy.dao.PolicyInsuredBaseDao;
import com.gclife.policy.model.bo.*;
import com.gclife.policy.model.config.PolicyTermEnum;
import com.gclife.policy.model.response.PolicyReportResponse;
import com.gclife.policy.report.model.response.*;
import com.gclife.policy.report.service.PolicyReportBusinessService;
import com.gclife.policy.service.base.*;
import com.gclife.product.api.ProductApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.calculate.ApplyRequest;
import com.gclife.product.model.response.insurnce.policy.PolicyCashValueResponse;
import com.gclife.renewal.api.RenewalReportApi;
import com.gclife.renewal.model.response.ReportRenewalResponse;
import com.gclife.report.api.ReportBaseApi;
import com.gclife.report.api.model.response.ActualPerformanceReportBo;
import com.gclife.report.api.model.response.ReserveWithdrawalReportBo;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.common.util.StringUtil.getAgeByBirthday;
import static com.gclife.policy.model.config.PolicyTermEnum.ADD_PREMIUM_OBJECT_CODE.EM_5;
import static com.gclife.policy.model.config.PolicyTermEnum.ADD_PREMIUM_OBJECT_CODE.FER_5;
import static com.gclife.policy.model.config.PolicyTermEnum.BUSINESS_TYPE.REINSTATEMENT;
import static com.gclife.policy.model.config.PolicyTermEnum.COVERAGE_STATUS.EXPIRED;
import static com.gclife.policy.model.config.PolicyTermEnum.COVERAGE_STATUS.INDEMNITY_TERMINATION;
import static com.gclife.policy.model.config.PolicyTermEnum.EXPIRY_PERIOD_UNIT.*;
import static com.gclife.policy.model.config.PolicyTermEnum.INSURED_STATUS.EFFECTIVE;
import static com.gclife.policy.model.config.PolicyTermEnum.INSURED_STATUS.SURRENDER;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_STATUS_FLAG.*;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_GROUP;
import static com.gclife.policy.model.config.PolicyTermEnum.POLICY_TYPE.LIFE_INSURANCE_PERSONAL;
import static com.gclife.policy.model.config.PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON;
import static com.gclife.policy.model.config.PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL;
import static com.gclife.policy.model.config.PolicyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.AGE;
import static com.gclife.policy.model.config.PolicyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.LIFELONG;
import static com.gclife.policy.model.config.PolicyTermEnum.RATINGS_NAME.*;
import static com.gclife.policy.model.config.PolicyTermEnum.YES_NO.NO;
import static com.gclife.policy.model.config.PolicyTermEnum.YES_NO.YES;

/**
 * <AUTHOR>
 * create 18-11-12
 * description:
 */
@Service
public class PolicyReportBusinessServiceImpl extends BaseBusinessServiceImpl implements PolicyReportBusinessService {
    @Autowired
    private PolicyBaseService policyBaseService;
    @Autowired
    private PolicyDao policyDao;
    @Autowired
    private ApplyPlanApi applyPlanApi;
    @Autowired
    private PolicyInsuredBaseService policyInsuredBaseService;
    @Autowired
    private RenewalReportApi renewalReportApi;
    @Autowired
    private PolicyPaymentBaseService policyPaymentBaseService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PolicyCoverageBaseService policyCoverageBaseService;
    @Autowired
    private PolicyPremiumBaseService policyPremiumBaseService;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private PolicyApplicantDao policyApplicantDao;
    @Autowired
    private PolicyInsuredBaseDao policyInsuredBaseDao;
    @Autowired
    private ClaimCoverageApi claimCoverageApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ReportBaseApi reportBaseApi;
    @Autowired
    private EndorseReportApi endorseReportApi;
    @Autowired
    private EndorseApi endorseApi;
    @Autowired
    private PolicyAgentHistoryBaseDao policyAgentHistoryBaseDao;
    @Autowired
    private PolicyCoverageHistoryBaseDao policyCoverageHistoryBaseDao;

    @Autowired
    private CustomerAgentBaseApi customerAgentBaseApi;

    /**
     * 查询投保单信息跟投保人信息（收付费明细报表）
     *
     * @param businessNo payment关联投保单id
     */
    @Override
    public ResultObject<List<PolicyReportResponse>> queryListPolicyReportBo(List<String> businessNo) {
        ResultObject<List<PolicyReportResponse>> resultObject = new ResultObject<>();
        List<PolicyReportBo> policyReportBos = policyBaseService.queryPolicyReport(businessNo);
        if (!AssertUtils.isNotEmpty(policyReportBos)) {
            return resultObject;
        }
        List<PolicyReportResponse> policyListResponses = (List<PolicyReportResponse>) this.converterList(policyReportBos, new TypeToken<List<PolicyReportResponse>>() {
        }.getType());
        resultObject.setData(policyListResponses);
        return resultObject;
    }

    /**
     * 查询业务报表（投保人资料）
     *
     * @return
     */
    @Override
    public ResultObject<List<PolicyApplicantReportResponse>> queryListPolicyApplicantReportBo(BasePageRequest basePageRequest, String startDate) {
        ResultObject<List<PolicyApplicantReportResponse>> resultObject = new ResultObject<>();

        List<PolicyApplicantReportBo> policyApplicantReportBos = new ArrayList<>(policyBaseService.queryPolicyApplicantReport(basePageRequest, startDate));

        List<String> originCustomerIds = new ArrayList<>();
        if (AssertUtils.isNotEmpty(policyApplicantReportBos)) {
            originCustomerIds = policyApplicantReportBos.stream().filter(policyApplicantReportBo -> AssertUtils.isNotEmpty(policyApplicantReportBo.getCustomerId())).map(PolicyApplicantReportBo::getCustomerId).collect(Collectors.toList());
        }
        List<PolicyApplicantReportBo> delegatePolicyApplicantReportBos = policyBaseService.queryDelegateApplicantReport(basePageRequest, startDate);
        if (AssertUtils.isNotEmpty(delegatePolicyApplicantReportBos)) {
            //移除掉相同客户
            List<String> finalOriginCustomerIds = originCustomerIds;
            if (AssertUtils.isNotEmpty(finalOriginCustomerIds)) {
                delegatePolicyApplicantReportBos.removeIf(policyApplicantReportBo -> finalOriginCustomerIds.contains(policyApplicantReportBo.getCustomerId()));
            }
            policyApplicantReportBos.addAll(delegatePolicyApplicantReportBos);
        }

        if (!AssertUtils.isNotEmpty(policyApplicantReportBos)) {
            return resultObject;
        }
        List<PolicyApplicantReportResponse> policyApplicantReportListResponses = (List<PolicyApplicantReportResponse>) this.converterList(policyApplicantReportBos, new TypeToken<List<PolicyApplicantReportResponse>>() {
        }.getType());
        resultObject.setData(policyApplicantReportListResponses);
        return resultObject;
    }

    /**
     * 查询被保人资料
     *
     * @param basePageRequest
     * @param startDate
     * @return
     */
    @Override
    public ResultObject<List<PolicyInsuredReportResponse>> queryListPolicyInsuredsReportBo(BasePageRequest basePageRequest, String startDate) {
        ResultObject<List<PolicyInsuredReportResponse>> resultObject = new ResultObject<>();

        List<PolicyInsuredReportBo> policyInsuredReportBos = policyBaseService.queryPolicyInsuredsReport(basePageRequest, startDate);
        if (!AssertUtils.isNotEmpty(policyInsuredReportBos)) {
            return resultObject;
        }
        List<PolicyInsuredReportResponse> policyInsuredReportResponses = (List<PolicyInsuredReportResponse>) this.converterList(policyInsuredReportBos, new TypeToken<List<PolicyInsuredReportResponse>>() {
        }.getType());
        resultObject.setData(policyInsuredReportResponses);
        return resultObject;
    }

    /**
     * 查询承保清单
     *
     * @param basePageRequest
     * @param reportType
     * @return
     */
    @Override
    public ResultObject<List<PolicyReportUnderwritingResponse>> queryListPolicyReportUnderwritingBo(BasePageRequest basePageRequest, String startDate, String reportType) {
        ResultObject<List<PolicyReportUnderwritingResponse>> resultObject = new ResultObject<>();
        List<PolicyReportUnderwritingBo> policyReportUnderwritingBos = policyBaseService.queryPolicyReportUnderwritingBo(basePageRequest, startDate, reportType);
        if (!AssertUtils.isNotEmpty(policyReportUnderwritingBos)) {
            return resultObject;
        }

        //取出代理人id
        List<String> agentIdList = policyReportUnderwritingBos.stream()
                .filter(policyReportUnderwritingBo1 -> AssertUtils.isNotEmpty(policyReportUnderwritingBo1.getAgentId()))
                .map(PolicyReportUnderwritingBo::getAgentId).distinct().collect(Collectors.toList());
        List<String> policyIds = policyReportUnderwritingBos.stream()
                .filter(policyReportUnderwritingBo1 -> AssertUtils.isNotEmpty(policyReportUnderwritingBo1.getPolicyId()))
                .map(PolicyReportUnderwritingBo::getPolicyId).distinct().collect(Collectors.toList());

        ResultObject<List<AgentResponse>> listResultObject = new ResultObject<>();
        if (AssertUtils.isNotEmpty(agentIdList)) {
            AgentApplyQueryRequest agentApplyQueryReqFc = new AgentApplyQueryRequest();
            agentApplyQueryReqFc.setListAgentId(agentIdList);
            listResultObject = agentApi.agentsGet(agentApplyQueryReqFc);
        }

        List<PolicyApplicantPo> policyApplicantPos = policyBaseService.queryAllPolicyApplicantPo(policyIds);

        for (PolicyReportUnderwritingBo policyReportUnderwritingBo : policyReportUnderwritingBos) {
            //  设置代理人信息
            if (!AssertUtils.isResultObjectListDataNull(listResultObject) && AssertUtils.isNotEmpty(policyReportUnderwritingBo.getAgentId())) {
                listResultObject.getData().forEach(applyAgentRespFc -> {
                    if (applyAgentRespFc.getAgentId().equals(policyReportUnderwritingBo.getAgentId())) {
                        policyReportUnderwritingBo.setAgentName(applyAgentRespFc.getAgentName());
                        policyReportUnderwritingBo.setAgentCode(applyAgentRespFc.getAgentCode());
                        policyReportUnderwritingBo.setAgentTypeCode(applyAgentRespFc.getAgentTypeCode());
                    }
                });
            }
//            //根据policy获取险种信息  团险信息未查询！
//            PolicyCoveragePo policyCoveragePo = policyBaseService.queryPolicyCoveragePo(policyReportUnderwritingBo.getPolicyId());
//            if (AssertUtils.isNotNull(policyCoveragePo)) {
//                policyReportUnderwritingBo.setProductId(policyCoveragePo.getProductId());
//                policyReportUnderwritingBo.setProductName(policyCoveragePo.getProductName());
//                policyReportUnderwritingBo.setPrimaryFlag(policyCoveragePo.getPrimaryFlag());
//                policyReportUnderwritingBo.setPremiumFrequency(policyCoveragePo.getPremiumFrequency());
//                policyReportUnderwritingBo.setCoveragePeriodUnit(policyCoveragePo.getCoveragePeriodUnit());
//                policyReportUnderwritingBo.setCoveragePeriod(policyCoveragePo.getCoveragePeriod());
//            }
            //根据policyid获取投保人信息
//            PolicyApplicantPo policyApplicantPo = policyBaseService.queryAllPolicyApplicantPo(policyReportUnderwritingBo.getPolicyId());
            if (AssertUtils.isNotEmpty(policyApplicantPos)) {
                policyApplicantPos.stream().filter(policyApplicantPo -> policyApplicantPo.getPolicyId().equals(policyReportUnderwritingBo.getPolicyId()))
                        .findFirst().ifPresent(policyApplicantPo -> {
                    policyReportUnderwritingBo.setApplicantName(policyApplicantPo.getName());
                    policyReportUnderwritingBo.setApplicantIdType(policyApplicantPo.getIdType());
                    policyReportUnderwritingBo.setApplicantIdNo(policyApplicantPo.getIdNo());
                    policyReportUnderwritingBo.setApplicantMobile(policyApplicantPo.getMobile());
                    policyReportUnderwritingBo.setApplicantOccupationCode(policyApplicantPo.getOccupationCode());
                    policyReportUnderwritingBo.setApplicantCareerRiskLevel(policyApplicantPo.getOccupationType());
                    policyReportUnderwritingBo.setApplicantCustomerId(policyApplicantPo.getCustomerId());
                    policyReportUnderwritingBo.setHomeAddress(policyApplicantPo.getHomeAddress());
                    policyReportUnderwritingBo.setHomeAreaCode(policyApplicantPo.getHomeAreaCode());
                });
            }
            PolicyInsuredPo policyInsuredPo = policyBaseService.queryOnePolicyInsuredPo(policyReportUnderwritingBo.getPolicyId());
            if (AssertUtils.isNotNull(policyInsuredPo)) {
                policyReportUnderwritingBo.setInsuredName(policyInsuredPo.getName());
                policyReportUnderwritingBo.setInsuredIdType(policyInsuredPo.getIdType());
                policyReportUnderwritingBo.setInsuredIdNo(policyInsuredPo.getIdNo());
                policyReportUnderwritingBo.setInsuredMobile(policyInsuredPo.getMobile());
                policyReportUnderwritingBo.setInsuredOccupationCode(policyInsuredPo.getOccupationCode());
                policyReportUnderwritingBo.setInsuredCareerRiskLevel(policyInsuredPo.getOccupationType());
                policyReportUnderwritingBo.setInsuredCustomerId(policyInsuredPo.getCustomerId());
                policyReportUnderwritingBo.setInsuredBirthday(policyInsuredPo.getBirthday());
                policyReportUnderwritingBo.setInsuredSex(policyInsuredPo.getSex());
            }
            //同步首单信息
            if (DateUtils.timeStrToString(policyReportUnderwritingBo.getPolicyCreateDate(), DateUtils.FORMATE3).equals(DateUtils.timeStrToString(policyReportUnderwritingBo.getPolicyUpdatedDate(), DateUtils.FORMATE3))) {
                //同步当天新产生的保单判定是否为首单
                List<PolicyApplicantPo> policyApplicantPoList = policyBaseService.queryPolicyApplicantByCustomerId(policyReportUnderwritingBo.getApplicantCustomerId());
                if (AssertUtils.isNotEmpty(policyApplicantPoList) && policyApplicantPoList.size() == 1) {
                    policyReportUnderwritingBo.setApplicantFirstPolicyDate(policyReportUnderwritingBo.getPolicyCreateDate());
                }
                List<PolicyInsuredPo> policyInsuredPoList = policyBaseService.queryPolicyInsuredByCustomerId(policyReportUnderwritingBo.getInsuredCustomerId());
                if (AssertUtils.isNotEmpty(policyInsuredPoList) && policyInsuredPoList.size() == 1) {
                    policyReportUnderwritingBo.setInsuredFirstPolicyDate(policyReportUnderwritingBo.getPolicyCreateDate());
                }
            }

            //根据policy获取当前时间倒序的第一条记录
            List<PolicyPaymentPo> policyPaymentPos = policyBaseService.queryAllPolicyPaymentPo(policyReportUnderwritingBo.getPolicyId());
            if (AssertUtils.isNotEmpty(policyPaymentPos)) {
                policyReportUnderwritingBo.setReceivableDate(policyPaymentPos.get(0).getReceivableDate());
                policyReportUnderwritingBo.setPaidTimes(String.valueOf(policyPaymentPos.size()));
            }

            if (AssertUtils.isNotNull(policyReportUnderwritingBo.getActualPremium()) && PolicyTermEnum.REPORT_TYPE.REGULATORY_POLICY.name().equals(reportType)) {
                policyReportUnderwritingBo.setTotalPremium(policyReportUnderwritingBo.getActualPremium());
            }
        }
        List<PolicyReportUnderwritingResponse> policyReportUnderwritingResponse = (List<PolicyReportUnderwritingResponse>) this.converterList(policyReportUnderwritingBos, new TypeToken<List<PolicyReportUnderwritingResponse>>() {
        }.getType());
        resultObject.setData(policyReportUnderwritingResponse);
        return resultObject;
    }

    @Override
    public ResultObject<List<PolicyGroupReportUnderwritingResponse>> queryListGroupPolicyReportUnderwritingBo(BasePageRequest basePageRequest, String startDate) {
        ResultObject<List<PolicyGroupReportUnderwritingResponse>> resultObject = new ResultObject<>();
        List<PolicyGroupReportUnderwritingBo> policyReportUnderwritingBos = policyBaseService.queryPolicyGroupReportUnderwritingBo(basePageRequest, startDate);
        if (!AssertUtils.isNotEmpty(policyReportUnderwritingBos)) {
            return resultObject;
        }
        //取出代理人id
        List<String> agentIdList = policyReportUnderwritingBos.stream()
                .filter(policyReportUnderwritingBo1 -> AssertUtils.isNotEmpty(policyReportUnderwritingBo1.getAgentId()))
                .map(PolicyGroupReportUnderwritingBo::getAgentId).distinct().collect(Collectors.toList());
        List<String> policyIds = policyReportUnderwritingBos.stream()
                .filter(policyReportUnderwritingBo1 -> AssertUtils.isNotEmpty(policyReportUnderwritingBo1.getPolicyId()))
                .map(PolicyGroupReportUnderwritingBo::getPolicyId).distinct().collect(Collectors.toList());

        ResultObject<List<AgentResponse>> listResultObject = new ResultObject<>();
        if (AssertUtils.isNotEmpty(agentIdList)) {
            AgentApplyQueryRequest agentApplyQueryReqFc = new AgentApplyQueryRequest();
            agentApplyQueryReqFc.setListAgentId(agentIdList);
            listResultObject = agentApi.agentsGet(agentApplyQueryReqFc);
        }

        List<PolicyApplicantPo> policyApplicantPos = policyBaseService.queryAllPolicyApplicantPo(policyIds);
        List<PolicyInsuredExtendPo> listPolicyInsuredExtend = policyInsuredBaseService.listPolicyInsuredExtendAll(policyIds);

        for (PolicyGroupReportUnderwritingBo policyReportUnderwritingBo : policyReportUnderwritingBos) {
            //  设置代理人信息
            if (!AssertUtils.isResultObjectListDataNull(listResultObject) && AssertUtils.isNotEmpty(policyReportUnderwritingBo.getAgentId())) {
                listResultObject.getData().forEach(applyAgentRespFc -> {
                    if (applyAgentRespFc.getAgentId().equals(policyReportUnderwritingBo.getAgentId())) {
                        policyReportUnderwritingBo.setAgentName(applyAgentRespFc.getAgentName());
                        policyReportUnderwritingBo.setAgentCode(applyAgentRespFc.getAgentCode());
                        policyReportUnderwritingBo.setAgentTypeCode(applyAgentRespFc.getAgentTypeCode());
                    }
                });
            }

            //根据policyid获取投保人信息
            if (AssertUtils.isNotEmpty(policyApplicantPos)) {
                policyApplicantPos.stream().filter(policyApplicantPo -> policyApplicantPo.getPolicyId().equals(policyReportUnderwritingBo.getPolicyId()))
                        .findFirst().ifPresent(policyApplicantPo -> {
                    policyReportUnderwritingBo.setCompanyName(policyApplicantPo.getName());
                    policyReportUnderwritingBo.setCompanyIdType(policyApplicantPo.getIdType());
                    policyReportUnderwritingBo.setCompanyIdNo(policyApplicantPo.getIdNo());
                    policyReportUnderwritingBo.setCompanyContractName(policyApplicantPo.getCompanyContractName());
                    policyReportUnderwritingBo.setCompanyContractMobile(policyApplicantPo.getCompanyContractMobile());
                    policyReportUnderwritingBo.setCompanyType(policyApplicantPo.getCompanyType());
                    policyReportUnderwritingBo.setDelegateName(policyApplicantPo.getDelegateName());
                    policyReportUnderwritingBo.setDelegateMobile(policyApplicantPo.getDelegateMobile());
                    policyReportUnderwritingBo.setApplicantCustomerId(policyApplicantPo.getCustomerId());
                });
            }

            //查询被保人个数
            PolicyInsuredCollectPo policyInsuredCollectPo = policyBaseService.queryPolicyInsuredCollect(policyReportUnderwritingBo.getPolicyId());
            if (AssertUtils.isNotNull(policyInsuredCollectPo)) {
                policyReportUnderwritingBo.setInsuredNumber(policyInsuredCollectPo.getEffectiveQuantity());
            }
            //险种
            List<PolicyCoveragePo> policyCoveragePos = policyCoverageBaseService.queryReportPolicyCoverage(policyReportUnderwritingBo.getPolicyId());
            //险种档次
            List<PolicyCoverageDutyBo> policyCoverageDutyBos = policyCoverageBaseService.queryReportPolicyCoverageDuty(policyReportUnderwritingBo.getPolicyId());
            policyReportUnderwritingBo.setPolicyCoverageDutyBos(policyCoverageDutyBos);
            //险种档次
            List<PolicyCoverageLevelPo> policyCoverageLevelList = policyCoverageBaseService.queryReportPolicyCoverageLevel(policyReportUnderwritingBo.getPolicyId());
            policyReportUnderwritingBo.setPolicyCoverageLevelBos(policyCoverageLevelList);

            if (AssertUtils.isNotEmpty(policyCoveragePos)) {
                List<PolicyGroupReportCoverageBo> policyGroupReportCoverageBos = (List<PolicyGroupReportCoverageBo>) this.converterList(policyCoveragePos, new TypeToken<List<PolicyGroupReportCoverageBo>>() {
                }.getType());
                policyGroupReportCoverageBos.forEach(policyGroupReportCoverageBo -> {
                    listPolicyInsuredExtend.stream().filter(policyInsuredExtendPo -> policyInsuredExtendPo.getInsuredId().equals(policyGroupReportCoverageBo.getInsuredId())).findFirst().ifPresent(policyInsuredExtendPo -> {
                        //  减员：被保人清单详情失效日期为申请日期； a.状态为失效时，需增加失效日期字段展示；
                        if (PolicyTermEnum.VALID_FLAG.invalid.name().equals(policyGroupReportCoverageBo.getValidFlag()) &&
                                PolicyTermEnum.VALID_FLAG.invalid.name().equals(policyInsuredExtendPo.getValidFlag())) {
                            policyGroupReportCoverageBo.setInvalidDate(policyInsuredExtendPo.getInvalidDate());
                        }
                    });

                    if (AssertUtils.isNotEmpty(policyCoverageDutyBos)) {
                        policyCoverageDutyBos.stream().filter(policyCoverageDutyBo -> policyCoverageDutyBo.getCoverageId().equals(policyGroupReportCoverageBo.getCoverageId()))
                                .findFirst().ifPresent(policyCoverageDutyBo -> {
                            policyGroupReportCoverageBo.setDutyId(policyCoverageDutyBo.getDutyId());
                            policyGroupReportCoverageBo.setDutyName(policyCoverageDutyBo.getDutyName());
                        });
                    }
                });
                policyReportUnderwritingBo.setPolicyGroupReportCoverageBos(policyGroupReportCoverageBos);
            }

            //被保人
            List<PolicyInsuredBo> policyInsuredList = policyInsuredBaseDao.queryReportPolicyInsured(policyReportUnderwritingBo.getPolicyId());
            policyReportUnderwritingBo.setPolicyInsuredBos(policyInsuredList);

            //同步首单信息
            if (DateUtils.timeStrToString(policyReportUnderwritingBo.getCreatedDate(), DateUtils.FORMATE3).equals(DateUtils.timeStrToString(policyReportUnderwritingBo.getUpdatedDate(), DateUtils.FORMATE3))) {
                //同步当天新产生的保单判定是否为首单
                List<PolicyApplicantPo> policyApplicantPoList = policyBaseService.queryPolicyApplicantByCustomerId(policyReportUnderwritingBo.getApplicantCustomerId());
                if (AssertUtils.isNotEmpty(policyApplicantPoList) && policyApplicantPoList.size() == 1) {
                    policyReportUnderwritingBo.setApplicantFirstPolicyDate(policyReportUnderwritingBo.getCreatedDate());
                }
            }

        }
        List<PolicyGroupReportUnderwritingResponse> policyGroupReportUnderwritingResponses = (List<PolicyGroupReportUnderwritingResponse>) this.converterList(policyReportUnderwritingBos, new TypeToken<List<PolicyGroupReportUnderwritingResponse>>() {
        }.getType());
        resultObject.setData(policyGroupReportUnderwritingResponses);
        return resultObject;
    }

    @Override
    public ResultObject<List<GroupSdfReportResponse>> queryListPolicyGroupSdfReport(BasePageRequest basePageRequest, String startDate) {
        ResultObject<List<GroupSdfReportResponse>> resultObject = new ResultObject<>();
        List<GroupSdfReportBo> groupSdfReportBos = policyBaseService.queryListPolicyGroupSdfReportBo(basePageRequest, startDate);
        if (!AssertUtils.isNotEmpty(groupSdfReportBos)) {
            return resultObject;
        }
        List<GroupSdfReportResponse> groupSdfReportResponses = (List<GroupSdfReportResponse>) this.converterList(groupSdfReportBos, new TypeToken<List<GroupSdfReportResponse>>() {
        }.getType());
        resultObject.setData(groupSdfReportResponses);
        return resultObject;
    }

    /**
     * 设置团险投保人代表客户ID
     *
     * @param password
     * @param updateFlag
     * @return
     */
    @Override
    @Transactional
    public ResultObject syncApplicantCustomer(String password, String updateFlag) {
        ResultObject resultObject = new ResultObject();
        if (!"GCLIFE".equals(password)) {
            return resultObject;
        }

        List<PolicyGroupReportSyncApplicantBo> syncApplicantBos = policyBaseService.querySyncApplicantCustomer();
        if (!AssertUtils.isNotEmpty(syncApplicantBos)) {
            return resultObject;
        }
        List<CustomerBusinessRequest> customerBusinessReqFcList = new ArrayList<>();
        syncApplicantBos.forEach(policyGroupReportSyncApplicantBo -> {
            CustomerBusinessRequest customerBusinessRequest = new CustomerBusinessRequest();
            customerBusinessRequest.setName(policyGroupReportSyncApplicantBo.getDelegateName());
            customerBusinessRequest.setIdNo(policyGroupReportSyncApplicantBo.getDelegateIdNo());
            customerBusinessRequest.setIdType(policyGroupReportSyncApplicantBo.getDelegateIdType());
            customerBusinessRequest.setBirthday(policyGroupReportSyncApplicantBo.getDelegateBirthday());
            customerBusinessRequest.setMobile(policyGroupReportSyncApplicantBo.getDelegateMobile());
            customerBusinessRequest.setUserId(policyGroupReportSyncApplicantBo.getAgentId());
            customerBusinessRequest.setMedalNo(PolicyTermEnum.CUSTOMER_TYPE.APPLICANT_DELEGATE.name());
            customerBusinessReqFcList.add(customerBusinessRequest);
        });
        ResultObject<List<UserCustomerResponse>> listResultObject = customerManageApi.saveCustomerMessagePoList(customerBusinessReqFcList);
        List<PolicyApplicantPo> policyApplicantPos = policyBaseService.queryApplicantCustomer();

        if (!AssertUtils.isResultObjectListDataNull(listResultObject) && AssertUtils.isNotEmpty(policyApplicantPos)) {
            List<PolicyApplicantPo> updatePolicyApplicantPos = new ArrayList<>();
            listResultObject.getData().forEach(userCustomerResponse -> {
                policyApplicantPos.stream().filter(policyApplicantPo -> policyApplicantPo.getDelegateIdNo().equals(userCustomerResponse.getIdNo())
                        && policyApplicantPo.getDelegateIdType().equals(userCustomerResponse.getIdType())).findFirst().ifPresent(policyApplicantPo -> {
                    policyApplicantPo.setDelegateCustomerId(userCustomerResponse.getCustomerId());
                    if (AssertUtils.isNotEmpty(updateFlag) && TerminologyConfigEnum.WHETHER.YES.name().equals(updateFlag)) {
                        policyApplicantPo.setUpdatedDate(DateUtils.getCurrentTime());
                    }
                    updatePolicyApplicantPos.add(policyApplicantPo);
                });
            });
            policyApplicantDao.update(updatePolicyApplicantPos);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<ActualPerformanceReportBo>> queryActualPerformance(List<String> policyIdList) {
        ResultObject<List<ActualPerformanceReportBo>> resultObject = new ResultObject<>();
        List<ActualPerformanceReportBo> actualPerformanceReportBoList = policyBaseService.queryActualPerformance(policyIdList);
        resultObject.setData(actualPerformanceReportBoList);
        return resultObject;
    }

    @Override
    public ResultObject<List<ActualPerformanceReportBo>> queryPolicyVersionActualPerformance(List<ActualPerformanceReportBo> actualPerformanceReportBoList) {
        ResultObject<List<ActualPerformanceReportBo>> resultObject = new ResultObject<>();
        List<String> versionNo = actualPerformanceReportBoList.stream().map(ActualPerformanceReportBo::getOldVersionNo).collect(Collectors.toList());
        List<ActualPerformanceReportBo> policyVersionActualPerformanceReportBoList = policyBaseService.queryPolicyVersionActualPerformance(versionNo);
        policyVersionActualPerformanceReportBoList.forEach(paprBo -> {
            actualPerformanceReportBoList.stream().filter(aprBo ->
                    aprBo.getPolicyId().equals(paprBo.getPolicyId())
                            && aprBo.getOldVersionNo().equals(paprBo.getOldVersionNo())
                            && aprBo.getProductId().equals(paprBo.getProductId()))
                    .findFirst().ifPresent(aprBo -> {
                ClazzUtils.copyPropertiesIgnoreNull(paprBo, aprBo);
            });
        });
        resultObject.setData(actualPerformanceReportBoList);
        return resultObject;
    }

    @Override
    public ResultObject quarterlyStatisticsReserveWithdrawalReport(String quarterDate) {
        ResultObject resultObject = new ResultObject();
        resultObject.setData(YES.name());
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(1);
        basePageRequest.setPageSize(1000);
        do {
            if (YES.name().equals(quarterlyStatisticsReserveWithdrawalReport(quarterDate, basePageRequest))) {
                break;
            }
            basePageRequest.setCurrentPage(basePageRequest.getCurrentPage() + 1);
        } while (true);

        return resultObject;
    }

    /**
     * 分页同步准备金数据
     *
     * @param quarterDate
     * @param basePageRequest
     * @return
     */
    private String quarterlyStatisticsReserveWithdrawalReport(String quarterDate, BasePageRequest basePageRequest) {
        //统计现有数据
        List<ReserveWithdrawalReportBo> reserveWithdrawalReportBoList = policyBaseService.quarterlyStatisticsReserveWithdrawalReport(quarterDate, basePageRequest);
        this.getLogger().info("查询准备金报表数据1：===============" + JSON.toJSONString(reserveWithdrawalReportBoList));
        //统计历史数据
        List<ReserveWithdrawalReportBo> reserveWithdrawalReportHistoryBoList = policyBaseService.quarterlyHistoryStatisticsReserveWithdrawalReport(quarterDate, basePageRequest);
        this.getLogger().info("查询准备金报表数据2：===============" + JSON.toJSONString(reserveWithdrawalReportHistoryBoList));
        reserveWithdrawalReportBoList.addAll(reserveWithdrawalReportHistoryBoList);
        if (!AssertUtils.isNotEmpty(reserveWithdrawalReportBoList)) {
            return YES.name();
        }
        List<String> policyIdList = reserveWithdrawalReportBoList.stream().map(ReserveWithdrawalReportBo::getPolicyId).distinct().collect(Collectors.toList());
        List<String> insuredIdList = reserveWithdrawalReportBoList.stream().map(ReserveWithdrawalReportBo::getInsuredId).distinct().collect(Collectors.toList());
        List<String> coverageIdList = reserveWithdrawalReportBoList.stream().map(ReserveWithdrawalReportBo::getCoverageId).distinct().collect(Collectors.toList());
        // 查询被保人保全数据，被保人是否是抵扣，抵扣没有被保人险种支付记录，缴费频次要加一
        List<ReportInsuredEndorseDetailResponse> insuredEndorseDetailResponses = endorseReportApi.queryInsuredEndorseDetailList(insuredIdList).getData();
        //查询保全险种的生效日期
        List<ReportEndorseResponse> reportEndorseResponseList = endorseReportApi.queryEndorseCoverageList(coverageIdList).getData();
        //查询保全险种的生效日期
        List<ReportEndorseResponse> policyEndorseResponseList = endorseReportApi.queryEndorseListByPolicyId(policyIdList).getData();
        // 查询缴费数据 个险存在被保人ID变化可能
        List<ReserveWithdrawalReportBo> reserveWithdrawalReportPaymentList = policyBaseService.quarterlyStatisticsReserveWithdrawalPaymentReport(insuredIdList);
        //查询险种的最新一次支付记录
        List<PolicyPaymentBo> coveragePaymentList = policyPaymentBaseService.quarterlyLatestPaymentByPolicyIdList(policyIdList);
        //保单初始代理人
        List<PolicyAgentBo> policyAgentBoList = policyAgentHistoryBaseDao.queryPolicyAgentByPolicyIdList(policyIdList);
        //查询险种的原始首期数据coverageIdList
        List<PolicyCoverageHistoryBo> policyCoverageHistoryBoList = policyCoverageHistoryBaseDao.queryFirstHistoryCoverageByCoverageIdList(coverageIdList);
        //查询理赔额
        List<String> claimProductIdList = Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id(), ProductTermEnum.PRODUCT.PRODUCT_7.id(), ProductTermEnum.PRODUCT.PRODUCT_12.id());
        List<ClaimCoverageAmountBo> coverageAmountBoList = claimCoverageApi.queryClaimCoverageAmount(coverageIdList).getData();
        List<PolicyCoveragePo> policyCoveragePoList = policyCoverageBaseService.listPolicyCoverageByIds(coverageIdList);
        //查询附加费
        List<PolicyAddPremiumPo> policyAddPremiumPoList = policyPremiumBaseService.listPolicyAddPremiumByCoverageIdList(coverageIdList,
                Arrays.asList(LIFE_RATINGS.name(),TPD_RATINGS.name(),EMPTY.name(), ADB_RATINGS.name()),
                Arrays.asList(EM_5.desc(), FER_5.desc()));
        //指定月份数据同步
        if (!AssertUtils.isNotEmpty(quarterDate)) {
            quarterDate = DateUtils.timeStrToString(DateUtils.getCurrentTime(), DateUtils.FORMATE2);
        }
        String finalQuarterDate = quarterDate;

        List<ReserveWithdrawalReportBo> reserveWithdrawalReportBos17 = new ArrayList<>();
        reserveWithdrawalReportBoList.forEach(reserveWithdrawalReportBo -> {
            //设置附加信息
            setPolicyAddPremium(policyAddPremiumPoList, reserveWithdrawalReportBo);
            //设置险种的第一次 原始保费 原始缴费周期
            if (AssertUtils.isNotEmpty(policyCoverageHistoryBoList)) {
                policyCoverageHistoryBoList.stream().filter(policyCoverageHistoryBo -> policyCoverageHistoryBo.getCoverageId().equals(reserveWithdrawalReportBo.getCoverageId()))
                        .findFirst().ifPresent(policyCoverageHistoryBo -> {
                    reserveWithdrawalReportBo.setOriOriginalPremium(policyCoverageHistoryBo.getOriginalPremium());
                    reserveWithdrawalReportBo.setOriPremiumFrequency(policyCoverageHistoryBo.getPremiumFrequency());
                });
            }
            //设置保全生效日期
            if (AssertUtils.isNotEmpty(policyEndorseResponseList)) {
                List<ReportEndorseResponse> endorseResponseList = policyEndorseResponseList.stream().filter(reportEndorseResponse -> reserveWithdrawalReportBo.getPolicyId().equals(reportEndorseResponse.getApplyId())).collect(Collectors.toList());
                endorseResponseList.stream().filter(reportEndorseResponse -> SURRENDER.name().equals(reportEndorseResponse.getProjectCode()))
                        .max(Comparator.comparingLong(ReportEndorseResponse::getEffectiveDate)).ifPresent(reportEndorseResponse -> {
                    reserveWithdrawalReportBo.setSurrenderDate(reportEndorseResponse.getEffectiveDate());
                });
                endorseResponseList.stream().filter(reportEndorseResponse -> REINSTATEMENT.name().equals(reportEndorseResponse.getProjectCode()))
                        .max(Comparator.comparingLong(ReportEndorseResponse::getEffectiveDate)).ifPresent(reportEndorseResponse -> {
                    reserveWithdrawalReportBo.setReinstatementDate(reportEndorseResponse.getEffectiveDate());
                });
            }

            // 计算投保人承保时的年龄
            if (AssertUtils.isNotNull(reserveWithdrawalReportBo.getApplyDate()) && AssertUtils.isNotNull(reserveWithdrawalReportBo.getApplicantBirthday())) {
                Integer ageYear = 0;
                try {
                    ageYear = DateUtils.getAgeYear(new Date(reserveWithdrawalReportBo.getApplicantBirthday()), new Date(reserveWithdrawalReportBo.getApplyDate()));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                reserveWithdrawalReportBo.setApplicantAge(ageYear.longValue());
            }

            //设置险种同保费 应交日期 缴费次数
            reserveWithdrawalReportBo.setRenewalInsuranceFrequency(reserveWithdrawalReportBo.getRenewalInsuranceFrequency() - 1);
            if (AssertUtils.isNotEmpty(reserveWithdrawalReportPaymentList)) {
                reserveWithdrawalReportPaymentList.stream().filter(reserveWithdrawalReportPayment ->
                        reserveWithdrawalReportPayment.getInsuredId().equals(reserveWithdrawalReportBo.getInsuredId()) &&
                                reserveWithdrawalReportPayment.getPolicyId().equals(reserveWithdrawalReportBo.getPolicyId()) &&
                                reserveWithdrawalReportPayment.getProductId().equals(reserveWithdrawalReportBo.getProductId())
                ).findFirst().ifPresent(reserveWithdrawalReportPayment -> {
                    reserveWithdrawalReportBo.setReceivableDate(reserveWithdrawalReportPayment.getReceivableDate());
                    reserveWithdrawalReportBo.setTotalActualPremium(reserveWithdrawalReportPayment.getTotalActualPremium());
                    reserveWithdrawalReportBo.setFrequency(reserveWithdrawalReportPayment.getFrequency());
                });
            }
            //设置险种第一次支付方式
            if (AssertUtils.isNotEmpty(coveragePaymentList)) {
                coveragePaymentList.stream().filter(policyPaymentBo ->
                        policyPaymentBo.getPolicyId().equals(reserveWithdrawalReportBo.getPolicyId()) &&
                                policyPaymentBo.getInsuredId().equals(reserveWithdrawalReportBo.getInsuredId()) &&
                                policyPaymentBo.getProductId().equals(reserveWithdrawalReportBo.getProductId()))
                        .findFirst().ifPresent(policyPaymentBo -> {
                    reserveWithdrawalReportBo.setPaymentModeCode(policyPaymentBo.getPaymentModeCode());
                    // 设置险种保费  不能设置档次责任实缴保费 只能设置险种的
                    if (!YES.name().equals(reserveWithdrawalReportBo.getDutyChooseFlag())) {
                        reserveWithdrawalReportBo.setPaymentActualPremium(policyPaymentBo.getActualPremium());
                    }
                });
            }
            Long receivableDate = reserveWithdrawalReportBo.getReceivableDate();
            Long approveDate = reserveWithdrawalReportBo.getApproveDate();
            Long endorseCoverageEffectiveDate = null;
            String coveragePeriodUnit = reserveWithdrawalReportBo.getCoveragePeriodUnit();
            String coveragePeriod = reserveWithdrawalReportBo.getCoveragePeriod();
            //同步 年月
            reserveWithdrawalReportBo.setQuarterDate(finalQuarterDate);
            //设置应交日期   没有应交日期设置应交日期，保全增加附加险、增减被保人
            if (AssertUtils.isNotEmpty(reportEndorseResponseList)) {
                Optional<ReportEndorseResponse> optionalReportEndorseResponse = reportEndorseResponseList.stream().filter(reportEndorseResponse -> (reportEndorseResponse.getCoverageId().equals(reserveWithdrawalReportBo.getCoverageId()))).findFirst();
                if (optionalReportEndorseResponse.isPresent()) {
                    ReportEndorseResponse reportEndorseResponse = optionalReportEndorseResponse.get();
                    if (!AssertUtils.isNotNull(reserveWithdrawalReportBo.getReceivableDate())) {
                        receivableDate = reportEndorseResponse.getEffectiveDate();
                    }
                    endorseCoverageEffectiveDate = reportEndorseResponse.getEffectiveDate();

                    //保全增减员的生效日期取实收日期
                    if ("gprj20200304004".equals(reportEndorseResponse.getProjectId())) {
                        endorseCoverageEffectiveDate = reportEndorseResponse.getGainedDate();
                    }
                }
            }
            //设置  保单年度、保单年度开始日期
            Long policyYearStartDate = null;
            long quarterMonthLastDay = DateUtils.getThisMonthLastDay(finalQuarterDate, DateUtils.FORMATE2);
            if (AssertUtils.isNotNull(approveDate) && AssertUtils.isNotNull(receivableDate)) {
                Long policyYear = (DateUtils.intervalMonth(approveDate, receivableDate) / 12 + 1);
                //#5,#20,#20A,#28趸交的保单年度应该根据准备金报表同步时间计算
                if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(),ProductTermEnum.PRODUCT.PRODUCT_20.id(),ProductTermEnum.PRODUCT.PRODUCT_20A.id(),ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(reserveWithdrawalReportBo.getProductId())) {
                    policyYear = (DateUtils.intervalMonth(approveDate, quarterMonthLastDay) / 12 + 1);
                }
                reserveWithdrawalReportBo.setPolicyYear(policyYear);
                if (policyYear == 1) {
                    policyYearStartDate = approveDate;
                } else {
                    policyYearStartDate = DateUtils.addStringYearsRT(approveDate, reserveWithdrawalReportBo.getPolicyYear().intValue() - 1);
                }
                reserveWithdrawalReportBo.setPolicyYearStartDate(policyYearStartDate);
            }
            //设置下次应缴日期
            Long nextReceivableDate = null;
            String premiumFrequency = reserveWithdrawalReportBo.getPremiumFrequency();
            if (AssertUtils.isNotNull(receivableDate) && !PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.SINGLE.name().equals(premiumFrequency)) {
                Long payToDate = getPayToDate(reserveWithdrawalReportBo.getInsuredBirthday(), reserveWithdrawalReportBo.getPremiumPeriod(), reserveWithdrawalReportBo.getPremiumPeriodUnit(), premiumFrequency, reserveWithdrawalReportBo.getApproveDate());
                if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
                    nextReceivableDate = nextReceivableDate(policyYearStartDate, payToDate, receivableDate, 1);
                } else if (SEASON.name().equals(premiumFrequency)) {
                    nextReceivableDate = nextReceivableDate(policyYearStartDate, payToDate, receivableDate, 3);
                } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                    nextReceivableDate = nextReceivableDate(policyYearStartDate, payToDate, receivableDate, 6);
                } else if (PolicyTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
                    nextReceivableDate = nextReceivableDate(policyYearStartDate, payToDate, receivableDate, 12);
                }
            }
            reserveWithdrawalReportBo.setNextReceivableDate(nextReceivableDate);
            //判断主险的险种类型（长期，短期）附加险跟着主险
            if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(reserveWithdrawalReportBo.getProductId())) {
                reserveWithdrawalReportBo.setProductType("LONG");
            } else if ("YEAR".equals(coveragePeriodUnit) && Integer.parseInt(coveragePeriod) <= 1) {
                //短期
                reserveWithdrawalReportBo.setProductType("SHORT");
            } else if ("MONTH".equals(coveragePeriodUnit) && Integer.parseInt(coveragePeriod) <= 12) {
                //短期
                reserveWithdrawalReportBo.setProductType("SHORT");
            } else if ("DAY".equals(coveragePeriodUnit) && Integer.parseInt(coveragePeriod) <= 365) {
                //短期
                reserveWithdrawalReportBo.setProductType("SHORT");
            } else {
                //长期
                reserveWithdrawalReportBo.setProductType("LONG");
            }
            //设置保额
            calculationAmount(reserveWithdrawalReportBo);
            //保额金额减去理赔金额
            if (AssertUtils.isNotEmpty(coverageAmountBoList) && claimProductIdList.contains(reserveWithdrawalReportBo.getProductId())) {
                coverageAmountBoList.stream().filter(claimCoverageAmountBo ->
                        claimCoverageAmountBo.getCoverageId().equals(reserveWithdrawalReportBo.getCoverageId()) &&
                                (AssertUtils.isNotEmpty(reserveWithdrawalReportBo.getDutyId()) ? reserveWithdrawalReportBo.getDutyId().equals(claimCoverageAmountBo.getProductDutyId()) : true))
                        .findFirst().ifPresent(claimCoverageAmountBo -> {
                    reserveWithdrawalReportBo.setAssessAmount(reserveWithdrawalReportBo.getAssessAmount().subtract(claimCoverageAmountBo.getTotalAmount()));
                });
            }

            List<String> assessAmountPolicyStatus = Arrays.asList(POLICY_STATUS_INDEMNITY_TERMINATION.name(),
                    POLICY_STATUS_HESITATION_REVOKE.name(),
                    POLICY_STATUS_INVALID_THOROUGH.name(),
                    POLICY_STATUS_EFFECT_TERMINATION.name(),
                    POLICY_STATUS_SURRENDER.name(),
                    POLICY_STATUS_IEXPIRE.name());
            List<String> assessAmountCoverageStatus = Arrays.asList(EXPIRED.name(), INDEMNITY_TERMINATION.name());


            // 短期险保单年度 都是1
            if (reserveWithdrawalReportBo.getProductType().equals("SHORT")) {
                reserveWithdrawalReportBo.setPolicyYear(1L);
                Long coveragePeriodStartDate = AssertUtils.isNotNull(endorseCoverageEffectiveDate) ? endorseCoverageEffectiveDate : receivableDate;
                reserveWithdrawalReportBo.setCoveragePeriodStartDate(coveragePeriodStartDate);
            }
            if (AssertUtils.isNotEmpty(policyAgentBoList)) {
                Optional<PolicyAgentBo> agentBo = policyAgentBoList.stream().filter(policyAgentBo -> policyAgentBo.getPolicyId().equals(reserveWithdrawalReportBo.getPolicyId())).findFirst();
                if (agentBo.isPresent()) {
                    PolicyAgentBo policyAgentBo = agentBo.get();
                    reserveWithdrawalReportBo.setInitialAgentId(policyAgentBo.getAgentId());
                } else {
                    reserveWithdrawalReportBo.setInitialAgentId(reserveWithdrawalReportBo.getAgentId());
                }
            }

            //同步被保人客户号
            if (AssertUtils.isNotEmpty(reserveWithdrawalReportBo.getInsuredCustomerId())) {
                ResultObject<CustomerAgentsResponse> baseCustomerAgent = customerAgentBaseApi.getBaseCustomerAgent(reserveWithdrawalReportBo.getInsuredCustomerId());
                if (!AssertUtils.isResultObjectDataNull(baseCustomerAgent)) {
                    reserveWithdrawalReportBo.setInsuredCustomerNo(baseCustomerAgent.getData().getCustomerNo());
                }
            }

            //设置被保人状态
            updateInsuredStatus(reserveWithdrawalReportBo, policyCoveragePoList, coverageAmountBoList, insuredEndorseDetailResponses);

            //17号产品特殊处理
            trans17Data(insuredEndorseDetailResponses, reserveWithdrawalReportBos17, reserveWithdrawalReportBo, assessAmountPolicyStatus, assessAmountCoverageStatus);

            if (assessAmountPolicyStatus.contains(reserveWithdrawalReportBo.getPolicyStatus()) || assessAmountCoverageStatus.contains(reserveWithdrawalReportBo.getCoverageStatus()) ||
                    (!EFFECTIVE.name().equals(reserveWithdrawalReportBo.getInsuredStatus()) && LIFE_INSURANCE_GROUP.name().equals(reserveWithdrawalReportBo.getPolicyType()))) {
                reserveWithdrawalReportBo.setAssessAmount(new BigDecimal(0));
            }

            // 查询被保人保全数据，被保人是否是抵扣，抵扣没有被保人险种支付记录，缴费频次设置为1
            if (AssertUtils.isNotEmpty(insuredEndorseDetailResponses) && AssertUtils.isNotEmpty(reserveWithdrawalReportBo.getInsuredId())) {
                insuredEndorseDetailResponses.stream()
                        .filter(riedr -> reserveWithdrawalReportBo.getInsuredId().equals(riedr.getAddInsuredId()) && reserveWithdrawalReportBo.getProductId().equals(riedr.getProductId()))
                        .forEach(riedr -> {
                            reserveWithdrawalReportBo.setPaymentActualPremium(new BigDecimal(0));
                            Long frequency = reserveWithdrawalReportBo.getFrequency();
                            reserveWithdrawalReportBo.setFrequency(1L);
                        });
            }
        });
        if (AssertUtils.isNotEmpty(reserveWithdrawalReportBoList) && AssertUtils.isNotEmpty(reserveWithdrawalReportBos17)) {
            reserveWithdrawalReportBoList.addAll(reserveWithdrawalReportBos17);
        }

        this.getLogger().info("开始同步准备金报表数据：===============" + JSON.toJSONString(reserveWithdrawalReportBoList));
        ResultObject<Void> reportResultObject = reportBaseApi.syncReserveWithdrawalReport(reserveWithdrawalReportBoList);
        AssertUtils.isResultObjectError(reportResultObject);
        this.getLogger().info("完成同步准备金报表结果：===============" + JSON.toJSONString(reportResultObject));
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(reserveWithdrawalReportBoList) ? reserveWithdrawalReportBoList.get(0).getTotalLine() : 1;
        Integer historyTotalLine = AssertUtils.isNotNull(reserveWithdrawalReportHistoryBoList) ? reserveWithdrawalReportHistoryBoList.get(0).getTotalLine() : 1;
        if (totalLine > (basePageRequest.getCurrentPage() * basePageRequest.getPageSize()) || historyTotalLine > (basePageRequest.getCurrentPage() * basePageRequest.getPageSize())) {
            return NO.name();
        }
        return YES.name();
    }

    private void trans17Data(List<ReportInsuredEndorseDetailResponse> insuredEndorseDetailResponses, List<ReserveWithdrawalReportBo> reserveWithdrawalReportBos17, ReserveWithdrawalReportBo reserveWithdrawalReportBo, List<String> assessAmountPolicyStatus, List<String> assessAmountCoverageStatus) {
        if (ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(reserveWithdrawalReportBo.getProductId())) {
            List<PolicyCoverageLevelPo> policyCoverageLevelPos = policyCoverageBaseService.getPolicyCoverageLevel(reserveWithdrawalReportBo.getCoverageId());
            BigDecimal levelAmount = policyCoverageLevelPos.stream()
                    .map(PolicyCoverageLevelPo::getAmount)
                    .filter(AssertUtils::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            reserveWithdrawalReportBo.setInitialAmount(levelAmount);
            reserveWithdrawalReportBo.setAssessAmount(levelAmount);
            Optional<PolicyCoverageLevelPo> eqTA = policyCoverageLevelPos.stream().filter(policyCoverageLevelPo -> "TA".equals(policyCoverageLevelPo.getProductLevel())).findFirst();
            List<PolicyCoverageLevelPo> notEqTA = policyCoverageLevelPos.stream().filter(policyCoverageLevelPo -> !"TA".equals(policyCoverageLevelPo.getProductLevel())).collect(Collectors.toList());
            if (eqTA.isPresent() && AssertUtils.isNotEmpty(notEqTA)) {
                PolicyCoverageLevelPo eqTASub = eqTA.get();
                //设置原有档次/保费/保额
                BigDecimal originalPremium = BigDecimal.ZERO;
                BigDecimal totalActualPremium = BigDecimal.ZERO;
                BigDecimal paymentActualPremium = BigDecimal.ZERO;
                BigDecimal oriOriginalPremium = BigDecimal.ZERO;
                BigDecimal totalAmount = BigDecimal.ZERO;
                List<String> productLevels = new ArrayList<>();
                List<String> mults = new ArrayList<>();
                for (PolicyCoverageLevelPo notEqTASub : notEqTA) {
                    productLevels.add(notEqTASub.getProductLevel());
                    mults.add(notEqTASub.getMult());
                    originalPremium = originalPremium.add(notEqTASub.getOriginalPremium());
                    oriOriginalPremium = oriOriginalPremium.add(notEqTASub.getOriginalPremium());
                    totalActualPremium = totalActualPremium.add(notEqTASub.getActualPremium());
                    paymentActualPremium = paymentActualPremium.add(notEqTASub.getActualPremium());
                    totalAmount = totalAmount.add(notEqTASub.getAmount());
                }
                reserveWithdrawalReportBo.setProductLevel(StringUtils.join(productLevels.toArray(), ","));
                reserveWithdrawalReportBo.setOriginalPremium(originalPremium);
                reserveWithdrawalReportBo.setTotalActualPremium(totalActualPremium);
                reserveWithdrawalReportBo.setPaymentActualPremium(paymentActualPremium);
                reserveWithdrawalReportBo.setOriOriginalPremium(oriOriginalPremium);
                reserveWithdrawalReportBo.setInitialAmount(totalAmount);
                reserveWithdrawalReportBo.setAssessAmount(totalAmount);
                reserveWithdrawalReportBo.setMult(StringUtils.join(mults.toArray(), ","));

                ReserveWithdrawalReportBo reserveWithdrawalReportBo17 = new ReserveWithdrawalReportBo();
                ClazzUtils.copyPropertiesIgnoreNull(reserveWithdrawalReportBo, reserveWithdrawalReportBo17);
                reserveWithdrawalReportBo17.setProductLevel(eqTASub.getProductLevel());
                reserveWithdrawalReportBo17.setOriginalPremium(eqTASub.getOriginalPremium());
                reserveWithdrawalReportBo17.setTotalActualPremium(eqTASub.getActualPremium());
                reserveWithdrawalReportBo17.setPaymentActualPremium(eqTASub.getActualPremium());
                reserveWithdrawalReportBo17.setOriOriginalPremium(eqTASub.getOriginalPremium());
                reserveWithdrawalReportBo17.setInitialAmount(eqTASub.getAmount());
                reserveWithdrawalReportBo17.setAssessAmount(eqTASub.getAmount());
                reserveWithdrawalReportBo17.setMult(eqTASub.getMult());
                if (assessAmountPolicyStatus.contains(reserveWithdrawalReportBo.getPolicyStatus()) || assessAmountCoverageStatus.contains(reserveWithdrawalReportBo.getCoverageStatus()) ||
                        (!EFFECTIVE.name().equals(reserveWithdrawalReportBo.getInsuredStatus()) && LIFE_INSURANCE_GROUP.name().equals(reserveWithdrawalReportBo.getPolicyType()))) {
                    reserveWithdrawalReportBo17.setAssessAmount(new BigDecimal(0));
                }
                // 查询被保人保全数据，被保人是否是抵扣，抵扣没有被保人险种支付记录，缴费频次设置为1
                if (AssertUtils.isNotEmpty(insuredEndorseDetailResponses) && AssertUtils.isNotEmpty(reserveWithdrawalReportBo17.getInsuredId())) {
                    insuredEndorseDetailResponses.stream()
                            .filter(riedr -> reserveWithdrawalReportBo17.getInsuredId().equals(riedr.getAddInsuredId()) && reserveWithdrawalReportBo17.getProductId().equals(riedr.getProductId()))
                            .forEach(riedr -> {
                                reserveWithdrawalReportBo17.setPaymentActualPremium(new BigDecimal(0));
                                reserveWithdrawalReportBo17.setFrequency(1L);
                            });
                }
                reserveWithdrawalReportBos17.add(reserveWithdrawalReportBo17);
            } else if (AssertUtils.isNotEmpty(notEqTA)) {
                BigDecimal totalAmount = notEqTA.stream().map(coverageLevel -> {
                    if (AssertUtils.isNotNull(coverageLevel.getAmount())) {
                        return coverageLevel.getAmount();
                    } else {
                        return BigDecimal.ZERO;
                    }
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
                reserveWithdrawalReportBo.setInitialAmount(totalAmount);
                reserveWithdrawalReportBo.setAssessAmount(totalAmount);
            }
        }
    }

    private void setPolicyAddPremium(List<PolicyAddPremiumPo> policyAddPremiumPoList, ReserveWithdrawalReportBo reserveWithdrawalReportBo) {
        if (AssertUtils.isNotEmpty(policyAddPremiumPoList)) {
            policyAddPremiumPoList.stream().filter(policyAddPremiumPo -> policyAddPremiumPo.getCoverageId().equals(reserveWithdrawalReportBo.getCoverageId()))
                    .forEach(policyAddPremiumPo -> {
                        if (EM_5.desc().equals(policyAddPremiumPo.getAddPremiumObjectCode())) {
                            reserveWithdrawalReportBo.setEmRate(policyAddPremiumPo.getAddPremiumObjectValue());
                            reserveWithdrawalReportBo.setEpy(policyAddPremiumPo.getAddPremiumPeriod());
                            reserveWithdrawalReportBo.setEmp(policyAddPremiumPo.getTotalAddPremium());

                            //根据不同的加费元素判断加费类型
                            if (LIFE_RATINGS.name().equals(policyAddPremiumPo.getRatingsName())) {
                                reserveWithdrawalReportBo.setDeathTotalAddPremium(policyAddPremiumPo.getTotalAddPremium());
                                reserveWithdrawalReportBo.setDeathAddPremiumObjectValue(policyAddPremiumPo.getAddPremiumObjectValue());
                                reserveWithdrawalReportBo.setDeathAddPremiumPeriod(policyAddPremiumPo.getAddPremiumPeriod());
                                reserveWithdrawalReportBo.setDeathAddPremiumStartDate(policyAddPremiumPo.getAddPremiumStartDate());
                                reserveWithdrawalReportBo.setDeathAddPremiumEndDate(policyAddPremiumPo.getAddPremiumEndDate());
                                reserveWithdrawalReportBo.setDeathRatingsName(policyAddPremiumPo.getRatingsName());
                            }

                            if (TPD_RATINGS.name().equals(policyAddPremiumPo.getRatingsName())) {
                                reserveWithdrawalReportBo.setTpdTotalAddPremium(policyAddPremiumPo.getTotalAddPremium());
                                reserveWithdrawalReportBo.setTpdAddPremiumObjectValue(policyAddPremiumPo.getAddPremiumObjectValue());
                                reserveWithdrawalReportBo.setTpdAddPremiumPeriod(policyAddPremiumPo.getAddPremiumPeriod());
                                reserveWithdrawalReportBo.setTpdAddPremiumStartDate(policyAddPremiumPo.getAddPremiumStartDate());
                                reserveWithdrawalReportBo.setTpdAddPremiumEndDate(policyAddPremiumPo.getAddPremiumEndDate());
                                reserveWithdrawalReportBo.setTpdRatingsName(policyAddPremiumPo.getRatingsName());
                            }

                            //目前没有adb类型的(20221014开发,先加上)
                            if (ADB_RATINGS.name().equals(policyAddPremiumPo.getRatingsName())) {
                                reserveWithdrawalReportBo.setAdbTotalAddPremium(policyAddPremiumPo.getTotalAddPremium());
                                reserveWithdrawalReportBo.setAdbAddPremiumObjectValue(policyAddPremiumPo.getAddPremiumObjectValue());
                                reserveWithdrawalReportBo.setAdbAddPremiumPeriod(policyAddPremiumPo.getAddPremiumPeriod());
                                reserveWithdrawalReportBo.setAdbAddPremiumStartDate(policyAddPremiumPo.getAddPremiumStartDate());
                                reserveWithdrawalReportBo.setAdbAddPremiumEndDate(policyAddPremiumPo.getAddPremiumEndDate());
                                reserveWithdrawalReportBo.setAdbRatingsName(policyAddPremiumPo.getRatingsName());
                            }

                            //天正说先默认把附加项的放到ci这列，后续有要求再改
                            if (EMPTY.name().equals(policyAddPremiumPo.getRatingsName())) {
                                reserveWithdrawalReportBo.setCiTotalAddPremium(policyAddPremiumPo.getTotalAddPremium());
                                reserveWithdrawalReportBo.setCiAddPremiumObjectValue(policyAddPremiumPo.getAddPremiumObjectValue());
                                reserveWithdrawalReportBo.setCiAddPremiumPeriod(policyAddPremiumPo.getAddPremiumPeriod());
                                reserveWithdrawalReportBo.setCiAddPremiumStartDate(policyAddPremiumPo.getAddPremiumStartDate());
                                reserveWithdrawalReportBo.setCiAddPremiumEndDate(policyAddPremiumPo.getAddPremiumEndDate());
                                reserveWithdrawalReportBo.setCiRatingsName(policyAddPremiumPo.getRatingsName());
                            }
                        }

                        if (FER_5.desc().equals(policyAddPremiumPo.getAddPremiumObjectCode())) {
                            reserveWithdrawalReportBo.setFer(policyAddPremiumPo.getAddPremiumObjectValue());
                            reserveWithdrawalReportBo.setFey(policyAddPremiumPo.getAddPremiumPeriod());
                            reserveWithdrawalReportBo.setFep(policyAddPremiumPo.getTotalAddPremium());
                            reserveWithdrawalReportBo.setFerEffectiveDate(policyAddPremiumPo.getAddPremiumStartDate());
                        }
                    });
        }
    }

    enum REPORT_INSURED_STATUS {

        EFFECTIVE("Effective"),
        REPLACED("Replaced"),
        REPLACED_ADD("Replaced+"),
        REPLACED_SUBTRACT("Replaced-"),
        ADD("Add"),
        SUBTRACT("Surrender"),
        CLAIM("Claim");


        private String name;

        private REPORT_INSURED_STATUS(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }


    private void updateInsuredStatus(ReserveWithdrawalReportBo reserveWithdrawalReportBo, List<PolicyCoveragePo> policyCoveragePoList, List<ClaimCoverageAmountBo> coverageAmountBoList, List<ReportInsuredEndorseDetailResponse> insuredEndorseDetailResponses) {
        String insuredId = reserveWithdrawalReportBo.getInsuredId();
        if (!AssertUtils.isNotEmpty(reserveWithdrawalReportBo.getInsuredId())) {
            return;
        }
        Map<Long, String> map = new TreeMap(Comparator.reverseOrder().reversed());
        if (AssertUtils.isNotEmpty(insuredEndorseDetailResponses)) {
            //添加新的对象之后去重
            insuredEndorseDetailResponses.stream()
                    .filter(insuredEndorse -> insuredId.equals(insuredEndorse.getInsuredId()))
                    .forEach(insuredEndorse -> {
                        Long effectiveDate = insuredEndorse.getEffectiveDate();
                        String addInsuredId = insuredEndorse.getAddInsuredId();
                        String subtractInsuredId = insuredEndorse.getSubtractInsuredId();
                        if (insuredId.equals(addInsuredId)) {
                            map.put(effectiveDate, REPORT_INSURED_STATUS.REPLACED_ADD.name());
                        } else if (insuredId.equals(subtractInsuredId)) {
                            map.put(effectiveDate, REPORT_INSURED_STATUS.REPLACED_SUBTRACT.name());
                        } else {
                            String name = REPORT_INSURED_STATUS.valueOf(insuredEndorse.getInsuredChangeFlag()).name();
                            map.put(effectiveDate, name);
                        }
                    });
        }
        if (AssertUtils.isNotEmpty(coverageAmountBoList)) {
            List<String> coverageIdList = policyCoveragePoList.stream().filter(policyCoveragePo -> reserveWithdrawalReportBo.getInsuredId().equals(policyCoveragePo.getInsuredId())).map(PolicyCoveragePo::getCoverageId).collect(Collectors.toList());
            coverageAmountBoList.stream().filter(claimCoverageAmountBo -> coverageIdList.contains(claimCoverageAmountBo.getCoverageId())).map(ClaimCoverageAmountBo::getClosingCaseDate).distinct().forEach(closingCaseDate -> {
                map.put(closingCaseDate, REPORT_INSURED_STATUS.CLAIM.name());
            });
        }
        if (AssertUtils.isNotEmpty(map)) {
            reserveWithdrawalReportBo.setInsuredChangeStatus(StringUtils.join(map.values(), ","));
        }
    }

    private void calculationAmount(ReserveWithdrawalReportBo reserveWithdrawalReportBo) {
        Long policyYear = reserveWithdrawalReportBo.getPolicyYear();
        Long renewalInsuranceFrequency = reserveWithdrawalReportBo.getRenewalInsuranceFrequency();
        String productId = reserveWithdrawalReportBo.getProductId();
        String policyId = reserveWithdrawalReportBo.getPolicyId();
        String productLevel = reserveWithdrawalReportBo.getProductLevel();
        String mult = reserveWithdrawalReportBo.getMult();
        Long insuredBirthday = reserveWithdrawalReportBo.getInsuredBirthday();
        BigDecimal coverageTotalAmount = AssertUtils.isNotEmpty(reserveWithdrawalReportBo.getTotalAmount()) ? new BigDecimal(reserveWithdrawalReportBo.getTotalAmount()) : new BigDecimal(0);
        Long approveDate = reserveWithdrawalReportBo.getApproveDate();
        String policyType = reserveWithdrawalReportBo.getPolicyType();
        String dutyId = reserveWithdrawalReportBo.getDutyId();
        Map<String, BigDecimal> amountMap = calculationAmount(policyYear, renewalInsuranceFrequency, productId, policyId, productLevel, mult, insuredBirthday, coverageTotalAmount, approveDate, policyType, dutyId);
        reserveWithdrawalReportBo.setInitialAmount(amountMap.get(InitialAmount));
        reserveWithdrawalReportBo.setAssessAmount(amountMap.get(AssessAmount));
    }


    private static Long nextReceivableDate(Long policyYearStartDate, Long maturityDate, Long receivableDate, int premiumFrequencyInterval) {
        Long nextReceivableDate = null;
        if (!AssertUtils.isNotNull(policyYearStartDate) || !AssertUtils.isNotNull(maturityDate) || !AssertUtils.isNotNull(receivableDate)) {
            return nextReceivableDate;
        }
        if (policyYearStartDate < maturityDate) {
            int interval = premiumFrequencyInterval;
            do {
                nextReceivableDate = DateUtils.addStringMonthRT(policyYearStartDate, premiumFrequencyInterval);
                premiumFrequencyInterval = premiumFrequencyInterval + interval;

            } while (Integer.parseInt(DateUtils.timeStrToString(nextReceivableDate, DateUtils.FORMATE16)) <= Integer.parseInt(DateUtils.timeStrToString(receivableDate, DateUtils.FORMATE16)));

            if (Integer.parseInt(DateUtils.timeStrToString(nextReceivableDate, DateUtils.FORMATE16)) > Integer.parseInt(DateUtils.timeStrToString(maturityDate, DateUtils.FORMATE16))) {
                return null;
            }
        }
        // 保单年度开始日期 = 满期，代表没缴费
        else if (policyYearStartDate.equals(maturityDate)) {
            return policyYearStartDate;
        }
        return nextReceivableDate;
    }


    public Long getPayToDate(long birthday, String period, String periodType, String premiumFrequency, long approveDate) {
        long payToDate = 0L;
        int age = getAgeByBirthday(birthday);
        if (AssertUtils.isNotEmpty(periodType) && AssertUtils.isNotEmpty(period)) {
            if (periodType.equals(LIFELONG.name())) {
                payToDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(approveDate), 120 - age);
                //缴费周期为年缴
                if (YEAR.name().equals(premiumFrequency)) {

                } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                    //半年缴费-加6个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 6);
                } else if (SEASON.name().equals(premiumFrequency)) {
                    //季度缴费　加9个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 9);
                } else if (MONTH.name().equals(premiumFrequency)) {
                    //月度缴费　加11个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 11);
                }

            } else if (periodType.equals(YEAR.name())) {
                payToDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - 1);
                //缴费周期为年缴
                if (YEAR.name().equals(premiumFrequency)) {

                } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                    //半年缴费-加6个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 6);
                } else if (SEASON.name().equals(premiumFrequency)) {
                    //季度缴费　加9个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 9);
                } else if (MONTH.name().equals(premiumFrequency)) {
                    //月度缴费　加11个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 11);
                }
            } else if (periodType.equals(AGE.name())) {
                payToDate = DateUtils.addStringYearsRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - age);
                //缴费周期为年缴
                if (YEAR.name().equals(premiumFrequency)) {

                } else if (SEMIANNUAL.name().equals(premiumFrequency)) {
                    //半年缴费-加6个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 6);
                } else if (SEASON.name().equals(premiumFrequency)) {
                    //季度缴费　加9个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 9);
                } else if (MONTH.name().equals(premiumFrequency)) {
                    //月度缴费　加11个月
                    payToDate = DateUtils.addStringMonthRT(payToDate, 11);
                }
            } else if (periodType.equals(MONTH.name())) {
                payToDate = DateUtils.addStringMonthRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - 1);
            } else if (periodType.equals(DAY.name())) {
                payToDate = DateUtils.addStringDayRT(DateUtils.timeToTimeLow(approveDate), Integer.valueOf(period) - 1);
            }
        }
        return payToDate;
    }

    private String InitialAmount = "InitialAmount";
    private String AssessAmount = "AssessAmount";

    private Map<String, BigDecimal> calculationAmount(Long policyYear, Long renewalInsuranceFrequency, String productId, String policyId,
                                                      String productLevel, String mult, Long insuredBirthday, BigDecimal coverageTotalAmount, Long approveDate, String policyType, String dutyId) {
        Map<String, BigDecimal> amountMap = new HashMap<>();
        amountMap.put(InitialAmount, new BigDecimal(0));
        amountMap.put(AssessAmount, new BigDecimal(0));

        policyYear = AssertUtils.isNotNull(policyYear) ? policyYear : 1;
        mult = AssertUtils.isNotEmpty(mult) ? mult : "1";
        int age = 0;
        if (AssertUtils.isNotNull(insuredBirthday)) {
            try {
                age = DateUtils.getAgeYear(new Date(insuredBirthday), new Date(approveDate));
            } catch (Exception e) {
                this.getLogger().error(insuredBirthday + "===================计算保额异常===getAgeYear==================");
                e.printStackTrace();
            }
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_1.id())) {
            //1号：意外死亡或高残金乘以份数，
            int amount = "A".equals(productLevel) ? 5000 : "B".equals(productLevel) ? 10000 : "C".equals(productLevel) ? 20000 : 0;
            BigDecimal multiply = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, multiply);
            if (AssertUtils.isNotNull(renewalInsuranceFrequency)) {
                if (renewalInsuranceFrequency.equals(new Long(1))) {
                    multiply = multiply.add(multiply.multiply(new BigDecimal(0.1)));
                } else if (renewalInsuranceFrequency.equals(new Long(2))) {
                    multiply = multiply.add(multiply.multiply(new BigDecimal(0.2)));
                } else if (renewalInsuranceFrequency >= new Long(3)) {
                    multiply = multiply.add(multiply.multiply(new BigDecimal(0.3)));
                }
            }
            amountMap.put(AssessAmount, multiply);
        }
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_3.id()) || productId.equals("PRO88000000000001")) {
            // 3号：每份可以领取教育金总额乘以份数
            BigDecimal totalXxInsuranceAmount = new BigDecimal(300)
                    .multiply(new BigDecimal(mult)).multiply(new BigDecimal("A".equals(productLevel) && age < 13 ? 13 - (age > 7 ? age : 7) : 0));
            BigDecimal totalZxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 400 : "B".equals(productLevel) ? 500 : 0)
                    .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age < 19 ? 19 - (age > 13 ? age : 13) : 0));
            BigDecimal totalDxInsuranceAmount = new BigDecimal("A".equals(productLevel) ? 500 : "B".equals(productLevel) ? 700 : "C".equals(productLevel) ? 1200 : 0)
                    .multiply(new BigDecimal(mult)).multiply(new BigDecimal(age < 23 ? 23 - (age > 19 ? age : 19) : 0));
            BigDecimal totalAmount = totalXxInsuranceAmount.add(totalZxInsuranceAmount).add(totalDxInsuranceAmount);
            amountMap.put(InitialAmount, totalAmount);
            amountMap.put(AssessAmount, totalAmount);

        }
        // 4号：保额，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_4.id())) {
            BigDecimal amount = coverageTotalAmount.multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, amount);
            int amountPolicyYear = policyYear.intValue() - 2;
            if (policyYear - 2 > 0 && "AMOUNT_UP".equals(productLevel)) {
                amount = amount.add(amount.multiply(new BigDecimal(0.03)).multiply(new BigDecimal(amountPolicyYear)));
            }
            amountMap.put(AssessAmount, amount);
        }
        // 5号：保额，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_5.id()) || productId.equals(ProductTermEnum.PRODUCT.PRODUCT_28.id())) {
            amountMap.put(InitialAmount, coverageTotalAmount);
            PolicyBo policyBo = policyBaseService.queryPolicyBo(policyId);
            //此处问题斌哥说后期优化
            ApplyRequest applyRequest = (ApplyRequest) converterObject(policyBo, ApplyRequest.class);
            ResultObject<List<PolicyCashValueResponse>> cashValueResultObject = productApi.getCashValue(applyRequest);
            List<PolicyCashValueResponse> policyCashValueResponseList = cashValueResultObject.getData();
            if (AssertUtils.isNotEmpty(cashValueResultObject.getData())) {
                Long finalPolicyYear = policyYear;
                policyCashValueResponseList.stream().filter(policyCashValueResponse -> policyCashValueResponse.getPolicyYear() == finalPolicyYear.longValue())
                        .findFirst().ifPresent(policyCashValueResponse -> {
                    amountMap.put(AssessAmount, policyCashValueResponse.getAmount());
                });
            }
        }
        // 7号：保险金额乘以份数，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_7.id())) {
            int amount = "A".equals(productLevel) ? 480 : "B".equals(productLevel) ? 1200 : "C".equals(productLevel) ? 2880 : "D".equals(productLevel) ? 6600 : 0;
            BigDecimal totalAmount = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, totalAmount);
            amountMap.put(AssessAmount, totalAmount);
        }
        // 8号：
        if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_8.id(), ProductTermEnum.PRODUCT.PRODUCT_9.id(), ProductTermEnum.PRODUCT.PRODUCT_13.id(), ProductTermEnum.PRODUCT.PRODUCT_14.id(), ProductTermEnum.PRODUCT.PRODUCT_15.id(),
                ProductTermEnum.PRODUCT.PRODUCT_19.id(), ProductTermEnum.PRODUCT.PRODUCT_20.id(), ProductTermEnum.PRODUCT.PRODUCT_20A.id(), ProductTermEnum.PRODUCT.PRODUCT_21.id(), ProductTermEnum.PRODUCT.PRODUCT_22.id(),
                ProductTermEnum.PRODUCT.PRODUCT_23A.id(), ProductTermEnum.PRODUCT.PRODUCT_23B.id(), ProductTermEnum.PRODUCT.PRODUCT_24.id(), ProductTermEnum.PRODUCT.PRODUCT_29.id(), ProductTermEnum.PRODUCT.PRODUCT_33.id(),
                ProductTermEnum.PRODUCT.PRODUCT_34.id()
        ).contains(productId)) {
            amountMap.put(InitialAmount, coverageTotalAmount);
            amountMap.put(AssessAmount, coverageTotalAmount);
        }
        if (LIFE_INSURANCE_GROUP.name().equals(policyType)) {
            if (AssertUtils.isNotEmpty(productLevel)) {
                String[] productLevels = productLevel.split(",");
                String[] mults = mult.split(",");
                for (int i = 0; i < productLevels.length; i++) {
                    calculationGroupAmount(amountMap, productId, dutyId, productLevels[i], mults[i]);
                }
            }
        }
        return amountMap;
    }

    private void calculationGroupAmount(Map<String, BigDecimal> amountMap, String productId, String dutyId, String productLevel, String mult) {
        BigDecimal initialAmount = amountMap.get(InitialAmount);
        BigDecimal assessAmount = amountMap.get(AssessAmount);

        mult = AssertUtils.isNotEmpty(mult) ? mult : "1";
        // 1+：意外死亡或高残金乘以份数，
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_1_PLUS.id())) {
            int amount = "A".equals(productLevel) ? 5000 : "B".equals(productLevel) ? 10000 : "C".equals(productLevel) ? 20000 : 0;
            BigDecimal totalAmount = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, totalAmount.add(initialAmount));
            amountMap.put(AssessAmount, totalAmount.add(assessAmount));
        }
        // 7+：每个档次保险金额份数之和
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id())) {
            int amount = "A".equals(productLevel) ? 1440 : "B".equals(productLevel) ? 2880 : "C".equals(productLevel) ? 6600 : 0;
            BigDecimal totalAmount = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, totalAmount.add(initialAmount));
            amountMap.put(AssessAmount, totalAmount.add(assessAmount));
        }
        // 11号：每个档次保险金额（1）份数之和,
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_11.id())) {
            int amount = "A".equals(productLevel) ? 400 : "B".equals(productLevel) ? 600 : "C".equals(productLevel) ? 800 : 0;
            BigDecimal totalAmount = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, totalAmount.add(initialAmount));
            amountMap.put(AssessAmount, totalAmount.add(assessAmount));
        }
        // 12号：每个选项对应档次保险金额乘以份数之和
        if (productId.equals(ProductTermEnum.PRODUCT.PRODUCT_12.id())) {
            int amount = PolicyTermEnum.DUTY.PRO8800000000000G12_DUTY_1.name().equals(dutyId) ? ("A".equals(productLevel) ? 300 : "B".equals(productLevel) ? 500 : "C".equals(productLevel) ? 1000 : 0) :
                    PolicyTermEnum.DUTY.PRO8800000000000G12_DUTY_2.name().equals(dutyId) ? ("A".equals(productLevel) ? 2000 : "B".equals(productLevel) ? 3000 : "C".equals(productLevel) ? 5000 : 0) :
                            PolicyTermEnum.DUTY.PRO8800000000000G12_DUTY_3.name().equals(dutyId) ? ("A".equals(productLevel) ? 3000 : "B".equals(productLevel) ? 6000 : "C".equals(productLevel) ? 10000 : 0) : 0;
            BigDecimal totalAmount = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, totalAmount.add(initialAmount));
            amountMap.put(AssessAmount, totalAmount.add(assessAmount));
        }

        // 17的保险金额按上传模板数据填列
        if (ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(productId)) {

        }

        // 18 单选档次对应的保额
        if (ProductTermEnum.PRODUCT.PRODUCT_18.id().equals(productId)) {
            int amount = 0;
            if (Arrays.asList("PLAN_1A", "PLAN_2A").contains(productLevel)) {
                amount = 3400;
            }
            if (Arrays.asList("PLAN_1B", "PLAN_2B").contains(productLevel)) {
                amount = 3900;
            }
            if (Arrays.asList("PLAN_1C", "PLAN_2C").contains(productLevel)) {
                amount = 6300;
            }
            if (Arrays.asList("PLAN_1D", "PLAN_2D").contains(productLevel)) {
                amount = 12300;
            }
            if (Arrays.asList("PLAN_1E", "PLAN_2E").contains(productLevel)) {
                amount = 20400;
            }
            BigDecimal totalAmount = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, totalAmount.add(initialAmount));
            amountMap.put(AssessAmount, totalAmount.add(assessAmount));
        }
        // 27 单选档次对应的保额
        if (ProductTermEnum.PRODUCT.PRODUCT_27.id().equals(productId)) {
            int amount = 0;
            if ("PLAN_A".equals(productLevel)) {
                amount = 10000;
            }
            if ("PLAN_B".equals(productLevel)) {
                amount = 12500;
            }
            if ("PLAN_C".equals(productLevel)) {
                amount = 15000;
            }
            if ("PLAN_D".equals(productLevel)) {
                amount = 17500;
            }
            if ("PLAN_E".equals(productLevel)) {
                amount = 20000;
            }
            if ("PLAN_F".equals(productLevel)) {
                amount = 30000;
            }
            if ("PLAN_G".equals(productLevel)) {
                amount = 40000;
            }
            if ("PLAN_H".equals(productLevel)) {
                amount = 50000;
            }
            BigDecimal totalAmount = new BigDecimal(amount).multiply(new BigDecimal(mult));
            amountMap.put(InitialAmount, totalAmount.add(initialAmount));
            amountMap.put(AssessAmount, totalAmount.add(assessAmount));
        }
    }


    @Override
    public ResultObject<List<ServiceChargeBankChannelBo>> syncPolicyServiceChargeBankChannel(BasePageRequest basePageRequest, String syncDate) {
        ResultObject resultObject = new ResultObject();
        List<ServiceChargeBankChannelBo> chargeBanChannelBoList = policyBaseService.syncPolicyServiceChargeBankChannel(basePageRequest, syncDate);
        resultObject.setData(chargeBanChannelBoList);
        return resultObject;
    }

    @Override
    public ResultObject<List<ServiceChargeBankChannelBo>> syncPolicyServiceChargeBankChannelPayment(BasePageRequest basePageRequest, String syncDate) {
        ResultObject resultObject = new ResultObject();
        List<ServiceChargeBankChannelBo> chargeBanChannelBoList = policyBaseService.syncPolicyServiceChargeBankChannelPayment(basePageRequest, syncDate);
        resultObject.setData(chargeBanChannelBoList);
        return resultObject;
    }

    @Override
    public ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy(BasePageRequest basePageRequest, String syncDate) {
        ResultObject resultObject = new ResultObject();
        List<SaleApplyPolicyBo> policyDetailBoList = policyBaseService.syncSaleReportPolicyDetail(basePageRequest, syncDate);

        if (!AssertUtils.isNotEmpty(policyDetailBoList)) {
            return resultObject;
        }
        List<String> policyIdList = policyDetailBoList.stream().map(SaleApplyPolicyBo::getPolicyId).distinct().collect(Collectors.toList());
        List<String> renewalPolicyNoList = policyDetailBoList.stream().map(SaleApplyPolicyBo::getPolicyNo).distinct().collect(Collectors.toList());
        List<SaleApplyPolicyBo> coverageBoList = policyBaseService.syncSaleReportCoverage(policyIdList);
        ResultObject<List<ReportRenewalResponse>> renewalResultObject = renewalReportApi.querySaleRenewalPolicy(renewalPolicyNoList);
        AssertUtils.isResultObjectError(this.getLogger(), renewalResultObject);
        List<ReportRenewalResponse> reportRenewalResponseList = renewalResultObject.getData();
        List<PolicyPaymentBo> coveragePaymentList = policyPaymentBaseService.queryFirstPaymentByPolicyIdList(policyIdList);

        List<PolicyAmountBo> policyAmountBoList = queryPolicyAmount(policyIdList);
        ResultObject<List<ReportEndorseResponse>> policyCoverageDetailList = endorseApi.queryPolicyCoverageDetailList(policyIdList);
        AssertUtils.isResultObjectError(this.getLogger(), policyCoverageDetailList);
        List<ReportEndorseResponse> policyCoverageDetailListData = policyCoverageDetailList.getData();

        List<PolicyPo> policyPos = policyDao.fetchByPolicyId(policyIdList.toArray(new String[0]));

        List<ProductTermEnum.PRODUCT> products = Arrays.asList(ProductTermEnum.PRODUCT.values());

        if (AssertUtils.isNotEmpty(coverageBoList)) {
            coverageBoList.forEach(saleApplyPolicyBo -> {
                if (AssertUtils.isNotEmpty(policyDetailBoList)) {
                    Optional<SaleApplyPolicyBo> first = policyDetailBoList.stream().filter(s -> s.getPolicyId().equals(saleApplyPolicyBo.getPolicyId())).findFirst();
                    if (first.isPresent()) {
                        SaleApplyPolicyBo s = first.get();
                        ClazzUtils.copyPropertiesIgnoreNull(s, saleApplyPolicyBo);
                    }
                }
                if (AssertUtils.isNotEmpty(policyAmountBoList)) {
                    double initialAmount = policyAmountBoList.stream()
                            .filter(policyAmountBo -> AssertUtils.isNotNull(policyAmountBo.getInitialAmount()) && policyAmountBo.getPolicyId().equals(saleApplyPolicyBo.getPolicyId()) && policyAmountBo.getProductId().equals(saleApplyPolicyBo.getProductId()))
                            .mapToDouble(policyAmountBo -> policyAmountBo.getInitialAmount().doubleValue()).sum();
                    saleApplyPolicyBo.setAmount(new BigDecimal(initialAmount));
                }
                products.stream().filter(product -> product.id().equals(saleApplyPolicyBo.getProductId()))
                        .findFirst().ifPresent(product -> saleApplyPolicyBo.setProductName(product.desc()));
                //保全多减了
                /*if (AssertUtils.isNotEmpty(policyCoverageDetailListData)) {
                    policyCoverageDetailListData.stream().filter(reportEndorseResponse ->
                            reportEndorseResponse.getApplyId().equals(saleApplyPolicyBo.getPolicyId()) &&
                                    reportEndorseResponse.getProductId().equals(saleApplyPolicyBo.getProductId()))
                            .findFirst().ifPresent(reportEndorseResponse -> {
                        if (AssertUtils.isNotNull(reportEndorseResponse.getRefundAmount())) {
                            saleApplyPolicyBo.setTotalActualPremium(saleApplyPolicyBo.getTotalActualPremium().subtract(reportEndorseResponse.getRefundAmount()));
                        }
                    });
                }*/
                if (AssertUtils.isNotNull(reportRenewalResponseList)) {
                    reportRenewalResponseList.stream().filter(reportRenewalResponse -> reportRenewalResponse.getRenewalPolicyNo().equals(saleApplyPolicyBo.getPolicyNo()))
                            .findFirst().ifPresent(reportRenewalResponse -> {
                        if (LIFE_INSURANCE_PERSONAL.name().equals(saleApplyPolicyBo.getApplyType())) {
                            saleApplyPolicyBo.setAppSubmitUnderwritingDate(reportRenewalResponse.getApplyDate());
                            saleApplyPolicyBo.setUnderwriteStartDate(reportRenewalResponse.getApplyConfirmDate());
                            saleApplyPolicyBo.setUnderwriteEndDate(reportRenewalResponse.getConfirmDate());
                        }
                        if (LIFE_INSURANCE_GROUP.name().equals(saleApplyPolicyBo.getApplyType())) {
                            saleApplyPolicyBo.setAppSubmitUnderwritingDate(reportRenewalResponse.getApplyDate());
                            saleApplyPolicyBo.setUnderwriteStartDate(reportRenewalResponse.getApplyUnderwriteDate());
                            saleApplyPolicyBo.setUnderwriteEndDate(reportRenewalResponse.getUnderwriteDate());
                        }
                    });
                }
                if (AssertUtils.isNotNull(coveragePaymentList)) {
                    coveragePaymentList.stream().filter(policyPaymentBo -> policyPaymentBo.getPolicyId().equals(saleApplyPolicyBo.getPolicyId()))
                            .findFirst().ifPresent(policyPaymentBo -> {
                        saleApplyPolicyBo.setGainedDate(policyPaymentBo.getGainedDate());
                    });
                }

                    PolicyPremiumBo policyPremiumBo = policyPremiumBaseService.queryPolicyPremiumBo(saleApplyPolicyBo.getPolicyId());
                    if (AssertUtils.isNotNull(policyPremiumBo)) {
                        //若是现金折扣，则只设置主险，不用设置附加险
                        if (AssertUtils.isNotNull(policyPremiumBo.getSpecialDiscount()) && AssertUtils.isNotNull(policyPremiumBo.getDiscountModel())) {
                            if (ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name().equals(policyPremiumBo.getDiscountModel())) {
                                saleApplyPolicyBo.setSpecialDiscount(policyPremiumBo.getSpecialDiscount());
                                saleApplyPolicyBo.setDiscountModel(policyPremiumBo.getDiscountModel());
                                saleApplyPolicyBo.setPremiumBeforeDiscount(policyPremiumBo.getPremiumBeforeDiscount());
                                saleApplyPolicyBo.setApplyTotalActualPremium(policyPremiumBo.getTotalActualPremium());
                                saleApplyPolicyBo.setPromotionalCode(policyPremiumBo.getPromotionalCode());
                            }
                            if (ProductTermEnum.DISCOUNT_MODEL.FIXED_AMOUNT.name().equals(policyPremiumBo.getDiscountModel()) && PolicyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(saleApplyPolicyBo.getPrimaryFlag())) {
                                saleApplyPolicyBo.setSpecialDiscount(policyPremiumBo.getSpecialDiscount());
                                saleApplyPolicyBo.setDiscountModel(policyPremiumBo.getDiscountModel());
                                saleApplyPolicyBo.setPremiumBeforeDiscount(policyPremiumBo.getPremiumBeforeDiscount());
                                saleApplyPolicyBo.setApplyTotalActualPremium(policyPremiumBo.getTotalActualPremium());
                            }
                        }
                    }
                policyPos.stream().filter(policyPo -> policyPo.getPolicyId().equals(saleApplyPolicyBo.getPolicyId())).findFirst().ifPresent(policyPo -> {
                    // eMoney PromoCode
                    if ("INIT_AGENT_ONLINE003".equals(saleApplyPolicyBo.getAgentId()) && PolicyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(saleApplyPolicyBo.getChannelTypeCode())) {
                        saleApplyPolicyBo.setPromotionalCode(policyPo.getReferralCode());
                    }
                    // 激活码对应的参考号作为销售报表里的优惠码
                    if (AssertUtils.isNotEmpty(policyPo.getActivationCode())) {
                        ResultObject<ScratchCardCoverageResponse> scratchCardCoverage = applyPlanApi.getScratchCardCoverage(policyPo.getActivationCode());
                        if (!AssertUtils.isResultObjectDataNull(scratchCardCoverage) && AssertUtils.isNotNull(scratchCardCoverage.getData().getCard())) {
                            saleApplyPolicyBo.setPromotionalCode(scratchCardCoverage.getData().getCard().getReferenceNo());
                            saleApplyPolicyBo.setPartnerId(scratchCardCoverage.getData().getCard().getPartnerId());
                            saleApplyPolicyBo.setRemark(scratchCardCoverage.getData().getRemark());
                        }
                    }
                });
            });
        }
        resultObject.setData(coverageBoList);
        return resultObject;
    }

    @Override
    public List<PolicyAmountBo> queryPolicyAmount(List<String> policyIdList) {
        List<PolicyAmountBo> policyAmountBoList = policyBaseService.queryPolicyAmount(policyIdList);
        if (!AssertUtils.isNotEmpty(policyAmountBoList)) {
            return policyAmountBoList;
        }
        // 查询理赔额
        List<String> claimProductIdList = Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_7.id(), ProductTermEnum.PRODUCT.PRODUCT_7_PLUS.id(), ProductTermEnum.PRODUCT.PRODUCT_12.id());
        List<String> claimCoverageIdList = policyAmountBoList.stream().filter(reserveWithdrawalReportBo -> claimProductIdList.contains(reserveWithdrawalReportBo.getProductId())
        ).map(PolicyAmountBo::getCoverageId).collect(Collectors.toList());
        List<ClaimCoverageAmountBo> claimCoverageAmountBoList = null;
        if (AssertUtils.isNotEmpty(claimCoverageIdList)) {
            ResultObject<List<ClaimCoverageAmountBo>> listResultObject = claimCoverageApi.queryClaimCoverageAmount(claimCoverageIdList);
            claimCoverageAmountBoList = listResultObject.getData();
        }
        List<ClaimCoverageAmountBo> finalClaimCoverageAmountBoList = claimCoverageAmountBoList;
        List<String> assessAmountPolicyStatus = Arrays.asList(POLICY_STATUS_INDEMNITY_TERMINATION.name(),
                POLICY_STATUS_HESITATION_REVOKE.name(),
                POLICY_STATUS_INVALID_THOROUGH.name(),
                POLICY_STATUS_EFFECT_TERMINATION.name(),
                POLICY_STATUS_SURRENDER.name(),
                POLICY_STATUS_IEXPIRE.name());
        List<String> assessAmountCoverageStatus = Arrays.asList(EXPIRED.name(), INDEMNITY_TERMINATION.name());

        policyAmountBoList.forEach(policyAmountBo -> {
            Long effectiveDate = policyAmountBo.getEffectiveDate();
            Long receivableDate = policyAmountBo.getReceivableDate();
            if (AssertUtils.isNotNull(effectiveDate) && AssertUtils.isNotNull(receivableDate)) {
                Long policyYear = (DateUtils.intervalMonth(effectiveDate, receivableDate) / 12 + 1);
                policyAmountBo.setPolicyYear(policyYear);
            }
            //设置保额
            Long policyYear = policyAmountBo.getPolicyYear();
            Long renewalInsuranceFrequency = policyAmountBo.getRenewalInsuranceFrequency();
            String productId = policyAmountBo.getProductId();
            String policyId = policyAmountBo.getPolicyId();
            if (policyId == null) {
                System.out.println(JSON.toJSON(policyAmountBo));
            }
            String productLevel = policyAmountBo.getProductLevel();
            String mult = policyAmountBo.getMult();
            Long insuredBirthday = policyAmountBo.getInsuredBirthday();
            BigDecimal coverageTotalAmount = AssertUtils.isNotNull(policyAmountBo.getAmount()) ? policyAmountBo.getAmount() : new BigDecimal(0);
            Long approveDate = policyAmountBo.getApproveDate();
            String policyType = policyAmountBo.getPolicyType();
            String dutyId = policyAmountBo.getDutyId();
            Map<String, BigDecimal> amountMap = calculationAmount(policyYear, renewalInsuranceFrequency, productId, policyId, productLevel, mult, insuredBirthday, coverageTotalAmount, approveDate, policyType, dutyId);
            policyAmountBo.setInitialAmount(amountMap.get(InitialAmount));
            policyAmountBo.setAssessAmount(amountMap.get(AssessAmount));
            if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_17.id(), ProductTermEnum.PRODUCT.PRODUCT_18.id(), ProductTermEnum.PRODUCT.PRODUCT_26.id(), ProductTermEnum.PRODUCT.PRODUCT_27.id(), ProductTermEnum.PRODUCT.PRODUCT_29.id(), ProductTermEnum.PRODUCT.PRODUCT_33.id()).contains(productId)) {
                policyAmountBo.setInitialAmount(policyAmountBo.getAmount());
                policyAmountBo.setAssessAmount(policyAmountBo.getAmount());
            }

            if (AssertUtils.isNotEmpty(finalClaimCoverageAmountBoList) && claimProductIdList.contains(policyAmountBo.getProductId())) {
                finalClaimCoverageAmountBoList.stream().filter(claimCoverageAmountBo ->
                        claimCoverageAmountBo.equals(policyAmountBo.getCoverageId()) &&
                                claimCoverageAmountBo.getProductDutyId().equals(policyAmountBo.getDutyId())
                ).findFirst().ifPresent(claimCoverageAmountBo -> {
                    policyAmountBo.setAssessAmount(policyAmountBo.getAssessAmount().subtract(claimCoverageAmountBo.getTotalAmount()));
                });
            }

            if (assessAmountPolicyStatus.contains(policyAmountBo.getPolicyStatus()) || assessAmountCoverageStatus.contains(policyAmountBo.getCoverageStatus())
                    ||
                    (!EFFECTIVE.name().equals(policyAmountBo.getInsuredStatus()) && LIFE_INSURANCE_GROUP.name().equals(policyAmountBo.getPolicyType()))) {
                policyAmountBo.setAssessAmount(new BigDecimal(0));
            }
        });
        return policyAmountBoList;
    }

/*    public int getAgeByBirthday(Long ageDate) {
        int age = 0;
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = format.parse(format.format(ageDate));
            age = DateUtils.getAgeYear(date);

        } catch (Exception e) {
            throw new RequestException(PolicyErrorConfigEnum.APPLY_APP_TRANS_BIRTHDAY_TO_AGE_ERROR);
        }
        return age;
    }*/
}