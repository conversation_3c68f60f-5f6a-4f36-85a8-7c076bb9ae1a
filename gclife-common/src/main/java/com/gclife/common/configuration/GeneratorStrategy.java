package com.gclife.common.configuration;

import org.jooq.meta.CatalogDefinition;
import org.jooq.meta.Definition;
import org.jooq.meta.SchemaDefinition;
import org.jooq.tools.StringUtils;
//import org.jooq.util.CatalogDefinition;
//import org.jooq.util.Definition;
//import org.jooq.util.SchemaDefinition;
//import org.jooq.util.DefaultGeneratorStrategy;
import  org.jooq.codegen.DefaultGeneratorStrategy;
/**
 * jooq自定义生成策略
 */
public class GeneratorStrategy extends DefaultGeneratorStrategy {


    /**
     * Override this method to define how your Java classes and Java files should
     * be named. This example applies no custom setting and uses CamelCase versions
     * instead
     */
    @Override
    public String getJavaClassName(Definition definition, Mode mode) {
        String name = getFixedJavaClassName(definition);
        return name != null ? name : this.getJavaClassName0(definition, mode);
    }


    private String getJavaClassName0(Definition definition, Mode mode) {
        StringBuilder result = new StringBuilder();
        result.append(StringUtils.toCamelCase(definition.getOutputName().replace(' ', '_').replace('-', '_').replace('.', '_')));
        if (mode == Mode.RECORD) {
            result.append("Record");
        } else if (mode == Mode.DAO) {
            result.append("Dao");
        } else if (mode == Mode.POJO) {
            result.append("Po");
        } else if (mode == Mode.INTERFACE) {
            result.insert(0, "I");
        }
        System.out.println("----------> " + result.toString());
        return result.toString();
    }

    final String getFixedJavaClassName(Definition definition) {
        if (definition instanceof CatalogDefinition && ((CatalogDefinition) definition).isDefaultCatalog()) {
            return "DefaultCatalog";
        } else {
            return definition instanceof SchemaDefinition && ((SchemaDefinition) definition).isDefaultSchema() ? "DefaultSchema" : null;
        }
    }


    @Override
    public String getJavaClassExtends(Definition definition, Mode mode) {
        if (mode == Mode.POJO) {
            return "com.gclife.common.model.pojo.BasePojo";
        }else{
            return null;
        }
    }





}