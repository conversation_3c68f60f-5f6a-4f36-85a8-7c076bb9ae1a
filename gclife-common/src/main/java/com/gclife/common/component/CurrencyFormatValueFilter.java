package com.gclife.common.component;

import com.alibaba.fastjson.serializer.ValueFilter;
import com.gclife.common.annotation.CurrencyFormat;
import com.gclife.common.util.ReflectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * <AUTHOR> chenjinrong
 * @Date: 2020/8/27 10:15
 * @Description:
 */
public class CurrencyFormatValueFilter implements ValueFilter {

    Logger logger = LoggerFactory.getLogger(CurrencyFormatValueFilter.class);
    @Override
    public Object process(Object object, String name, Object value) {
        return convertValue(object, name, value);
    }

    Object convertValue(Object object, String name, Object value) {
        try {
            /**
             * 反射获取field
             */
            Field field = ReflectionUtils.getDeclaredField(object,name);
            if(field ==null){
                if (name != null && name.length() != 0) {
                    char[] chars = name.toCharArray();
                    if(chars[0] >= 'A' && chars[0]<='Z'){
                        chars[0] = Character.toUpperCase(chars[0]);
                        String pascalName = new String(chars);
                        field = ReflectionUtils.getDeclaredField(object,pascalName);
                    }
                }
            }
            /**
             *判断字段是否存在@CurrencyFormat注解
             */
            if (field != null && field.isAnnotationPresent(CurrencyFormat.class)) {
                CurrencyFormat dataFormat = field.getAnnotation(CurrencyFormat.class);
                if (dataFormat.pattern() != null) {
                    if(value instanceof String){
                        return new DecimalFormat(dataFormat.pattern()).format(new BigDecimal(value.toString()));
                    }
                    return new DecimalFormat(dataFormat.pattern()).format(value);
                }
                return value;
            }
        }catch (Exception e){
            logger.error("货币格式出现异常：{}", ExceptionUtils.getFullStackTrace(e));
        }
        return value;
    }
}
