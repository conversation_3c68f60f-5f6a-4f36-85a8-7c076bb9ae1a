package com.gclife.common.util;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.Iterator;


public class ImageUtils {


    /**
     * 图片压缩成字节
     * @param img               要压缩的图片
     * @param compressConfig  压缩配置
     * @return
     */
    public byte [] compress(BufferedImage img,CompressConfig compressConfig) {

        ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
        //compress(img, compressConfig,byteArrayOutputStream);
        return byteArrayOutputStream.toByteArray();
    }

//
//    /**
//     * 图片压缩
//     * @param img               要压缩的图片
//     * @param compressConfig   压缩配置
//     * @param outputStream     输出到哪里
//     */
//    public static void compress(BufferedImage img,CompressConfig compressConfig,OutputStream outputStream) {
//
//        try {
//            if (null== img || img.getWidth(null) == -1) {
//                return;
//            }
//
//            Size  size= compressConfig.getSize();
//            int oldWidth=img.getWidth(null);
//            int oldHeight = img.getHeight(null);
//            int width = size.getWidth( oldWidth,oldHeight );
//            int height = size.getHeight( oldWidth,oldHeight );
//
//            BufferedImage tag = new BufferedImage( width,  height, BufferedImage.TYPE_INT_RGB);
//            tag.getGraphics().drawImage(img.getScaledInstance(width, height, compressConfig.getAlgorithm().value()), 0, 0, null);
//            JPEGEncodeParam param=JPEGCodec.getDefaultJPEGEncodeParam(tag);
//            param.setQuality(compressConfig.getQuality(),false);
//            JPEGImageEncoder encoder=JPEGCodec.createJPEGEncoder(outputStream,param);
//            encoder.encode(tag);
//        } catch (IOException ex) {
//            ex.printStackTrace();
//        }finally {
//            IoUtils.closeQuietly(outputStream);
//        }
//    }

    public static String getImageFormatName(InputStream inputStream,boolean autoClose) {
        ImageInputStream iis = null;
        try {
            iis = ImageIO.createImageInputStream(inputStream);
            Iterator<ImageReader> imageReaders = ImageIO.getImageReaders(iis);
            if (!imageReaders.hasNext()) {
                return null;
            }
            return imageReaders.next().getFormatName();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            IoUtils.closeQuietly(iis);
            IoUtils.closeOrReset(inputStream,autoClose);
        }
        return null;
    }

    /**
     * 判断是否是图片
     * @param inputStream
     * @return
     */
    public static boolean isImage(InputStream inputStream,boolean autoClose){

        try {
            return null != ImageIO.read(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }finally {
            IoUtils.closeOrReset(inputStream,autoClose);
        }
    }

    /**
     * 给图片转换格式
     * @param image           图片的流
     * @param format      要转换成的格式
     * @param os           输入的流
     * @return
     * @throws IOException
     */
    public static void converter(BufferedImage image,String format, OutputStream os)
            throws IOException{
        //此方法以关闭 input 与 output
        ImageIO.write(image, format, os);
    }

    /**
     * 给图片转换图片
     * @param image
     * @param format
     * @param formatFile
     * @return
     * @throws IOException
     */
    public static File converter(BufferedImage image, String format, File formatFile)
            throws IOException{
        ImageIO.write(image, format, formatFile);
        return formatFile;
    }

    public static byte[] converter(BufferedImage image,String format){
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            converter(image, format, out);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }


    public static BufferedImage read(InputStream inputStream) throws IOException {
        return ImageIO.read(inputStream);
    }

    public static BufferedImage read(File file) throws IOException {
        return ImageIO.read(file);
    }

    public static BufferedImage read(URL url) throws IOException {
        return ImageIO.read(url);
    }

    public static BufferedImage read(ImageInputStream imageInputStream) throws IOException {
        return ImageIO.read(imageInputStream);
    }


    /**
     * 通过BufferedImage图片流调整图片大小
     */
    public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) throws IOException {
        Image resultingImage = originalImage.getScaledInstance(targetWidth, targetHeight, Image.SCALE_AREA_AVERAGING);
        BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        outputImage.getGraphics().drawImage(resultingImage, 0, 0, null);
        return outputImage;
    }


    /**
     * BufferedImage图片流转byte[]数组
     */
    public static byte[] imageToBytes(BufferedImage bImage) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            ImageIO.write(bImage, "jpg", out);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toByteArray();
    }


    /**
     * byte[]数组转BufferedImage图片流
     */
    private static BufferedImage bytesToBufferedImage(byte[] ImageByte) {
        ByteArrayInputStream in = new ByteArrayInputStream(ImageByte);
        BufferedImage image = null;
        try {
            image = ImageIO.read(in);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return image;
    }

}
