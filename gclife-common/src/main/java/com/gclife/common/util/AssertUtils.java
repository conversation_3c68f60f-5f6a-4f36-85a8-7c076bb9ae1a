/**
 * Aibton.com Inc.
 * Copyright (c) 2004-2017 All Rights Reserved.
 */
package com.gclife.common.util;

import com.gclife.common.exception.RequestException;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.BaseErrorConfigEnum;
import com.gclife.common.model.inter.IEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * 断言工具类
 * <AUTHOR>
 * @version v 0.1 2017/5/9 22:10 huzhihui Exp $$
 */
public class AssertUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(AssertUtils.class);

    /**
     * 断言不为整型；如果为不为整型 || ""  "  "    抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNotInteger(Logger logger, String value, IEnum enumConfig) {
        try {
            Long.valueOf(value);
        } catch (Exception e) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }


    /**
     * 断言不为大于等于0的整数；如果为不为大于等于0的整数 || ""  "  "    抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNotPureDigital(Logger logger, String value, IEnum enumConfig) {
        try {
            if(Long.valueOf(value)<0){
                logger.error(enumConfig.getValue());
                throw new RequestException(enumConfig);
            }
        } catch (Exception e) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }

    /**
     * 断言不为大于等于0的数；如果为不为大于等于0的数 || ""  "  "    抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNotDigital(Logger logger, String value, IEnum enumConfig) {
        try {
            if(new BigDecimal(value).compareTo(new BigDecimal(0))==-1){
                logger.error(enumConfig.getValue());
                throw new RequestException(enumConfig);
            }
        } catch (Exception e) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }


    /**
     * 断言不为空；如果为null || ""  "  "    返回false
     * @param value 待检查值
     */
    public static boolean isNotEmpty(String value) {
        if (null == value || value.trim().equals("")) {
            return false;
        }else {
            return  true;
        }
    }

    /**
     * 断言不为空；如果为null || ""  "  "    抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNotEmpty(Logger logger, String value, IEnum enumConfig) {
        if (null == value || value.trim().equals("")) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }

    /**
     * 断言不为空；如果为null || ""  "  "    抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param msg   异常返回信息
     */
    public static void isNotEmpty(Logger logger, String value, String msg) {
        if (null == value || value.trim().equals("")) {
            logger.error(msg);
            throw new RequestException(msg);
        }
    }


    /**
     * 断言不为空；如果不为null 返回true
     * @param value 待检查值
     * @return
     */
    public static boolean isNotNull(Object value) {
        if (null == value) {
            return false;
        }else{
            if(value instanceof List){
                if(((List) value).size()==0){
                    return false;
                }
            }else if(value instanceof Map){
                if(((Map) value).size()==0){
                    return false;
                }
            }else if(value.getClass().isArray()){
                return Array.getLength(value)>0;
            }
            return true;
        }
    }

    /**
     * 断言不为空；如果为null 抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNotNull(Logger logger, Object value, IEnum enumConfig) {
        if (null == value) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }

    /**
     * 断言不为空；如果为null 抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param msg   异常返回信息
     */
    public static void isNotNull(Logger logger, Object value, String msg) {
        if (null == value) {
            logger.error(msg);
            throw new RequestException(msg);
        }
    }

    /**
     * 断言不为空；如果为null || list.size() == 0    抛出异常终止执行
     * @param logger    Logger
     * @param list  待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNotEmpty(Logger logger, List list, IEnum enumConfig) {
        if (null == list || list.size() == 0) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }

    /**
     * 断言不为空；如果为null || list.size() == 0    抛出异常终止执行
     * @param list  待检查值
     * @return  boolean    异常返回枚举
     */
    public static boolean isNotEmpty(List list) {
        if (null == list || list.size() == 0) {
            return false;
        }
        return true;
    }

    /**
     * 断言不为空；如果为null    抛出异常终止执行
     * @param list  待检查值
     * @return  boolean    异常返回枚举
     */
    public static boolean isNotEmpty(Iterable list) {
        if (null == list) {
            return false;
        }
        return true;
    }

    /**
     * 断言不为空；如果为null || list.size() == 0    抛出异常终止执行
     * @param logger    Logger
     * @param list  待检查值
     * @param msg   异常返回信息
     */
    public static void isNotEmpty(Logger logger, List list, String msg) {
        if (null == list || list.size() == 0) {
            logger.error(msg);
            throw new RequestException(msg);
        }
    }

    /**
     * 断言不为空；如果为null || map.size() == 0     抛出异常终止执行
     * @param logger    Logger
     * @param map   待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNotEmpty(Logger logger, Map map, IEnum enumConfig) {
        if (null == map || map.size() == 0) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }

    /**
     * 断言不为空；如果为null || map.size() == 0     抛出异常终止执行
     * @param map   待检查值
     * @return boolean    异常返回枚举
     */
    public static boolean isNotEmpty(Map map) {
        if (null == map || map.size() == 0) {
            return false;
        }
        return true;
    }

    /**
     * 断言不为空；如果为null || map.size() == 0     抛出异常终止执行
     * @param logger    Logger
     * @param map   待检查值
     * @param msg   异常返回信息
     */
    public static void isNotEmpty(Logger logger, Map map, String msg) {
        if (null == map || map.size() == 0) {
            logger.error(msg);
            throw new RequestException(msg);
        }
    }

    /**
     * 断言为空；如果为不为null 抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isNull(Logger logger, Object value, IEnum enumConfig) {
        if (null != value) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }

    /**
     * 断言请求失败；如果为不为null 抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isResultObjectError(Logger logger, ResultObject value, IEnum enumConfig) {
        if(value==null){
            throw new RequestException(BaseErrorConfigEnum.FEGIN_REQUEST_RESULTOBJECT_IS_NULL.name());
        }else
        if ( !BaseErrorConfigEnum.SUCCESS.name().equals(value.getError())) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }


    /**
     * 断言请求数据为空
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isResultObjectDataNull(Logger logger, ResultObject value, IEnum enumConfig) {
        if(value==null){
            throw new RequestException(BaseErrorConfigEnum.FEGIN_REQUEST_RESULTOBJECT_IS_NULL.name());
        }else
        if ( !BaseErrorConfigEnum.SUCCESS.name().equals(value.getError())) {
            throwsResultObjectException(logger,value);
        }else if(value.getData()==null){
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }


    /**
     * 断言请求数据为空
     * @param logger    Logger
     * @param value 待检查值
     * @param enumConfig    异常返回枚举
     */
    public static void isResultObjectListDataNull(Logger logger, ResultObject value, IEnum enumConfig) {
        if(value==null){
            throw new RequestException(BaseErrorConfigEnum.FEGIN_REQUEST_RESULTOBJECT_IS_NULL.name());
        }else
        if (!BaseErrorConfigEnum.SUCCESS.name().equals(value.getError())) {
            throwsResultObjectException(logger,value);
        }else if(value.getData()==null || ((List)value.getData()).size()==0) {
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }


    /**
     * 断言请求失败；如果为空 且执行不为SUCCESS
     * @param value 待检查值
     * @return boolean
     */
    public static boolean isResultObjectError(ResultObject value) {
        if (null == value || !BaseErrorConfigEnum.SUCCESS.name().equals(value.getError())) {
            return true;
         }else {
            return false;
        }
    }


    /**
     * 断言请求数据为空
     * @param value 待检查值
     * @return boolean
     */
    public static boolean isResultObjectDataNull(ResultObject value) {
        if (null == value || !BaseErrorConfigEnum.SUCCESS.name().equals(value.getError()) ||
                value.getData()==null) {
            return true;
        }else {
            return false;
        }
    }


    /**
     * 断言请求数据为空
     * @param value 待检查值
     * @return
     */
    public static boolean isResultObjectListDataNull(ResultObject value) {
        if (null == value || !BaseErrorConfigEnum.SUCCESS.name().equals(value.getError()) ||
                value.getData()==null || ((List)value.getData()).size()==0) {
            return true;
        }else {
            return false;
        }
    }

    /**
     * 验证邮箱
     * @param email　邮箱
     * @return  返回
     */
    public static boolean isEmail(String email) {
        return email.matches("\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*");
    }

    public static boolean isDouble(String num) {
        boolean flag = true;

        try {
            Double.valueOf(num);
        } catch (Exception var3) {
            flag = false;
        }

        return flag;
    }

    public static void isDouble(Logger logger,String num) {
        try {
            Double.valueOf(num);
        } catch (Exception var3) {
            logger.error(BaseErrorConfigEnum.DOUBLE_VALID_ERROR.name());
            throw new RequestException(BaseErrorConfigEnum.DOUBLE_VALID_ERROR);
        }
    }

    public static void isDouble(Logger logger,String num,IEnum iEnum) {
        try {
            if(AssertUtils.isNotEmpty(num)){
                Double.valueOf(num);
            }
        } catch (Exception var3) {
            logger.error(iEnum.getValue());
            throw new RequestException(iEnum);
        }
    }


    public static boolean isDoubleGtZero(String num) {
        boolean flag = true;
        try {
            if(Double.valueOf(num)<=0)throw new Exception();
        } catch (Exception var3) {
            flag = false;
        }

        return flag;
    }

    public static void isDoubleGtZero(Logger logger,String num) {
        try {
            if(Double.valueOf(num)<=0)throw new Exception();
        } catch (Exception var3) {
            logger.error(BaseErrorConfigEnum.DOUBLE_VALID_GT_ZERO_ERROR.name());
            throw new RequestException(BaseErrorConfigEnum.DOUBLE_VALID_GT_ZERO_ERROR);
        }
    }


    /**
     * 验证邮箱
     * @param email　邮箱
     * @return  返回
     */
    public static void isEmail(Logger logger,String email,IEnum enumConfig) {
        if(!email.matches("\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*")){
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }

    /**
     * 判断字符串是否为空
     * @param str
     * null、“ ”、“null”都返回true
     * @return
     */
    public static boolean isNullString(String str) {
        return (null == str || StringUtils.isEmpty(str.trim()) || "null".equals(str.trim().toLowerCase())) ? true : false;
    }

    /**
     * 判断字符串是否为空
     * @param str
     * null、“ ”、“null”都返回true
     * @return
     */
    public static boolean isNotNullString(String str) {
        return (null == str || StringUtils.isEmpty(str.trim()) || "null".equals(str.trim().toLowerCase())) ? false : true;
    }

    /**
     * 判断字符串是否为空
     * @param str
     * null、“ ”、“null”都返回true
     * @return
     */
    public static void isNotNullString(Logger logger,String str,IEnum enumConfig) {
        if((null == str || StringUtils.isEmpty(str.trim()) || "null".equals(str.trim().toLowerCase())) ? false : true){
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }



    /**
     * 验证正数
     * @param num　数字
     * @return  返回
     */
    public static boolean isPositiveNum(long num) {
        return num>0?true:false;
    }

    /**
     * 验证正数
     * @param num　数字
     * @return  返回
     */
    public static void isPositiveNum(Logger logger,long num,IEnum enumConfig) {
        if(num<=0?true:false){
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }
    }


    /***
     *判断是否为时间戳
     */
    public static boolean isTimestamp(long time){
        boolean flag =false;
        try {
            String a =""+time;
            if(a.length()<=13){
                flag=true;
            }
        }catch (Exception e){
        }
        return flag;
    }



    /***
     *判断是否为时间戳
     */
    public static void isTimestamp(Logger logger, long time){
        String a =""+time;
//        if(a.length()>13){
//            logger.error(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL.getValue());
//            throw new RequestException(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL);
//        }
    }

    /***
     *判断是否为时间戳
     */
    public static boolean isDateTimestamp(long time){
        boolean flag =true;
        try {
            String a =""+time;
//            if(a.length()<=13 && "000000000".equals(DateUtils.timeStrToString(time,"HHmmssSSS"))){
//                flag=true;
//            }
        }catch (Exception e){
        }
        return flag;
    }

    /***
     *判断是否为时间戳是否到日期
     */
    public static void isDateTimestamp(Logger logger, long time){
        LOGGER.debug("==========11time111============:"+time);
        LOGGER.debug("====================local111==========:"+ TimeZone.getDefault());
        LOGGER.debug("==========date=111===========:"+DateUtils.timeStrToString(time,"yyyy-MM-dd HH:mm:ss"));
        String a =""+time;
        if(a.length()>13){
            //logger.error(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL.getValue());
            //throw new RequestException(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL);
        }
         else if(!"000000000".equals(DateUtils.timeStrToString(time,"HHmmssSSS"))){
            LOGGER.debug("==========11time============:"+time);
            LOGGER.debug("====================local==========:"+ TimeZone.getDefault());
            LOGGER.debug("==========date============:"+DateUtils.timeStrToString(time,"yyyy-MM-dd HH:mm:ss"));
            //logger.error(BaseErrorConfigEnum.TIMESTAMP_DATE_FORMAT_FAIL.getValue());
            //throw new RequestException(BaseErrorConfigEnum.TIMESTAMP_DATE_FORMAT_FAIL);
        }

    }

    public static void isDateFormat(Logger logger,String s,String pattern){
        try {
            if(AssertUtils.isNotEmpty(s)){
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
                simpleDateFormat.setLenient(false);
                Date date =simpleDateFormat.parse(s);
                String s2 =simpleDateFormat.format(date);
                if(s2.length()!=s.length()){
                    logger.error(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL.getValue());
                    throw new RequestException(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL);
                }
            }
        }catch (Exception e){
            logger.error(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL.getValue());
            throw new RequestException(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL);
        }

    }

    public static void isDateFormat(Logger logger,String s,String pattern,IEnum enumConfig){
        try {
            if(AssertUtils.isNotEmpty(s)){
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
                simpleDateFormat.setLenient(false);
                Date date =simpleDateFormat.parse(s);
                String s2 =simpleDateFormat.format(date);
                if(s2.length()!=s.length()){
                    logger.error(enumConfig.getValue());
                    throw new RequestException(enumConfig);
                }
            }
        }catch (Exception e){
            logger.error(enumConfig.getValue());
            throw new RequestException(enumConfig);
        }

    }

    public static boolean isDateFormat(String s,String pattern){
        try {
            if(AssertUtils.isNotEmpty(s)){
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
                simpleDateFormat.setLenient(false);
                Date date =simpleDateFormat.parse(s);
                String s2 =simpleDateFormat.format(date);
                if(s2.length()!=s.length()){
                    return false;
                }
                return true;
            }else {
                return false;
            }
        }catch (Exception e){
            return false;
        }
    }



    public static void isDateFormat(Logger logger,String s){
        try {
            if(AssertUtils.isNotEmpty(s)){
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                simpleDateFormat.setLenient(false);
                Date date =simpleDateFormat.parse(s);
                String s2 =simpleDateFormat.format(date);
                if(s2.length()!=s.length()){
                    logger.error(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL.getValue());
                    throw new RequestException(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL);
                }
            }
        }catch (Exception e){
            logger.error(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL.getValue());
            throw new RequestException(BaseErrorConfigEnum.TIMESTAMP_FORMAT_FAIL);
        }

    }


    public static boolean isDateFormat(String s){
        try {
            if(AssertUtils.isNotEmpty(s)){
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                simpleDateFormat.setLenient(false);
                Date date =simpleDateFormat.parse(s);
                String s2 =simpleDateFormat.format(date);
                if(s2.length()!=s.length()){
                    return false;
                }
            }
        }catch (Exception e){
            return false;
        }
        return true;
    }

    /**
     * 判断对象里面属性是否全为空
     * @param obj 对象
     * @return  boolean
     */
    public static boolean checkObjFieldAllIsNull(Object obj) {
        boolean flag = true;
        try {
            for(Field f : obj.getClass().getDeclaredFields()){
                f.setAccessible(true);
                if(AssertUtils.isNotNull(f.get(obj))){
                    flag = false;
                    return flag;
                }
            }
        }catch (Exception e){

        }
        return flag;
    }


    /**
     * 断言请求失败；如果为不为null 抛出异常终止执行
     * @param logger    Logger
     * @param value 待检查值
     */
    public static void isResultObjectError(Logger logger, ResultObject value) {
        if(value==null){
            throw new RequestException(BaseErrorConfigEnum.FEGIN_REQUEST_RESULTOBJECT_IS_NULL.name());
        }else
        if (!BaseErrorConfigEnum.SUCCESS.name().equals(value.getError())) {
            throwsResultObjectException(logger,value);
        }
    }


    /**
     * 断言请求数据为空
     * @param logger    Logger
     * @param value 待检查值
     */
    public static void isResultObjectDataNull(Logger logger, ResultObject value) {
        if(value==null){
            throw new RequestException(BaseErrorConfigEnum.FEGIN_REQUEST_RESULTOBJECT_IS_NULL.name());
        }else
        if (!BaseErrorConfigEnum.SUCCESS.name().equals(value.getError()) ||
                value.getData()==null) {
            throwsResultObjectException(logger,value);
        }
    }


    /**
     * 断言请求数据为空
     * @param logger    Logger
     * @param value 待检查值
     */
    public static void isResultObjectListDataNull(Logger logger, ResultObject value) {
        if(value==null){
            throw new RequestException(BaseErrorConfigEnum.FEGIN_REQUEST_RESULTOBJECT_IS_NULL.name());
        }else
        if ( !BaseErrorConfigEnum.SUCCESS.name().equals(value.getError()) ||
                value.getData()==null || ((List)value.getData()).size()==0) {
            throwsResultObjectException(logger,value);
        }
    }


    /**
     * ResultObject错误抛出
     * @param logger　日志
     * @param value　对象
     */
    private static void throwsResultObjectException(Logger logger, ResultObject value){
        //计算方法的问题
        IEnum iEnum = new IEnum() {
            @Override
            public String getCode() {
                return value.getError();
            }

            @Override
            public String getValue() {
                return value.getMessage();
            }

            @Override
            public String getGroup() {
                return value.getGroup();
            }

            @Override
            public String getCode(String s, String s1) {
                return null;
            }

            @Override
            public String getValue(String s, String s1) {
                return null;
            }

            @Override
            public String getName() {
                return value.getErrorCode();
            }
        };
        logger.error(iEnum.getValue());
        throw new RequestException(iEnum);
    }

}
