package com.gclife.common.model.config;

/**
 * 术语 枚举
 *
 * <AUTHOR>
 */
public class TerminologyConfigEnum {

    private TerminologyConfigEnum() {
        throw new AssertionError();
    }

    public interface ConstantType {
    }


    /**
     * 根节点
     */
    public enum ROOT implements ConstantType {
        ROOT("根节点");
        private String code;
        private String desc;

        private ROOT(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 语言
     */
    public enum LANGUAGE implements ConstantType {
        KM_KH("柬埔寨"),
        EN_US("英文"),
        ZH_CN("中文"),
        ZH_HK("中文香港"),
        ZH_TW("中文台湾"),
        JA_JP("日语"),
        KO_KR("朝鲜语(韩国)"),
        MS_MY("马来西亚语"),
        TH_TH("泰语"),
        VI_VN("越南语"),
        ID_ID("印度尼西亚语"),
        RU_RU("俄语"),
        ;
        private String code;
        private String desc;

        private LANGUAGE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 代理人否在职状态
     */
    public enum AGENT_STATUS implements ConstantType {
        DRAG("离职"),
        INDUCTION("在职"),
        ;
        private String code;
        private String desc;

        private AGENT_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 流水出入类型
     */
    public enum ACCOUNT_STREAM_TYPE implements ConstantType {
        INPUT("流入"),
        OUTPUT("流出"),
        ;
        private String code;
        private String desc;

        private ACCOUNT_STREAM_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 地址默认标识
     */
    public enum ADDRESS_DEFAULT_FLAG implements ConstantType {
        FALSE("否"),
        TRUE("是"),
        ;
        private String code;
        private String desc;

        private ADDRESS_DEFAULT_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 职员是否在职状态
     */
    public enum EMPLOYEE_STATUS implements ConstantType {
        DRAG("离职"),
        INDUCTION("在职"),
        ;
        private String code;
        private String desc;

        private EMPLOYEE_STATUS(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 职员是否在职状态
     */
    public enum WHETHER implements ConstantType {
        YES("是"),
        NO("否"),
        ;
        private String code;
        private String desc;

        private WHETHER(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 启用
     */
    public enum ENABLED implements ConstantType {
        ENABLED("启用"),
        DISABLED("禁用"),
        ;
        private String code;
        private String desc;

        private ENABLED(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 性别
     */
    public enum GENDER implements ConstantType {
        FEMALE("女"),
        MALE("男"),
        ;
        private String code;
        private String desc;

        private GENDER(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 菜单是否有效
     */
    public enum MENU_EFFECTIVE_TYPE implements ConstantType {
        INEFFECTIVE("无效"),
        EFFECTIVE("有效"),

        ;
        private String code;
        private String desc;

        private MENU_EFFECTIVE_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 菜单是否显示
     */
    public enum MENU_VISIBLE_TYPE implements ConstantType {
        INVISIBLE("不显示"),
        VISIBLE("显示"),
        ;
        private String code;
        private String desc;

        private MENU_VISIBLE_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 基础角色标志
     */
    public enum ROLE_BASE_FLAG implements ConstantType {
        YES("是"),
        NO("否"),
        ;
        private String code;
        private String desc;

        private ROLE_BASE_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 工作流任务状态
     */
    public enum VALID_FLAG implements ConstantType {
        /**
         * 有效
         */
        effective("有效"),
        /**
         * 失效
         */
        invalid("失效");;
        private String code;
        private String desc;

        private VALID_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 币种
     */
    public enum CURRENCY implements ConstantType {
        CNY("人民币"),
        SUR("俄罗斯卢布"),
        CAD("加拿大元"),
        IDR("印尼盾"),
        ATS("奥地利先令"),
        DEM("德国马克"),
        LUF("意大利里拉"),
        ITL("意大利里拉"),
        SGD("新加坡元"),
        NZD("新西兰元"),
        JPY("日元"),
        KHR("柬埔寨瑞尔"),
        EUR("欧元"),
        BEF("比利时法郎"),
        FRF("法国法郎"),
        THB("泰铢"),
        HKD("港币"),
        AUD("澳大利亚元"),
        IEP("爱尔兰镑"),
        CHF("瑞士法郎"),
        USD("美元"),
        FIM("芬兰马克"),
        GBP("英镑"),
        NLG("荷兰盾"),
        PHP("菲律宾比索"),
        PTE("葡萄牙埃斯库多"),
        ESP("西班牙比塞塔"),
        KRW("韩国元"),
        MYR("马来西亚林吉特"),
        ;
        private String code;
        private String desc;

        private CURRENCY(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 折扣类型
     */
    public enum DISCOUNT_TYPE implements ConstantType {
        /**
         * 公司承担
         */
        FULLY_BORNE_COMPANY("公司承担"),
        /**
         * 业务员承担
         */
        FULLY_BORNE_AGENT("业务员承担"),
        /**
         * 按比例承担
         */
        SPLIT_BETWEEN_COMPANY_AND_AGENT("按比例承担"),
        ;

        private String code;
        private String desc;

        private DISCOUNT_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

}
