package com.gclife.apply.report.controller;

import com.gclife.apply.report.service.ApplyReportBusinessService;
import com.gclife.common.configuration.system.permissions.AutoSaveURL;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.report.api.model.response.SaleApplyPolicyBo;
import com.gclife.report.api.model.response.ServiceChargeBankChannelBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * create 18-11-8
 * description:
 */
@Api(tags = "投保单报表基础接口", description = "投保单报表基础接口")
@RefreshScope
@RestController
@RequestMapping("v1/base/apply")
public class ApplyReportController extends BaseController {
    @Autowired
    private ApplyReportBusinessService applyReportBusinessService;

    @ApiOperation(value = "获取投保单报表数据", notes = "获取投保单报表数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "report")
    @AutoSaveURL()
    public ResultObject syncReportApply() {
        return null;
    }

    @ApiOperation(value = "查询投保单信息跟投保人信息--收付费明细报表", notes = "查询投保单信息跟投保人信息--收付费明细报表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "applys")
    public ResultObject listApplyRecord(@RequestBody List<String> businessId) {
        return applyReportBusinessService.queryListApplyReportBo(businessId);
    }


    @ApiOperation(value = "执行同步投保单银保渠道明细表", notes = "执行同步投保单银保渠道明细表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/apply/service/charge/bank/channel")
    public ResultObject<List<ServiceChargeBankChannelBo>> syncApplyServiceChargeBankChannel(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return applyReportBusinessService.syncApplyServiceChargeBankChannel(basePageRequest, syncDate);
    }

    @ApiOperation(value = "执行同步销售报表数据", notes = "执行同步销售报表数据")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "sync/sale/apply/policy")
    public ResultObject<List<SaleApplyPolicyBo>> syncSaleApplyPolicy(@RequestParam(value = "currentPage") Integer currentPage, @RequestParam(value = "pageSize") Integer pageSize, @RequestParam(value = "syncDate") String syncDate) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return applyReportBusinessService.syncSaleApplyPolicy(basePageRequest, syncDate);
    }

}