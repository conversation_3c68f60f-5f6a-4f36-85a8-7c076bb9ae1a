package com.gclife.apply.controller.group;

import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.request.BaseApplyUnderwriteDecisionRequest;
import com.gclife.apply.model.response.group.ApplyReviewResponse;
import com.gclife.apply.service.business.group.GroupUnderWriteService;
import com.gclife.common.configuration.system.permissions.AutoCheckPermissions;
import com.gclife.common.controller.base.BaseController;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.policy.model.response.UnderwriteInfoResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "group_underwrite", description = "团险投保单人工核保")
@RestController
@RequestMapping(value = "/v1/group/")
public class GroupUnderWriteController extends BaseController {

    @Autowired
    private GroupUnderWriteService groupUnderWriteService;

    @ApiOperation(value = "underwrite/sign", notes = "人工核保任务签收")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @ApiParam(value = "投保单ID", name = "applyId", example = "APPLY_0e17d997-2287-4598-94b6-b0ea467a9aab",required = true)
    @PutMapping(value = "underwrite/sign")
    public ResultObject underWriteSignPost(String applyId) {
        return groupUnderWriteService.underWriteSignPost(getCurrentLoginUsers(), applyId);
    }


    @ApiOperation(value = "查询投保人工核保列表", notes = "查询投保单人工核保列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "apply/artificial/underwriting")
    public ResultObject<BasePageResponse<ApplyReviewResponse>> getArtificialUnderwriting(ApplyListRequest applyListVo) {
        return groupUnderWriteService.getArtificialUnderwriting(this.getCurrentLoginUsers(), applyListVo);

    }

    @ApiOperation(value = "下发核保决定", notes = "下发核保决定")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @PostMapping(value = "apply/send/underwriting")
    public ResultObject postSendUnderwriting(@RequestBody BaseApplyUnderwriteDecisionRequest applyUnderwriteDecisionRequest) {
        return groupUnderWriteService.postSendUnderwriting(this.getCurrentLoginUsers(), this.getAppRequestHandler(),applyUnderwriteDecisionRequest);
    }

   /* @ApiOperation(value = "上传暂予承保申请书影像(只针对17号产品)", notes = "上传暂予承保申请书影像")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @PostMapping(value = "pre/underwriting/image/attachment/{applyId}")
    public ResultObject preUnderwritingAttachmentSave(@PathVariable String applyId, @RequestBody List<PreImageAttachmentRequest> appAttachmentRequests) {
        System.out.println("上传暂予承保申请书影像--" + JSONObject.toJSONString(appAttachmentRequests));
        return groupUnderWriteService.savePreUnderwritingImageAttachment(applyId, appAttachmentRequests);
    }

    @ApiOperation(value = "获取暂予承保申请书影像(只针对17号产品)", notes = "获取暂予承保申请书影像")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "pre/underwriting/image/attachment")
    public ResultObject<List<AppAttachmentResponse>> getPreUnderwritingImageAttachment(@RequestParam(value = "applyId") String applyId) {
        return groupUnderWriteService.getPreUnderwritingImageAttachment(applyId,this.getCurrentLoginUsers());
    }*/

    @ApiOperation(value = "暂予承保生效处理", notes = "暂予承保生效处理（定时任务调用）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @GetMapping(value = "approved/hc/effective")
    public String dealApprovedHcEffective(@RequestParam(name = "pageSize") Integer pageSize,
                                @RequestParam(name = "currentPage") Integer currentPage) {
        BasePageRequest basePageRequest = new BasePageRequest();
        basePageRequest.setCurrentPage(currentPage);
        basePageRequest.setPageSize(pageSize);
        return groupUnderWriteService.dealApprovedHcEffective(this.getCurrentLoginUsers(), basePageRequest);
    }

    @ApiOperation(value = "查询团险人工核保信息", notes = "查询团险人工核保信息")
    @ApiResponses({
            @ApiResponse(code = 200, message = "请求成功")
    })
    @AutoCheckPermissions
    @GetMapping(value = "uw/info")
    public ResultObject<UnderwriteInfoResponse> getGroupUWInfo(@RequestParam(name = "applyId") String applyId) {
        return groupUnderWriteService.getGroupUWInfo(applyId);
    }
}
