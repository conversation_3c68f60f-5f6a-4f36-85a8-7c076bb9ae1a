package com.gclife.apply.service.business.app.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.agent.model.response.AgentSignBaseResponse;
import com.gclife.agent.model.response.AgentSimpleBaseResponse;
import com.gclife.app.api.AppPlanApi;
import com.gclife.apply.api.ApplyPaymentApi;
import com.gclife.apply.core.jooq.tables.daos.*;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.*;
import com.gclife.apply.dao.app.AppApplyBusinessDao;
import com.gclife.apply.dao.app.ApplyPlanBusinessDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.app.ApplyApplicantPlanBo;
import com.gclife.apply.model.bo.app.ApplyInfoExtBo;
import com.gclife.apply.model.bo.app.ApplyInsuredPlanBo;
import com.gclife.apply.model.bo.app.ApplyPlanBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.ModelConstantEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.feign.ApplyAttachmentResp;
import com.gclife.apply.model.feign.ProductCertifyRespFc;
import com.gclife.apply.model.request.*;
import com.gclife.apply.model.request.app.AppAttachmentRequest;
import com.gclife.apply.model.request.app.*;
import com.gclife.apply.model.respone.ApplyAutoUWResponse;
import com.gclife.apply.model.respone.IdentityOcrResponse;
import com.gclife.apply.model.response.*;
import com.gclife.apply.model.response.app.*;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.ApplyAutoUnderWriteBusinessService;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.business.PaymentBusinessService;
import com.gclife.apply.service.business.app.ApplyPlanBusinessService;
import com.gclife.apply.service.data.ApplyBoService;
import com.gclife.apply.service.data.impl.ApplyPlanDataSaveService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.business.ApplyPlanBusinessValidate;
import com.gclife.apply.validate.parameter.app.ApplyPlanParameterValidate;
import com.gclife.apply.validate.parameter.app.transform.AppApplyTransData;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.config.AttachmentPdfEnum;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.policy.policy.PolicyAttachmentBo;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.certify.api.CertifyApplyApi;
import com.gclife.certify.model.response.CertifyNumberResponse;
import com.gclife.common.error.ThirdpartyErrorConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.function.GcFactorFunction;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.BaseTermEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.message.model.request.SmsVerifyCodeCheckRequest;
import com.gclife.party.api.CustomerClientApi;
import com.gclife.party.api.CustomerManageApi;
import com.gclife.party.model.request.CustomerBusinessRequest;
import com.gclife.party.model.response.ClientAgentResponse;
import com.gclife.party.model.response.UserCustomerResponse;
import com.gclife.platform.api.*;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.BankResponse;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.PolicyAttachmentResponse;
import com.gclife.product.api.ProductApi;
import com.gclife.product.api.ProductCertifyApi;
import com.gclife.product.api.ProductSalesApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.request.calculate.ApplyRequest;
import com.gclife.product.model.response.apply.ApplyResponse;
import com.gclife.product.model.response.insurnce.certify.CertifyAttachmentTypeResponse;
import com.gclife.product.model.response.sales.ProductSalesDetailedResponse;
import com.gclife.thirdparty.api.ThirdpartyApi;
import com.gclife.thirdparty.model.response.ocr.OcrGeneralResponse;
import com.gclife.workflow.api.WorkFlowApi;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyErrorConfigEnum.*;
import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_PLAN_STATUS.FINISH;
import static com.gclife.apply.model.config.ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.*;
import static com.gclife.apply.model.config.ApplyTermEnum.MSG_BUSINESS_TYPE.PRODUCTION_PLAN_NOTICE;

/**
 * <AUTHOR>
 * create 17-11-27
 * description:
 */
@Service
@Slf4j
public class ApplyPlanBusinessServiceImpl extends BaseBusinessServiceImpl implements ApplyPlanBusinessService {
    @Autowired
    private AppApplyTransData appApplyTransData;
    @Autowired
    private ApplyPlanBusinessDao applyPlanBusinessDao;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private PlatformBankApi platformBankApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private ApplyPlanParameterValidate applyPlanParameterValidate;
    @Autowired
    private ApplyExtDao applyExtDao;
    @Autowired
    private ApplyBoService applyBoService;
    @Autowired
    private ProductSalesApi productSalesApi;
    @Autowired
    private ProductCertifyApi productCertifyApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private ApplyPlanDataSaveService applyPlanDataSaveService;
    @Autowired
    private ApplyPlanBusinessValidate applyPlanBusinessValidate;
    @Autowired
    private AppApplyBusinessDao appApplyBusinessDao;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private PaymentBusinessService paymentBusinessService;
    @Autowired
    private ApplyConfigBranchExtDao applyConfigBranchExtDao;
    @Autowired
    private CertifyApplyApi certifyApplyApi;
    @Autowired
    private ApplyApplicantDao applyApplicantDao;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private ApplyAgentExtDao applyAgentExtDao;
    @Autowired
    private CustomerManageApi customerManageApi;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyBeneficiaryDao applyBeneficiaryDao;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;
    @Autowired
    private ApplyAttachmentBaseService applyAttachmentBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private ApplyLoanBaseService applyLoanBaseService;
    @Autowired
    private ApplyReferralInfoBaseService applyReferralInfoBaseService;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private ApplyOtherInfoBaseService applyOtherInfoBaseService;
    @Autowired
    private ApplyInsuredDao applyInsuredDao;
    @Autowired
    private ApplyAutoUnderWriteBusinessService applyAutoUnderWriteBusinessService;
    @Autowired
    private ThirdpartyApi thirdpartyApi;
    @Autowired
    private IdentityOcrRecordBaseService identityOcrRecordBaseService;
    @Autowired
    private ApplyBaseDao applyBaseDao;
    @Autowired
    private ApplyPlanBaseDao applyPlanBaseDao;
    @Autowired
    private ApplyInsuredBaseDao applyInsuredBaseDao;
    @Autowired
    private AppPlanApi appPlanApi;
    @Autowired
    private ApplyPaymentApi applyPaymentApi;
    @Autowired
    private ApplyElectronicSignatureDao applyElectronicSignatureDao;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private CustomerClientApi customerClientApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private ApplyPlanBaseService applyPlanBaseService;
    @Autowired
    private ApplyCoveragePlanDao applyCoveragePlanDao;

    @Override
    public ResultObject<BasePageResponse<ApplyPlanBo>> getApplyPlanList(String keyword, String agentId, Integer currentPage, Integer pageSize) {
        ResultObject<BasePageResponse<ApplyPlanBo>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), agentId, ApplyErrorConfigEnum.APPLY_AGENT_ID_IS_NOT_NULL);
            BasePageRequest basePageRequest = new BasePageRequest();
            if (AssertUtils.isNotNull(currentPage)) {
                basePageRequest.setCurrentPage(currentPage);
            }
            if (AssertUtils.isNotNull(pageSize)) {
                basePageRequest.setPageSize(pageSize);
            }
            List<ApplyPlanBo> listApplyPlanBo = applyPlanBusinessDao.getApplyPlanListByAgentId(keyword, agentId, basePageRequest);
            if (AssertUtils.isNotEmpty(listApplyPlanBo)) {
                BasePageResponse<ApplyPlanBo> basePageResponse = BasePageResponse.getData(currentPage, pageSize, listApplyPlanBo.get(0).getTotalLine(), listApplyPlanBo);
                resultObject.setData(basePageResponse);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyApplicantBo> getApplicantInfo(String applyId) {
        ResultObject<ApplyApplicantBo> resultObject = new ResultObject<>();
        try {
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);

            ApplyApplicantBo applyApplicantBo = applyPlanBusinessDao.loadApplyApplicantById(applyId);
            ApplyOccupationNaturePo applyOccupationNaturePo = applyOtherInfoBaseService.queryApplyOccupationNaturePo(applyApplicantBo.getApplyId(), ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            if (AssertUtils.isNotNull(applyOccupationNaturePo)) {
                applyApplicantBo.setOccupationNature(applyOccupationNaturePo);
            }
            AssertUtils.isNotNull(this.getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
            resultObject.setData(applyApplicantBo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyPo> getApplyInfo(String applyId) {
        ResultObject<ApplyPo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            resultObject.setData(applyPo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AppApplyBankResponse> getApplicantBankInfo(String applyId) {
        ResultObject<AppApplyBankResponse> resultObject = new ResultObject<>();
        AppApplyBankResponse appApplyBankResponse = new AppApplyBankResponse();
        try {
            ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
            appApplyBankResponse.setApplicantName(applyApplicantBo.getName());
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyAccountBo applyAccountBo = applyPlanBusinessDao.loadAccountById(applyId);
            if (!AssertUtils.isNotNull(applyAccountBo)) {
                resultObject.setData(appApplyBankResponse);
                return resultObject;
            }

            SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.getTerminology(TerminologyTypeEnum.BANK_CARD_TYPE.name(), applyAccountBo.getAccountType()).getData();
            AreaNameResponse areaNameRespFc = platformAreaApi.areaNameGet(applyAccountBo.getAreaCode()).getData();
            BankResponse bankResponse = platformBankApi.bankGet(applyAccountBo.getBankCode()).getData();

            //数据转换
            appApplyBankResponse.setBankCode(applyAccountBo.getBankCode());
            appApplyBankResponse.setBankName(AssertUtils.isNotNull(bankResponse) ? bankResponse.getBankName() : applyAccountBo.getBankCode());
            appApplyBankResponse.setAccountType(applyAccountBo.getAccountType());
            appApplyBankResponse.setAccountTypeName(AssertUtils.isNotNull(syscodeRespFc) ? syscodeRespFc.getCodeName() : applyAccountBo.getAccountType());
            appApplyBankResponse.setAreaCode(applyAccountBo.getAreaCode());
            appApplyBankResponse.setCity(AssertUtils.isNotNull(areaNameRespFc) ? areaNameRespFc.getAreaName() : applyAccountBo.getAreaCode());
            appApplyBankResponse.setAccountNo(applyAccountBo.getAccountNo());
            resultObject.setData(appApplyBankResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_APPLY_ACCOUNT_QUERY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject putApplicantBankInfo(AppPlanBankRequest appPlanBankRequest) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            applyPlanParameterValidate.validParameterAppPlanBank(appPlanBankRequest);
            applyPlanBusinessValidate.validBusinessAppPlanBank(appPlanBankRequest);

            ApplyApplicantBo applyApplicantBo = applyPlanBusinessDao.loadApplyApplicantById(appPlanBankRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
            BankResponse bankResponse = platformBankApi.bankGet(appPlanBankRequest.getBankCode()).getData();
            AssertUtils.isNotNull(this.getLogger(), bankResponse, ApplyErrorConfigEnum.APPLY_BUSINESS_BANK_IS_NOT_FOUND_OBJECT);

            ApplyAccountBo applyAccountBo = applyPlanBusinessDao.loadAccountById(appPlanBankRequest.getApplyId());
            if (!AssertUtils.isNotNull(applyAccountBo)) {
                applyAccountBo = new ApplyAccountBo();
            }
            //数据转换
            applyAccountBo = appApplyTransData.transApplyAccountBo(appPlanBankRequest, applyApplicantBo, applyAccountBo, bankResponse);

            applyBoService.saveApplyAccountPo(applyAccountBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_APPLY_ACCOUNT_SAVE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyPlanBo> getApplyPlanInfo(String applyPlanId) {
        ResultObject<ApplyPlanBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyPlanId, ApplyErrorConfigEnum.APPLY_PLAN_ID_IS_NOT_NULL);
            ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(applyPlanId, null, null);
            if (AssertUtils.isNotNull(applyPlanBo)) {
                List<ApplyPlanTracePo> applyPlanTracePos = applyBaseService.queryApplyPlanTracePo(applyPlanBo.getApplyPlanId());
                applyPlanBo.setApplyPlanTraces(applyPlanTracePos);
                ApplyPlanLoanPo applyPlanLoanPo = applyLoanBaseService.queryApplyPlanLoanPo(applyPlanId);
                applyPlanBo.setPlanLoanContract(applyPlanLoanPo);
                //计划书附件
                applyPlanBo.setAttachments(applyAttachmentBaseService.getApplyPlanAttachment(applyPlanId));
            }
            resultObject.setData(applyPlanBo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyPlanBo> getApplyPlanByApplyId(String applyId) {
        ResultObject<ApplyPlanBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(null, applyId, null);
            if (AssertUtils.isNotNull(applyPlanBo)) {
                List<ApplyPlanTracePo> applyPlanTracePos = applyBaseService.queryApplyPlanTracePo(applyPlanBo.getApplyPlanId());
                applyPlanBo.setApplyPlanTraces(applyPlanTracePos);
                ApplyPlanLoanPo applyPlanLoanPo = applyLoanBaseService.queryApplyPlanLoanPo(applyPlanBo.getApplyPlanId());
                applyPlanBo.setPlanLoanContract(applyPlanLoanPo);
                applyPlanBo.setAttachments(applyAttachmentBaseService.getApplyPlanAttachment(applyPlanBo.getApplyPlanId()));
            }
            resultObject.setData(applyPlanBo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据激活码获取计划书详情
     *
     * @param activationCode 激活码
     * @return ApplyPlanBo
     */
    @Override
    public ResultObject<ApplyPlanBo> getApplyPlanInfoByActivationCode(String activationCode) {
        ResultObject<ApplyPlanBo> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(activationCode)) {
            return resultObject;
        }
        ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(null, null, activationCode);
        if (AssertUtils.isNotNull(applyPlanBo)) {
            applyPlanBo.setAttachments(applyAttachmentBaseService.getApplyPlanAttachment(applyPlanBo.getApplyPlanId()));
        }
        resultObject.setData(applyPlanBo);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<ApplyPlanBo> saveApplyPlanInfo(ApplyPlanBo applyPlanBo, Users currentLoginUsers) {
        ResultObject<ApplyPlanBo> resultObject = new ResultObject<>();
        ApplyPo apply = null;
        try {
            this.getLogger().info("applyPlanBo请求参数：" + JSONObject.toJSONString(applyPlanBo));
            Map<String, String> messageParamMap = new HashMap<>();
            //查询代理人信息
            ResultObject<AgentResponse> agentResponseResultObject = agentApi.agentByIdGet(applyPlanBo.getAgentId());
            AssertUtils.isResultObjectDataNull(this.getLogger(), agentResponseResultObject, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            AgentResponse agentResponse = agentResponseResultObject.getData();
            String channelType = agentResponse.getChannelTypeCode();
            // 查询机构信息
            ResultObject<BranchResponse> branchResultObject = platformBranchBaseApi.queryOneBranchById(agentResponse.getBranchId());
            AssertUtils.isResultObjectError(getLogger(), branchResultObject);
            BranchResponse branchResponse = branchResultObject.getData();
//            //代理人签约状态判断
//            ResultObject<AgentSignAuditRespFc> respFcResultObject = agentServiceInterface.getAgentSignAudit();
//            if (AssertUtils.isResultObjectError(respFcResultObject) || !ApplyTermEnum.CERTIFICATION_STATUS.CERTIFIED.name().equals(respFcResultObject.getData().getAuditStatus())) {
//                throw new RequestException(ApplyErrorConfigEnum.APPLY_PLAN_AGENT_NO_SIGN);
//            }
            //代理人签约状态判断
            ResultObject<AgentSignBaseResponse> respFcResultObject = baseAgentApi.queryOneAgentSignedAgentId(applyPlanBo.getAgentId());
            AssertUtils.isResultObjectError(this.getLogger(), respFcResultObject);
            if (AssertUtils.isResultObjectError(respFcResultObject) ||
                    !ApplyTermEnum.SIGN_STATUS.SIGN_COMPLETE.name().equals(respFcResultObject.getData().getSignStatus())) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_PLAN_AGENT_NO_SIGN);
            }
            //生成计划书号
            if (!AssertUtils.isNotEmpty(applyPlanBo.getApplyPlanNo())) {
                ResultObject<CertifyNumberResponse> certifyNumberRespFcResultObject = certifyApplyApi.certifyNumberGetNew(channelType,
                        ApplyTermEnum.BUSINESS_TYPE.PLAN.name(),
                        ApplyTermEnum.APPLICANT_TYPE.PERSONAL.name(),
                        AssertUtils.isNotNull(branchResponse.getBranchBank()) ? branchResponse.getBranchBank().getBankAbbreviation() : null,
                        branchResponse.getBranchId(), null, null, null);
                AssertUtils.isResultObjectDataNull(getLogger(), certifyNumberRespFcResultObject, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_CERTIFY_ERROR);
                applyPlanBo.setApplyPlanNo(certifyNumberRespFcResultObject.getData().getCertifyNumber());
            }
            //保存计划书
            applyBoService.saveApplyPlanPo(applyPlanBo);

            //同步客户app身份关联信息到投被保人计划书
            this.getLogger().info("客户app请求用户id：" + JSONObject.toJSONString(applyPlanBo.getClientUserId()));
            ClientAgentResponse clientAgentResponse = customerClientApi.getClientAgentInfo(applyPlanBo.getClientUserId()).getData();
            this.getLogger().info("客户app查询信息：" + JSONObject.toJSONString(clientAgentResponse));

            ApplyApplicantPlanBo applyApplicantPlanBo = applyPlanBo.getApplicant();
            ApplyInsuredPlanBo applyInsuredPlanBo = applyPlanBo.getInsured();

            /*this.getLogger().info("计划书投保人投保单id：" + JSONObject.toJSONString(applyPlanBo.getApplyId()));
            if (AssertUtils.isNotEmpty(applyPlanBo.getApplyId())) {
                apply = applyBaseDao.getApply(applyPlanBo.getApplyId());
            }*/

            //保存投保人
            if (AssertUtils.isNotNull(applyApplicantPlanBo)) {
                applyApplicantPlanBo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                ApplyApplicantPlanBo applicant = applyPlanBo.getApplicant();
                if (AssertUtils.isNotEmpty(applicant.getName())) {
                    applicant.setName(applicant.getName().toUpperCase().trim());
                    messageParamMap.put("customerName", applicant.getName());
                }
                //客户app数据回显填充，计划书投保单保存
                if (AssertUtils.isNotNull(clientAgentResponse)) {
                    applicant.setCustomerId(clientAgentResponse.getCustomerAgentId());
                    applicant.setFamilyName(clientAgentResponse.getFamilyName());
                    applicant.setGivenName(clientAgentResponse.getGivenName());
                    applicant.setName(clientAgentResponse.getFamilyName() + " " + clientAgentResponse.getGivenName());
                    applicant.setSex(clientAgentResponse.getSex());
                    applicant.setBirthday(clientAgentResponse.getBirthday());
                    applicant.setIdType(clientAgentResponse.getIdType());
                    applicant.setIdNo(clientAgentResponse.getIdNo());
                    applicant.setIdExpDate(clientAgentResponse.getIdExpDate());
                    applicant.setStature(clientAgentResponse.getStature());
                    applicant.setAvoirdupois(clientAgentResponse.getAvoirdupois());
                    applicant.setMobile(clientAgentResponse.getMobile());
                    applicant.setEmail(clientAgentResponse.getEmail());
                    this.getLogger().info("保存投保人成功：" + JSONObject.toJSONString(applicant));
                }
                applyBoService.saveApplyApplicantPlanPo(applicant);
                this.getLogger().info("保存投保人成功：" + JSONObject.toJSONString(applicant));

                //网销修改基础信息跳转，计划书信息改变同时修改投保单信息
                /*if (AssertUtils.isNotNull(apply)) {
                    if ("ONLINE".equals(apply.getChannelTypeCode())) {
                        ApplyApplicantPo applyApplicantPo = applyBaseDao.getApplyApplicant(applyPlanBo.getApplyId());
                        if (AssertUtils.isNotNull(applyApplicantPo)) {
                            applyApplicantPo.setSex(applyApplicantPlanBo.getSex());
                            applyApplicantPo.setBirthday(applyApplicantPlanBo.getBirthday());
                            applyApplicantPo.setOccupationCode(applyApplicantPlanBo.getOccupationCode());
                            applyApplicantDao.update(applyApplicantPo);
                        }
                    }
                }*/

            }

            //保存被保人
            if (AssertUtils.isNotNull(applyInsuredPlanBo)) {
                applyInsuredPlanBo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                if (AssertUtils.isNotEmpty(applyInsuredPlanBo.getName())) {
                    applyInsuredPlanBo.setName(applyInsuredPlanBo.getName().toUpperCase().trim());
                }
                //客户app数据回显填充，计划书投保单保存
                if (AssertUtils.isNotNull(clientAgentResponse)) {
                    applyInsuredPlanBo.setCustomerId(clientAgentResponse.getCustomerAgentId());
                    applyInsuredPlanBo.setFamilyName(clientAgentResponse.getFamilyName());
                    applyInsuredPlanBo.setGivenName(clientAgentResponse.getGivenName());
                    applyInsuredPlanBo.setName(clientAgentResponse.getFamilyName() + " " + clientAgentResponse.getGivenName());
                    applyInsuredPlanBo.setSex(clientAgentResponse.getSex());
                    applyInsuredPlanBo.setBirthday(clientAgentResponse.getBirthday());
                    applyInsuredPlanBo.setIdType(clientAgentResponse.getIdType());
                    applyInsuredPlanBo.setIdNo(clientAgentResponse.getIdNo());
                    applyInsuredPlanBo.setIdExpDate(clientAgentResponse.getIdExpDate());
                    applyInsuredPlanBo.setStature(clientAgentResponse.getStature());
                    applyInsuredPlanBo.setAvoirdupois(clientAgentResponse.getAvoirdupois());
                    applyInsuredPlanBo.setMobile(clientAgentResponse.getMobile());
                    applyInsuredPlanBo.setEmail(clientAgentResponse.getEmail());
                    this.getLogger().info("设置被保人成功：" + JSONObject.toJSONString(applyInsuredPlanBo));
                }
                applyBoService.saveApplyInsuredPlanPo(applyInsuredPlanBo);
                this.getLogger().info("保存被保人成功：" + JSONObject.toJSONString(applyInsuredPlanBo));

                //网销修改基础信息跳转，计划书信息改变同时修改投保单信息
                /*if (AssertUtils.isNotNull(apply)) {
                    if ("ONLINE".equals(apply.getChannelTypeCode())) {
                        ApplyInsuredBo applyInsuredBo = applyExtDao.loadApplyInsuredBoByApplyId(applyPlanBo.getApplyId());
                        if (AssertUtils.isNotNull(applyInsuredBo)) {
                            applyInsuredBo.setSex(applyApplicantPlanBo.getSex());
                            applyInsuredBo.setBirthday(applyApplicantPlanBo.getBirthday());
                            applyInsuredBo.setOccupationCode(applyApplicantPlanBo.getOccupationCode());
                            applyBoService.saveApplyInsuredPo(applyInsuredBo);
                        }
                    }
                }*/
            }

            //保存计划书贷款合同信息
            ApplyPlanLoanPo planLoanContract = applyPlanBo.getPlanLoanContract();
            if (AssertUtils.isNotNull(planLoanContract) && AssertUtils.isNotNull(planLoanContract.getLoanAmount())) {
                ApplyPlanLoanPo applyPlanLoanPo = applyLoanBaseService.queryApplyPlanLoanPo(applyPlanBo.getApplyPlanId());
                if (!AssertUtils.isNotNull(applyPlanLoanPo)) {
                    applyPlanLoanPo = new ApplyPlanLoanPo();
                }
                ClazzUtils.copyPropertiesIgnoreNull(planLoanContract, applyPlanLoanPo);
                if (!AssertUtils.isNotNull(applyPlanLoanPo.getLoanAmount())
                        && AssertUtils.isNotNull(applyPlanLoanPo.getNotConvertedLoanAmount())
                        && AssertUtils.isNotNull(applyPlanLoanPo.getExchangeRate())) {
                    applyPlanLoanPo.setLoanAmount(applyPlanLoanPo.getNotConvertedLoanAmount().multiply((BigDecimal.ONE.divide(applyPlanLoanPo.getExchangeRate(), 8, BigDecimal.ROUND_UP))));
                }
                applyPlanLoanPo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                applyLoanBaseService.saveApplyPlanLoanPo(currentLoginUsers.getUserId(), applyPlanLoanPo);
            }

            //保存计划书推荐信息
            ApplyPlanReferralInfoPo referralInfo = applyPlanBo.getReferralInfo();
            if (AssertUtils.isNotNull(referralInfo)) {
                ApplyPlanReferralInfoPo applyPlanReferralInfoPo = applyReferralInfoBaseService.queryApplyPlanReferralInfoPo(applyPlanBo.getApplyPlanId());
                if (!AssertUtils.isNotNull(applyPlanReferralInfoPo)) {
                    applyPlanReferralInfoPo = new ApplyPlanReferralInfoPo();
                }
                ClazzUtils.copyPropertiesIgnoreNull(referralInfo, applyPlanReferralInfoPo);
                applyPlanReferralInfoPo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                applyReferralInfoBaseService.saveApplyPlanReferralInfoPo(currentLoginUsers.getUserId(), applyPlanReferralInfoPo);
            }

            //保存计划书附件
            List<ApplyPlanAttachmentPo> attachments = applyPlanBo.getAttachments();
            if (AssertUtils.isNotEmpty(attachments)) {
                applyAttachmentBaseService.deleteApplyPlanAttachment(applyPlanBo.getApplyPlanId());
                attachments.forEach(applyPlanAttachmentPo -> {
                    applyPlanAttachmentPo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                    applyPlanAttachmentPo.setApplyPlanAttachmentId(null);
                });
                applyAttachmentBaseService.saveApplyPlanAttachment(attachments, currentLoginUsers.getUserId());
            }

            //保存推荐活动计划书附件
            List<ApplyPlanAttachmentPo> referralAttachments = applyPlanBo.getReferralAttachments();
            if (AssertUtils.isNotEmpty(referralAttachments) && AssertUtils.isNotEmpty(applyPlanBo.getReferralActivityId())) {
                applyAttachmentBaseService.deleteApplyPaymentAttachment(applyPlanBo.getApplyPlanId(), ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.REFERRAL_ACTIVITY_BANK.name());
                applyAttachmentBaseService.deleteApplyPaymentAttachment(applyPlanBo.getApplyPlanId(), ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.REFERRAL_ACTIVITY_ID.name());
                referralAttachments.forEach(applyPlanAttachmentPo -> {
                    applyPlanAttachmentPo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                    applyPlanAttachmentPo.setApplyPlanAttachmentId(null);
                });
                applyAttachmentBaseService.saveApplyPlanAttachment(referralAttachments, currentLoginUsers.getUserId());
            }
            //保存险种
            if (AssertUtils.isNotEmpty(applyPlanBo.getCoverages())) {
                applyPlanBo.getCoverages().forEach(applyPlanCoverageBo -> {
                    if (applyPlanCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())) {
                        messageParamMap.put("productName", applyPlanCoverageBo.getProductName());
                    }
                    applyPlanCoverageBo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                    applyBoService.saveApplyCoveragePlanPo(applyPlanCoverageBo);
                    //保存险种责任
                    if (AssertUtils.isNotEmpty(applyPlanCoverageBo.getListCoverageDuty())) {
                        applyPlanCoverageBo.getListCoverageDuty().forEach(applyCoverageDutyPlanBo -> {
                            applyCoverageDutyPlanBo.setApplyPlanId(applyPlanBo.getApplyPlanId());
                            applyCoverageDutyPlanBo.setCoveragePlanId(applyPlanCoverageBo.getCoveragePlanId());
                            applyBoService.saveApplyCoverageDutyPlanPo(applyCoverageDutyPlanBo);
                        });
                    }
                });
            }
            //发送消息  (兼容网销20a优惠码,已经发过就不再发了）
            if (AssertUtils.isNotNull(agentResponse) && FINISH.name().equals(applyPlanBo.getStatus()) && !AssertUtils.isNotEmpty(applyPlanBo.getPromotionalCode()) && !AssertUtils.isNotEmpty(applyPlanBo.getActivationCode())) {
                ResultObject<AgentResponse> recommendAgentResultObject = agentApi.agentByIdGet(agentResponse.getRecommendAgentId());
                AgentResponse recommendAgent = recommendAgentResultObject.getData();
                if (AssertUtils.isNotNull(recommendAgent)) {
                    messageParamMap.put("recommendAgentName", recommendAgent.getAgentName());
                    messageParamMap.put("agentName", agentResponse.getAgentName());
                    messageBusinessService.generateApplyPlanMessage(PRODUCTION_PLAN_NOTICE.name(), recommendAgent.getAgentId(), messageParamMap);
                }
            }
            resultObject.setData(applyPlanBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PLAN_SAVE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyNoResponse> getApplyNoInfo(String userId, String applyId, String deviceChannel) {
        ResultObject<ApplyNoResponse> resultObject = new ResultObject<>();
        ApplyNoResponse applyNoResponse = new ApplyNoResponse();
        try {
            //数据验证
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);

            ApplyNoExtResponse applyNoExtResponse = applyPlanBusinessDao.getApplyNoExt(applyId);

            AssertUtils.isNotNull(this.getLogger(), applyNoExtResponse, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            AgentResponse agentRespFc = agentApi.agentByUserIdGet(userId).getData();

            AssertUtils.isNotNull(this.getLogger(), agentRespFc, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

            ProductSalesDetailedResponse productSalesDetailedResponse = productSalesApi.getProductSimpleInfo(applyNoExtResponse.getProductId(), deviceChannel, ApplyTermEnum.YES_NO.NO.name()).getData();

            //转换
            if (AssertUtils.isNotNull(productSalesDetailedResponse)) {
                applyNoResponse.setProductId(productSalesDetailedResponse.getProductId());
                applyNoResponse.setProductName(productSalesDetailedResponse.getProductName());
                applyNoResponse.setProductBriefly(productSalesDetailedResponse.getProductBriefly());
                applyNoResponse.setProductAhumbnailUrl(productSalesDetailedResponse.getProductAhumbnailUrl());
                applyNoResponse.setProviderLogoUrl(productSalesDetailedResponse.getProvider().getLogoUrl());
            }
            applyNoResponse.setApplicantIdNo(applyNoExtResponse.getApplicantIdNo());
            applyNoResponse.setApplicantName(applyNoExtResponse.getApplicantName());
            applyNoResponse.setApplyNo(applyNoExtResponse.getApplyNo());
            //获取计划书
            ApplyPlanPo applyPlanPo = applyPlanBusinessDao.getApplyPlanPoByApplyId(applyId);
            if (AssertUtils.isNotNull(applyPlanPo)) {
                applyNoResponse.setApplyPlanNo(applyPlanPo.getApplyPlanNo());
            }
            resultObject.setData(applyNoResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_PLAN_APPLY_NO_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject deleteApplyPlanCoverage(ApplyCoveragePlanBo applyCoveragePlanBo) {
        ResultObject resultObject = new ResultObject<>();
        try {
            applyBoService.deleteApplyCoveragePlanPo(applyCoveragePlanBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PLAN_COVERAGE_DELETE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AppPageJumpResponse> getAppPageJump(String applyId, String deviceChannel) {
        ResultObject<AppPageJumpResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            //获取后续页面跳转标识
            AppPageJumpResponse appPageJumpResponse = applyPlanDataSaveService.loadAppPageJump(applyId, deviceChannel);
            resultObject.setData(appPageJumpResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APP_PAGE_JUMP_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<AppPageJumpResponse> postApplicantIdImage(IdImageRequest idImageRequest, String deviceChannel) {
        ResultObject<AppPageJumpResponse> resultObject = new ResultObject<>();
        try {
            //参数验证
            applyPlanParameterValidate.validParameterIdImage(idImageRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(idImageRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            idImageRequest.getListIdAttachId().forEach(idAttachId -> {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setApplyId(idImageRequest.getApplyId());
                applyAttachmentBo.setAttachmentId(idAttachId.getAttachmentId());
                applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE.name());
                applyAttachmentBo.setAttachmentSeq(idAttachId.getAttachmentSeq());
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            });
            //获取被保人
            ApplyInsuredBo applyInsuredBo = applyExtDao.loadApplyInsuredBoByApplyId(idImageRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyInsuredBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
            if (ModelConstantEnum.RELATION_TYPE.ONESELF.name().equals(applyInsuredBo.getRelationship())) {
                this.postInsuredIdImage(idImageRequest);
            }
            //获取后续页面跳转标识
            AppPageJumpResponse appPageJumpResponse = applyPlanDataSaveService.loadAppPageJump(idImageRequest.getApplyId(), deviceChannel);
            resultObject.setData(appPageJumpResponse);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_ID_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AppPageJumpResponse> postOnlineApplicantIdImage(IdImageRequest idImageRequest, AppRequestHeads appRequestHeads) {
        ResultObject<AppPageJumpResponse> resultObject = new ResultObject<>();
        try {
            //参数验证
            applyPlanParameterValidate.validParameterIdImage(idImageRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(idImageRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }

            //网销护照附件处理
            List<ApplyAttachmentBo> applyApplicantPassportBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT.name());
            if (AssertUtils.isNotEmpty(applyApplicantPassportBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyApplicantPassportBos);
            }

            List<ApplyAttachmentBo> applyApplicantPassportVisaBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT_VISA.name());
            if (AssertUtils.isNotEmpty(applyApplicantPassportVisaBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyApplicantPassportVisaBos);
            }

            idImageRequest.getListIdAttachId().forEach(idAttachId -> {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setApplyId(idImageRequest.getApplyId());
                applyAttachmentBo.setAttachmentId(idAttachId.getAttachmentId());
                applyAttachmentBo.setAttachmentTypeCode(idAttachId.getAttachmentTypeCode());
                applyAttachmentBo.setAttachmentSeq(idAttachId.getAttachmentSeq());
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            });
            //获取被保人
            /*ApplyInsuredBo applyInsuredBo = applyExtDao.loadApplyInsuredBoByApplyId(idImageRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyInsuredBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
            if (ModelConstantEnum.RELATION_TYPE.ONESELF.name().equals(applyInsuredBo.getRelationship())) {
                this.postInsuredIdImageOnline(idImageRequest,appRequestHeads);
            }*/
            //获取后续页面跳转标识
            AppPageJumpResponse appPageJumpResponse = applyPlanDataSaveService.loadAppPageJump(idImageRequest.getApplyId(), appRequestHeads.getDeviceChannel());
            resultObject.setData(appPageJumpResponse);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_ID_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postInsuredIdImageOnline(IdImageRequest idImageRequest, AppRequestHeads appRequestHeads) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            applyPlanParameterValidate.validParameterIdImage(idImageRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(idImageRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }

            //网销护照附件处理
            List<ApplyAttachmentBo> applyInsuredPassportBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT.name());
            if (AssertUtils.isNotEmpty(applyInsuredPassportBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyInsuredPassportBos);
            }

            List<ApplyAttachmentBo> applyInsuredPassportVisaBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT_VISA.name());
            if (AssertUtils.isNotEmpty(applyInsuredPassportVisaBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyInsuredPassportVisaBos);
            }

            idImageRequest.getListIdAttachId().forEach(idAttachId -> {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(idAttachId.getAttachmentTypeCode());
                applyAttachmentBo.setApplyId(idImageRequest.getApplyId());
                applyAttachmentBo.setAttachmentId(idAttachId.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(idAttachId.getAttachmentSeq());
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            });
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_ID_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyImageResponse> onlineApplicantIdImageGet(String applyId) {
        ResultObject<ApplyImageResponse> resultObject = new ResultObject<>();
        ApplyImageResponse applyImageResponse = new ApplyImageResponse();


        //删除原有
        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE.name());
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            List<String> attachmentIds = applyAttachmentBos.stream().map(ApplyAttachmentBo::getAttachmentId).distinct().collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(attachmentIds);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    listResultObject.getData().stream().filter(attachmentResponse -> attachmentResponse.getMediaId().equals(applyAttachmentBo.getAttachmentId()))
                            .findFirst().ifPresent(attachmentResponse -> applyAttachmentBo.setUrl(attachmentResponse.getUrl()));
                });
            }
            applyImageResponse.setApplicantIdImages(applyAttachmentBos);
        }

        //删除原有
        List<ApplyAttachmentBo> applyPassportAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT.name());
        if (AssertUtils.isNotEmpty(applyPassportAttachmentBos)) {
            List<String> attachmentIds = applyPassportAttachmentBos.stream().map(ApplyAttachmentBo::getAttachmentId).distinct().collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(attachmentIds);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                applyPassportAttachmentBos.forEach(applyAttachmentBo -> {
                    listResultObject.getData().stream().filter(attachmentResponse -> attachmentResponse.getMediaId().equals(applyAttachmentBo.getAttachmentId()))
                            .findFirst().ifPresent(attachmentResponse -> applyAttachmentBo.setUrl(attachmentResponse.getUrl()));
                });
            }
            applyImageResponse.setApplicantPassportImages(applyPassportAttachmentBos);
        }

        //删除原有
        List<ApplyAttachmentBo> applyPassportVisaAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT_VISA.name());
        if (AssertUtils.isNotEmpty(applyPassportVisaAttachmentBos)) {
            List<String> attachmentIds = applyPassportVisaAttachmentBos.stream().map(ApplyAttachmentBo::getAttachmentId).distinct().collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(attachmentIds);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                applyPassportVisaAttachmentBos.forEach(applyAttachmentBo -> {
                    listResultObject.getData().stream().filter(attachmentResponse -> attachmentResponse.getMediaId().equals(applyAttachmentBo.getAttachmentId()))
                            .findFirst().ifPresent(attachmentResponse -> applyAttachmentBo.setUrl(attachmentResponse.getUrl()));
                });
            }
            applyImageResponse.setApplicantPassportVisaImages(applyPassportVisaAttachmentBos);
        }

        resultObject.setData(applyImageResponse);
        return resultObject;
    }

    @Override
    public ResultObject<ApplyImageResponse> onlineInsuredIdImageGet(String applyId) {
        ResultObject<ApplyImageResponse> resultObject = new ResultObject<>();
        ApplyImageResponse applyImageResponse = new ApplyImageResponse();

        //删除原有
        List<ApplyAttachmentBo> applyInsuredAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, "CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE");
        if (AssertUtils.isNotEmpty(applyInsuredAttachmentBos)) {
            List<String> attachmentIds = applyInsuredAttachmentBos.stream().map(ApplyAttachmentBo::getAttachmentId).distinct().collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(attachmentIds);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                applyInsuredAttachmentBos.forEach(applyAttachmentBo -> {
                    listResultObject.getData().stream().filter(attachmentResponse -> attachmentResponse.getMediaId().equals(applyAttachmentBo.getAttachmentId()))
                            .findFirst().ifPresent(attachmentResponse -> applyAttachmentBo.setUrl(attachmentResponse.getUrl()));
                });
            }
            applyImageResponse.setInsuredIdImages(applyInsuredAttachmentBos);
        }
        //删除原有
        List<ApplyAttachmentBo> applyInsuredPassportAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT.name());
        if (AssertUtils.isNotEmpty(applyInsuredPassportAttachmentBos)) {
            List<String> attachmentIds = applyInsuredPassportAttachmentBos.stream().map(ApplyAttachmentBo::getAttachmentId).distinct().collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(attachmentIds);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                applyInsuredPassportAttachmentBos.forEach(applyAttachmentBo -> {
                    listResultObject.getData().stream().filter(attachmentResponse -> attachmentResponse.getMediaId().equals(applyAttachmentBo.getAttachmentId()))
                            .findFirst().ifPresent(attachmentResponse -> applyAttachmentBo.setUrl(attachmentResponse.getUrl()));
                });
            }
            applyImageResponse.setInsuredPassportImages(applyInsuredPassportAttachmentBos);
        }

        //删除原有
        List<ApplyAttachmentBo> applyInsuredPassportVisaAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT_VISA.name());
        if (AssertUtils.isNotEmpty(applyInsuredPassportVisaAttachmentBos)) {
            List<String> attachmentIds = applyInsuredPassportVisaAttachmentBos.stream().map(ApplyAttachmentBo::getAttachmentId).distinct().collect(Collectors.toList());
            ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(attachmentIds);
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                applyInsuredPassportVisaAttachmentBos.forEach(applyAttachmentBo -> {
                    listResultObject.getData().stream().filter(attachmentResponse -> attachmentResponse.getMediaId().equals(applyAttachmentBo.getAttachmentId()))
                            .findFirst().ifPresent(attachmentResponse -> applyAttachmentBo.setUrl(attachmentResponse.getUrl()));
                });
            }
            applyImageResponse.setInsuredPassportVisaImages(applyInsuredPassportVisaAttachmentBos);
        }

        resultObject.setData(applyImageResponse);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postInsuredIdImage(IdImageRequest idImageRequest) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            applyPlanParameterValidate.validParameterIdImage(idImageRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(idImageRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(idImageRequest.getApplyId(), ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
            }
            idImageRequest.getListIdAttachId().forEach(idAttachId -> {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE.name());
                applyAttachmentBo.setApplyId(idImageRequest.getApplyId());
                applyAttachmentBo.setAttachmentId(idAttachId.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(idAttachId.getAttachmentSeq());
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            });
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_ID_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postApplicantBankCardImage(BankCardImageRequest bankCardImageRequest) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            applyPlanParameterValidate.validParameterBankCardImage(bankCardImageRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(bankCardImageRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            List<ApplyAttachmentBo> fronts = applyPlanBusinessDao.getIdAttachment(bankCardImageRequest.getApplyId(), ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_BANKCARD_FRONT.name());
            if (AssertUtils.isNotEmpty(fronts)) {
                applyBoService.deleteApplyAttachmentBoList(fronts);
            }
            List<ApplyAttachmentBo> backs = applyPlanBusinessDao.getIdAttachment(bankCardImageRequest.getApplyId(), ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_BANKCARD_BACK.name());
            if (AssertUtils.isNotEmpty(backs)) {
                applyBoService.deleteApplyAttachmentBoList(backs);
            }
            //正面
            long initSeq = 1;
            ApplyAttachmentBo applyAttachmentFront = new ApplyAttachmentBo();
            applyAttachmentFront.setApplyId(bankCardImageRequest.getApplyId());
            applyAttachmentFront.setAttachmentId(bankCardImageRequest.getBankCardAttachFront().getAttachmentId());
            applyAttachmentFront.setAttachmentSeq(initSeq);
            applyAttachmentFront.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_BANKCARD_FRONT.name());
            applyBoService.saveApplyAttachmentPo(applyAttachmentFront);
            //反面
            ApplyAttachmentBo applyAttachmentBack = new ApplyAttachmentBo();
            applyAttachmentBack.setApplyId(bankCardImageRequest.getApplyId());
            applyAttachmentBack.setAttachmentId(bankCardImageRequest.getBankCardAttachBack().getAttachmentId());
            applyAttachmentBack.setAttachmentSeq(initSeq);
            applyAttachmentBack.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_BANKCARD_BACK.name());
            applyBoService.saveApplyAttachmentPo(applyAttachmentBack);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_BANKCARD_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getImageAttachment(String applyId) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<ApplyCoverageBo> applyCoverageBos = applyPlanBusinessDao.loadApplyCoverage(applyId);
            AssertUtils.isNotEmpty(this.getLogger(), applyCoverageBos, ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
            List<String> listProductId = applyCoverageBos.stream().map(ApplyCoverageBo::getProductId).distinct().collect(Collectors.toList());
            ResultObject<List<CertifyAttachmentTypeResponse>> productCertify = productCertifyApi.queryProductCertify(listProductId);
            if (AssertUtils.isResultObjectError(productCertify)) {
                resultObject.setErrorInfo(productCertify);
                return resultObject;
            }
            List<CertifyAttachmentTypeResponse> certifyAttachmentTypeResponses = productCertify.getData();
            if (AssertUtils.isNotEmpty(certifyAttachmentTypeResponses)) {
                List<SyscodeRespFc> syscodeRespFcs = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.CERTIFY_ATTACHMENT_TYPE.name()).getData();
                if (AssertUtils.isNotEmpty(syscodeRespFcs)) {
                    certifyAttachmentTypeResponses.forEach(productCertifyRespFc -> {
                        syscodeRespFcs.stream().filter(syscodeRespFc -> syscodeRespFc.getCodeKey().equals(productCertifyRespFc.getAttachmentTypeCode())).findFirst().ifPresent((value) -> {
                            productCertifyRespFc.setAttachmentTypeName(value.getCodeName());
                        });
                        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, productCertifyRespFc.getAttachmentTypeCode());
                        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                            applyAttachmentBos.forEach(applyAttachmentBo -> {
                                AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(productCertifyRespFc, AppAttachmentResponse.class);
                                String attachmentId = applyAttachmentBo.getAttachmentId();
                                ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                                if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                                    appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                                }
                                appAttachmentResponse.setAttachmentId(attachmentId);
                                applyAttachmentRespList.add(appAttachmentResponse);
                            });
                            //String attachmentId = applyAttachmentBos.get(0).getAttachmentId();
                        } else {
                            AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(productCertifyRespFc, AppAttachmentResponse.class);
                            applyAttachmentRespList.add(appAttachmentResponse);
                        }
                    });
                }
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postAppImageAttachment(String applyId, List<AppAttachmentRequest> certifyRespFcs) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageAttachment(certifyRespFcs);

            //List<AppAttachmentResponse> productCertifyRespFcs = this.getImageAttachment(applyId).getData();
            //AssertUtils.isNotEmpty(this.getLogger(), productCertifyRespFcs, ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            //List<String> codes = productCertifyRespFcs.stream().map(AppAttachmentResponse::getAttachmentTypeCode).distinct().collect(Collectors.toList());

            //分组
            List<ProductCertifyRespFc> productCertifyRespFcList = new ArrayList<>();
            HashMap<String, ProductCertifyRespFc> hashMap = new HashMap<>();
            certifyRespFcs.forEach(certifyRespFc -> {
                if (hashMap.containsKey(certifyRespFc.getAttachmentTypeCode())) {
                    hashMap.get(certifyRespFc.getAttachmentTypeCode()).getAttachments().add(new ApplyAttachmentResp(certifyRespFc.getAttachmentId(), certifyRespFc.getAttachmentSeq()));
                } else {
                    ProductCertifyRespFc productCertifyRespFc = (ProductCertifyRespFc) this.converterObject(certifyRespFc, ProductCertifyRespFc.class);
                    List<ApplyAttachmentResp> attachmentRespList = new ArrayList<>();
                    attachmentRespList.add(new ApplyAttachmentResp(certifyRespFc.getAttachmentId(), certifyRespFc.getAttachmentSeq()));
                    productCertifyRespFc.setAttachments(attachmentRespList);
                    hashMap.put(productCertifyRespFc.getAttachmentTypeCode(), productCertifyRespFc);
                }
            });
            for (String key : hashMap.keySet()) {
                productCertifyRespFcList.add(hashMap.get(key));
            }

            //(sprint-v4.0.5.20210425) 可上传多张图片，第二张非必传，APP调整影像上传样式
            //校验编码是否来自get接口
            /*productCertifyRespFcList.forEach(productCertifyRespFc -> {
                if (!codes.contains(productCertifyRespFc.getAttachmentTypeCode()) || codes.size() != certifyRespFcs.size()) {
                    throw new RequestException(ApplyErrorConfigEnum.APPLY_IMAGE_ATTACHMENT_CODE_FORMAT_ERROR);
                }
                productCertifyRespFcs.stream().filter(productCertifyRespFc1 -> productCertifyRespFc.getAttachmentTypeCode().equals(productCertifyRespFc1.getAttachmentTypeCode())).findFirst().ifPresent((value) -> {
                    if (value.getRequiredWeight() > productCertifyRespFc.getAttachments().size() || value.getMaxWeight() < productCertifyRespFc.getAttachments().size()) {
                        throw new RequestException(ApplyErrorConfigEnum.APPLY_ATTACHMENT_IS_NO_LESS_THAN_REQUIRED_OR_MORE_THAN_MAX);
                    }
                });
            });*/

            applyPlanDataSaveService.saveImageAttachmentData(applyId, productCertifyRespFcList);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getAgentImageSignatureAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_AGENT.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_AGENT.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    appAttachmentResponse.setUploadDate(applyAttachmentBo.getCreatedDate());
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postAgentImageSignatureAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests, Users users) {
        this.getLogger().info("影像件请求数据--{}", JSONObject.toJSONString(appAttachmentRequests));
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveAgentImageSignatureAttachmentData(applyId, appAttachmentRequests);

            ArrayList<String> list = new ArrayList<>();
            list.add("ZH_CN");
            list.add("EN_US");
            list.add("KM_KH");
            new Thread(() -> {
                ApplyThreadLocalBo.asyncFlag.set(true);// 可以使用消息队列进行异步生成
                for (String language : list) {
                    ResultObject<List<AttachmentResponse>> listResultObject = paymentBusinessService.applyPdfGenerate(applyId, language);
                    AssertUtils.isResultObjectError(this.getLogger(), listResultObject);
                    this.getLogger().info("返回参数" + listResultObject);
                }
                ApplyThreadLocalBo.asyncFlag.set(false);
            }).start();
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getOtherAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_OTHER.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_OTHER.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postImageOtherAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests, Users users) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageOtherAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveImageOtherAttachmentData(applyId, appAttachmentRequests);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getApplicantImageSignatureAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    appAttachmentResponse.setUploadDate(applyAttachmentBo.getCreatedDate());
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postApplicantImageSignatureAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests, Users users) {
        this.getLogger().info("影像件请求数据--{}", JSONObject.toJSONString(appAttachmentRequests));
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageSignatureAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveImageApplicantSignatureAttachmentData(applyId, appAttachmentRequests);

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);
            //查询applyBo
            ApplyBo applyBo = applyBaseService.queryApply(applyId);

            appAttachmentRequests.forEach(appAttachmentRequest -> {

                if (AssertUtils.isNotNull(appAttachmentRequest)
                        && ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT.name().equals(appAttachmentRequest.getAttachmentTypeCode())) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setApplicantAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setApplicantAttachmentUrl(mediaUrl.getData().getUrl());
                        }

                        applyElectronicSignaturePo.setApplicantSubmitStatus(ApplyTermEnum.YES_NO.YES.name());

                        //applyPlanBaseDao.saveSignature(applyId, applyElectronicSignaturePo, null, null, null);
                    }

                    if (AssertUtils.isNotNull(appAttachmentRequest.getIsFlag())) {
                        if ("YES".equals(appAttachmentRequest.getIsFlag())) {
                            if (AssertUtils.isNotNull(applyBo)) {
                                if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                                    if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
                                        AgentResponse agentResponse = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
                                        if (AssertUtils.isNotNull(agentResponse)) {
                                            applyBo.getApplyAgentBo().setAgentName(agentResponse.getAgentName());
                                            //发送投保人电子签名完成通知app消息
                                            messageBusinessService.pushApplyApplicantSignatureMessageAgent(ApplyTermEnum.MSG_BUSINESS_TYPE.SIGNATURE_CUSTOMER_COMPLETED_TO_AGENT_REMIND_APP.name(),
                                                    applyBo, users);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (AssertUtils.isNotNull(appAttachmentRequest)
                        && ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_HANDWRITING_APPLICANT.name().equals(appAttachmentRequest.getAttachmentTypeCode())) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setApplicantHandAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setApplicantHandAttachmentUrl(mediaUrl.getData().getUrl());
                        }
                    }
                }
            });

            if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                applyPlanBaseDao.saveSignature(applyId, applyElectronicSignaturePo, null, null, null);
            }

            // 异步生成投保单 且将生成好的投保单PDF转为图片
            asyncGenerateApplyAndImage(applyId);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getInsuredImageSignatureAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_INSURED.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_INSURED.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    appAttachmentResponse.setUploadDate(applyAttachmentBo.getCreatedDate());
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postInsuredImageSignatureAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests, Users users) {
        this.getLogger().info("影像件请求数据--{}", JSONObject.toJSONString(appAttachmentRequests));
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageSignatureAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveImageInsuredSignatureAttachmentData(applyId, appAttachmentRequests);

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);

            appAttachmentRequests.forEach(appAttachmentRequest -> {
                if (AssertUtils.isNotNull(appAttachmentRequest)
                        && ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_INSURED.name().equals(appAttachmentRequest.getAttachmentTypeCode())) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setInsuredAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setInsuredAttachmentUrl(mediaUrl.getData().getUrl());
                        }

                        applyElectronicSignaturePo.setInsuredSubmitStatus(ApplyTermEnum.YES_NO.YES.name());

                    }

                    if (AssertUtils.isNotNull(appAttachmentRequest.getIsFlag())) {
                        //ISFLAG判断投被保人是否在一起,YES为不在,no为在一起投保
                        if ("YES".equals(appAttachmentRequest.getIsFlag())) {
                            //查询applyBo
                            ApplyBo applyBo = applyBaseService.queryApply(applyId);
                            if (AssertUtils.isNotNull(applyBo)) {
                                if (AssertUtils.isNotNull(applyBo.getApplyAgentBo())) {
                                    if (AssertUtils.isNotNull(applyBo.getApplyAgentBo().getAgentId())) {
                                        AgentResponse agentResponse = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
                                        if (AssertUtils.isNotNull(agentResponse)) {
                                            applyBo.getApplyAgentBo().setAgentName(agentResponse.getAgentName());
                                            //发送被保人电子签名完成通知app消息
                                            messageBusinessService.pushApplyInsuredSignatureMessageAgent(ApplyTermEnum.MSG_BUSINESS_TYPE.SIGNATURE_INSURED_COMPLETED_TO_AGENT_REMIND_APP.name(),
                                                    applyBo, users);
                                            messageBusinessService.pushApplyAllSignatureMessageAgent(ApplyTermEnum.MSG_BUSINESS_TYPE.SIGNATURE_ALL_COMPLETED_TO_AGENT_REMIND_APP.name(),
                                                    applyBo, users);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if (AssertUtils.isNotNull(appAttachmentRequest)
                        && ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_HANDWRITING_INSURED.name().equals(appAttachmentRequest.getAttachmentTypeCode())) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setInsuredHandAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setInsuredHandAttachmentUrl(mediaUrl.getData().getUrl());
                        }
                    }
                }

            });

            if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                applyPlanBaseDao.saveSignature(applyId, applyElectronicSignaturePo, null, null, null);
            }

            // 上传被保人电子签名附件时 重新生成投保单附件
            asyncGenerateApplyAndImage(applyId);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 异步生成投保单 且将生成好的投保单PDF转为图片
     * 优先生成英文
     *
     * @param applyId
     */
    private void asyncGenerateApplyAndImage(String applyId) {
        // 生成附件前，更新状态为生成中
        policyApi.updateAttachmentGenerateStatus(applyId, AttachmentPolicyEnum.APPLY_BOOK.name(), null, AttachmentPdfEnum.GENERATING.name());
        List<String> languages = Arrays.asList("EN_US", "KM_KH");
        ApplyThreadLocalBo.asyncFlag.set(true);// 可以使用消息队列进行异步生成
        for (String language : languages) {
            generateApplyAfterTransformImage(applyId, language);
        }
        ApplyThreadLocalBo.asyncFlag.set(false);
    }

    /**
     * 生成投保单PDF 然后将生成好的PDF转图片
     *
     * @param applyId
     * @param language
     */
    private void generateApplyAfterTransformImage(String applyId, String language) {
        try {
            ApplyThreadLocalBo.convertImageFlag.set(true);// PDF转图片
            paymentBusinessService.applyPdfGenerate(applyId, language);
            ApplyThreadLocalBo.convertImageFlag.set(false);
        } catch (Exception e) {
            // 生成过程中抛出异常 则更新状态为生成完成
            policyApi.updateAttachmentGenerateStatus(applyId, AttachmentPolicyEnum.APPLY_BOOK.name(), language, AttachmentPdfEnum.FINISH.name());
            e.printStackTrace();
        }
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getApplicantImageSignatureVideoAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT_VIDEO.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_APPLICANT_VIDEO.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postApplicantImageSignatureVideoAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveImageApplicantSignatureVideoAttachmentData(applyId, appAttachmentRequests);

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);

            appAttachmentRequests.forEach(appAttachmentRequest -> {
                if (AssertUtils.isNotNull(appAttachmentRequest)) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setApplicantVideoAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setApplicantVideoAttachmentUrl(mediaUrl.getData().getUrl());
                        }

                        applyElectronicSignaturePo.setApplicantVideoStatus(ApplyTermEnum.YES_NO.YES.name());

                        applyPlanBaseDao.saveSignature(applyId, applyElectronicSignaturePo, null, null, null);
                    }
                }
            });
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getInsuredImageSignatureVideoAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_INSURED_VIDEO.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_INSURED_VIDEO.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postInsuredImageSignatureVideoAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveImageInsuredSignatureVideoAttachmentData(applyId, appAttachmentRequests);

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);

            appAttachmentRequests.forEach(appAttachmentRequest -> {
                if (AssertUtils.isNotNull(appAttachmentRequest)) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setInsuredVideoAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setInsuredVideoAttachmentUrl(mediaUrl.getData().getUrl());
                        }

                        applyElectronicSignaturePo.setInsuredVideoStatus(ApplyTermEnum.YES_NO.YES.name());

                        applyPlanBaseDao.saveSignature(applyId, applyElectronicSignaturePo, null, null, null);
                    }
                }
            });
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getTogetherVideoAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_TOGETHER_VIDEO.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_TOGETHER_VIDEO.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postTogetherVideoAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveImageTogetherVideoAttachmentData(applyId, appAttachmentRequests);

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);

            appAttachmentRequests.forEach(appAttachmentRequest -> {
                if (AssertUtils.isNotNull(appAttachmentRequest)) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setTogetherVideoAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setTogetherVideoAttachmentUrl(mediaUrl.getData().getUrl());
                        }

                        applyElectronicSignaturePo.setTogetherVideoAttachmentStatus(ApplyTermEnum.YES_NO.YES.name());

                        applyPlanBaseDao.saveSignature(applyId, applyElectronicSignaturePo, null, null, null);
                    }
                }
            });
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getTogetherPhotoAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_TOGETHER_PHOTO.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_TOGETHER_PHOTO.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postTogetherPhotoAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            applyPlanParameterValidate.validImageAttachment(appAttachmentRequests);

            applyPlanDataSaveService.saveImageTogetherPhotoAttachmentData(applyId, appAttachmentRequests);

            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);

            appAttachmentRequests.forEach(appAttachmentRequest -> {
                if (AssertUtils.isNotNull(appAttachmentRequest)) {
                    String attachmentId = appAttachmentRequest.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);

                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignaturePo.setTogetherPhotoAttachmentId(attachmentId);

                        if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                            applyElectronicSignaturePo.setTogetherPhotoAttachmentUrl(mediaUrl.getData().getUrl());
                        }

                        applyElectronicSignaturePo.setTogetherPhotoAttachmentStatus(ApplyTermEnum.YES_NO.YES.name());

                        applyPlanBaseDao.saveSignature(applyId, applyElectronicSignaturePo, null, null, null);
                    }
                }
            });
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postImageAttachment(ImageAttachmentRequest imageAttachmentRequest) {
        ResultObject resultObject = new ResultObject<>();
        try {
            //参数验证
            applyPlanParameterValidate.validParameterImageAttachment(imageAttachmentRequest);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(imageAttachmentRequest.getApplyId());
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            //保存人身保险产品风险提示书
            applyPlanDataSaveService.saveRiskTipsData(imageAttachmentRequest);

            //保存人身保险产品投保提示书
            applyPlanDataSaveService.saveApplyTipsData(imageAttachmentRequest);

            //保存电子保单申请确认书
            applyPlanDataSaveService.saveApplicationConfirmationData(imageAttachmentRequest);

        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AppApplyInfoCheckResponse> getApplyInfoCheck(String userId, AppRequestHeads appRequestHeads, String applyId, String deviceChannel) {
        ResultObject<AppApplyInfoCheckResponse> resultObject = new ResultObject<>();
        //信息核对
        AppApplyInfoCheckResponse appApplyInfoCheckResponse = new AppApplyInfoCheckResponse();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            //代理人信息
            AgentResponse agentRespFc = agentApi.agentByUserIdGet(userId).getData();
            AssertUtils.isNotNull(this.getLogger(), agentRespFc, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            ApplyNoExtResponse applyNoExtResponse = applyPlanBusinessDao.getApplyNoExt(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyNoExtResponse, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            ProductSalesDetailedResponse productSalesDetailedResponse = productSalesApi.getProductSimpleInfo(applyNoExtResponse.getProductId(), deviceChannel, ApplyTermEnum.YES_NO.NO.name()).getData();
            if (AssertUtils.isNotNull(productSalesDetailedResponse)) {
                appApplyInfoCheckResponse.setProductId(productSalesDetailedResponse.getProductId());
                appApplyInfoCheckResponse.setProductName(productSalesDetailedResponse.getProductName());
                appApplyInfoCheckResponse.setProductAhumbnailUrl(productSalesDetailedResponse.getProductAhumbnailUrl());
            }
            appApplyInfoCheckResponse.setApplyNo(applyNoExtResponse.getApplyNo());

            //获取投保方案
            appApplyInfoCheckResponse.setApplyPlan(appApplyTransData.getApplyPlan(applyId, agentRespFc.getBranchId()));
            //获取投保人信息
            appApplyInfoCheckResponse.setApplicant(appApplyTransData.getApplicant(applyId));
            //获取被保人信息
            appApplyInfoCheckResponse.setListInsured(appApplyTransData.getListInsured(applyId));
            //获取受益人信息
            appApplyInfoCheckResponse.setListBeneficiary(appApplyTransData.getListBeneficiary(applyId));
            //获取投保人银行信息
            appApplyInfoCheckResponse.setBank(this.getApplicantBankInfo(applyId).getData());
            //获取投保人收件信息
            appApplyInfoCheckResponse.setSendAddress(appApplyTransData.getSendAddress(applyId));
            //保单持有人
            appApplyInfoCheckResponse.setHolder(appApplyTransData.getApplyHolder(applyId));

            List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = applyBaseService.queryApplyLoanBeneficiary(applyId, TerminologyConfigEnum.WHETHER.NO.name());
            if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
                ApplyLoanBeneficiaryResponse loanBeneficiary = new ApplyLoanBeneficiaryResponse();
                loanBeneficiary.setBeneficiaryBranchId(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchId());
                loanBeneficiary.setBeneficiaryBranchCode(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchCode());
                loanBeneficiary.setBeneficiaryBranchName(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchName());
                loanBeneficiary.setBeneficiaryNoOrder(applyBeneficiaryInfoBos.get(0).getBeneficiaryNoOrder());
                loanBeneficiary.setBeneficiaryProportion(applyBeneficiaryInfoBos.get(0).getBeneficiaryProportion());
                appApplyInfoCheckResponse.setLoanBeneficiary(loanBeneficiary);
                appApplyInfoCheckResponse.setContractLoanFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }

            //针对20A网销产品国籍国际化特殊处理
            appApplyInfoCheckResponse.getApplicant().setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyId, appApplyInfoCheckResponse.getApplicant().getNationality(), appRequestHeads.getLanguage()));

            List<CustomerResponse> listInsured = appApplyInfoCheckResponse.getListInsured();
            for (CustomerResponse customerResponse : listInsured) {
                customerResponse.setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyId, customerResponse.getNationality(), appRequestHeads.getLanguage()));
            }


            //返回数据
            resultObject.setData(appApplyInfoCheckResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_PLAN_INFO_CHECK_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AppApplyInfoCheckResponse> getApplyInfoCheckOnline(String userId, AppRequestHeads appRequestHeads, String applyId, String deviceChannel) {
        ResultObject<AppApplyInfoCheckResponse> resultObject = new ResultObject<>();
        //信息核对
        AppApplyInfoCheckResponse appApplyInfoCheckResponse = new AppApplyInfoCheckResponse();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            //代理人信息
            AgentResponse agentRespFc = agentApi.agentByUserIdGet(userId).getData();
            AssertUtils.isNotNull(this.getLogger(), agentRespFc, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            ApplyNoExtResponse applyNoExtResponse = applyPlanBusinessDao.getApplyNoExt(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyNoExtResponse, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            ProductSalesDetailedResponse productSalesDetailedResponse = productSalesApi.getProductSimpleInfo(applyNoExtResponse.getProductId(), deviceChannel, ApplyTermEnum.YES_NO.NO.name()).getData();
            if (AssertUtils.isNotNull(productSalesDetailedResponse)) {
                appApplyInfoCheckResponse.setProductId(productSalesDetailedResponse.getProductId());
                appApplyInfoCheckResponse.setProductName(productSalesDetailedResponse.getProductName());
                appApplyInfoCheckResponse.setProductAhumbnailUrl(productSalesDetailedResponse.getProductAhumbnailUrl());
            }
            appApplyInfoCheckResponse.setApplyNo(applyNoExtResponse.getApplyNo());

            //获取投保方案
            appApplyInfoCheckResponse.setApplyPlan(appApplyTransData.getApplyPlanOnline(applyId, agentRespFc.getBranchId(), appRequestHeads));
            //获取投保人信息
            appApplyInfoCheckResponse.setApplicant(appApplyTransData.getApplicant(applyId));
            //获取被保人信息
            appApplyInfoCheckResponse.setListInsured(appApplyTransData.getListInsured(applyId));
            //获取受益人信息
            appApplyInfoCheckResponse.setListBeneficiary(appApplyTransData.getListBeneficiary(applyId));
            //获取投保人银行信息
            appApplyInfoCheckResponse.setBank(this.getApplicantBankInfo(applyId).getData());
            //获取投保人收件信息
            appApplyInfoCheckResponse.setSendAddress(appApplyTransData.getSendAddress(applyId));

            List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = applyBaseService.queryApplyLoanBeneficiary(applyId, TerminologyConfigEnum.WHETHER.NO.name());
            if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
                ApplyLoanBeneficiaryResponse loanBeneficiary = new ApplyLoanBeneficiaryResponse();
                loanBeneficiary.setBeneficiaryBranchId(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchId());
                loanBeneficiary.setBeneficiaryBranchCode(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchCode());
                loanBeneficiary.setBeneficiaryBranchName(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchName());
                loanBeneficiary.setBeneficiaryNoOrder(applyBeneficiaryInfoBos.get(0).getBeneficiaryNoOrder());
                loanBeneficiary.setBeneficiaryProportion(applyBeneficiaryInfoBos.get(0).getBeneficiaryProportion());
                appApplyInfoCheckResponse.setLoanBeneficiary(loanBeneficiary);
                appApplyInfoCheckResponse.setContractLoanFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }

            //针对20A网销产品国籍国际化特殊处理
            appApplyInfoCheckResponse.getApplicant().setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyId, appApplyInfoCheckResponse.getApplicant().getNationality(), appRequestHeads.getLanguage()));

            appApplyInfoCheckResponse.getListInsured().forEach(customerResponse -> {
                customerResponse.setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyId, customerResponse.getNationality(), appRequestHeads.getLanguage()));
            });


            //返回数据
            resultObject.setData(appApplyInfoCheckResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_PLAN_INFO_CHECK_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject deleteApplyCoverageDutyPlan(ApplyCoverageDutyPlanBo applyCoverageDutyPlanBo) {
        ResultObject resultObject = new ResultObject<>();
        try {
            applyBoService.deleteApplyCoverageDutyPlanPo(applyCoverageDutyPlanBo);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PLAN_COVERAGE_DUTY_DELETE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<ApplyApplicantBo> getApplicantInfoByApplyPlanId(Users users, String applyPlanId) {
        ResultObject<ApplyApplicantBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyPlanId, ApplyErrorConfigEnum.APPLY_PLAN_ID_IS_NOT_NULL);
            //代理人签约状态判断
            ResultObject<AgentSignBaseResponse> respFcResultObject = baseAgentApi.queryOneAgentSignedAgentId(users.getUserId());
            AssertUtils.isResultObjectError(this.getLogger(), respFcResultObject);
            this.getLogger().debug("respFcResultObject========" + JSON.toJSONString(respFcResultObject));
            if (AssertUtils.isResultObjectError(respFcResultObject) ||
                    !ApplyTermEnum.SIGN_STATUS.SIGN_COMPLETE.name().equals(respFcResultObject.getData().getSignStatus())) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_APPLY_AGENT_NO_SIGN);
            }
            ApplyApplicantBo applyApplicantBo = applyPlanBusinessDao.getApplyApplicantBo(applyPlanId);
            ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(applyPlanId, null, null);
            AssertUtils.isNotNull(getLogger(), applyPlanBo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);
            if (!AssertUtils.isNotNull(applyApplicantBo)) {
                //创建投保单
                applyApplicantBo = appApplyTransData.transApplyPlanApplicant(users.getUserId(), applyPlanBo);
            } else {
                Optional<ApplyCoveragePlanBo> first = applyPlanBo.getCoverages().stream().filter(applyCoveragePlanBo -> Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_20A.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyCoveragePlanBo.getProductId())).findFirst();
                ApplyPo applyPo = applyBaseService.queryApplyPo(applyApplicantBo.getApplyId());
                //若是五号产品正在重新投保的过程中，则用计划书数据覆盖投保单数据
                if (first.isPresent() && ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name().equals(applyPo.getApplyStatus())) {
                    appApplyTransData.transOverwriteApplyData(users.getUserId(), applyApplicantBo.getApplyId(), applyPlanBo);
                }
                if ("YES".equals(applyApplicantBo.getOnlineInfoDiffFlag()) && applyApplicantBo.getSex().equals(applyPlanBo.getApplicant().getSex()) &&
                        applyApplicantBo.getBirthday().equals(applyPlanBo.getApplicant().getBirthday()) &&
                        applyApplicantBo.getOccupationCode().equals(applyPlanBo.getApplicant().getOccupationCode())) {
                    applyApplicantBo.setOnlineInfoDiffFlag("NO");
                }
            }
            if (AssertUtils.isNotEmpty(applyApplicantBo.getExpectedPremiumSources())) {
                applyApplicantBo.setListExpectedPremiumSources(JSON.parseArray(applyApplicantBo.getExpectedPremiumSources(), String.class));
            }
            ApplyOccupationNaturePo applyOccupationNaturePo = applyOtherInfoBaseService.queryApplyOccupationNaturePo(applyApplicantBo.getApplyId(), ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            if (AssertUtils.isNotNull(applyOccupationNaturePo)) {
                applyApplicantBo.setOccupationNature(applyOccupationNaturePo);
            }

            //针对20A网销产品国籍国际化特殊处理
            if (AssertUtils.isNotNull(applyApplicantBo.getApplyId()) && AssertUtils.isNotEmpty(applyApplicantBo.getNationality())) {
                applyApplicantBo.setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyApplicantBo.getApplyId(), applyApplicantBo.getNationality(), users.getLanguage()));
            }

            this.getLogger().info("======clientUserId:=========" + applyPlanBo.getClientUserId());
            applyApplicantBo.setClientUserId(applyPlanBo.getClientUserId());
            applyApplicantBo.setCacheData(applyPlanBo.getCacheData());

            //查询网销计划书保险金额
            List<ApplyCoveragePlanPo> applyCoveragePlanPos = applyCoveragePlanDao.fetchByApplyPlanId(applyPlanId);
            if (AssertUtils.isNotEmpty(applyCoveragePlanPos)) {
                final BigDecimal[] amount = {null};
                applyCoveragePlanPos.stream().filter(applyCoveragePlanPo -> ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(applyCoveragePlanPo.getProductId())).findFirst().ifPresent(applyCoveragePlanPo -> {
                    amount[0] = new BigDecimal(applyCoveragePlanPo.getAmount());
                });
                applyApplicantBo.setAmount(amount[0]);
            }
            if (AssertUtils.isNotEmpty(applyPlanBo.getActivationCode()) && AssertUtils.isNotEmpty(applyPlanBo.getCoverages())) {
                ApplyCoveragePlanBo applyCoveragePlanBo = applyPlanBo.getCoverages().get(0);
                // 刮刮卡500的保额不用填写健康告知
                if (AssertUtils.isNotEmpty(applyCoveragePlanBo.getAmount()) &&
                        new BigDecimal(applyCoveragePlanBo.getAmount()).compareTo(new BigDecimal("500")) == 0
                ) {
                    applyApplicantBo.setHealthNoticeFlag("NO");
                }
            }

            resultObject.setData(applyApplicantBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLICANT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<ApplyApplicantBo> saveApplicantInfoByApplyPlanId(String userId, String applyPlanId, ApplyApplicantBo applyApplicantBo, AppRequestHeads appRequestHeads) {
        ResultObject<ApplyApplicantBo> resultObject = new ResultObject<>();
        try {
            log.info("========================计划书投保人信息=======================:" + JSON.toJSONString(applyApplicantBo));
            AssertUtils.isNotEmpty(getLogger(), applyPlanId, ApplyErrorConfigEnum.APPLY_PLAN_ID_IS_NOT_NULL);
            ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(applyPlanId, null, null);
            AssertUtils.isNotNull(getLogger(), applyPlanBo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);

            ApplyApplicantBo applyApplicantBoOrigin = applyPlanBusinessDao.getApplyApplicantBo(applyPlanId);
            AssertUtils.isNotNull(getLogger(), applyApplicantBoOrigin, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);

            String applyId = applyApplicantBoOrigin.getApplyId();
            applyApplicantBo.setApplyId(applyId);
            //参数验证
            applyPlanParameterValidate.validParameterApplicantInfo(applyApplicantBo, appRequestHeads, applyPlanBo.getActivationCode());
            //业务验证
            applyPlanBusinessValidate.validParameterApplicantInfo(applyApplicantBo);

            // 网销计划书和投保单投保人信息不一致标识
            if (!applyApplicantBo.getSex().equals(applyApplicantBoOrigin.getSex()) ||
                    !applyApplicantBo.getBirthday().equals(applyApplicantBoOrigin.getBirthday()) ||
                    !applyApplicantBo.getOccupationCode().equals(applyApplicantBoOrigin.getOccupationCode())) {
                applyApplicantBo.setOnlineInfoDiffFlag("YES");
            }
            applyApplicantBo.setApplicantId(applyApplicantBoOrigin.getApplicantId());
            applyApplicantBo.setCustomerId(applyApplicantBoOrigin.getCustomerId());
            if (AssertUtils.isNotEmpty(applyApplicantBo.getName())) {
                applyApplicantBo.setName(applyApplicantBo.getName().toUpperCase().trim());
            } else if (AssertUtils.isNotEmpty(applyApplicantBoOrigin.getName())) {
                applyApplicantBo.setName(applyApplicantBoOrigin.getName().toUpperCase().trim());
            }
            //网销姓和名处理
            if (AssertUtils.isNotEmpty(applyApplicantBo.getFamilyName())) {
                applyApplicantBo.setFamilyName(applyApplicantBo.getFamilyName().toUpperCase().trim());
            } else if (AssertUtils.isNotEmpty(applyApplicantBoOrigin.getFamilyName())) {
                applyApplicantBo.setFamilyName(applyApplicantBoOrigin.getFamilyName().toUpperCase().trim());
            }
            if (AssertUtils.isNotEmpty(applyApplicantBo.getGivenName())) {
                applyApplicantBo.setGivenName(applyApplicantBo.getGivenName().toUpperCase().trim());
            } else if (AssertUtils.isNotEmpty(applyApplicantBoOrigin.getGivenName())) {
                applyApplicantBo.setGivenName(applyApplicantBoOrigin.getGivenName().toUpperCase().trim());
            }
            if (AssertUtils.isNotEmpty(applyApplicantBo.getSex())) {
                applyApplicantBo.setSex(applyApplicantBo.getSex());
            } else if (AssertUtils.isNotEmpty(applyApplicantBoOrigin.getSex())) {
                applyApplicantBo.setSex(applyApplicantBoOrigin.getSex());
            }
            if (AssertUtils.isNotNull(applyApplicantBo.getBirthday())) {
                applyApplicantBo.setBirthday(applyApplicantBo.getBirthday());
            } else if (AssertUtils.isNotNull(applyApplicantBoOrigin.getBirthday())) {
                applyApplicantBo.setBirthday(applyApplicantBoOrigin.getBirthday());
            }
            applyApplicantBo.setValidFlag(ApplyTermEnum.VALID_FLAG.effective.name());
            applyApplicantBo.setApplicantType(ApplyTermEnum.APPLY_APPLICANT_TYPE.PERSONAL.name());
            if (AssertUtils.isNotEmpty(applyApplicantBo.getListExpectedPremiumSources())) {
                applyApplicantBo.setExpectedPremiumSources(JackSonUtils.toJson(applyApplicantBo.getListExpectedPremiumSources()));
            }
            //保存客户
            CustomerBusinessRequest customerBusinessRequest = (CustomerBusinessRequest) this.converterObject(applyApplicantBo, CustomerBusinessRequest.class);
            customerBusinessRequest.setUserId(userId);
            customerBusinessRequest.setClientUserId(applyPlanBo.getClientUserId());
            log.info("========================计划书投保人信息保存客户参数=======================:" + JSON.toJSONString(customerBusinessRequest));
            ResultObject<UserCustomerResponse> respFcResultObject = customerManageApi.saveCustomerMessagePo(customerBusinessRequest);
            log.info("========================计划书投保人信息保存客户返回=======================:" + JSON.toJSONString(respFcResultObject));
            AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, ApplyErrorConfigEnum.APPLY_SAVE_CUSTOMER_ERROR);
            applyApplicantBo.setCustomerId(respFcResultObject.getData().getCustomerId());
            //验证客户与代理人关系
            GcFactorFunction gcFuction = () -> {
                applyPlanBusinessValidate.validParameterCustomerAndAgent(customerBusinessRequest, applyPlanBo.getAgentId());
            };
            Map<String, Object> map = new HashMap<String, Object>();
            map.put(BaseTermEnum.BASE_FACTOR_CONFIG_VALUE.GC.name(), gcFuction);
            this.handleDifferent(map, ApplyTermEnum.BASE_FACTOR_CONFIG_CODE.APPLY_AGENT_SELF_PRESERVATION_VERIFICATION.name());

            // 保存附件
            if (AssertUtils.isNotEmpty(applyApplicantBo.getAttachmentId())) {
                List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE.name());
                if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                    applyAttachmentBos.stream()
                            .filter(applyAttachmentBo -> applyAttachmentBo.getAttachmentSeq() == 1)
                            .findFirst().ifPresent(applyAttachmentBo -> {
                                applyBoService.deleteApplyAttachmentPo(applyAttachmentBo);
                            });
                }
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_APPLICANT_IDTYPE.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(applyApplicantBo.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(1L);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            }

            //网销证件类型为护照时,保存护照相关附件
            if (AssertUtils.isNotEmpty(applyApplicantBo.getPasswordAttachmentId())) {
                List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT.name());
                if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                    applyAttachmentBos.stream()
                            .filter(applyAttachmentBo -> applyAttachmentBo.getAttachmentSeq() == 1)
                            .findFirst().ifPresent(applyAttachmentBo -> {
                                applyBoService.deleteApplyAttachmentPo(applyAttachmentBo);
                            });
                }
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(applyApplicantBo.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(1L);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            }

            if (AssertUtils.isNotEmpty(applyApplicantBo.getPasswordVisaAttachmentId())) {
                List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT_VISA.name());
                if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                    applyAttachmentBos.stream()
                            .filter(applyAttachmentBo -> applyAttachmentBo.getAttachmentSeq() == 1)
                            .findFirst().ifPresent(applyAttachmentBo -> {
                                applyBoService.deleteApplyAttachmentPo(applyAttachmentBo);
                            });
                }
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_APPLICANT_PASSPORT_VISA.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(applyApplicantBo.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(1L);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            }

            //设置职业类型
            applyApplicantBo.setOccupationType(appApplyTransData.getOccupationType(applyApplicantBo.getOccupationCode(), applyId));
            //更新投保人
            applyApplicantBo.setApplyId(applyId);
            applyBoService.saveApplyApplicantPo(applyApplicantBo);
            //保存职业性质
            if (AssertUtils.isNotNull(applyApplicantBo.getOccupationNature()) && !AssertUtils.checkObjFieldAllIsNull(applyApplicantBo.getOccupationNature())) {
                applyOtherInfoBaseService.deleteApplyOccupationNaturePo(applyId, ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
                applyApplicantBo.getOccupationNature().setApplyOccupationNatureId(null);
                applyApplicantBo.getOccupationNature().setSeq("100");
                applyApplicantBo.getOccupationNature().setApplyId(applyId);
                applyApplicantBo.getOccupationNature().setCustomerType(ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
                applyOtherInfoBaseService.saveApplyOccupationNaturePo(userId, applyApplicantBo.getOccupationNature());
            }
            //保存收件人
            applyBoService.saveApplyContactInfoPo(appApplyTransData.transformContactInfo(applyApplicantBo));
            resultObject.setData(applyApplicantBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_APPLICANT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyInsuredBo> getApplyInsuredInfo(String applyId, AppRequestHeads appRequestHeads) {
        ResultObject<ApplyInsuredBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyInsuredBo applyInsuredBo = applyPlanBusinessDao.getApplyInsuredBo(applyId);
            if (AssertUtils.isNotEmpty(applyInsuredBo.getExpectedPremiumSources())) {
                applyInsuredBo.setListExpectedPremiumSources(JSON.parseArray(applyInsuredBo.getExpectedPremiumSources(), String.class));
            }
            ApplyOccupationNaturePo applyOccupationNaturePo = applyOtherInfoBaseService.queryApplyOccupationNaturePo(applyId, ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            if (AssertUtils.isNotNull(applyOccupationNaturePo)) {
                applyInsuredBo.setOccupationNature(applyOccupationNaturePo);
            }

            //针对20A网销产品国籍国际化特殊处理
            applyInsuredBo.setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyId, applyInsuredBo.getNationality(), appRequestHeads.getLanguage()));
            resultObject.setData(applyInsuredBo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_INSURED_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<ApplyInsuredBo> saveApplyInsuredInfo(String userId, String applyId, ApplyInsuredBo applyInsuredBo, AppRequestHeads appRequestHeads) {
        ResultObject<ApplyInsuredBo> resultObject = new ResultObject<>();
        try {
            log.info("========================计划书被保人信息=======================:" + JSONObject.toJSONString(applyInsuredBo));
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            //参数验证
            applyPlanParameterValidate.validParameterInsuredInfo(applyInsuredBo, applyPo, appRequestHeads);
            //业务验证
            applyPlanBusinessValidate.validParameterInsuredInfo(applyInsuredBo);

            // 查询投保单被保人信息
            ApplyInsuredBo applyInsuredBoOrigin = applyPlanBusinessDao.getApplyInsuredBo(applyId);
            if (AssertUtils.isNotNull(applyInsuredBoOrigin)) {
                applyInsuredBo.setInsuredId(applyInsuredBoOrigin.getInsuredId());
                applyInsuredBo.setCustomerId(applyInsuredBoOrigin.getCustomerId());
                if (AssertUtils.isNotEmpty(applyInsuredBo.getName())) {
                    applyInsuredBo.setName(applyInsuredBo.getName().toUpperCase().trim());
                } else if (AssertUtils.isNotEmpty(applyInsuredBoOrigin.getName())) {
                    applyInsuredBo.setName(applyInsuredBoOrigin.getName().toUpperCase().trim());
                }
                //网销姓和名处理
                if (AssertUtils.isNotEmpty(applyInsuredBo.getFamilyName())) {
                    applyInsuredBo.setFamilyName(applyInsuredBo.getFamilyName().toUpperCase().trim());
                } else if (AssertUtils.isNotEmpty(applyInsuredBoOrigin.getFamilyName())) {
                    applyInsuredBo.setFamilyName(applyInsuredBoOrigin.getFamilyName().toUpperCase().trim());
                }
                if (AssertUtils.isNotEmpty(applyInsuredBo.getGivenName())) {
                    applyInsuredBo.setGivenName(applyInsuredBo.getGivenName().toUpperCase().trim());
                } else if (AssertUtils.isNotEmpty(applyInsuredBoOrigin.getGivenName())) {
                    applyInsuredBo.setGivenName(applyInsuredBoOrigin.getGivenName().toUpperCase().trim());
                }
                if (AssertUtils.isNotEmpty(applyInsuredBo.getSex())) {
                    applyInsuredBo.setSex(applyInsuredBo.getSex());
                } else if (AssertUtils.isNotEmpty(applyInsuredBoOrigin.getSex())) {
                    applyInsuredBo.setSex(applyInsuredBoOrigin.getSex());
                }
                if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
                    applyInsuredBo.setBirthday(applyInsuredBo.getBirthday());
                } else if (AssertUtils.isNotNull(applyInsuredBoOrigin.getBirthday())) {
                    applyInsuredBo.setBirthday(applyInsuredBoOrigin.getBirthday());
                }
                applyInsuredBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                if (!ApplyTermEnum.RELATIONSHIP_WITH_THE_APPLICANT.ONESELF.name().equals(applyInsuredBo.getRelationship())) {
                    applyInsuredBo.setRelationship(applyInsuredBoOrigin.getRelationship());
                }
            }

            // 保存附件
            if (AssertUtils.isNotEmpty(applyInsuredBo.getAttachmentId())) {
                List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE.name());
                if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                    applyAttachmentBos.stream()
                            .filter(applyAttachmentBo -> applyAttachmentBo.getAttachmentSeq() == 1)
                            .findFirst().ifPresent(applyAttachmentBo -> {
                                applyBoService.deleteApplyAttachmentPo(applyAttachmentBo);
                            });
                }
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_INSURED_IDTYPE.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(applyInsuredBo.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(1L);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            }

            //网销证件类型为护照时,保存护照相关附件
            if (AssertUtils.isNotEmpty(applyInsuredBo.getPasswordAttachmentId())) {
                List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT.name());
                if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                    applyAttachmentBos.stream()
                            .filter(applyAttachmentBo -> applyAttachmentBo.getAttachmentSeq() == 1)
                            .findFirst().ifPresent(applyAttachmentBo -> {
                                applyBoService.deleteApplyAttachmentPo(applyAttachmentBo);
                            });
                }
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(applyInsuredBo.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(1L);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            }

            if (AssertUtils.isNotEmpty(applyInsuredBo.getPasswordVisaAttachmentId())) {
                List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT_VISA.name());
                if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                    applyAttachmentBos.stream()
                            .filter(applyAttachmentBo -> applyAttachmentBo.getAttachmentSeq() == 1)
                            .findFirst().ifPresent(applyAttachmentBo -> {
                                applyBoService.deleteApplyAttachmentPo(applyAttachmentBo);
                            });
                }
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                applyAttachmentBo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CERTIFY_ATTACHMENT_APPLY_INSURED_PASSPORT_VISA.name());
                applyAttachmentBo.setApplyId(applyId);
                applyAttachmentBo.setAttachmentId(applyInsuredBo.getAttachmentId());
                applyAttachmentBo.setAttachmentSeq(1L);
                applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
            }

            //保存客户
            CustomerBusinessRequest customerBusinessRequest = (CustomerBusinessRequest) this.converterObject(applyInsuredBo, CustomerBusinessRequest.class);
            customerBusinessRequest.setUserId(userId);
            ResultObject<UserCustomerResponse> respFcResultObject = customerManageApi.saveCustomerMessagePo(customerBusinessRequest);
            AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, ApplyErrorConfigEnum.APPLY_SAVE_CUSTOMER_ERROR);
            applyInsuredBo.setCustomerId(respFcResultObject.getData().getCustomerId());
            if (AssertUtils.isNotEmpty(applyInsuredBo.getOccupationCode())) {
                //设置职业类型
                applyInsuredBo.setOccupationType(appApplyTransData.getOccupationType(applyInsuredBo.getOccupationCode(), applyId));
            }
            if (AssertUtils.isNotEmpty(applyInsuredBo.getListExpectedPremiumSources())) {
                applyInsuredBo.setExpectedPremiumSources(JackSonUtils.toJson(applyInsuredBo.getListExpectedPremiumSources()));
            }
            applyInsuredBo.setApplyId(applyId);
            applyBoService.saveApplyInsuredPo(applyInsuredBo);

            //保存职业性质
            ApplyOccupationNaturePo occupationNature = applyInsuredBo.getOccupationNature();
            if (AssertUtils.isNotNull(occupationNature)) {
                applyOtherInfoBaseService.deleteApplyOccupationNaturePo(applyId, ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
                occupationNature.setApplyOccupationNatureId(null);
                occupationNature.setSeq("100");
                occupationNature.setApplyId(applyId);
                occupationNature.setCustomerType(ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
                applyOtherInfoBaseService.saveApplyOccupationNaturePo(userId, occupationNature);
            }

            //最后同步计划书数据
            if ("ONLINE".equals(applyPo.getChannelTypeCode())) {
                Users users = new Users();
                users.setUserId(userId);
                this.calculateApplyPlanPremium(applyPo, applyInsuredBo.getPromotionalCode(), users);
                // 保存网销推荐码
                if (AssertUtils.isNotEmpty(applyInsuredBo.getReferralCode())) {
                    applyPo.setReferralCode(applyInsuredBo.getReferralCode());
                    applyBaseService.saveApply(userId, applyPo);
                }
                this.getLogger().info("最后同步网销20a优惠码计划书数据" + JSONObject.toJSONString(applyPo));
            }

            //计算保费
            ResultObject calculatePremiumResultObject = appApplyTransData.calculatePremiumNew(applyPo, applyInsuredBo.getPromotionalCode());
            if (AssertUtils.isResultObjectDataNull(calculatePremiumResultObject)) {
                resultObject.setErrorInfo(calculatePremiumResultObject);
            }
            //产生保费记录
            applyPremiumBaseService.saveApplyPremium(userId, applyId, PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_INITIAL.name());

            // 刮刮卡流程：保存被保人的最后，重新更新一次计划书信息，并生产计划书附件
            if (AssertUtils.isNotEmpty(applyPo.getActivationCode())) {
                appApplyTransData.updatePlanInfo(applyInsuredBo, this.getApplyPlanByApplyId(applyId).getData());
            }

            resultObject.setData(applyInsuredBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_INSURED_ERROR);
            }
        }
        return resultObject;
    }

    public void calculateApplyPlanPremium(ApplyPo applyPo, String promotionalCode, Users users) throws RequestException {
        ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(null, applyPo.getApplyId(), null);
        ApplyBo applyBo = applyExtDao.loadApplyBoById(applyPo.getApplyId());
        getLogger().info("applyPo:{}", JackSonUtils.toJson(applyBo));
        AppApplyBo appApplyBo = (AppApplyBo) this.converterObject(applyBo, AppApplyBo.class);
        appApplyBo.setBranchId(applyPo.getSalesBranchId());
        System.out.println("计算参数：" + JSONObject.toJSONString(appApplyBo));
        com.gclife.product.model.request.calculate.ApplyRequest applyRequest = (com.gclife.product.model.request.calculate.ApplyRequest) converterObject(appApplyBo, ApplyRequest.class);
        applyRequest.setBusinessType("APPLY_TWO");
        String participationDiscountFlag = TerminologyConfigEnum.WHETHER.NO.name();
        if (AssertUtils.isNotNull(applyPlanBo) && (AssertUtils.isNotNull(applyPlanBo.getSpecialDiscount()) || AssertUtils.isNotNull(applyPlanBo.getPromotionType()))) {
            participationDiscountFlag = TerminologyConfigEnum.WHETHER.YES.name();
        }


        applyRequest.setDiscountDate(DateUtils.getCurrentTime());
        applyRequest.setPromotionType(applyPlanBo.getPromotionType());


        //网销20A优惠码官网与app兼容
        if (AssertUtils.isNotEmpty(promotionalCode)) {
            applyRequest.setPromotionalCode(promotionalCode);
            if ("GC00005".equals(promotionalCode)) {
                applyPlanBo.setAgentId("INIT_AGENT_ONLINE002");
            }
        } else {
            participationDiscountFlag = TerminologyConfigEnum.WHETHER.NO.name();
            applyRequest.setPromotionType(null);
            applyPlanBo.setPromotionType(null);
            applyPlanBo.setDiscountModel(null);
            applyPlanBo.setDiscountType(null);
        }
        applyRequest.setParticipationDiscountFlag(participationDiscountFlag);
        //算费后的计划书信息同步
        ResultObject<ApplyResponse> resultObject = productApi.trialCalculation(applyRequest);
        if (!AssertUtils.isResultObjectDataNull(resultObject)) {
            //ApplyPlanBo applyPlanBo1 = (ApplyPlanBo) this.converterObject(resultObject.getData(), ApplyPlanBo.class);
            ClazzUtils.copyPropertiesIgnoreNull(resultObject.getData(), applyPlanBo);
            ClazzUtils.copyPropertiesIgnoreNull(resultObject.getData().getApplicant(), applyPlanBo.getApplicant());
            applyPlanBo.getApplicant().setBirthday(Long.valueOf(resultObject.getData().getApplicant().getBirthday()));
            ClazzUtils.copyPropertiesIgnoreNull(resultObject.getData().getListInsured().get(0), applyPlanBo.getInsured());
            applyPlanBo.getInsured().setBirthday(Long.valueOf(resultObject.getData().getListInsured().get(0).getBirthday()));

//            ClazzUtils.copyPropertiesIgnoreNull(resultObject.getData().getListInsured().get(0).getListCoverage().get(0),applyPlanBo.getCoverages().get(0));
            applyPlanBo.setReceivablePremium(resultObject.getData().getActualPremium());
            applyPlanBo.setPromotionalCode(promotionalCode);
            applyPlanBo.setPromotionalPremium(resultObject.getData().getOnlineDiscountAmount());
            applyPlanBo.setApplyId(applyPo.getApplyId());
            this.saveApplyPlanInfo(applyPlanBo, users);
        }
    }

    @Override
    public ResultObject<List<ApplyBeneficiaryInfoBo>> getBeneficialInfo(String applyId) {
        ResultObject<List<ApplyBeneficiaryInfoBo>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            resultObject.setData(applyPlanBusinessDao.getApplyBeneficialBoList(applyId, null));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_BENEFICIAL_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveBeneficialInfo(Users users, String applyId, List<ApplyBeneficiaryInfoBo> listBeneficiaryInfoBo, AppRequestHeads appRequestHeads) {
        ResultObject<ApplyApplicantBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            //参数验证
            applyPlanParameterValidate.validParameterBeneficialList(listBeneficiaryInfoBo, appRequestHeads, applyPo);
            //业务验证
            applyPlanBusinessValidate.validParameterBeneficialList(listBeneficiaryInfoBo, applyId, appRequestHeads.getDeviceChannel());
            //删除已有的受益人
            List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBoList = applyPlanBusinessDao.getApplyBeneficialBoList(applyId, TerminologyConfigEnum.WHETHER.YES.name());
            applyBoService.deleteApplyBeneficiaryInfoBoList(applyBeneficiaryInfoBoList);
            //获取被保人
            ApplyInsuredBo applyInsuredBo = applyExtDao.loadApplyInsuredBoByApplyId(applyId);
            AssertUtils.isNotNull(getLogger(), applyInsuredBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);

            List<ApplyBeneficialAttachmentRequest> applyBeneficialAttachmentRequests = new ArrayList<>();

            listBeneficiaryInfoBo.forEach(applyBeneficiaryInfoBo -> {
                //受益人
                ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                applyBeneficiaryBo.setName(applyBeneficiaryBo.getName().toUpperCase().trim());
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getFamilyName())) {
                    applyBeneficiaryBo.setFamilyName(applyBeneficiaryBo.getFamilyName().toUpperCase().trim());
                }
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getGivenName())) {
                    applyBeneficiaryBo.setGivenName(applyBeneficiaryBo.getGivenName().toUpperCase().trim());
                }

                //保存客户
                CustomerBusinessRequest customerBusinessRequest = (CustomerBusinessRequest) this.converterObject(applyBeneficiaryBo, CustomerBusinessRequest.class);
                customerBusinessRequest.setUserId(users.getUserId());
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getIdNo()) && AssertUtils.isNotEmpty(applyBeneficiaryBo.getIdType())) {
                    ResultObject<UserCustomerResponse> respFcResultObject = customerManageApi.saveCustomerMessagePo(customerBusinessRequest);
                    AssertUtils.isResultObjectDataNull(getLogger(), respFcResultObject, ApplyErrorConfigEnum.APPLY_SAVE_CUSTOMER_ERROR);
                    applyBeneficiaryBo.setCustomerId(respFcResultObject.getData().getCustomerId());
                }
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getOccupationCode())) {
                    //设置职业类型
                    applyBeneficiaryBo.setOccupationType(appApplyTransData.getOccupationType(applyBeneficiaryBo.getOccupationCode(), applyId));
                }
                applyBeneficiaryBo.setApplyId(applyId);
                applyBoService.saveApplyBeneficiaryPo(applyBeneficiaryBo);
                //受益人关系
                applyBeneficiaryInfoBo.setApplyId(applyId);
                applyBeneficiaryInfoBo.setInsuredId(applyInsuredBo.getInsuredId());
                applyBeneficiaryInfoBo.setBeneficiaryId(applyBeneficiaryBo.getBeneficiaryId());
                if (!AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getModifyFlag())) {
                    applyBeneficiaryInfoBo.setModifyFlag(TerminologyConfigEnum.WHETHER.YES.name());
                }
                applyBoService.saveApplyBeneficiaryInfoPo(applyBeneficiaryInfoBo);

                if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getListBeneficiaryAttachment())) {
                    List<ApplyBeneficiaryAttachmentBo> listBeneficiaryAttachment = applyBeneficiaryInfoBo.getListBeneficiaryAttachment();
                    ApplyBeneficialAttachmentRequest applyBeneficialAttachmentRequest = new ApplyBeneficialAttachmentRequest();
                    List<AttachmentRequest> attachmentRequests = new ArrayList<>();
                    String beneficiaryId = applyBeneficiaryBo.getBeneficiaryId();
                    String name = applyBeneficiaryBo.getName();
                    applyBeneficialAttachmentRequest.setBeneficiaryId(beneficiaryId);
                    applyBeneficialAttachmentRequest.setName(name);
                    listBeneficiaryAttachment.forEach(applyBeneficiaryAttachmentBo -> {
                        if (AssertUtils.isNotEmpty(applyBeneficiaryAttachmentBo.getAttachmentId())) {
                            attachmentRequests.add((AttachmentRequest) this.converterObject(applyBeneficiaryAttachmentBo, AttachmentRequest.class));
                        }
                    });

                    applyBeneficialAttachmentRequest.setListAttachment(attachmentRequests);

                    if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getIdNo()) && AssertUtils.isNotEmpty(applyBeneficiaryBo.getIdType())) {
                        applyBeneficialAttachmentRequests.add(applyBeneficialAttachmentRequest);
                    }
                }

            });

            if (AssertUtils.isNotEmpty(applyBeneficialAttachmentRequests)) {
                this.saveBeneficialAttachment(users, applyId, applyBeneficialAttachmentRequests);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_BENEFICIAL_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<AppApplyBo> getAppApplyInfo(String applyId) {

        ResultObject<AppApplyBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            resultObject.setData(applyPlanBusinessDao.getApplyInfo(applyId));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLICANT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<ApplyContactInfoBo> saveApplyContactInfo(String applyId, ApplyContactInfoBo applyContactInfoBo) {
        ResultObject<ApplyContactInfoBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            //参数验证
            applyPlanParameterValidate.validParameterApplyContactInfo(applyContactInfoBo);
            //业务验证
            applyPlanBusinessValidate.validParameterApplyContactInfo(applyContactInfoBo);
            ApplyContactInfoBo applyContactInfoBoOrigin = applyPlanBusinessDao.getApplyContactInfoBo(applyId);
            if (AssertUtils.isNotNull(applyContactInfoBoOrigin)) {
                applyContactInfoBo.setApplyContactId(applyContactInfoBoOrigin.getApplyContactId());
                applyContactInfoBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                if (!AssertUtils.isNotEmpty(applyContactInfoBoOrigin.getContractMobile())) {
                    applyContactInfoBo.setContractMobile(null);
                } else {
                    applyContactInfoBo.setContractMobile(applyContactInfoBoOrigin.getContractMobile());
                }
            }
            applyContactInfoBo.setContractPhone(applyContactInfoBo.getContractMobile());
            applyContactInfoBo.setApplyId(applyId);
            applyBoService.saveApplyContactInfoPo(applyContactInfoBo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_APPLY_CONTACT_INFO_SAVE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject deleteBeneficialInfo(String applyId, List<ApplyBeneficiaryInfoBo> listBeneficiaryInfoBo) {
        ResultObject<ApplyApplicantBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            if (AssertUtils.isNotEmpty(listBeneficiaryInfoBo)) {
                applyBoService.deleteApplyBeneficiaryInfoBoList(listBeneficiaryInfoBo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_DELETE_BENEFICIAL_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<ApplyPlanPaymentResponse> payForPlanApply(String userId, String applyId, ApplyPlanPaymentRequest applyPlanPaymentRequest) {
        ResultObject<ApplyPlanPaymentResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            String paymentMethod = applyPlanPaymentRequest.getPaymentMethod();
            ApplyInfoExtBo applyInfoExtBo = appApplyBusinessDao.getApplyInfoExtById(applyId);
            AssertUtils.isNotNull(getLogger(), applyInfoExtBo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR);
            ApplyPremiumPayBo applyPremium = applyInfoExtBo.getApplyPremium();
            if (!AssertUtils.isNotNull(applyPremium) && !ApplyTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name().equals(applyPremium.getFeeStatus())) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_PREMIUM_PAY_IS_EXIST);
            }
            //代理人签约状态判断
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            ResultObject<AgentSignBaseResponse> respFcResultObject = baseAgentApi.queryOneAgentSignedAgentId(applyBo.getApplyAgentBo().getAgentId());
            AssertUtils.isResultObjectError(this.getLogger(), respFcResultObject);
            if (AssertUtils.isResultObjectError(respFcResultObject) ||
                    !ApplyTermEnum.SIGN_STATUS.SIGN_COMPLETE.name().equals(respFcResultObject.getData().getSignStatus())) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_PLAN_AGENT_NO_SIGN);
            }
            //调用支付
            applyInfoExtBo.setInitialPaymentMode(paymentMethod);
            applyInfoExtBo.setPaymentMode(paymentMethod);
            ApplyPremiumPayBo applyPremiumPayBo = paymentBusinessService.handApplyPayment(applyInfoExtBo);
            ApplyPlanPaymentResponse applyPlanPaymentResponse = new ApplyPlanPaymentResponse();
            if (AssertUtils.isNotNull(applyPremiumPayBo)) {
                applyPlanPaymentResponse.setPaymentUrl(applyPremiumPayBo.getPaymentUrl());
            }
            if (ApplyTermEnum.PAYMENT_METHODS.WING_H5.name().equals(paymentMethod)) {
                //获取当前机构的所有父机构
                ResultObject<List<BranchResponse>> listResultObject = platformBranchApi.userParentBranchs(applyInfoExtBo.getSalesBranchId());
                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    List<String> branchIds = listResultObject.getData().stream().map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());
                    ApplyConfigDetailBo applyConfigDetailBo = applyConfigBranchExtDao.loadApplyConfigDetailBo(branchIds, paymentMethod);
                    if (AssertUtils.isNotNull(applyConfigDetailBo)) {
                        applyPlanPaymentResponse.setPaymentCertificate(applyConfigDetailBo.getValue());
                    }
                }
            }
            //写死如果是银行转账就需要上传支付凭证
            if (ApplyTermEnum.PAYMENT_METHODS.BANK_TRANSFER.name().equals(paymentMethod)) {
                AssertUtils.isNotEmpty(this.getLogger(), applyPlanPaymentRequest.getListAttachment(), APPLY_ATTACHMENT_ID_IS_NOT_NULL);
                applyPlanPaymentRequest.getListAttachment().forEach(attachmentRequest -> {
                    AssertUtils.isNotEmpty(this.getLogger(), attachmentRequest.getAttachmentId(), APPLY_ATTACHMENT_ID_IS_NOT_NULL);
                });
                //先删除指定支付附件
                applyAttachmentBaseService.deleteApplyAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT.name());
                //保存现有附件
                long initSeq = 1;
                //添加
                List<ApplyAttachmentPo> newAttachmentPos = new ArrayList<>();
                for (AttachmentRequest attachmentRequest : applyPlanPaymentRequest.getListAttachment()) {
                    ApplyAttachmentPo applyAttachmentPo = new ApplyAttachmentPo();
                    applyAttachmentPo.setApplyId(applyId);
                    applyAttachmentPo.setAttachmentId(attachmentRequest.getAttachmentId());
                    applyAttachmentPo.setAttachmentSeq(initSeq);
                    applyAttachmentPo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT.name());
                    applyAttachmentPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
                    applyAttachmentPo.setCreatedUserId(userId);
                    applyAttachmentPo.setUpdatedUserId(userId);
                    initSeq = initSeq + 1;
                    newAttachmentPos.add(applyAttachmentPo);
                }
                applyAttachmentBaseService.saveApplyAttachment(newAttachmentPos);
            }
            resultObject.setData(applyPlanPaymentResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_APP_PAYMENT_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyPlanBo> getApplyPlanInfoInitial(String agentId, String productId, String clientUserId) {

        ResultObject<ApplyPlanBo> resultObject = new ResultObject<>();
        try {
            if (!AssertUtils.isNotEmpty(clientUserId)) {
                AssertUtils.isNotEmpty(getLogger(), agentId, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            }
            AssertUtils.isNotEmpty(getLogger(), productId, ApplyErrorConfigEnum.APPLY_INPUT_PRODUCT_ID_IS_NOT_NULL);
            ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoByProductId(agentId, productId, clientUserId);
            if (AssertUtils.isNotNull(applyPlanBo)) {
                List<ApplyPlanAttachmentPo> applyPlanAttachment = applyAttachmentBaseService.getApplyPlanAttachment(applyPlanBo.getApplyPlanId());
                if (AssertUtils.isNotEmpty(applyPlanAttachment)) {
                    applyPlanBo.setAttachments(applyPlanAttachment);
                }
            }
            resultObject.setData(applyPlanBo);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据投保单ID查询计划书对象
     *
     * @param applyId 投保单ID
     * @return 计划书对象
     */
    @Override
    public ResultObject<ApplyPlanResponse> getApplyPlanPoByApplyId(String applyId) {
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        ResultObject<ApplyPlanResponse> resultObject = new ResultObject<>();
        try {
            ApplyPlanPo applyPlanPo = this.applyPlanBusinessDao.getApplyPlanPoByApplyId(applyId);

            ApplyPlanResponse applyPlanResponse = (ApplyPlanResponse) this.converterObject(applyPlanPo, ApplyPlanResponse.class);

            resultObject.setData(applyPlanResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject<ApplyAutoUWResponse> postApplyUnderwrite(Users users, AppRequestHeads appRequestHeads, String applyId) {
        ResultObject<ApplyAutoUWResponse> resultObject = new ResultObject<>();
        ApplyAutoUWResponse applyAutoUWResponse = new ApplyAutoUWResponse();

        AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        ApplyInfoExtBo applyInfoExtBo = appApplyBusinessDao.getApplyInfoExtById(applyId);
        //设置app点击承保时间
        if (!AssertUtils.isNotNull(applyInfoExtBo.getAppSubmitUnderwritingDate())) {
            applyInfoExtBo.setAppSubmitUnderwritingDate(DateUtils.getCurrentTime());
        }
        log.info("=========================申请承保==============================");
        log.info(JSON.toJSONString(applyInfoExtBo));
        log.info("===========================申请承保============================");
        AssertUtils.isNotNull(getLogger(), applyInfoExtBo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR);
        if (AssertUtils.isNotEmpty(applyInfoExtBo.getApplyStatus()) && !applyInfoExtBo.getApplyStatus().equals(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name())) {
            throwsException(log, ApplyErrorConfigEnum.APPLY_APPLY_HAVE_WAIT_UNDERWRITING);
//            applyAutoUWResponse.setPassAutoUnderWritingFlag(false);
//            applyAutoUWResponse.setPaymentFlag(false);
//            resultObject.setData(applyAutoUWResponse);
//            return resultObject;
        }
        //打开智能核保
        applyAutoUWResponse = applyAutoUnderWriteBusinessService.initiateAutoUnderWriting(applyId, users, appRequestHeads, "AUTO_UW");

        //查询paymentId
        ApplyPremiumBo applyPremium = applyBaseDao.getApplyPremium(applyId);
        if (AssertUtils.isNotNull(applyPremium)) {
            String paymentId = applyPremium.getPaymentId();
            applyAutoUWResponse.setPaymentId(paymentId);
        }

        //智能核保是否通过
        if (applyAutoUWResponse.isPassAutoUnderWritingFlag()) {
            //设置投保单为核保通过状态
            applyInfoExtBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());
            applyInfoExtBo.setOnlineLanguage(appRequestHeads.getLanguage());
            applyBoService.saveApplyPo(applyInfoExtBo);
            //此处不用开启工作流，而是在支付回调成功之后开启智能核保线路的工作流
        } else {
            applyInfoExtBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE.name());
            applyBoService.saveApplyPo(applyInfoExtBo);
            //开启工作流
            ResultObject startProcess = workFlowApi.startProcess(appApplyTransData.getStartProcessParam(users.getUserId(), applyInfoExtBo));
            AssertUtils.isResultObjectError(log, startProcess);
            try {
                // 发送消息给后台操作人员
                ApplyBo applyBo = applyBaseService.queryApply(applyId);
                // 查询代理人信息
                AgentResponse applyAgentRespFc = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
                applyBo.getApplyAgentBo().setAgentName(applyAgentRespFc.getAgentName());
                messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APP_APPLY_INSURE_SUBMIT_CP.name(), applyBo);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        resultObject.setData(applyAutoUWResponse);
        return resultObject;
    }

    @Override
    public ResultObject getConfirmInformation(String userId, String applyId) {
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        ResultObject resultObject = new ResultObject<>();
        try {
            ApplyPlanPo applyPlanPo = this.applyPlanBusinessDao.getApplyPlanPoByApplyId(applyId);
            AssertUtils.isNotNull(getLogger(), applyPlanPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_PLAN_IS_NOT_FOUND_OBJECT);
            AssertUtils.isNotNull(getLogger(), applyPlanPo.getReceivablePremium(), ApplyErrorConfigEnum.APPLY_PLAN_RECEIVABLE_PREMIUM_ERROR);
            ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ACCEPT_IS_NULL_ERROR);
            AssertUtils.isNotNull(getLogger(), applyPo.getReceivablePremium(), ApplyErrorConfigEnum.APPLY_APPLY_RECEIVABLE_PREMIUM_ERROR);
            if (applyPlanPo.getReceivablePremium().compareTo(applyPo.getReceivablePremium()) != 0) {
                //计划书费用和保费不一致
                throw new RequestException(ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_PREMIUM_IS_NOT_EQUALS_PLAN);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_APPLY_CONFIRM_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyContactInfoResponse> getApplyContactInfo(String applyId) {
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        ResultObject<ApplyContactInfoResponse> resultObject = new ResultObject<>();
        ApplyContactInfoResponse applyContactInfoResponse = null;
        try {
            ApplyContactInfoBo applyContactInfoBo = applyPlanBusinessDao.getApplyContactInfoBo(applyId);
            if (AssertUtils.isNotNull(applyContactInfoBo)) {
                applyContactInfoResponse = (ApplyContactInfoResponse) converterObject(applyContactInfoBo, ApplyContactInfoResponse.class);
                applyContactInfoResponse.setContractMobile(applyContactInfoBo.getContractPhone());
            } else {
                List<ApplyApplicantPo> applyApplicantPoList = applyApplicantDao.fetchByApplyId(applyId);
                if (AssertUtils.isNotEmpty(applyApplicantPoList) && applyApplicantPoList.size() > 0) {
                    applyContactInfoResponse = new ApplyContactInfoResponse();
                    applyContactInfoResponse.setContractName(applyApplicantPoList.get(0).getName());
                    applyContactInfoResponse.setContractMobile(applyApplicantPoList.get(0).getMobile());
                    applyContactInfoResponse.setPostcodes(applyApplicantPoList.get(0).getHomeZipCode());
                    applyContactInfoResponse.setSendAddrAreaCode(applyApplicantPoList.get(0).getHomeAreaCode());
                    applyContactInfoResponse.setSendAddrContact(applyApplicantPoList.get(0).getHomeAddress());
                }
            }
            resultObject.setData(applyContactInfoResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_APP_CONTACT_INFO_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject updatePlanRecipient(ApplyPlanPo applyPlanPo) {
        ResultObject resultObject = new ResultObject<>();
        try {
            applyBoService.saveApplyPlanPo(applyPlanPo);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PLAN_ROLLBACK_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 根据搜索条件分页查询计划书
     *
     * @param applyPlanListRequest 请求对象
     * @param users                当前用户
     * @return list
     */
    @Override
    public ResultObject<BasePageResponse<ApplyPlanListResponse>> listApplyPlanByPage(ApplyPlanListRequest applyPlanListRequest, Users users) {
        ResultObject<BasePageResponse<ApplyPlanListResponse>> resultObject = new ResultObject<>();
        try {
            // 查询当前用户管理的销售机构
            ResultObject<List<BranchResponse>> listResultObject = platformBranchBaseApi.queryUserOptionBranchTreeLeaf(users.getUserId());
            AssertUtils.isResultObjectError(listResultObject);
            List<BranchResponse> branchResponses = listResultObject.getData();
            if (!AssertUtils.isNotEmpty(branchResponses)) {
                throwsException(ApplyErrorConfigEnum.APPLY_QUERY_INPUT_SALE_BRANCH_ERROR);
            }
            List<String> branchIds = branchResponses.stream()
                    .filter(branchBaseRespFc -> AssertUtils.isNotEmpty(branchBaseRespFc.getBranchId()))
                    .map(BranchResponse::getBranchId).distinct().collect(Collectors.toList());

            // 查询销售机构下所有代理人信息
            ResultObject<List<AgentSimpleBaseResponse>> listAgentResultObject = baseAgentApi.queryAgentsByBranchs(branchIds);
            AssertUtils.isResultObjectError(listAgentResultObject);
            List<AgentSimpleBaseResponse> agentSimpleBaseRespFcs = listAgentResultObject.getData();
            if (!AssertUtils.isNotEmpty(agentSimpleBaseRespFcs)) {
                throwsException(ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            }
            // 经关键筛选代理人名称的代理人ID集
            List<String> agentIdsFiler = agentSimpleBaseRespFcs.stream()
                    .filter(agentSimpleBaseRespFc -> {
                        boolean flag = AssertUtils.isNotEmpty(agentSimpleBaseRespFc.getAgentId());
                        if (AssertUtils.isNotEmpty(applyPlanListRequest.getKeyword())) {
                            flag = flag && agentSimpleBaseRespFc.getAgentName().contains(applyPlanListRequest.getKeyword());
                        }
                        return flag;
                    }).map(AgentSimpleBaseResponse::getAgentId).distinct().collect(Collectors.toList());

            // 未经筛选的代理人ID集
            List<String> agentIds = agentSimpleBaseRespFcs.stream()
                    .filter(agentSimpleBaseRespFc -> AssertUtils.isNotEmpty(agentSimpleBaseRespFc.getAgentId()))
                    .map(AgentSimpleBaseResponse::getAgentId).distinct().collect(Collectors.toList());

            // 查询数据
            List<ApplyPlanBo> applyPlanBos = applyPlanBusinessDao.listApplyPlanByPage(applyPlanListRequest, agentIds, agentIdsFiler);
            if (!AssertUtils.isNotEmpty(applyPlanBos)) {
                return resultObject;
            }
            // 数据转换
            List<ApplyPlanListResponse> applyPlanListResponses = getApplyPlanListResponses(agentSimpleBaseRespFcs, applyPlanBos);

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyPlanBos) ? applyPlanBos.get(0).getTotalLine() : null;

            BasePageResponse<ApplyPlanListResponse> basePageResponse = BasePageResponse.getData(applyPlanListRequest.getCurrentPage(), applyPlanListRequest.getPageSize(), totalLine, applyPlanListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PLAN_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyPremiumBo> queryApplyPremiumInfo(String applyId) {
        ResultObject<ApplyPremiumBo> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
            resultObject.setData(applyPremiumBaseService.queryApplyPremium(applyId));
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PAYMENT_INFO_ERROR);
        }
        return resultObject;
    }

    /**
     * 保存受益人附件信息
     *
     * @param users                             用户
     * @param applyId                           投保单ID
     * @param applyBeneficialAttachmentRequests 受益人附件
     * @return ResultObject
     */
    @Override
    public ResultObject saveBeneficialAttachment(Users users, String applyId, List<ApplyBeneficialAttachmentRequest> applyBeneficialAttachmentRequests) {
        ResultObject resultObject = new ResultObject();
        this.getLogger().info("保存受益人附件请求参数====================" + JackSonUtils.objectToJsonStr(applyBeneficialAttachmentRequests));

        AssertUtils.isNotEmpty(getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        //验证数据,多了不要,id不一样的不要
        AssertUtils.isNotEmpty(this.getLogger(), applyBeneficialAttachmentRequests, APPLY_APP_BENEFICIAL_IS_NOT_NULL);

        boolean is20A = ProductTermEnum.PRODUCT.PRODUCT_20A.id().equals(applyDataTransform.getMainProductId(applyId));

//        ResultObject<List<ApplyBeneficialAttachmentRequest>> beneficialAttachment = this.getBeneficialAttachment(users, applyId);
//        AssertUtils.isResultObjectListDataNull(this.getLogger(), beneficialAttachment, ApplyErrorConfigEnum.APPLY_APP_BENEFICIAL_IS_NOT_NULL_FORMAT_ERROR);
//        List<ApplyBeneficialAttachmentRequest> attachmentData = beneficialAttachment.getData();


        /*if (applyBeneficialAttachmentRequests.size() != attachmentData.size()) {
            throw new RequestException(APPLY_APP_BENEFICIAL_IS_NOT_NULL_FORMAT_ERROR);
        }*/
//        List<String> beneficiaryIds = attachmentData.stream().map(ApplyBeneficialAttachmentRequest::getBeneficiaryId).collect(Collectors.toList());
//        applyBeneficialAttachmentRequests.forEach(applyBeneficialAttachmentRequest -> {
//            if (!AssertUtils.isNotEmpty(applyBeneficialAttachmentRequest.getBeneficiaryId()) || !beneficiaryIds.contains(applyBeneficialAttachmentRequest.getBeneficiaryId())) {
//                throw new RequestException(APPLY_APP_BENEFICIAL_IS_NOT_NULL_FORMAT_ERROR);
//            }
//            AssertUtils.isNotEmpty(this.getLogger(), applyBeneficialAttachmentRequest.getListAttachment(), APPLY_ATTACHMENT_ID_IS_NOT_NULL);
//            applyBeneficialAttachmentRequest.getListAttachment().forEach(attachmentRequest -> AssertUtils.isNotEmpty(this.getLogger(), attachmentRequest.getAttachmentId(), APPLY_ATTACHMENT_ID_IS_NOT_NULL));
//            //附件数量也要保持一致
//            attachmentData.stream().filter(data -> data.getBeneficiaryId().equals(applyBeneficialAttachmentRequest.getBeneficiaryId())).findFirst().ifPresent(data -> {
//                if (applyBeneficialAttachmentRequest.getListAttachment().size() != data.getListAttachment().size()) {
//                    throw new RequestException(APPLY_IMAGE_ATTACHMENT_FORMAT_ERROR);
//                }
//            });
//        });

        //删除原有受益人附件
        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_IDTYPE.name());
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
        }

        //网销护照删除原有受益人附件
        List<ApplyAttachmentBo> applyPassportAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_PASSPORT.name());
        if (AssertUtils.isNotEmpty(applyPassportAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyPassportAttachmentBos);
        }

        //删除原有受益人附件
        List<ApplyAttachmentBo> applyPassportVisaAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_PASSPORT_VISA.name());
        if (AssertUtils.isNotEmpty(applyPassportVisaAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyPassportVisaAttachmentBos);
        }

        long index = 1;
        for (ApplyBeneficialAttachmentRequest applyBeneficialAttachmentRequest : applyBeneficialAttachmentRequests) {
            //根据受益人ID删除受益人附件关联表
            applyExtDao.deleteApplyBeneficiaryAttachmentPo(applyBeneficialAttachmentRequest.getBeneficiaryId());
            //保存附件表，类型为受益人证件
            for (AttachmentRequest attachmentRequest : applyBeneficialAttachmentRequest.getListAttachment()) {
                ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
                if (AssertUtils.isNotEmpty(attachmentRequest.getAttachmentId())) {
                    if (AssertUtils.isNotEmpty(attachmentRequest.getAttachmentTypeCode())) {
                        applyAttachmentBo.setAttachmentTypeCode(attachmentRequest.getAttachmentTypeCode());
                    } else {
                        applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_BENEFICIARY_IDTYPE.name());
                    }
                    applyAttachmentBo.setApplyId(applyId);
                    applyAttachmentBo.setCreatedUserId(users.getUserId());
                    applyAttachmentBo.setAttachmentId(attachmentRequest.getAttachmentId());
                    applyAttachmentBo.setAttachmentSeq(index);
                    applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
                    index++;
                    //保存受益人附件关联表
                    ApplyBeneficiaryAttachmentPo applyBeneficiaryAttachmentPo = new ApplyBeneficiaryAttachmentPo();
                    ApplyBeneficiaryPo applyBeneficiaryPo = applyBeneficiaryDao.findById(applyBeneficialAttachmentRequest.getBeneficiaryId());
                    AssertUtils.isNotNull(this.getLogger(), applyBeneficiaryPo, APPLY_APP_BENEFICIAL_IS_NOT_FOUND_OBJECT);
                    applyBeneficiaryAttachmentPo.setCustomerId(applyBeneficiaryPo.getCustomerId());
                    applyBeneficiaryAttachmentPo.setAttachmentId(attachmentRequest.getAttachmentId());
                    applyBoService.saveApplyBeneficiaryAttachmentPo(applyBeneficiaryAttachmentPo);
                }
            }
        }
        return resultObject;
    }

    /**
     * 获取受益人附件信息
     *
     * @param users   用户
     * @param applyId 投保单ID
     * @return ApplyBeneficialAttachmentRequests
     */
    @Override
    public ResultObject<List<ApplyBeneficialAttachmentRequest>> getBeneficialAttachment(Users users, String applyId, AppRequestHeads appRequestHeads) {

        ResultObject<List<ApplyBeneficialAttachmentRequest>> resultObject = new ResultObject<>();
        List<ApplyBeneficialAttachmentRequest> applyBeneficialAttachmentRequests = new ArrayList<>();
        List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBoList = applyPlanBusinessDao.getApplyBeneficialBoList(applyId, TerminologyConfigEnum.WHETHER.YES.name());

        //查询投被保人
        ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        ApplyInsuredBo applyInsuredBo = applyExtDao.loadApplyInsuredBoByApplyId(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyInsuredBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        //移除受益人为投保人,或者受益人为被保人的数据
        applyBeneficiaryInfoBoList.removeIf(applyBeneficiaryInfoBo ->
                !AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId()) || (
                        applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId().equals(applyApplicantBo.getCustomerId())
                                || applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getCustomerId().equals(applyInsuredBo.getCustomerId())));

        if (!AssertUtils.isNotEmpty(applyBeneficiaryInfoBoList)) {
            return resultObject;
        }

        applyBeneficiaryInfoBoList.forEach(applyBeneficiaryInfoBo -> {
            ApplyBeneficialAttachmentRequest applyBeneficialAttachmentRequest = new ApplyBeneficialAttachmentRequest();
            applyBeneficialAttachmentRequest.setBeneficiaryId(applyBeneficiaryInfoBo.getBeneficiaryId());
            applyBeneficialAttachmentRequest.setName(applyBeneficiaryInfoBo.getApplyBeneficiaryBo().getName());
            //附件
            List<AttachmentRequest> attachmentRequests = (List<AttachmentRequest>) this.converterList(applyBeneficiaryInfoBo.getListBeneficiaryAttachment(), new TypeToken<List<AttachmentRequest>>() {
            }.getType());
            if (AssertUtils.isNotEmpty(attachmentRequests)) {
                List<String> attachmentIds = attachmentRequests.stream().map(AttachmentRequest::getAttachmentId).distinct().collect(Collectors.toList());
                ResultObject<List<AttachmentResponse>> listResultObject = attachmentApi.attachmentList(attachmentIds);
                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    attachmentRequests.forEach(attachmentRequest -> {
                        listResultObject.getData().stream().filter(attachmentResponse -> attachmentResponse.getMediaId().equals(attachmentRequest.getAttachmentId()))
                                .findFirst().ifPresent(attachmentResponse -> attachmentRequest.setUrl(attachmentResponse.getUrl()));
                    });
                }
                if (attachmentRequests.size() == 1 && !"browser".equals(appRequestHeads.getDeviceChannel()) && !AssertUtils.isNotEmpty(applyPo.getActivationCode())) {
                    AttachmentRequest attachmentRequest = new AttachmentRequest();
                    attachmentRequest.setAttachmentId(null);
                    attachmentRequest.setAttachmentSeq(null);
                    attachmentRequest.setUrl(null);
                    attachmentRequests.add(attachmentRequest);
                }
                applyBeneficialAttachmentRequest.setListAttachment(attachmentRequests);
            } else {
                applyBeneficialAttachmentRequest.setListAttachment(this.newBlankAttachment());
            }
            applyBeneficialAttachmentRequests.add(applyBeneficialAttachmentRequest);
        });
        resultObject.setData(applyBeneficialAttachmentRequests);
        return resultObject;
    }

    /**
     * 计划书分享页保存查询用户信息接口
     *
     * @param applyPlanTraceRequest
     * @return
     */
    @Override
    @Transactional
    public ResultObject saveApplyPlanTrace(ApplyPlanTraceRequest applyPlanTraceRequest) {
        ResultObject resultObject = new ResultObject();
        ApplyPlanTracePo applyPlanTracePo = new ApplyPlanTracePo();
        applyPlanTracePo.setApplyPlanId(applyPlanTraceRequest.getApplyPlanId());
        String userId = applyPlanTraceRequest.getUserId();
        applyPlanTracePo.setUserId(userId);
        ResultObject<AgentBaseResponse> agentBaseRespFcResultObject = baseAgentApi.queryOneAgentById(userId);
        if (!AssertUtils.isResultObjectDataNull(agentBaseRespFcResultObject)) {
            AgentBaseResponse data = agentBaseRespFcResultObject.getData();
            applyPlanTracePo.setNickName(data.getAgentName());
            ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(data.getAgentDetail().getHeadAttachId());
            if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                applyPlanTracePo.setUserHeadUrl(mediaUrl.getData().getUrl());
            }
        }
        applyPlanTracePo.setViewTime(DateUtils.getCurrentTime());
        applyBaseService.saveApplyPlanTrace(userId, applyPlanTracePo);

        //查询计划书
        ApplyPlanBo applyPlanBo = applyPlanBusinessDao.queryOneApplyPlanBoByApplyPlanId(applyPlanTraceRequest.getApplyPlanId());
        if (AssertUtils.isNotNull(applyPlanBo)) {
            //发送客户点击进来的的微信消息
            if (!applyPlanTraceRequest.getUserId().equals(applyPlanBo.getAgentId())) {
                Map<String, String> map = new HashMap<>();
                map.put("userName", AssertUtils.isNotEmpty(applyPlanTracePo.getNickName()) ? applyPlanTracePo.getNickName() : "无");
                //发送消息
                messageBusinessService.pushApplyMessageSingle(ApplyTermEnum.MSG_BUSINESS_TYPE.APP_APPLY_INSURE_SUBMIT_CP.name(), applyPlanBo, applyPlanBo.getAgentId(), map);
            }
        }


        return resultObject;
    }

    /**
     * 计划书分享更新分享时间
     *
     * @param users
     * @param applyPlanId
     * @return
     */
    @Override
    @Transactional
    public ResultObject shareApplyPlan(Users users, String applyPlanId) {
        ResultObject resultObject = new ResultObject();
        ApplyPlanBo applyPlanBo = applyPlanBusinessDao.getApplyPlanBoById(applyPlanId, null, null);
        ApplyPlanPo applyPlanPo = new ApplyPlanPo();
        ClazzUtils.copyPropertiesIgnoreNull(applyPlanBo, applyPlanPo);
        if (!AssertUtils.isNotNull(applyPlanPo.getShareDate())) {
            applyPlanPo.setShareDate(DateUtils.getCurrentTime());
            applyBoService.saveApplyPlanPo(applyPlanPo);
        }
        return resultObject;
    }

    /**
     * 分页查询计划书追踪用户
     *
     * @param users           用户
     * @param applyPlanId     计划书ID
     * @param basePageRequest 分页参数
     * @return <List<ApplyPlanTraceBo>>
     */
    @Override
    public ResultObject<List<ApplyPlanTraceBo>> getApplyPlanTracePage(Users users, String applyPlanId, BasePageRequest basePageRequest) {
        ResultObject<List<ApplyPlanTraceBo>> resultObject = new ResultObject<>();
        List<ApplyPlanTraceBo> applyPlanTraceBos = applyBaseService.queryApplyPlanTraceByPage(applyPlanId, basePageRequest);
        resultObject.setData(applyPlanTraceBos);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveCustomerImageAttachment(String applyId, CustomerAttachmentRequest customerAttachmentRequest) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        applyPlanParameterValidate.validCustomerImageAttachment(customerAttachmentRequest);
        List<ApplyAttachmentBo> customerPhotoBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CUSTOMER_INSURED_PHOTO.name());
        List<ApplyAttachmentBo> customerVideoBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CUSTOMER_INSURED_VIDEO.name());
        //删除已有附件
        applyBoService.deleteApplyAttachmentBoList(customerPhotoBos);
        applyBoService.deleteApplyAttachmentBoList(customerVideoBos);
        //保存新的附件
        if (AssertUtils.isNotNull(customerAttachmentRequest.getCustomerImage())) {
            ApplyAttachmentBo applyAttachmentBo = (ApplyAttachmentBo) converterObject(customerAttachmentRequest.getCustomerImage(), ApplyAttachmentBo.class);
            applyAttachmentBo.setApplyId(applyId);
            applyAttachmentBo.setAttachmentSeq(1L);
            applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        }
        if (AssertUtils.isNotNull(customerAttachmentRequest.getCustomerVideo())) {
            ApplyAttachmentBo applyAttachmentBo = (ApplyAttachmentBo) converterObject(customerAttachmentRequest.getCustomerVideo(), ApplyAttachmentBo.class);
            applyAttachmentBo.setApplyId(applyId);
            applyAttachmentBo.setAttachmentSeq(1L);
            applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        }
        return resultObject;
    }

    /**
     * 贷款合同上传
     *
     * @param applyId               投保单ID
     * @param appAttachmentRequests 贷款合同
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postContractLoanAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        ResultObject resultObject = new ResultObject();
        //参数验证
        ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

        AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequests, APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequest.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequest.getAttachmentSeq() + "", ApplyErrorConfigEnum.APPLY_ATTACHMENT_SEQ_IS_NOT_NULL);
        });

        //删除原有
        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_LOAN_CONTRACT.name());
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
        }
        //保存
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
            applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_LOAN_CONTRACT.name());
            applyAttachmentBo.setApplyId(applyId);
            applyAttachmentBo.setAttachmentId(appAttachmentRequest.getAttachmentId());
            applyAttachmentBo.setAttachmentSeq(appAttachmentRequest.getAttachmentSeq());
            applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
        return resultObject;
    }

    /**
     * 上传修改申请书
     *
     * @param applyId               投保单ID
     * @param appAttachmentRequests 修改申请书
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject postModifyApplicationAttachment(String applyId, List<AppAttachmentRequest> appAttachmentRequests) {
        ResultObject resultObject = new ResultObject();
        //参数验证
        ApplyPo applyPo = applyExtDao.loadApplyPoById(applyId);
        AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

        AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequests, APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequest.getAttachmentId(), ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), appAttachmentRequest.getAttachmentSeq() + "", ApplyErrorConfigEnum.APPLY_ATTACHMENT_SEQ_IS_NOT_NULL);
        });

        //删除原有
        List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_MODIFY_APPLICATION.name());
        if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
            applyBoService.deleteApplyAttachmentBoList(applyAttachmentBos);
        }
        //保存
        appAttachmentRequests.forEach(appAttachmentRequest -> {
            ApplyAttachmentBo applyAttachmentBo = new ApplyAttachmentBo();
            applyAttachmentBo.setAttachmentTypeCode(ModelConstantEnum.CERTIFY_ATTACHMENT.CERTIFY_ATTACHMENT_APPLY_MODIFY_APPLICATION.name());
            applyAttachmentBo.setApplyId(applyId);
            applyAttachmentBo.setAttachmentId(appAttachmentRequest.getAttachmentId());
            applyAttachmentBo.setAttachmentSeq(appAttachmentRequest.getAttachmentSeq());
            applyBoService.saveApplyAttachmentPo(applyAttachmentBo);
        });
        return resultObject;
    }

    @Override
    public ResultObject<CustomerAttachmentRequest> getCustomerImageAttachment(String applyId) {
        ResultObject<CustomerAttachmentRequest> resultObject = new ResultObject<>();
        CustomerAttachmentRequest customerAttachmentRequest = new CustomerAttachmentRequest();
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        List<ApplyAttachmentBo> customerPhotoBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CUSTOMER_INSURED_PHOTO.name());
        List<ApplyAttachmentBo> customerVideoBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.CUSTOMER_INSURED_VIDEO.name());
        ResultObject<List<SyscodeResponse>> queryInternational = platformInternationalBaseApi.queryInternational(TerminologyTypeEnum.CERTIFY_ATTACHMENT_TYPE.name(), null);
        List<SyscodeResponse> syscodeResponses = queryInternational.getData();
        if (AssertUtils.isNotNull(customerPhotoBos)) {
            AppAttachmentRequest customerImage = new AppAttachmentRequest();
            ClazzUtils.copyPropertiesIgnoreNull(customerPhotoBos.get(0), customerImage);
            syscodeResponses.stream().filter(syscodeRespFc -> syscodeRespFc.getCodeKey().equals(customerImage.getAttachmentTypeCode())).findFirst().ifPresent(syscodeResponse -> {
                customerImage.setAttachmentTypeName(syscodeResponse.getCodeName());
            });
            ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(customerImage.getAttachmentId());
            if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                customerImage.setUrl(mediaUrl.getData().getUrl());
            }
            customerAttachmentRequest.setCustomerImage(customerImage);
        }
        if (AssertUtils.isNotNull(customerVideoBos)) {
            AppAttachmentRequest customerVideo = new AppAttachmentRequest();
            ClazzUtils.copyPropertiesIgnoreNull(customerVideoBos.get(0), customerVideo);
            syscodeResponses.stream().filter(syscodeRespFc -> syscodeRespFc.getCodeKey().equals(customerVideo.getAttachmentTypeCode())).findFirst().ifPresent(syscodeResponse -> {
                customerVideo.setAttachmentTypeName(syscodeResponse.getCodeName());
            });
            ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(customerVideo.getAttachmentId());
            if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                customerVideo.setUrl(mediaUrl.getData().getUrl() + "?x-oss-process=video/snapshot,t_0,m_fast,f_png,ar_auto");
            }
            customerAttachmentRequest.setCustomerVideo(customerVideo);
        }
        resultObject.setData(customerAttachmentRequest);
        return resultObject;
    }

    /**
     * 查询保单持有人信息
     *
     * @param applyId
     * @return
     */
    @Override
    public ResultObject<ApplyHolderResponse> getApplyHolderInfo(String applyId) {
        ResultObject<ApplyHolderResponse> resultObject = new ResultObject<>();
        ApplyHolderPo applyHolderPo = applyOtherInfoBaseService.queryApplyHolderPo(applyId);
        if (AssertUtils.isNotNull(applyHolderPo)) {
            resultObject.setData((ApplyHolderResponse) this.converterObject(applyHolderPo, ApplyHolderResponse.class));
        }
        return resultObject;
    }

    /**
     * 保存保单持有人信息
     *
     * @param userId
     * @param applyHolderRequest
     * @return
     */
    @Override
    @Transactional
    public ResultObject saveApplyHolderInfo(String userId, ApplyHolderRequest applyHolderRequest) {
        String applyId = applyHolderRequest.getApplyId();
        AssertUtils.isNotEmpty(log, applyId, APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applyHolderRequest.getRelationship(), APPLY_HOLDER_RELATIONSHIP_IS_NOT_NULL);
        if (ApplyTermEnum.RELATIONSHIP_WITH_THE_INSURED.OTHER.name().equals(applyHolderRequest.getRelationship())) {
            AssertUtils.isNotEmpty(log, applyHolderRequest.getRelationshipInstructions(), APPLY_APP_BENEFICIAL_RELATIONSHIP_INSTRUCTIONS_IS_NOT_NULL);
        }
        AssertUtils.isNotEmpty(log, applyHolderRequest.getName(), AGENT_AGENT_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applyHolderRequest.getBirthdayFormat(), AGENT_AGENT_BIRTHDAY_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applyHolderRequest.getSex(), AGENT_ID_SEX_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applyHolderRequest.getIdType(), AGENT_ID_TYPE_CODE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applyHolderRequest.getIdNo(), AGENT_AGENT_ID_NO_IS_NOT_NULL);
        AssertUtils.isNotEmpty(log, applyHolderRequest.getIdExpDateFormat(), AGENT_VALIDITY_OF_CERTIFICATE_IS_NOT_NULL);
        long birthday = DateUtils.stringToTime(applyHolderRequest.getBirthdayFormat(), DateUtils.FORMATE18);
        if (applyDataTransform.getAgeByBirthday(birthday) < 18) {
            throw new RequestException(ApplyErrorConfigEnum.APPLY_HOLDER_IS_NOT_ADULT);
        }

        // 查询投被保人
        ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(log, applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        // 保单持有人与投保人为同一人时，弹窗提示：“投保人已默认为保单持有人，请重新录入或跳过”
        if (applyApplicantBo.getName().trim().equalsIgnoreCase(applyHolderRequest.getName().trim())
                && DateUtils.timeToTimeLow(applyApplicantBo.getBirthday()) == DateUtils.timeToTimeLow(birthday)
                && applyApplicantBo.getSex().equals(applyHolderRequest.getSex())
                && applyApplicantBo.getIdNo().trim().equalsIgnoreCase(applyHolderRequest.getIdNo().trim())
        ) {
            throw new RequestException(ApplyErrorConfigEnum.APPLY_HOLDER_IS_REPEAT);
        }

        ApplyHolderPo applyHolderPo = applyOtherInfoBaseService.queryApplyHolderPo(applyId);
        if (!AssertUtils.isNotNull(applyHolderPo)) {
            applyHolderPo = new ApplyHolderPo();
        }
        applyHolderPo.setApplyId(applyId);
        applyHolderPo.setRelationship(applyHolderRequest.getRelationship());
        applyHolderPo.setRelationshipInstructions(applyHolderRequest.getRelationshipInstructions());
        applyHolderPo.setName(applyHolderRequest.getName());
        applyHolderPo.setBirthday(birthday);
        applyHolderPo.setSex(applyHolderRequest.getSex());
        applyHolderPo.setIdType(applyHolderRequest.getIdType());
        applyHolderPo.setIdNo(applyHolderRequest.getIdNo());
        applyHolderPo.setIdExpDate(DateUtils.stringToTime(applyHolderRequest.getIdExpDateFormat(), DateUtils.FORMATE18));
        applyOtherInfoBaseService.saveApplyHolderPo(userId, applyHolderPo);
        return ResultObject.success();
    }

    @Override
    public ResultObject<List<ApplyOtherInsuranceResponse>> getApplyOtherInsurancePo(String applyId) {
        ResultObject<List<ApplyOtherInsuranceResponse>> resultObject = new ResultObject<>();
        String insuredName = null;
        List<ApplyInsuredPo> applyInsuredPos = applyInsuredDao.fetchByApplyId(applyId);
        if (AssertUtils.isNotEmpty(applyInsuredPos)) {
            insuredName = applyInsuredPos.get(0).getName();
        }

        List<ApplyOtherInsuranceBo> applyOtherInsurancePos = applyOtherInfoBaseService.queryApplyOtherInsurancePo(applyId);
        if (!AssertUtils.isNotEmpty(applyOtherInsurancePos)) {
            applyOtherInsurancePos.add(new ApplyOtherInsuranceBo());
        }
        for (ApplyOtherInsuranceBo applyOtherInsuranceBo : applyOtherInsurancePos) {
            applyOtherInsuranceBo.setInsuredName(insuredName);
        }
        List<ApplyOtherInsuranceResponse> applyOtherInsuranceResponses = (List<ApplyOtherInsuranceResponse>) this.converterList(applyOtherInsurancePos, new TypeToken<List<ApplyOtherInsuranceResponse>>() {
        }.getType());
        resultObject.setData(applyOtherInsuranceResponses);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveApplyOtherInsurancePo(String userId, String applyId, List<ApplyOtherInsurancePo> applyOtherInsurancePos) {
        if (!AssertUtils.isNotEmpty(applyOtherInsurancePos)) {
            return ResultObject.success();
        }
        String insuredName = null;
        List<ApplyInsuredPo> applyInsuredPos = applyInsuredDao.fetchByApplyId(applyId);
        if (AssertUtils.isNotEmpty(applyInsuredPos)) {
            insuredName = applyInsuredPos.get(0).getName();
        }

        applyOtherInfoBaseService.deleteApplyOtherInsurancePo(applyId);
        int seq = 100;
        for (ApplyOtherInsurancePo applyOtherInsurancePo : applyOtherInsurancePos) {
            applyOtherInsurancePo.setSeq(seq + "");
            applyOtherInsurancePo.setApplyId(applyId);
            applyOtherInsurancePo.setInsuredName(AssertUtils.isNotEmpty(insuredName) ? insuredName : applyOtherInsurancePo.getInsuredName());
            seq++;
        }
        applyOtherInfoBaseService.saveApplyOtherInsurancePo(userId, applyOtherInsurancePos);
        return ResultObject.success();
    }

    @Override
    public ResultObject<List<ApplyStatementPo>> getApplyStatement(String userId, String applyId) {
        ResultObject<List<ApplyStatementPo>> resultObject = new ResultObject<>();
        List<ApplyStatementPo> applyStatementPos = applyOtherInfoBaseService.queryApplyStatementPo(applyId);
        if (!AssertUtils.isNotEmpty(applyStatementPos)) {
            resultObject.setData(applyStatementPos);
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveApplyStatement(String userId, String applyId, List<ApplyStatementRequest> applyStatementRequests) {
        if (!AssertUtils.isNotEmpty(applyStatementRequests)) {
            return ResultObject.success();
        }
        applyOtherInfoBaseService.deleteApplyStatementPo(applyId);
        List<ApplyStatementPo> applyStatementPos = new ArrayList<>();
        applyStatementRequests.forEach(applyStatementRequest -> {
            ApplyStatementPo applyStatementPo = new ApplyStatementPo();
            ClazzUtils.copyPropertiesIgnoreNull(applyStatementRequest, applyStatementPo);
            applyStatementPo.setApplyId(applyId);
            applyStatementPos.add(applyStatementPo);
        });
        applyOtherInfoBaseService.saveApplyStatementPo(userId, applyStatementPos);
        return ResultObject.success();
    }

    /**
     * OCR身份信息识别
     *
     * @param identityOcrRequest 影像附件
     * @param users              用户
     * @return
     */
    @Override
    @Transactional
    public ResultObject<IdentityOcrResponse> getIdentityOcr(IdentityOcrRequest identityOcrRequest, Users users) {
        AssertUtils.isNotEmpty(getLogger(), identityOcrRequest.getApplyId(), APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), identityOcrRequest.getAttachmentId(), APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        // 查询附件
        ResultObject<AttachmentResponse> attachmentObject = attachmentApi.attachmentGet(identityOcrRequest.getAttachmentId());
        AssertUtils.isResultObjectDataNull(getLogger(), attachmentObject);
        // 调第三方ocr识别身份信息
        ResultObject<OcrGeneralResponse> ocrGeneralObject = thirdpartyApi.ocrGeneral(attachmentObject.getData().getUrl());
        AssertUtils.isResultObjectDataNull(getLogger(), ocrGeneralObject);
        String ocrResult = ocrGeneralObject.getData().getOcrResult();
        // 保存识别结果
        IdentityOcrRecordPo identityOcrRecordPo = new IdentityOcrRecordPo();
        identityOcrRecordPo.setApplyId(identityOcrRequest.getApplyId());
        identityOcrRecordPo.setAttachmentId(identityOcrRequest.getAttachmentId());
        identityOcrRecordPo.setOcrResult(ocrResult);
        identityOcrRecordBaseService.save(identityOcrRecordPo, users.getUserId());

        ResultObject<IdentityOcrResponse> resultObject = new ResultObject<>();
        try {
            // 解析身份信息
            IdentityOcrResponse identityOcrResponse = new IdentityOcrResponse();
            JSONObject jsonObject = JSON.parseObject(ocrResult);
            String content = jsonObject.getString("content");
            if (AssertUtils.isNotEmpty(content)) {
                // 通用文字识别高精版
                content = jsonObject.getString("content").replaceAll("\\s*", "");
            } else {
                // 通用文字识别
                JSONArray jsonArray = JSON.parseArray(jsonObject.getString("ret"));
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < jsonArray.size(); i++) {
                    stringBuilder.append(jsonArray.getJSONObject(i).getString("word"));
                }
                content = stringBuilder.toString().replaceAll("\\s*", "");
            }
            if (content.contains("IDKHM")) {
                content = content.substring(content.indexOf("IDKHM"));
                identityOcrResponse = appApplyTransData.parsingId(content);
            } else if (content.contains("PNKH")) {
                content = content.substring(content.lastIndexOf("PNKH"));
                identityOcrResponse = appApplyTransData.parsingPassport(content);
            } else {
                throwsException(getLogger(), ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_GENERATE_OCR_IS_ERROR);
            }
            resultObject.setData(identityOcrResponse);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(getLogger(), resultObject, e, ThirdpartyErrorConfigEnum.THIRDPARTY_BUSINESS_GENERATE_OCR_IS_ERROR);
        }

        return resultObject;
    }

    @Override
    public ResultObject<CheckBmiResponse> checkBmi(CheckBmiRequest checkBmiRequest) {
        ResultObject<CheckBmiResponse> resultObject = new ResultObject<>();
        CheckBmiResponse checkBmiResponse = new CheckBmiResponse();
        AssertUtils.isNotEmpty(getLogger(), checkBmiRequest.getStature(), APPLY_INPUT_APPLICANT_STATURE_IS_NOT_NULL);
        AssertUtils.isNotEmpty(getLogger(), checkBmiRequest.getAvoirdupois(), APPLY_INPUT_APPLICANT_AVOIRDUPOIS_IS_NOT_NULL);
        //投保人身高必须大于0
        if (new BigDecimal(checkBmiRequest.getStature()).compareTo(BigDecimal.ZERO) <= 0 && "PRO880000000000020A".equals(checkBmiRequest.getProductId())) {
            checkBmiResponse.setBmi(new BigDecimal("0"));
            resultObject.setData(checkBmiResponse);
            resultObject.setIenum(APPLY_INPUT_APPLICANT_BMI_IS_NOT_NORMAL);
            return resultObject;
        }
        //投保人体重必须大于0
        if (new BigDecimal(checkBmiRequest.getAvoirdupois()).compareTo(BigDecimal.ZERO) <= 0 && "PRO880000000000020A".equals(checkBmiRequest.getProductId())) {
            checkBmiResponse.setBmi(new BigDecimal("0"));
            resultObject.setData(checkBmiResponse);
            resultObject.setIenum(APPLY_INPUT_APPLICANT_BMI_IS_NOT_NORMAL);
            return resultObject;
        }

        if (AssertUtils.isNotNull(checkBmiRequest.getAvoirdupois()) && AssertUtils.isNotNull(checkBmiRequest.getStature())) {
            BigDecimal powStature = new BigDecimal(checkBmiRequest.getStature()).divide(new BigDecimal("100")).pow(2);
            BigDecimal bmi = new BigDecimal(checkBmiRequest.getAvoirdupois()).divide(powStature, 2, BigDecimal.ROUND_HALF_UP);
            //int value = bmi.intValue();
            checkBmiResponse.setBmi(bmi);
            if ((bmi.compareTo(new BigDecimal(18)) < 0 || bmi.compareTo(new BigDecimal(35)) > 0) && "PRO880000000000020A".equals(checkBmiRequest.getProductId())) {
                resultObject.setIenum(APPLY_INPUT_APPLICANT_BMI_IS_NOT_NORMAL);
            }
        }
        resultObject.setData(checkBmiResponse);
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject generateSignature(String applyId, Users users, String togetherFlag) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyInfoExtBo applyInfoExtBo = appApplyBusinessDao.getApplyInfoExtById(applyId);
            if (AssertUtils.isNotNull(applyInfoExtBo)) {
                if ("YES".equals(togetherFlag)) {
                    ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);
                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignatureDao.delete(applyElectronicSignaturePo);
                    }
                } else {
                    ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);
                    if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                        applyElectronicSignatureDao.delete(applyElectronicSignaturePo);
                    }
                    List<ApplyInsuredPo> applyInsuredPos = applyInsuredBaseDao.listApplyInsuredPo(applyId);
                    if (AssertUtils.isNotNull(applyInsuredPos)) {
                        applyInsuredPos.forEach(applyInsuredPo -> {
                            //查询applyBo
                            ApplyBo applyBo = applyBaseService.queryApply(applyId);

                            if (AssertUtils.isNotNull(applyInsuredPo)) {
                                String relationship = applyInsuredPo.getRelationship();
                                Long birthday = applyInsuredPo.getBirthday();
                                int age = applyDataTransform.getAgeByBirthday(birthday);
                                if (AssertUtils.isNotNull(relationship)) {
                                    applyPlanBaseDao.saveSignature(applyId, null, applyInfoExtBo, relationship, age);
                                }
                                if (AssertUtils.isNotNull(applyBo)) {
                                    if (age < 18) {
                                        //发送电子签名消息给投保人
                                        messageBusinessService.pushApplySignatureMessageApplicant(ApplyTermEnum.MSG_BUSINESS_TYPE.INSURANCE_ESIGNATURE_TO_CUSTOMER.name(),
                                                applyBo, users);
                                    } else if ("ONESELF".equals(relationship)) {
                                        //发送电子签名消息给投保人
                                        messageBusinessService.pushApplySignatureMessageApplicant(ApplyTermEnum.MSG_BUSINESS_TYPE.INSURANCE_ESIGNATURE_TO_CUSTOMER.name(),
                                                applyBo, users);
                                    } else {
                                        //发送电子签名消息给投保人
                                        messageBusinessService.pushApplySignatureMessageApplicant(ApplyTermEnum.MSG_BUSINESS_TYPE.INSURANCE_ESIGNATURE_TO_CUSTOMER.name(),
                                                applyBo, users);
                                        //发送电子签名消息给被保人
                                        messageBusinessService.pushApplySignatureMessageInsured(ApplyTermEnum.MSG_BUSINESS_TYPE.INSURANCE_ESIGNATURE_TO_INSURED.name(),
                                                applyBo, users);
                                    }
                                }
                            }
                        });
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_SIGNATURE_INFO_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<SignatureResponse> querySignature(String applyId, Users users) {
        ResultObject<SignatureResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyElectronicSignaturePo applyElectronicSignaturePo = applyPlanBaseDao.querySignature(applyId);
            if (!AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                resultObject.setData(null);
            }
            if (AssertUtils.isNotNull(applyElectronicSignaturePo)) {
                SignatureResponse signatureResponse = (SignatureResponse) this.converterObject(applyElectronicSignaturePo, SignatureResponse.class);
                signatureResponse.setApplicantVideoAttachmentUrl(AssertUtils.isNullString(applyElectronicSignaturePo.getApplicantVideoAttachmentUrl()) ? null : applyElectronicSignaturePo.getApplicantVideoAttachmentUrl() + "?x-oss-process=video/snapshot,t_0,m_fast,f_png,ar_auto");
                if (AssertUtils.isNotNull(applyElectronicSignaturePo.getInsuredVideoAttachmentUrl())) {
                    signatureResponse.setInsuredVideoAttachmentUrl(AssertUtils.isNullString(applyElectronicSignaturePo.getInsuredVideoAttachmentUrl()) ? null : applyElectronicSignaturePo.getInsuredVideoAttachmentUrl() + "?x-oss-process=video/snapshot,t_0,m_fast,f_png,ar_auto");
                }
                if (AssertUtils.isNotNull(applyElectronicSignaturePo.getTogetherVideoAttachmentUrl())) {
                    signatureResponse.setTogetherVideoAttachmentUrl(AssertUtils.isNullString(applyElectronicSignaturePo.getTogetherVideoAttachmentUrl()) ? null : applyElectronicSignaturePo.getTogetherVideoAttachmentUrl() + "?x-oss-process=video/snapshot,t_0,m_fast,f_png,ar_auto");
                }

                resultObject.setData(signatureResponse);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_SIGNATURE_INFO_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<IsOneselfResponse> queryIsOneself(String applyId) {
        ResultObject<IsOneselfResponse> resultObject = new ResultObject<>();
        try {
            IsOneselfResponse isOneselfResponse = new IsOneselfResponse();
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<ApplyInsuredPo> applyInsuredPos = applyInsuredBaseDao.listApplyInsuredPo(applyId);
            if (AssertUtils.isNotNull(applyInsuredPos)) {
                applyInsuredPos.forEach(applyInsuredPo -> {
                    if (AssertUtils.isNotNull(applyInsuredPo)) {
                        String relationship = applyInsuredPo.getRelationship();
                        Long birthday = applyInsuredPo.getBirthday();
                        int age = applyDataTransform.getAgeByBirthday(birthday);
                        if (age < 18) {
                            isOneselfResponse.setIsOneself("YES");
                            isOneselfResponse.setIsNonage("YES");
                        } else if ("ONESELF".equals(relationship)) {
                            isOneselfResponse.setIsOneself("YES");
                        } else {
                            isOneselfResponse.setIsOneself("NO");
                        }
                    }
                });
            }

            //查询计划书保单号
            ApplyPlanBo applyPlanBo = applyPlanBaseDao.queryApplyPlan(applyId);
            if (AssertUtils.isNotNull(applyPlanBo)) {
                isOneselfResponse.setApplyPlanNo(applyPlanBo.getApplyPlanNo());
            }
            resultObject.setData(isOneselfResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IS_ONESELF_INFO_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject generatePdfAttachment(String applyId, Users users) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            ApplyPlanBo applyPlanBo = applyPlanBaseDao.queryApplyPlan(applyId);
            List<String> languages = Arrays.asList("EN_US", "KM_KH");
            if (AssertUtils.isNotNull(applyPlanBo)) {
                String applyPlanId = applyPlanBo.getApplyPlanId();
                new Thread(() -> {
                    for (String language : languages) {
                        appPlanApi.planPdfGenerate2Image(applyPlanId, language);
                    }
                }).start();
            }

            // 生成附件前，更新状态为生成中
            policyApi.updateAttachmentGenerateStatus(applyId, AttachmentPolicyEnum.APPLY_BOOK.name(), null, AttachmentPdfEnum.GENERATING.name());
            // 生成投保单
            new Thread(() -> {
                ApplyThreadLocalBo.asyncFlag.set(true);// 可以使用消息队列进行异步生成
                for (String language : languages) {
                    generateApplyAfterTransformImage(applyId, language);
                }
                ApplyThreadLocalBo.asyncFlag.set(false);
            }).start();
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PdfAttachmentResponse> queryPdfAttachment(String applyId, Users users) {
        ResultObject<PdfAttachmentResponse> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            PdfAttachmentResponse pdfAttachmentResponse = new PdfAttachmentResponse();
            ResultObject<PolicyAttachmentResponse> policyAttachmentResponseResultObject = policyApi.getAttachmentByType(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_BOOK.name(), users.getLanguage());
            if (AssertUtils.isNotNull(policyAttachmentResponseResultObject)) {
                if (AssertUtils.isNotNull(policyAttachmentResponseResultObject.getData())) {
                             /*AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_AGENT.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }*/
                    String attachmentId = policyAttachmentResponseResultObject.getData().getAttachmentId();
                    pdfAttachmentResponse.setApplyDetailLinkAttachmentId(attachmentId);
                    ResultObject<AttachmentResponse> attachmentResponseResultObject = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(attachmentResponseResultObject)) {
                        AttachmentResponse attachmentResponse = attachmentResponseResultObject.getData();
                        if (AssertUtils.isNotNull(attachmentResponse)) {
                            String url = attachmentResponse.getUrl();
                            pdfAttachmentResponse.setApplyDetailLinkUrl(url);
                        }
                    }
                }

            }

            //List<ApplyAttachmentBo> applyAttachmentBos1 = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.PLAN_BOOK.name());
            ApplyPlanBo applyPlanBo = applyPlanBaseDao.queryApplyPlan(applyId);
            if (AssertUtils.isNotNull(applyPlanBo)) {
                String applyPlanId = applyPlanBo.getApplyPlanId();
                ResultObject<PolicyAttachmentResponse> policyAttachmentResponseResultObject1 = policyApi.getAttachmentByType(applyPlanId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.PLAN_BOOK.name(), users.getLanguage());
                if (AssertUtils.isNotNull(policyAttachmentResponseResultObject1)) {
                    if (AssertUtils.isNotNull(policyAttachmentResponseResultObject1.getData())) {
                             /*AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PLAN_SIGNATURE_AGENT.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }*/
                        String attachmentId = policyAttachmentResponseResultObject1.getData().getAttachmentId();
                        pdfAttachmentResponse.setPlanDetailLinkAttachmentId(attachmentId);
                        ResultObject<AttachmentResponse> attachmentResponseResultObject = attachmentApi.attachmentGet(attachmentId);
                        if (!AssertUtils.isResultObjectDataNull(attachmentResponseResultObject)) {
                            AttachmentResponse attachmentResponse = attachmentResponseResultObject.getData();
                            if (AssertUtils.isNotNull(attachmentResponse)) {
                                String url = attachmentResponse.getUrl();
                                pdfAttachmentResponse.setPlanDetailLinkUrl(url);
                            }
                        }
                    }

                }
            }
            resultObject.setData(pdfAttachmentResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<LoginUserSignatureResponse> loginCustomerSignature(String applyNo, Users users, SmsVerifyCodeCheckRequest smsVerifyCodeCheckRequest) {
        ResultObject<LoginUserSignatureResponse> resultObject = new ResultObject<>();
        LoginUserSignatureResponse loginUserSignatureResponse = new LoginUserSignatureResponse();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyNo, ApplyErrorConfigEnum.APPLY_APPLY_NO_IS_NOT_NULL);

            ApplyPo applyByApplyNo = applyBaseDao.getApplyByApplyNo(applyNo);
            if (AssertUtils.isNotNull(applyByApplyNo)) {
                String applyId = applyByApplyNo.getApplyId();
                if (AssertUtils.isNotNull(applyId)) {
                    loginUserSignatureResponse.setApplyId(applyId);

                    //查询计划书保单号
                    ApplyPlanBo applyPlanBo = applyPlanBaseDao.queryApplyPlan(applyId);
                    if (AssertUtils.isNotNull(applyPlanBo)) {
                        loginUserSignatureResponse.setApplyPlanNo(applyPlanBo.getApplyPlanNo());
                    }

                    //查询applyBo
                    ApplyBo applyBo = applyBaseService.queryApply(applyId);
                    if (AssertUtils.isNotNull(applyBo)) {
                        ResultObject<SignatureResponse> signatureResponseResultObject = querySignature(applyId, users);
                        if (AssertUtils.isNotNull(signatureResponseResultObject.getData())) {
                            SignatureResponse signatureResponse = signatureResponseResultObject.getData();
                            String isOneself = signatureResponse.getIsOneself();
                            String applicantSubmitStatus = signatureResponse.getApplicantSubmitStatus();
                            String insuredSubmitStatus = signatureResponse.getInsuredSubmitStatus();
                            loginUserSignatureResponse.setIsOneself(isOneself);
                            loginUserSignatureResponse.setApplicantSubmitStatus(applicantSubmitStatus);
                            loginUserSignatureResponse.setInsuredSubmitStatus(insuredSubmitStatus);
                        }
                        ApplyApplicantBo applyApplicantBo = applyBo.getApplicant();
                        if (AssertUtils.isNotNull(applyApplicantBo)) {
                            String name = applyApplicantBo.getName();
                            String mobile = applyApplicantBo.getMobile();
                            String mobile2 = applyApplicantBo.getMobile_2();
                            if (AssertUtils.isNotNull(name)) {
                                loginUserSignatureResponse.setApplicantName(name);
                            }
                            if (AssertUtils.isNotNull(mobile)) {
                                loginUserSignatureResponse.setApplicantMobile(mobile);
                            }
                            if (AssertUtils.isNotNull(mobile2)) {
                                loginUserSignatureResponse.setApplicantMobile2(mobile2);
                            }
                        }


                        List<ApplyInsuredBo> listInsured = applyBo.getListInsured();
                        if (AssertUtils.isNotNull(listInsured)) {
                            listInsured.forEach(applyInsuredBo -> {
                                if (AssertUtils.isNotNull(applyInsuredBo)) {
                                    String name = applyInsuredBo.getName();
                                    String mobile = applyInsuredBo.getMobile();
                                    String mobile2 = applyInsuredBo.getMobile_2();
                                    if (AssertUtils.isNotNull(name)) {
                                        loginUserSignatureResponse.setInsuredName(name);
                                    }
                                    if (AssertUtils.isNotNull(mobile)) {
                                        loginUserSignatureResponse.setInsuredMobile(mobile);
                                    }
                                    if (AssertUtils.isNotNull(mobile2)) {
                                        loginUserSignatureResponse.setInsuredMobile2(mobile2);
                                    }
                                }
                            });
                        }
                        resultObject.setData(loginUserSignatureResponse);
                    } else {
                        resultObject.setData(null);
                    }
                }
            } else {
                resultObject.setData(null);
            }

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SIGNATURE_USER_LOGIN_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<Void> getSignaturePrintFile(HttpServletResponse response, String attachmentId, Users users) {
        ResultObject<Void> resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(this.getLogger(), attachmentId, ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        try {
            if (AssertUtils.isNotEmpty(attachmentId)) {
                //下载PDF保单文件
                ResultObject<AttachmentByteResponse> attachmentByteResFcResultObject = attachmentPDFDocumentApi.electronicPolicyDownload(attachmentId);
                this.getLogger().info(attachmentByteResFcResultObject.getMessage());
                if (!AssertUtils.isResultObjectDataNull(attachmentByteResFcResultObject)) {
                    AttachmentByteResponse data = attachmentByteResFcResultObject.getData();
                    // 返回文件流
                    byte[] bytesFile = data.getFileByte();
                    System.out.println(bytesFile.length);
                    response.setHeader("Content-Type", "application/pdf");
                    response.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode("httpclient-tutorial.pdf", "UTF-8"));
                    response.setHeader("Access-Control-Allow-Origin", "*");
                    response.setHeader("Access-Control-Allow-Methods", "POST,GET");
                    response.setHeader("Access-Control-Allow-Credentials", "true");
                    ServletOutputStream outputStream = response.getOutputStream();
                    outputStream.write(bytesFile);
                    return resultObject;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_PDF_DOWNLOAD_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject queryRegenerateApplyPdfAttachment(String applyId, String applyAttachmentId, String language) {
        ResultObject resultObject = new ResultObject<>();
        AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentId, ApplyErrorConfigEnum.APPLY_ATTACHMENT_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), language, ApplyErrorConfigEnum.APPLY_PARAMETER_LANGUAGE_IS_NOT_NULL);

        ResultObject<PolicyAttachmentBo> policyAttachmentBoResultObject = policyApi.getAttachmentGenerateStatus(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_BOOK.name(), language);
        if (AssertUtils.isNotNull(policyAttachmentBoResultObject)) {
            PolicyAttachmentBo policyAttachmentBo = policyAttachmentBoResultObject.getData();
            if (AssertUtils.isNotNull(policyAttachmentBo)) {
                String attachmentId = policyAttachmentBo.getAttachmentId();
                String generateStatus = policyAttachmentBo.getGenerateStatus();
                // 只有附件生成状态是 已生成 才响应 attachmentId
                if (AttachmentPdfEnum.FINISH.name().equals(generateStatus)) {
                    resultObject.setData(attachmentId);
                }
            }
        }
        return resultObject;
    }

    /**
     * 为空时创建两个空对象方便app处理
     *
     * @return AttachmentRequests
     */
    private List<AttachmentRequest> newBlankAttachment() {
        List<AttachmentRequest> attachmentRequests = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            AttachmentRequest attachmentRequest = new AttachmentRequest();
            attachmentRequest.setAttachmentId(null);
            attachmentRequest.setAttachmentSeq(null);
            attachmentRequest.setUrl(null);
            attachmentRequests.add(attachmentRequest);
        }
        return attachmentRequests;
    }

    /**
     * 转换投保单计划书数据
     *
     * @param agentSimpleBaseRespFcs
     * @param applyPlanBos
     * @return
     */
    private List<ApplyPlanListResponse> getApplyPlanListResponses(List<AgentSimpleBaseResponse> agentSimpleBaseRespFcs, List<ApplyPlanBo> applyPlanBos) {
        List<ApplyPlanListResponse> applyPlanListResponses = (List<ApplyPlanListResponse>) this.converterList(
                applyPlanBos, new TypeToken<List<ApplyPlanListResponse>>() {
                }.getType()
        );
        applyPlanListResponses.forEach(applyPlanListResponse -> {
            // 代理人姓名
            if (AssertUtils.isNotEmpty(applyPlanListResponse.getAgentId())) {
                agentSimpleBaseRespFcs.stream()
                        .filter(agentSimpleBaseRespFc -> agentSimpleBaseRespFc.getAgentId().equals(applyPlanListResponse.getAgentId()))
                        .findFirst().ifPresent(agentSimpleBaseRespFc -> applyPlanListResponse.setAgentName(agentSimpleBaseRespFc.getAgentName()));
            }
            // 生成日期
            if (AssertUtils.isNotNull(applyPlanListResponse.getCreatedDate())) {
                applyPlanListResponse.setCreatedDateStr(DateUtils.timeStrToString(applyPlanListResponse.getCreatedDate(), DateUtils.FORMATE5));
            }
            // 投保状态
            if (AssertUtils.isNotEmpty(applyPlanListResponse.getApplyStatus())) {
                if (ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name().equals(applyPlanListResponse.getApplyStatus())) {
                    applyPlanListResponse.setApplyStatus(ApplyTermEnum.PLAN_APPLY_STATUS.UNDERWRITING.desc());
                } else {
                    applyPlanListResponse.setApplyStatus(ApplyTermEnum.PLAN_APPLY_STATUS.UNCOVERED.desc());
                }
            }
        });
        return applyPlanListResponses;
    }
}
