package com.gclife.apply.service.business.group.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPaymentAttachmentPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.dao.ApplyPaymentInstrumentExtDao;
import com.gclife.apply.dao.PaymentExtDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.ModelConstantEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.request.AttachmentDetailRequest;
import com.gclife.apply.model.request.PaymentListRequest;
import com.gclife.apply.model.request.group.ApplyPaymentNotifyRequest;
import com.gclife.apply.model.response.group.*;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.business.group.GroupPaymentService;
import com.gclife.apply.service.business.impl.ApplyAbandonedBusinessServiceImpl;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.parameter.group.LanguageUtils;
import com.gclife.apply.validate.parameter.transform.ApplyToPolicyTransData;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.client.api.ClientCustomerCenterApi;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.payment.api.PaymentApi;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.model.config.PaymentTermEnum;
import com.gclife.payment.model.request.PaymentUpdateRequest;
import com.gclife.payment.model.response.PaymentItemResponse;
import com.gclife.payment.model.response.PaymentResponse;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.report.api.ReportBaseApi;
import com.gclife.report.api.model.request.ReportApplySuspenseRequest;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.config.ProcessParam;
import com.gclife.workflow.model.request.StartProcessRequest;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyErrorConfigEnum.*;
import static com.gclife.apply.model.config.ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT;
import static com.gclife.apply.model.config.ApplyTermEnum.CHARGE_TYPE.NORMAL_PAYMENT;
import static com.gclife.apply.model.config.ApplyTermEnum.PAYMENT_TYPE.PAYMENT;
import static com.gclife.payment.model.config.PaymentTermEnum.PAYMENT_STATUS.PAYMENT_FINISHED;

/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
@Service
@Slf4j
public class GroupPaymentServiceImpl extends BaseBusinessServiceImpl implements GroupPaymentService {
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private PaymentBaseApi paymentBaseApi;
    @Autowired
    private PaymentApi paymentApi;
    @Autowired
    private ApplyPaymentBaseService applyPaymentBaseService;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private ReportBaseApi reportBaseApi;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private PaymentExtDao paymentExtDao;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private ApplyToPolicyTransData applyToPolicyTransData;
    @Autowired
    private ApplyAttachmentBaseService applyAttachmentBaseService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private ApplyAbandonedBusinessServiceImpl applyAbandonedBusinessService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyPaymentInstrumentExtDao applyPaymentInstrumentExtDao;
    @Autowired
    private MessageBaseBusinessService messageBaseBusinessService;
    @Autowired
    private ClientCustomerCenterApi clientCustomerCenterApi;

    @Override
    public ResultObject<GroupPaymentResponse> getPayment(String applyId, String paymentId) {
        ResultObject resultObject = new ResultObject();
        GroupPaymentResponse groupPaymentResponse = new GroupPaymentResponse();
        try {
            //基本信息
            PaymentBaseResponse paymentBase = new PaymentBaseResponse();
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyBo, APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            paymentBase.setApplyNo(applyBo.getApplyNo());
            paymentBase.setCompanyName(applyBo.getApplicant().getCompanyName());
            // 调用平台微服务查询机构信息
            BranchResponse branchResponse = platformBranchApi.branchGet(applyBo.getSalesBranchId()).getData();
            if (AssertUtils.isNotNull(branchResponse)) {
                paymentBase.setSalesBranchName(branchResponse.getBranchName());
            }
            if (AssertUtils.isNotEmpty(applyBo.getListCoverage()) && AssertUtils.isNotNull(applyBo.getListCoverage().get(0))) {
                paymentBase.setProductName(applyBo.getListCoverage().get(0).getProductName());
            }
            ApplyAgentBo applyAgentBo = applyBo.getApplyAgentBo();
            if (AssertUtils.isNotNull(applyAgentBo) && AssertUtils.isNotEmpty(applyAgentBo.getAgentId())) {
                AgentResponse applyAgentRespFc = agentApi.agentByIdGet(applyAgentBo.getAgentId()).getData();
                paymentBase.setAgentCode(applyAgentRespFc.getAgentCode());
                paymentBase.setAgentName(applyAgentRespFc.getAgentName());
                paymentBase.setAgentMobile(applyAgentRespFc.getMobile());
            }
            groupPaymentResponse.setPaymentBase(paymentBase);
            //缴费记录
            if (AssertUtils.isNotEmpty(paymentId)) {
                List<PaymentItemResponse> itemRespFcList = paymentApi.listPaymentItemByPaymentId(paymentId).getData();
                if (AssertUtils.isNotEmpty(itemRespFcList)) {
                    List<PaymentItemResponse> paymentItemResponseList = (List<PaymentItemResponse>) this.converterList(itemRespFcList, new TypeToken<List<PaymentItemResponse>>() {
                    }.getType());
                    groupPaymentResponse.setListPaymentItem(paymentItemResponseList);
                }
            }
            //支付凭证影像
            List<ApplyPaymentAttachmentPo> applyPaymentAttachmentPos = applyAttachmentBaseService.listApplyPaymentAttachment(applyId);
            if (AssertUtils.isNotEmpty(applyPaymentAttachmentPos)) {
                List<PaymentAttachmentResponse> paymentAttachmentResponseList = (List<PaymentAttachmentResponse>) this.converterList(applyPaymentAttachmentPos, new TypeToken<List<PaymentAttachmentResponse>>() {
                }.getType());
                groupPaymentResponse.setListPaymentAttachment(paymentAttachmentResponseList);
            }
            resultObject.setData(groupPaymentResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(APPLY_QUERY_APPLY_PAYMENT_INFO_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public void handPaymentNotify(String userId, ApplyPaymentNotifyRequest applyPaymentNotifyRequest) {
        if (!AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getBusinessId()) || !AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getStatus())
                || !AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getPaymentId())) {
            return;
        }
        boolean errorFlag = false;
        try {
            log.info("handPaymentNotify start time :" + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
            log.info("handPaymentNotify start :" + JSONObject.toJSONString(applyPaymentNotifyRequest));
            ApplyBo applyBo = applyBaseService.queryApply(applyPaymentNotifyRequest.getBusinessId());
            ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(applyPaymentNotifyRequest.getBusinessId());
            if (!AssertUtils.isNotNull(applyBo) || !AssertUtils.isNotNull(applyPremiumBo)) {
                return;
            }
            ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyPaymentNotifyRequest.getBusinessId(), applyPremiumBo.getPaymentType());
            if (!AssertUtils.isNotNull(applyPaymentTransactionBo) || !AssertUtils.isNotEmpty(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos())) {
                return;
            }
            //保存收款附件
            if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getListAttachment())) {
                applyAttachmentBaseService.deleteApplyPaymentAttachment(applyPaymentNotifyRequest.getBusinessId(), ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_CERTIFY.name());
                applyPaymentNotifyRequest.getListAttachment().forEach(attachmentId -> {
                    ApplyPaymentAttachmentPo applyPaymentAttachmentPo = new ApplyPaymentAttachmentPo();
                    applyPaymentAttachmentPo.setApplyId(applyPaymentNotifyRequest.getBusinessId());
                    applyPaymentAttachmentPo.setAttachmentId(attachmentId);
                    applyPaymentAttachmentPo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_CERTIFY.name());
                    applyPaymentAttachmentPo.setPaymentTransactionId(applyPaymentTransactionBo.getPaymentTransactionId());
                    applyPaymentAttachmentPo.setPremiumId(applyPremiumBo.getPremiumId());
                    applyAttachmentBaseService.saveApplyPaymentAttachment(userId, applyPaymentAttachmentPo);
                });
            }
            //保存支付事务总表
            applyPaymentTransactionBo.setArrivalDate(DateUtils.getCurrentTime());
            if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getAmount())) {
                if (applyPaymentTransactionBo.getPaymentAmount().compareTo(new BigDecimal(applyPaymentNotifyRequest.getAmount())) == 0) {
                    applyPremiumBo.setMatchResult(ApplyTermEnum.YES_NO.YES.name());
                    applyPaymentTransactionBo.setMatchResult(ApplyTermEnum.YES_NO.YES.name());
                } else {
                    applyPremiumBo.setMatchResult(ApplyTermEnum.YES_NO.NO.name());
                    applyPaymentTransactionBo.setMatchResult(ApplyTermEnum.YES_NO.NO.name());
                }
            }
            applyPaymentTransactionBo.setPaymentStatus(applyPaymentNotifyRequest.getStatus());
            if (AssertUtils.isNotNull(applyPaymentNotifyRequest.getActualPayDate())) {
                applyPaymentTransactionBo.setActualPayDate(Long.parseLong(applyPaymentNotifyRequest.getActualPayDate()));
            }
            applyPaymentTransactionBaseService.saveApplyPaymentTransaction(userId, applyPaymentTransactionBo);
            //保存支付事务详细表
            applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                applyPaymentTransactionItemBo.setPaymentStatus(applyPaymentNotifyRequest.getStatus());
                if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getPaymentMethodCode())) {
                    applyPaymentTransactionItemBo.setPaymentMethodCode(applyPaymentNotifyRequest.getPaymentMethodCode());
                }
            });
            applyPaymentTransactionBaseService.saveApplyPaymentTransactionItem(userId, applyPaymentTransactionBo.getApplyPaymentTransactionItemBos());
            //处理提前缴费的回调
            if (ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name().equals(applyPaymentTransactionBo.getPaymentType()) && !applyPaymentNotifyRequest.isApproveFlag()) {
//                applyBo.setInitialPaymentMode(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());
//                applyBo.setPaymentMode(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());
//                applyBaseService.saveApply(userId, applyBo);

                if (PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentNotifyRequest.getStatus())) {
                    log.info("支付成功发生暂保短信");
                    AppRequestHeads appRequestHeads = new AppRequestHeads();
                    appRequestHeads.setDeviceChannel("gclife_agent_app");

                    // 修改投保单状态
                    applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_TEMPORARY_COVER_EFFECTIVE.name());
                    applyBaseService.saveApply(userId, applyBo);

                    /*====================================================================*/
                    //完成预交保费后的暂保通知
                    // 发送消息给微信/钉钉
                    messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.NOTICE_AFTER_COMPLETION_OF_PREPAYMENT_OF_PREMIUM.name(), applyBo);

                    // 发消息给业务员
                    messageBusinessService.pushApplyMessageSingle(
                            ApplyTermEnum.MSG_BUSINESS_TYPE.NOTICE_AFTER_COMPLETION_OF_PREPAYMENT_OF_PREMIUM.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());

                    // 发消息给客户
                    messageBusinessService.pushApplyPreStoreMessageCustomer(ApplyTermEnum.MSG_BUSINESS_TYPE.NOTICE_AFTER_COMPLETION_OF_PREPAYMENT_OF_PREMIUM_CUSTOMER.name(), applyBo, appRequestHeads);
                }
                return;
            }

            if (AssertUtils.isNotNull(applyPaymentNotifyRequest.getActualPayDate())) {
                applyBo.setEffectiveDate(Long.parseLong(applyPaymentNotifyRequest.getActualPayDate()));
                applyBo.setApproveDate(Long.parseLong(applyPaymentNotifyRequest.getActualPayDate()));
            }

            //保存支付结果到保费表
            applyPremiumBo.setPremiumStatus(applyPaymentNotifyRequest.getStatus());
            if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentNotifyRequest.getStatus())) {
                applyPremiumBo.setArrivalDate(DateUtils.getCurrentTime());
            }
            applyPremiumBo.setRemark(applyPaymentNotifyRequest.getRemark());
            if (AssertUtils.isNotNull(applyPaymentNotifyRequest.getActualPayDate())) {
                applyPremiumBo.setReceivableDate(Long.parseLong(applyPaymentNotifyRequest.getActualPayDate()));
            }
            applyPremiumBaseService.saveApplyPremium(userId, applyPremiumBo);
            applyBo.setInitialPaymentMode(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());

            //针对17号产品的暂予承保处理
            Optional<ApplyCoverageBo> first = applyBo.getListInsuredCoverage().stream().filter(applyCoverageBo ->
                    applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).distinct().findFirst();
            if (first.isPresent() && ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(first.get().getProductId())
                    && PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentNotifyRequest.getStatus())
                    && ApplyTermEnum.YES_NO.YES.name().equals(applyBo.getPreUnderwritingFlag())
            ) {
                //未到暂予承保开始日期就回调，直接return，定时任务根据支付状态处理承保
                if (AssertUtils.isNotNull(applyBo.getPreUnderwritingStartDate()) && applyBo.getPreUnderwritingStartDate() > DateUtils.getCurrentTime()) {
                    return;
                }
                // 修改投保单状态
                applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
                applyBaseService.saveApply(userId, applyBo);
                //修改保单状态
                ResultObject resultObject = policyApi.updatePrePolicyStatus(applyBo.getApplyId(), ApplyTermEnum.YES_NO.YES.name());
                AssertUtils.isResultObjectError(this.getLogger(), resultObject);

                //工作流调用
                log.info("handPaymentNotify start workflow " + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
                ResultObject resultObject1 = workFlowApi.paymentNotify(applyPaymentNotifyRequest.getBusinessId(), ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
                log.info("handPaymentNotify workflow end time " + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
                AssertUtils.isResultObjectError(this.getLogger(), resultObject1);

                //发送保单承保消息
                try {
                    //设置代理人信息
                    AgentResponse agentResponse = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
                    applyBo.getApplyAgentBo().setAgentCode(agentResponse.getAgentCode());
                    applyBo.getApplyAgentBo().setAgentName(agentResponse.getAgentName());

                    this.getLogger().info("团单承保提醒业务员短信");
                    messageBaseBusinessService.pushApplyMessageAgent(ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_APPLY_APPROVE_REMINDER_AGENT.name(), applyBo, agentResponse.getUserId());

                    this.getLogger().info("团单承保提醒客户");
                    messageBaseBusinessService.pushApplyMessageCustomerSMS(ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_APPLY_APPROVE_REMINDER_CUSTOMER.name(), applyBo, applyBo.getApplicant().getDelegateMobile());

                    this.getLogger().info("团单承保出单成功发消息给后台操作人员及业务员");
                    messageBaseBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_CP.name(), applyBo);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                ApplyApplicantBo applyApplicant = applyBo.getApplicant();
                if (AssertUtils.isNotNull(applyApplicant)) {
                    String customerId = applyApplicant.getCustomerId();
                    String mobile = applyApplicant.getMobile();
                    // 同步投保人手机号到客户
                    clientCustomerCenterApi.syncApplicationMobileToCustomer(customerId, mobile);
                }
                return;
            }

            //保存投保单状态,支付状态成功调用承保
            if (PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentNotifyRequest.getStatus())) {
                applyBo.setPaymentMode(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().get(0).getPaymentMethodCode());
                applyBaseService.saveApply(userId, applyBo);

                //投保单转保单
                //推动工作流,支付成功调用工作流
                String workflowType = null;
                if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name().equals(applyBo.getApplyType())) {
                    workflowType = ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name();
                    //团险投保单转保单
                    applyPaymentBaseService.transferApplyToPolicy(applyPaymentNotifyRequest.getBusinessId(), userId, applyPaymentNotifyRequest.getActualPayDate());
                } else if (ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyBo.getApplyType())) {
                    workflowType = ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT.name();
                    //个险投保单转保单
                    applyToPolicyTransData.transferApplyToPolicy(applyPaymentNotifyRequest.getBusinessId(), applyPaymentNotifyRequest.getActualPayDate());
                }
                //判断是否通过个险智能核保,是:开启智能核保线路的工作流,否:原有流程回调
                if (applyDataTransform.doWhetherThroughAutoUW(applyBo.getApplyId(), applyBo.getApplyType())) {
                    //开启工作流
                    StartProcessRequest startProcessRequest = new StartProcessRequest();
                    startProcessRequest.setBusinessNo(applyBo.getApplyId());
                    startProcessRequest.setCreateUserId(userId);
                    startProcessRequest.setWorkflowType(ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT.name());
                    ProcessParam processParam = new ProcessParam();
                    processParam.setBranchId(applyBo.getSalesBranchId());
                    processParam.setBranchEnabledFlag(ModelConstantEnum.BRANCH_ENABLED_FLAG.BRANCH_ENABLED.name());
                    processParam.setSourceType(ModelConstantEnum.WORKFLOW_STATUS.SMART_UNDERWRITE_ENTRY.name());
                    startProcessRequest.setData(processParam);
                    log.info("正在开启智能核保工作流流程:{}", JackSonUtils.toJson(startProcessRequest));
                    ResultObject startProcess = workFlowApi.startProcess(startProcessRequest);
                    AssertUtils.isResultObjectError(log, startProcess);
                } else {
                    log.info("handPaymentNotify start workflow " + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
                    ResultObject resultObject = workFlowApi.paymentNotify(applyPaymentNotifyRequest.getBusinessId(), workflowType);
                    log.info("handPaymentNotify workflow end time " + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
                    AssertUtils.isResultObjectError(this.getLogger(), resultObject);
                }
                // 6.8.5相同客户的网销投保单只能支付成功一个，作废其他
                if (ApplyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(applyBo.getChannelTypeCode())) {
                    applyDataTransform.doRepeatOnlineApply(applyBo.getApplyId(), applyBo.getApplicant().getCustomerId());
                }
            } else if (applyPaymentNotifyRequest.getStatus().equals(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_PASS.name())
                    || applyPaymentNotifyRequest.getStatus().equals(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_NOPASS.name())) {
                //发送审核通过或者不通过的消息
                try {
                    if (applyPaymentNotifyRequest.getStatus().equals(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_PASS.name())) {
                        //审核通过
                        messageBusinessService.pushApplyMessageSingle(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_PAY_AUDIT_PASS.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());
                    } else if (applyPaymentNotifyRequest.getStatus().equals(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_AUDIT_NOPASS.name())) {
                        //审核不通过
                        messageBusinessService.pushApplyMessageSingle(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_PAY_AUDIT_NOPASS.name(), applyBo, applyBo.getApplyAgentBo().getAgentId());
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (applyPaymentNotifyRequest.getStatus().equals(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FAILED.name())) {
                applyBaseService.saveApply(userId, applyBo);
            } else if (applyPaymentNotifyRequest.getStatus().equals(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_INVALID.name())) {
                //支付作废超时处理 作废
                applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_ABANDONED.name());
                applyBo.setInvalidDate(DateUtils.getCurrentTime());
                applyBaseService.saveApply(userId, applyBo);
                //投保单作废时提醒
                applyAbandonedBusinessService.sendAbandonedMessage(applyBo.getApplyId());
            }
            ApplyApplicantBo applyApplicant = applyBo.getApplicant();
            if (AssertUtils.isNotNull(applyApplicant)) {
                String customerId = applyApplicant.getCustomerId();
                String mobile = applyApplicant.getMobile();
                // 同步投保人手机号到客户
                clientCustomerCenterApi.syncApplicationMobileToCustomer(customerId, mobile);
            }
            log.info("handPaymentNotify end time " + DateUtils.timeStrToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            e.printStackTrace();
            errorFlag = true;
            this.getLogger().info("[大中华人寿-" + DateUtils.dateToString(new Date()) + "]-支付回调error:" + JSON.toJSONString(e.getMessage()));
            throwsTransactionalException(this.getLogger(), e, ApplyErrorConfigEnum.APPLY_PAYMENT_HAND_PAYMENT_NOTIFY_ERROR);
        } finally {
            if (errorFlag) {
                TransactionStatus finallyTransactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
                try {
                    //承保失败
                    if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getBusinessId())) {
                        //查询投保单
                        ApplyBo applyBo = applyBaseService.queryApply(applyPaymentNotifyRequest.getBusinessId());
                        if (AssertUtils.isNotNull(applyBo)) {
                            //发送承保失败消息
                            messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_FAIL.name(), applyBo);
                            applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_FAILED.name());
                            applyBaseService.saveApply(userId, applyBo);
                        }
                        // POLICY回滚
                        policyApi.applyToPolicyRollback(applyPaymentNotifyRequest.getBusinessId());
                    }
                    //提交事物
                    platformTransactionManager.commit(finallyTransactionStatus);
                } catch (Exception e) {
                    platformTransactionManager.rollback(finallyTransactionStatus);
                }
            }
        }
    }

    /**
     * 处理支付回调
     *
     * @param userId
     * @param applyPaymentNotifyRequest 请求类
     */
    @Override
    public void refundPaymentNotify(String userId, ApplyPaymentNotifyRequest applyPaymentNotifyRequest) {
        if (!AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getBusinessId()) || !AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getStatus())
                || !AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getReceiptId())) {
            return;
        }
        try {
            ApplyPo applyPo = applyBaseService.queryApplyPo(applyPaymentNotifyRequest.getBusinessId());
            ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(applyPaymentNotifyRequest.getBusinessId());
            if (!AssertUtils.isNotNull(applyPo) || !AssertUtils.isNotNull(applyPremiumBo)) {
                return;
            }
            ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyPaymentNotifyRequest.getBusinessId(), null, ApplyTermEnum.PAYMENT_TYPE.SUSPENSE_PREMIUM_REFUND.name());
            if (!AssertUtils.isNotNull(applyPaymentTransactionBo) || !AssertUtils.isNotEmpty(applyPaymentTransactionBo.getApplyPaymentTransactionItemBos())) {
                return;
            }
            //保存收款附件
            if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getListAttachment())) {
                applyAttachmentBaseService.deleteApplyPaymentAttachment(applyPaymentNotifyRequest.getBusinessId(), ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_INSTRUMENT.name());
                applyPaymentNotifyRequest.getListAttachment().forEach(attachmentId -> {
                    ApplyPaymentAttachmentPo applyPaymentAttachmentPo = new ApplyPaymentAttachmentPo();
                    applyPaymentAttachmentPo.setApplyId(applyPaymentNotifyRequest.getBusinessId());
                    applyPaymentAttachmentPo.setAttachmentId(attachmentId);
                    applyPaymentAttachmentPo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.RECEIPT_INSTRUMENT.name());
                    applyPaymentAttachmentPo.setPaymentTransactionId(applyPaymentTransactionBo.getPaymentTransactionId());
                    applyPaymentAttachmentPo.setPremiumId(applyPremiumBo.getPremiumId());
                    applyAttachmentBaseService.saveApplyPaymentAttachment(userId, applyPaymentAttachmentPo);
                });
            }
            //保存支付事务总表
            applyPaymentTransactionBo.setArrivalDate(AssertUtils.isNotNull(applyPaymentNotifyRequest.getActualPayDate()) ? Long.parseLong(applyPaymentNotifyRequest.getActualPayDate()) : DateUtils.getCurrentTime());
            if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getAmount())) {
                if (applyPaymentTransactionBo.getPaymentAmount().compareTo(new BigDecimal(applyPaymentNotifyRequest.getAmount())) == 0) {
                    applyPremiumBo.setMatchResult(ApplyTermEnum.YES_NO.YES.name());
                    applyPaymentTransactionBo.setMatchResult(ApplyTermEnum.YES_NO.YES.name());
                } else {
                    applyPremiumBo.setMatchResult(ApplyTermEnum.YES_NO.NO.name());
                    applyPaymentTransactionBo.setMatchResult(ApplyTermEnum.YES_NO.NO.name());
                }
            }
            applyPaymentTransactionBo.setPaymentStatus(applyPaymentNotifyRequest.getStatus());
            applyPaymentTransactionBaseService.saveApplyPaymentTransaction(userId, applyPaymentTransactionBo);
            //保存支付事务详细表
            applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                applyPaymentTransactionItemBo.setPaymentStatus(applyPaymentNotifyRequest.getStatus());
                if (AssertUtils.isNotEmpty(applyPaymentNotifyRequest.getPaymentMethodCode())) {
                    applyPaymentTransactionItemBo.setPaymentMethodCode(applyPaymentNotifyRequest.getPaymentMethodCode());
                }
            });
            applyPaymentTransactionBaseService.saveApplyPaymentTransactionItem(userId, applyPaymentTransactionBo.getApplyPaymentTransactionItemBos());

            //保存一遍，设置更新时间，方便报表同步数据
            applyBaseService.saveApply(userId, applyPo);

            //主动设置暂收报表的退回时间
            if (PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentNotifyRequest.getStatus())) {
                ReportApplySuspenseRequest applySuspenseRequest = new ReportApplySuspenseRequest();
                applySuspenseRequest.setApplyId(applyPo.getApplyId());
                applySuspenseRequest.setApplyStatus(applyPo.getApplyStatus());
                applySuspenseRequest.setSuspensePremiumRefundDate(applyPaymentTransactionBo.getArrivalDate());
                applySuspenseRequest.setSuspensePremiumRefund(applyPaymentTransactionBo.getPaymentAmount());
                applySuspenseRequest.setSuspensePremiumRefundStatus(applyPaymentTransactionBo.getPaymentStatus());
                ResultObject resultObject = reportBaseApi.suspenseSpecialTreatment(applySuspenseRequest);
                log.info("主动设置暂收报表的退回时间:{}", JackSonUtils.toJson(resultObject));
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().info("[大中华人寿-" + DateUtils.dateToString(new Date()) + "]-支付回调error:" + JSON.toJSONString(e.getMessage()));
            throwsTransactionalException(this.getLogger(), e, ApplyErrorConfigEnum.APPLY_PAYMENT_HAND_PAYMENT_NOTIFY_ERROR);
        }
    }

    @Override
    public ResultObject<GroupPaymentResponse> savePayment(Users currentLoginUsers, AppRequestHeads appRequestHandler, PaymentUpdateRequest paymentResultRequest) {
//    public ResultObject<GroupPaymentResponse> savePayment(Users currentLoginUsers, PaymentUpdateRequest paymentResultRequest) {
        ResultObject resultObject = new ResultObject();
        try {
            paymentResultRequest.setDeviceChannelId(appRequestHandler.getDeviceChannel());
//            paymentResultRequest.setDeviceChannelId(currentLoginUsers.getDeviceChannelId());
            paymentResultRequest.setUserId(currentLoginUsers.getUserId());
            ResultObject<PaymentResponse> paymentRespFcResultObject = paymentBaseApi.successPayment(paymentResultRequest);
            AssertUtils.isResultObjectError(this.getLogger(), paymentRespFcResultObject);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(APPLY_PAYMENT_CONFIRM_PAYMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<PaymentApplyDetailResponse> paymentDetail(Users currentLoginUsers, String applyId, String paymentId) {
        ResultObject<PaymentApplyDetailResponse> resultObject = new ResultObject<>();
        try {
            //基本信息
            PaymentApplyDetailResponse paymentApplyDetailResponse = new PaymentApplyDetailResponse();
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            ApplyApplicantBo applicant = applyBo.getApplicant();
            paymentApplyDetailResponse.setApplyNo(applyBo.getApplyNo());
            if (AssertUtils.isNotNull(applicant)) {
                paymentApplyDetailResponse.setCompanyName(applicant.getCompanyName());
            }
            List<ApplyCoverageBo> listCoverage = applyBo.getListCoverage();
            if (AssertUtils.isNotEmpty(listCoverage)) {
                ApplyCoverageBo applyCoverageBo = listCoverage.stream().filter(coverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag())).findFirst().get();
                if (AssertUtils.isNotNull(applyCoverageBo)) {
                    paymentApplyDetailResponse.setProductName(applyCoverageBo.getProductName());
                }
            }

            ApplyAgentBo applyAgentBo = applyBo.getApplyAgentBo();
            if (AssertUtils.isNotNull(applyAgentBo)) {
                AgentResponse agentRespFc = agentApi.agentByIdGet(applyAgentBo.getAgentId()).getData();
                paymentApplyDetailResponse.setAgentCode(agentRespFc.getAgentCode());
                paymentApplyDetailResponse.setAgentName(agentRespFc.getAgentName());
                paymentApplyDetailResponse.setMobile(agentRespFc.getMobile());
                paymentApplyDetailResponse.setBranchName(agentRespFc.getBranchName());
            }
            //缴费记录
            List<PaymentItemResponse> itemRespFcList = paymentApi.listPaymentItemByPaymentId(paymentId).getData();
            if (AssertUtils.isNotEmpty(itemRespFcList)) {
                List<PaymentItemResponse> paymentItemResponseList = (List<PaymentItemResponse>) this.converterList(itemRespFcList, new TypeToken<List<PaymentItemResponse>>() {
                }.getType());
                paymentApplyDetailResponse.setListPaymentItem(paymentItemResponseList);
            }
            //支付凭证
            List<ApplyPaymentAttachmentPo> applyPaymentAttachmentPos = applyAttachmentBaseService.listApplyPaymentAttachment(applyId, APPLY_PAYMENT_INSTRUMENT.name());
            if (AssertUtils.isNotEmpty(applyPaymentAttachmentPos)) {
                List<PaymentAttachmentResponse> paymentAttachmentList = (List<PaymentAttachmentResponse>) this.converterList(applyPaymentAttachmentPos, new TypeToken<List<PaymentAttachmentResponse>>() {
                }.getType());
                paymentApplyDetailResponse.setListPaymentAttachment(paymentAttachmentList);
            }
            resultObject.setData(paymentApplyDetailResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<GroupPaymentListResponse>> getGroupApplyPayments(Users currentLoginUsers, PaymentListRequest paymentListRequest) {
        ResultObject<BasePageResponse<GroupPaymentListResponse>> resultObject = new ResultObject<>();
        try {
            ResultObject<List<BranchResponse>> listResultObject = platformBranchApi.userManagerLeafBranchs();
            AssertUtils.isResultObjectListDataNull(this.getLogger(), listResultObject);
            List<BranchResponse> branchResponses = listResultObject.getData();
            List<String> saleBranchIds = branchResponses.stream().map(BranchResponse::getBranchId).collect(Collectors.toList());
            if (!AssertUtils.isNotEmpty(saleBranchIds)) {
                return resultObject;
            }
            List<PaymentListBo> paymentListBos = paymentExtDao.loadGroupPaymentList(saleBranchIds, paymentListRequest, ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name());
            if (!AssertUtils.isNotEmpty(paymentListBos)) {
                return resultObject;
            }
            List<String> agentCodes = new ArrayList<>();
            List<String> allBranchIds = new ArrayList<>();
            paymentListBos.forEach(applyUnderWriteListBo -> {
                allBranchIds.add(applyUnderWriteListBo.getManagerBranchId());
                allBranchIds.add(applyUnderWriteListBo.getSalesBranchId());
                agentCodes.add(applyUnderWriteListBo.getAgentCode());
            });

            List<BranchResponse> allBranchResponses = platformBranchApi.branchsPost(allBranchIds).getData();

            AgentApplyQueryRequest applyAgentReqFc = new AgentApplyQueryRequest();
            applyAgentReqFc.setListAgentCode(agentCodes);
            List<AgentResponse> agentRespFcs = agentApi.agentsGet(applyAgentReqFc).getData();
            AssertUtils.isNotEmpty(this.getLogger(), agentRespFcs, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

            List<GroupPaymentListResponse> groupPaymentListResponses = (List<GroupPaymentListResponse>) this.converterList(paymentListBos, new TypeToken<List<GroupPaymentListResponse>>() {
            }.getType());
            groupPaymentListResponses.forEach(groupPaymentListResponse -> {
                groupPaymentListResponse.setManagerBranchName(LanguageUtils.getBranchName(allBranchResponses, groupPaymentListResponse.getManagerBranchId()));
                groupPaymentListResponse.setSalesBranchName(LanguageUtils.getBranchName(allBranchResponses, groupPaymentListResponse.getSalesBranchId()));

                agentRespFcs.stream().filter(applyAgentRespFc -> groupPaymentListResponse.getAgentCode().equals(applyAgentRespFc.getAgentCode())).findFirst().ifPresent((value) -> {
                    groupPaymentListResponse.setAgentName(value.getAgentName());
                    groupPaymentListResponse.setAgentCode(value.getAgentCode());
                });
            });
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(paymentListBos) ? paymentListBos.get(0).getTotalLine() : null;

            BasePageResponse<GroupPaymentListResponse> basePageResponse = BasePageResponse.getData(paymentListRequest.getCurrentPage(), paymentListRequest.getPageSize(), totalLine, groupPaymentListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_PAYMENT_INFO_ERROR);
            }
        }
        return resultObject;
    }


    /**
     * 保存附件
     *
     * @param attachmentDetail
     * @param currentLoginUsers
     * @return
     */
    @Override
    @Transactional
    public ResultObject saveApplyPaymentInstrument(AttachmentDetailRequest attachmentDetail, Users currentLoginUsers, AppRequestHeads appRequestHeads) {
        ResultObject resultObject = new ResultObject();
        String businessId = attachmentDetail.getBusinessId();
        //先删除指定支付附件
        applyAttachmentBaseService.deleteApplyAttachment(attachmentDetail.getBusinessId(), attachmentDetail.getAttachmentTypeCode());
        //保存现有附件
        ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(businessId);
        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransaction(businessId);
        //删除支付附件
        applyAttachmentBaseService.deleteApplyPaymentAttachment(businessId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT.name());
        if (AssertUtils.isNotEmpty(attachmentDetail.getAttachmentRequestList())) {
            //保存支付附件
            attachmentDetail.getAttachmentRequestList().forEach(attachmentRequest -> {
                ApplyPaymentAttachmentPo applyPaymentAttachmentPo = new ApplyPaymentAttachmentPo();
                applyPaymentAttachmentPo.setPremiumId(applyPremiumBo.getPremiumId());
                applyPaymentAttachmentPo.setApplyId(businessId);
                applyPaymentAttachmentPo.setPaymentTransactionId(applyPaymentTransactionBo.getPaymentTransactionId());
                applyPaymentAttachmentPo.setAttachmentId(attachmentRequest.getAttachmentId());
                applyPaymentAttachmentPo.setAttachmentTypeCode(ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT.name());
                applyAttachmentBaseService.saveApplyPaymentAttachment(currentLoginUsers.getUserId(), applyPaymentAttachmentPo);
            });
        }
        applyPaymentTransactionBo.setPaymentStatus(PAYMENT_FINISHED.name());
        applyPaymentTransactionBaseService.saveApplyPaymentTransaction(currentLoginUsers.getUserId(), applyPaymentTransactionBo);
        List<ApplyPaymentTransactionItemBo> applyPaymentTransactionItemBoList = applyPaymentTransactionBo.getApplyPaymentTransactionItemBos();
        if (AssertUtils.isNotNull(applyPaymentTransactionItemBoList)) {
            applyPaymentTransactionItemBoList.forEach(applyPaymentTransactionItemBo -> {
                applyPaymentTransactionBo.setPaymentStatus(PAYMENT_FINISHED.name());
            });
            applyPaymentTransactionBaseService.addApplyPaymentTransactionItem(currentLoginUsers.getUserId(), applyPaymentTransactionItemBoList);
        }
        if (NORMAL_PAYMENT.name().equals(applyPaymentTransactionBo.getPaymentType()) && PAYMENT.name().equals(applyPaymentTransactionBo.getFeeType())) {
            if (AssertUtils.isNotNull(applyPremiumBo)) {
                applyPremiumBo.setPremiumStatus(PAYMENT_FINISHED.name());
                applyPremiumBaseService.saveApplyPremium(currentLoginUsers.getUserId(), applyPremiumBo);
            }
        }
        ApplyBo applyBo = applyBaseService.queryApply(businessId);
        log.info("==========发送客户凭证上传通知消息============1");
        log.info("appRequestHeads:" + JackSonUtils.toJson(appRequestHeads));
        if (AssertUtils.isNotNull(applyBo) && AssertUtils.isNotNull(appRequestHeads) && ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_PERSONAL.name().equals(applyBo.getApplyType())
                && !"gclife_agent_app".equals(appRequestHeads.getDeviceChannel())) {
            log.info("==========发送客户凭证上传通知消息============2");
            messageBusinessService.pushBusinessCertificateMessage(PaymentTermEnum.MSG_BUSINESS_TYPE.UPLOAD_BANK_TRANSFER_PAYMENT_VOUCHER.name(), applyBo);
            messageBusinessService.pushBusinessCertificateMessageNew(PaymentTermEnum.MSG_BUSINESS_TYPE.UPLOAD_BANK_TRANSFER_PAYMENT_VOUCHER.name(), applyBo);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AttachmentResponse>> queryApplyPaymentInstrument(AttachmentDetailRequest attachmentDetail, Users currentLoginUsers) {
        ResultObject<List<AttachmentResponse>> resultObject = new ResultObject<>();
        if (!AssertUtils.isNotEmpty(attachmentDetail.getBusinessId())) {
            return resultObject;
        }
        List<ApplyPaymentAttachmentPo> applyPaymentAttachmentPos = applyAttachmentBaseService.listApplyPaymentAttachment(attachmentDetail.getBusinessId(), ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT.name());
        if (AssertUtils.isNotEmpty(applyPaymentAttachmentPos)) {
            List<AttachmentResponse> attachmentRequestList = JSONArray.parseArray(JSON.toJSONString(applyPaymentAttachmentPos), AttachmentResponse.class);
            resultObject.setData(attachmentRequestList);
        }
        return resultObject;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResultObject paymentReturnInstrument(AttachmentDetailRequest attachmentDetail, Users users, AppRequestHeads appRequestHandler) {
        ResultObject resultObject = new ResultObject();
        String businessId = attachmentDetail.getBusinessId();
        // 先删除指定支付附件
        applyAttachmentBaseService.deleteApplyAttachment(attachmentDetail.getBusinessId(), attachmentDetail.getAttachmentTypeCode());
        // 保存现有附件
        ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(businessId);
        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransaction(businessId);
        // 删除支付附件
        applyAttachmentBaseService.deleteApplyPaymentAttachment(businessId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.APPLY_PAYMENT_INSTRUMENT.name());

        // 退回支付状态
        applyPaymentTransactionBo.setPaymentStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name());
        applyPaymentTransactionBaseService.saveApplyPaymentTransaction(users.getUserId(), applyPaymentTransactionBo);
        List<ApplyPaymentTransactionItemBo> applyPaymentTransactionItemBoList = applyPaymentTransactionBo.getApplyPaymentTransactionItemBos();
        if (AssertUtils.isNotNull(applyPaymentTransactionItemBoList)) {
            applyPaymentTransactionItemBoList.forEach(applyPaymentTransactionItemBo -> {
                applyPaymentTransactionBo.setPaymentStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name());
            });
            applyPaymentTransactionBaseService.addApplyPaymentTransactionItem(users.getUserId(), applyPaymentTransactionItemBoList);
        }
        if (AssertUtils.isNotNull(applyPremiumBo)) {
            applyPremiumBo.setPremiumStatus(PaymentTermEnum.PAYMENT_STATUS.PAYMENT_WAITTING.name());
            applyPremiumBaseService.saveApplyPremium(users.getUserId(), applyPremiumBo);
        }
        return resultObject;
    }
}
