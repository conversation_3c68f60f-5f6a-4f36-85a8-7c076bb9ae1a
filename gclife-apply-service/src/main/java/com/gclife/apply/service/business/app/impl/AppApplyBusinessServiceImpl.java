package com.gclife.apply.service.business.app.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.app.model.request.AppGroupPolicyQueryRequest;
import com.gclife.app.model.response.*;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.app.AppApplyBusinessDao;
import com.gclife.apply.dao.app.ApplyPlanBusinessDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.app.ApplyInfoExtBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.request.app.ApplyQueryRequest;
import com.gclife.apply.model.response.app.ApplyInfoResponse;
import com.gclife.apply.model.response.app.ApplyListResponse;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.app.AppApplyBusinessService;
import com.gclife.apply.service.data.ApplyBoService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.parameter.app.transform.ApplyTransData;
import com.gclife.common.error.PolicyErrorConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.payment.api.PaymentApi;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.api.PlatformCareerApi;
import com.gclife.platform.model.response.CareerNameResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.request.PolicyQueryRequest;
import com.gclife.policy.model.response.PolicyTodoListResponse;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.thirdparty.api.ThirdpartyShortUrlApi;
import com.gclife.thirdparty.model.config.ThirdpartyTermEnum;
import com.gclife.thirdparty.model.request.ShortUrlRequest;
import com.gclife.thirdparty.model.response.ShortUrlResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.TerminationTaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL;
import static com.gclife.apply.model.config.GroupErrorConfigEnum.GROUP_APPLY_QUERY_APPLY_ERROR;


/**
 * <AUTHOR>
 * create 18-1-8
 * description:
 */
@Service
@Slf4j
public class AppApplyBusinessServiceImpl extends BaseBusinessServiceImpl implements AppApplyBusinessService {

    @Autowired
    private AppApplyBusinessDao appApplyBusinessDao;

    @Autowired
    private ApplyTransData applyTransData;

    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;

    @Autowired
    private ApplyPlanBusinessDao applyPlanBusinessDao;

    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private ApplyLoanBaseService applyLoanBaseService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyBoService applyBoService;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private PaymentBaseApi paymentBaseApi;
    @Autowired
    private PaymentApi paymentApi;
    @Autowired
    private ThirdpartyShortUrlApi thirdpartyShortUrlApi;
    @Autowired
    private ApplyChangeBaseService applyChangeBaseService;

    @Override
    public ResultObject<BasePageResponse<ApplyListResponse>> getApplyList(ApplyQueryRequest applyQueryRequest) {
        AssertUtils.isNotEmpty(log, applyQueryRequest.getAgentId(), ApplyErrorConfigEnum.APPLY_AGENT_ID_IS_NOT_NULL);
        ResultObject<BasePageResponse<ApplyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<ApplyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        try {
            //查询状态的分类列表
            List<String> listCodes = new ArrayList<>();
            if (AssertUtils.isNotEmpty(applyQueryRequest.getPolicyStatus())) {
                ResultObject<List<SyscodeRespFc>> listResultObject = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.APPLY_STATUS.name(), applyQueryRequest.getPolicyStatus());
                if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                    listCodes = listResultObject.getData().stream().map(SyscodeRespFc::getCodeKey).distinct().collect(Collectors.toList());
                }
            }
            //查询保单数据
            List<ApplyExtBo> listApplyExtBo = appApplyBusinessDao.getApplyExtBoList(applyQueryRequest, listCodes);
            if (AssertUtils.isNotNull(listApplyExtBo)) {
                policyListResponseBasePageResponse.setData(applyTransData.transApplyListData(listApplyExtBo));
                resultObject.setData(policyListResponseBasePageResponse);
            }
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
            e.printStackTrace();
        }

        return resultObject;
    }

    @Override
    public ResultObject<ApplyInfoResponse> getApplyInfo(String applyId, Users currentLoginUsers) {
        AssertUtils.isNotEmpty(log, applyId, ApplyErrorConfigEnum.APPLY_APPLY_ID_IS_NOT_NULL);
        ResultObject<ApplyInfoResponse> resultObject = new ResultObject<>();
        try {
            ApplyInfoExtBo applyInfoExtBo = appApplyBusinessDao.getApplyInfoExtById(applyId);
            //查询保单数据
            if (!AssertUtils.isNotNull(applyInfoExtBo)) {
                return resultObject;
            }
            //投保人
            List<ApplyInsuredBo> applyInsuredBos = applyBaseService.queryApplyInsured(applyInfoExtBo.getApplyId());
            applyInfoExtBo.setListApplyInsured(applyInsuredBos);
            //设置险种
            List<ApplyCoverageBo> applyCoverageBos = applyBaseService.queryApplyCoverageList(applyId);
            if (AssertUtils.isNotEmpty(applyCoverageBos)) {
                Map<String, List<ApplyCoverageBo>> map = applyCoverageBos.stream()
                        .filter(coverageBo -> AssertUtils.isNotEmpty(coverageBo.getInsuredId()))
                        .collect(Collectors.groupingBy(ApplyCoverageBo::getInsuredId));
                // 设置被保人险种信息
                applyInsuredBos.forEach(applyInsuredBo -> {
                    if (AssertUtils.isNotEmpty(map.get(applyInsuredBo.getInsuredId()))) {
                        applyInsuredBo.setListCoverage(map.get(applyInsuredBo.getInsuredId()));
                        applyInsuredBo.setMult(map.get(applyInsuredBo.getInsuredId()).get(0).getMult());
                    }
                });
            }

            //查询受益人
            List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = applyBaseService.queryApplyBeneficiaryList(applyId);
            if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
                Map<String, List<ApplyBeneficiaryInfoBo>> map = applyBeneficiaryInfoBos.stream()
                        .filter(applyBeneficiaryInfoBo -> AssertUtils.isNotEmpty(applyBeneficiaryInfoBo.getInsuredId()))
                        .collect(Collectors.groupingBy(ApplyBeneficiaryInfoBo::getInsuredId));
                // 设置被保人受益人
                applyInsuredBos.forEach(applyInsuredBo -> {
                    if (AssertUtils.isNotEmpty(map.get(applyInsuredBo.getInsuredId()))) {
                        applyInsuredBo.setListBeneficiary(map.get(applyInsuredBo.getInsuredId()));
                    }
                });
            }
            ApplyPlanPo applyPlanPo = applyPlanBusinessDao.getApplyPlanPoByApplyId(applyId);
            ApplyInfoResponse applyInfoResponse = applyTransData.transformPolicyData(applyInfoExtBo, applyPlanPo, currentLoginUsers);
            // 暂保条款入口按钮是否展示
            ApplyPaymentTransactionBo applyPaymentTransactionBo =
                    applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
            if (AssertUtils.isNotNull(applyPaymentTransactionBo) &&
                    ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
                applyInfoResponse.setTemporaryCoverFlag(TerminologyConfigEnum.WHETHER.YES.name());
                // 获取暂保条款链接
                ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
                shortUrlRequest.setShortType(ThirdpartyTermEnum.SHORT_TYPE.TEMPORARY_COVER.name());
                shortUrlRequest.setBusinessId(applyId);
                getLogger().info("投保单ID：{}，获取暂保条款链接请求参数：{}", applyId, JSON.toJSONString(shortUrlRequest));
                ResultObject<ShortUrlResponse> urlResultObject = thirdpartyShortUrlApi.queryShortUrl(shortUrlRequest);
                getLogger().info("投保单ID：{}，获取暂保条款链接返回结果：{}", applyId, JSON.toJSONString(urlResultObject));
                if (!AssertUtils.isResultObjectDataNull(urlResultObject)) {
                    applyInfoResponse.setTemporaryCoverUrl(urlResultObject.getData().getFullUrl());
                }
            }
            resultObject.setData(applyInfoResponse);
        } catch (Exception e) {
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
            e.printStackTrace();
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<ApplyListResponse>> getNewApplyList(ApplyQueryRequest applyQueryRequest) {
        AssertUtils.isNotEmpty(log, applyQueryRequest.getAgentId(), ApplyErrorConfigEnum.APPLY_AGENT_ID_IS_NOT_NULL);
        ResultObject<BasePageResponse<ApplyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<ApplyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        //查询状态的分类列表
        List<String> listCodes = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applyQueryRequest.getPolicyStatus())) {
            ResultObject<List<SyscodeRespFc>> listResultObject = platformBaseInternationServiceApi.getTerminologyList(ApplyTermEnum.APPLY_STATUS_FLAG.NEW_APPLY_STATUS.name(), applyQueryRequest.getPolicyStatus());
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                listCodes = listResultObject.getData().stream().map(SyscodeRespFc::getCodeKey).distinct().collect(Collectors.toList());
            }
        }
        //查询保单数据
        List<ApplyExtBo> listApplyExtBo = appApplyBusinessDao.getApplyExtBoList(applyQueryRequest, listCodes);
        if (AssertUtils.isNotNull(listApplyExtBo)) {
            policyListResponseBasePageResponse.setData(applyTransData.transApplyListData(listApplyExtBo));
            policyListResponseBasePageResponse.setTotalLine(listApplyExtBo.get(0).getTotalLine());
            resultObject.setData(policyListResponseBasePageResponse);
        }
        return resultObject;
    }

    /**
     * 查询客户APP的待办列表
     *
     * @param policyQueryRequest policyQueryRequest
     * @param users              用户
     * @return PolicyTodoListResponse
     */
    @Override
    public ResultObject<List<PolicyTodoListResponse>> getClientApplyQueryTodoList(PolicyQueryRequest policyQueryRequest, Users users) {
        ResultObject<List<PolicyTodoListResponse>> resultObject = new ResultObject<>();

        AssertUtils.isNotEmpty(getLogger(), policyQueryRequest.getCustomerIds(), PolicyErrorConfigEnum.POLICY_CUSTOMER_ID_IS_NOT_NULL);

        //查询保单数据
        List<ApplyTodoListBo> clientPolicyQueryTodoList = appApplyBusinessDao.getClientPolicyQueryTodoList(policyQueryRequest);
        if (AssertUtils.isNotNull(clientPolicyQueryTodoList)) {
            List<PolicyTodoListResponse> policyTodoListResponses = new ArrayList<>();
            clientPolicyQueryTodoList.forEach(applyTodoListBo -> {
                PolicyTodoListResponse policyTodoListResponse = new PolicyTodoListResponse();
                ClazzUtils.copyPropertiesIgnoreNull(applyTodoListBo, policyTodoListResponse);
                policyTodoListResponse.setCreateDate(applyTodoListBo.getCreatedDate());
                if (ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name().equals(applyTodoListBo.getApplyStatus())) {
                    policyTodoListResponse.setButtonCode("CONTINUE_TO_INSURE");
                } else {
                    policyTodoListResponse.setButtonCode("PAYMENT");
                    Long expireEndTime = null;
                    //缴费截止日期=下发核保日期+7天
                    ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(applyTodoListBo.getApplyId());
                    if (AssertUtils.isNotNull(applyUnderwriteDecisionPo)) {
                        Long createdDate = applyUnderwriteDecisionPo.getCreatedDate();
                        //5.8.3 作废时间默认14天
                        expireEndTime = DateUtils.addStringDayRT(createdDate, 14);
                        //若是存在先做了支付到期时间延长，则要设置变更后的支付截止时间
                        ApplyChangeRecordPo applyChangeRecordPo = applyChangeBaseService.queryApplyChangedRecord(applyTodoListBo.getApplyId(), ApplyTermEnum.CHANGE_TYPE.EXTENDED_PAYMENT_DUE_TIME.name());
                        if (AssertUtils.isNotNull(applyChangeRecordPo)) {
                            expireEndTime = Long.parseLong(applyChangeRecordPo.getAfterChange());
                        }
                    }
                    policyTodoListResponse.setPaymentExpDate(expireEndTime);
                    if (AssertUtils.isNotNull(expireEndTime)) {
                        policyTodoListResponse.setPaymentExpDateMS(expireEndTime - DateUtils.getCurrentTime());
                    }
                    policyTodoListResponse.setBusinessId(applyTodoListBo.getApplyId());
                }
                policyTodoListResponse.setColorValue("#62BC46");
                policyTodoListResponses.add(policyTodoListResponse);
            });
            resultObject.setData(policyTodoListResponses);
        }
        return resultObject;
    }

    /**
     * 我的投保单列表-团险投保单
     *
     * @param appGroupPolicyQueryRequest 请求参数
     * @param users                      用户
     * @return AppGroupPolicyListResponse
     */
    @Override
    public ResultObject<BasePageResponse<AppGroupPolicyListResponse>> getAppGroupApplyList(AppGroupPolicyQueryRequest appGroupPolicyQueryRequest, Users users) {
        AssertUtils.isNotEmpty(log, appGroupPolicyQueryRequest.getAgentId(), ApplyErrorConfigEnum.APPLY_AGENT_ID_IS_NOT_NULL);
        ResultObject<BasePageResponse<AppGroupPolicyListResponse>> resultObject = new ResultObject<>();
        BasePageResponse<AppGroupPolicyListResponse> policyListResponseBasePageResponse = new BasePageResponse<>();
        //查询状态的分类列表
        List<String> listCodes = new ArrayList<>();
        if (AssertUtils.isNotEmpty(appGroupPolicyQueryRequest.getPolicyStatus())) {
            ResultObject<List<SyscodeRespFc>> listResultObject = platformBaseInternationServiceApi.getTerminologyList(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS.name(), appGroupPolicyQueryRequest.getPolicyStatus());
            if (!AssertUtils.isResultObjectListDataNull(listResultObject)) {
                listCodes = listResultObject.getData().stream().map(SyscodeRespFc::getCodeKey).distinct().collect(Collectors.toList());
            }
        }
        //查询保单数据
        List<AppGroupPolicyListResponse> groupPolicyQueryBos = appApplyBusinessDao.getGroupListApplyExtBo(appGroupPolicyQueryRequest, listCodes);
        if (AssertUtils.isNotNull(groupPolicyQueryBos)) {
            policyListResponseBasePageResponse.setData(groupPolicyQueryBos);
            policyListResponseBasePageResponse.setTotalLine(groupPolicyQueryBos.get(0).getTotalLine());
            resultObject.setData(policyListResponseBasePageResponse);
        }
        return resultObject;
    }

    /**
     * 我的投保单-团险投保单详情
     *
     * @param applyId 投保单ID
     * @param users   用户
     * @return AppGroupPolicyDetailResponse
     */
    @Override
    public ResultObject<AppGroupPolicyDetailResponse> getAppGroupApplyInfo(String applyId, Users users) {
        ResultObject<AppGroupPolicyDetailResponse> resultObject = new ResultObject<>();
        AppGroupPolicyDetailResponse groupPolicyQueryDetail = new AppGroupPolicyDetailResponse();
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(log, applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        if (!ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name().equals(applyPo.getApplyType())) {
            return resultObject;
        }
        ClazzUtils.copyPropertiesIgnoreNull(applyPo, groupPolicyQueryDetail);

        ApplyApplicantBo applyApplicantBo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(log, applyApplicantBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        AppGroupPolicyApplicantResponse groupApplicant = new AppGroupPolicyApplicantResponse();
        ClazzUtils.copyPropertiesIgnoreNull(applyApplicantBo, groupApplicant);

        if (AssertUtils.isNotEmpty(groupApplicant.getCompanyIndustry())) {
            ResultObject<CareerNameResponse> careerNameResponseResultObject = platformCareerApi.careerNameGet(groupApplicant.getCompanyIndustry());
            if (!AssertUtils.isResultObjectDataNull(careerNameResponseResultObject)) {
                groupApplicant.setCompanyIndustryName(careerNameResponseResultObject.getData().getCareerName());
            }
        }
        groupPolicyQueryDetail.setGroupApplicant(groupApplicant);
        groupPolicyQueryDetail.setCompanyName(applyApplicantBo.getCompanyName());

        //查询被保人
        List<ApplyInsuredBo> applyInsuredBos = applyBaseService.queryApplyInsured(applyId);
        AssertUtils.isNotEmpty(log, applyInsuredBos, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        groupPolicyQueryDetail.setInsuredSum(applyInsuredBos.size() + "");
        List<AppGroupPolicyInsuredListResponse> groupInsuredList = new ArrayList<>();
        applyInsuredBos.forEach(applyInsuredBo -> {
            AppGroupPolicyInsuredListResponse appGroupPolicyInsuredListResponse = new AppGroupPolicyInsuredListResponse();
            appGroupPolicyInsuredListResponse.setApplyId(applyInsuredBo.getApplyId());
            appGroupPolicyInsuredListResponse.setInsuredId(applyInsuredBo.getInsuredId());
            appGroupPolicyInsuredListResponse.setName(applyInsuredBo.getName());
            appGroupPolicyInsuredListResponse.setSex(applyInsuredBo.getSex());
            appGroupPolicyInsuredListResponse.setOccupationCode(applyInsuredBo.getOccupationCode());
            appGroupPolicyInsuredListResponse.setInsuredStatus(applyInsuredBo.getInsuredStatus());
            groupInsuredList.add(appGroupPolicyInsuredListResponse);
        });
        groupPolicyQueryDetail.setGroupInsuredList(groupInsuredList);

        //查询保单公共险种
        // 查询公共险种信息
        List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.listApplyCoverage(applyId);
        AssertUtils.isNotEmpty(log, applyCoveragePos, ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        ResultObject<List<SyscodeResponse>> queryInternational = platformInternationalBaseApi.queryInternational("PRODUCT_ID", null);

        applyCoveragePos.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                .findFirst().ifPresent(policyCoveragePo -> {
                    groupPolicyQueryDetail.setProductId(policyCoveragePo.getProductId());
                    groupPolicyQueryDetail.setProductName(policyCoveragePo.getProductName());
                    if (!AssertUtils.isResultObjectListDataNull(queryInternational)) {
                        queryInternational.getData().stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(policyCoveragePo.getProductId())).findFirst()
                                .ifPresent(syscodeResponse -> groupPolicyQueryDetail.setProductName(syscodeResponse.getCodeName()));
                    }
                });
        List<String> additionalProductId = applyCoveragePos.stream().filter(policyCoveragePo -> !ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(policyCoveragePo.getPrimaryFlag()))
                .map(ApplyCoveragePo::getProductId).collect(Collectors.toList());
        List<String> additionalProductName = new ArrayList<>();
        if (!AssertUtils.isResultObjectListDataNull(queryInternational) && AssertUtils.isNotEmpty(additionalProductId)) {
            additionalProductId.forEach(s -> queryInternational.getData().stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals(s)).findFirst()
                    .ifPresent(syscodeResponse -> additionalProductName.add(syscodeResponse.getCodeName())));
        }
        groupPolicyQueryDetail.setAdditionalProductName(additionalProductName);

        // 查询投保单保费
        ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(applyId);
        AssertUtils.isNotNull(log, applyPremiumBo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_PREMIUM_IS_NOT_FOUNT_OBJECT);
        groupPolicyQueryDetail.setTotalPremium(applyPremiumBo.getPeriodTotalPremium());

        resultObject.setData(groupPolicyQueryDetail);
        return resultObject;
    }

    /**
     * 我的投保单-团险投保单被保人详情
     *
     * @param applyId   投保单ID
     * @param insuredId 被保人ID
     * @param users     用户
     * @return AppGroupInsuredDetailResponse
     */
    @Override
    public ResultObject<AppGroupInsuredDetailResponse> getAppGroupApplyInsuredDetail(String applyId, String insuredId, Users users) {
        ResultObject<AppGroupInsuredDetailResponse> resultObject = new ResultObject<>();
        AppGroupInsuredDetailResponse appGroupInsuredDetail = new AppGroupInsuredDetailResponse();
        List<ApplyInsuredBo> applyInsuredBos = applyBaseService.queryApplyInsured(applyId);
        AssertUtils.isNotEmpty(log, applyInsuredBos, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        Optional<ApplyInsuredBo> first = applyInsuredBos.stream().filter(applyInsuredBo -> applyInsuredBo.getInsuredId().equals(insuredId)).findFirst();
        ApplyInsuredBo applyInsuredBo = new ApplyInsuredBo();
        if (first.isPresent()) {
            applyInsuredBo = first.get();
        } else {
            throwsException(ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        }
        ClazzUtils.copyPropertiesIgnoreNull(applyInsuredBo, appGroupInsuredDetail);

        //产品信息
        List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.getGroupApplyCoverageList(applyId, insuredId);
        if (AssertUtils.isNotEmpty(applyCoveragePos)) {
            List<AppGroupInsuredProductResponse> listCoverage = (List<AppGroupInsuredProductResponse>) this.converterList(
                    applyCoveragePos, new TypeToken<List<AppGroupInsuredProductResponse>>() {
                    }.getType()
            );
            List<String> coverageIds = applyCoveragePos.stream().map(ApplyCoveragePo::getCoverageId).collect(Collectors.toList());
            // 查询险种档次信息
            List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.listApplyCoverageLevel(coverageIds);
            if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
                // 按险种ID分组
                Map<String, List<ApplyCoverageLevelPo>> coverageLevelPoMap =
                        applyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(ApplyCoverageLevelPo::getCoverageId));
                // 查询责任包括已失效
                List<ApplyCoverageDutyBo> applyCoverageDutyBos = applyCoverageBaseService.getApplyCoverageDutyList(applyId);
                listCoverage.forEach(coverage -> {
                    if (AssertUtils.isNotEmpty(coverageLevelPoMap.get(coverage.getCoverageId()))) {
                        List<AppGroupInsuredProductExtResponse> productExtResponses = new ArrayList<>();
                        coverageLevelPoMap.get(coverage.getCoverageId()).forEach(coverageLevelPo -> {
                            AppGroupInsuredProductExtResponse policyCoverageExtResponse = new AppGroupInsuredProductExtResponse();
                            ClazzUtils.copyPropertiesIgnoreNull(coverageLevelPo, policyCoverageExtResponse);
                            if (AssertUtils.isNotEmpty(coverageLevelPo.getCoverageDutyId())) {
                                // 设置责任
                                applyCoverageDutyBos.stream()
                                        .filter(coverageDutyBo -> coverageDutyBo.getCoverageDutyId().equals(coverageLevelPo.getCoverageDutyId()))
                                        .findFirst().ifPresent(coverageDutyBo -> policyCoverageExtResponse.setDutyId(coverageDutyBo.getDutyId()));
                            }
                            productExtResponses.add(policyCoverageExtResponse);
                        });
                        coverage.setCoverageExt(productExtResponses);
                    }
                });
            }
            //排序
            if (AssertUtils.isNotEmpty(listCoverage)) {
                listCoverage.sort(Comparator.comparing(AppGroupInsuredProductResponse::getPrimaryFlag, Comparator.nullsLast(String::compareTo)).reversed()
                        .thenComparing(AppGroupInsuredProductResponse::getProductId, Comparator.nullsLast(String::compareTo)));
            }
            appGroupInsuredDetail.setInsuredProduct(listCoverage);
        }

        resultObject.setData(appGroupInsuredDetail);
        return resultObject;
    }

    /**
     * APP重新申请投保
     *
     * @param applyId 投保单ID
     * @param users   用户
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject reapplyAppInfo(String applyId, Users users) {
        ResultObject resultObject = new ResultObject();
        AssertUtils.isNotEmpty(getLogger(), applyId, APPLY_APPLY_ID_IS_NOT_NULL);
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(getLogger(), applyPo, GROUP_APPLY_QUERY_APPLY_ERROR);

        List<ApplyCoveragePo> applyCoveragePosOfInsured = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        //贷款合同信息
        String mainProductId = applyDataTransform.getMainProductId(applyId);
        if (!AssertUtils.isNotEmpty(applyCoveragePosOfInsured) || !Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_20A.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(mainProductId)) {
            return resultObject;
        }

        boolean initialFlag = true;
        if (ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name().equals(applyPo.getApplyStatus())) {
            initialFlag = false;
        }
        //删除已经产生的核保通知书
        applyUnderwriteBaseService.deleteApplyUnderwriteNoticePo(applyId);
        //更改投保单和计划书的状态
        applyPo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name());
        String userId = users.getUserId();
        applyBaseService.saveApplyPo(userId, applyPo);

        ApplyPlanPo applyPlanPo = applyPlanBusinessDao.getApplyPlanPoByApplyId(applyId);
        if (AssertUtils.isNotNull(applyPlanPo)) {
            applyPlanPo.setStatus(ApplyTermEnum.APPLY_PLAN_STATUS.INITIAL.name());
            applyBoService.saveApplyPlanPo(applyPlanPo);

            //删除已产生的计划书附件
            ResultObject deletePlanBookByApplyPlanId = policyApi.deletePlanBookByApplyPlanId(applyPlanPo.getApplyPlanId());
            AssertUtils.isResultObjectError(getLogger(), deletePlanBookByApplyPlanId);
        }

        //存在初始化的投保单使用重新投保功能，只有非初始化的投保单才保存操作表，控制APP流程是否上传修改申请书
        if (initialFlag) {
            //保存投保单操作表
            ApplyOperationPo applyOperationPo = applyBaseService.queryApplyOperationPo(applyId);
            if (!AssertUtils.isNotNull(applyOperationPo)) {
                applyOperationPo = new ApplyOperationPo();
            }
            applyOperationPo.setApplyId(applyId);
            applyOperationPo.setOperationCode(ApplyTermEnum.APPLY_OPERATION_CODE.REAPPLYING.name());
            applyBaseService.saveApplyOperationPo(applyOperationPo, userId);
        }

        ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(applyId);
        if (AssertUtils.isNotNull(applyPremiumBo)) {
            applyPremiumBo.setPremiumStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INITIAL.name());
            applyPremiumBaseService.saveApplyPremium(userId, applyPremiumBo);
        }

        //针对五号产品的重新投保功能，作废掉没成功的支付数据，可能存在支付成功的单，用于后续投保单支付，多退少补
        List<ApplyPaymentTransactionBo> applyPaymentTransactionBos = applyPaymentTransactionBaseService.queryApplyPaymentTransactions(applyId);
        if (AssertUtils.isNotEmpty(applyPaymentTransactionBos)) {
            List<String> paymentTypes = Arrays.asList(ApplyTermEnum.CHARGE_TYPE.NORMAL_PAYMENT.name(), ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
            applyPaymentTransactionBos.stream().filter(applyPaymentTransactionBo -> paymentTypes.contains(applyPaymentTransactionBo.getPaymentType())
                    && Arrays.asList(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name(), PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name()).contains(applyPaymentTransactionBo.getPaymentStatus()))
                    .forEach(applyPaymentTransactionBo -> {
                        ResultObject<Void> voidResultObject = paymentApi.updatePaymentStatus(applyPaymentTransactionBo.getPaymentId(), ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                        this.getLogger().info("作废发起支付的支付记录：{}", JackSonUtils.toJson(voidResultObject));
                        applyPaymentTransactionBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                        applyPaymentTransactionBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                        applyPaymentTransactionBo.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                            applyPaymentTransactionItemBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                            applyPaymentTransactionItemBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                        });
                        applyPaymentTransactionBaseService.saveApplyPaymentTransaction(userId, applyPaymentTransactionBo);
                    });
        }

        //结束这单的工作流
        if (initialFlag) {
            TerminationTaskRequest terminationTaskRequest = new TerminationTaskRequest();
            terminationTaskRequest.setBusinessNo(applyPo.getApplyId());
            terminationTaskRequest.setTerminationMessage(null);
            this.getLogger().info("正在结束工作流");
            ResultObject resultObject1 = workFlowApi.terminationTask(terminationTaskRequest);
//            AssertUtils.isResultObjectError(getLogger(), resultObject1);
        }
        return resultObject;
    }
}
