package com.gclife.apply.service.business.group.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.agent.model.response.AgentSignBaseResponse;
import com.gclife.apply.core.jooq.tables.pojos.ApplyPo;
import com.gclife.apply.core.jooq.tables.pojos.ApplyUnderwriteTaskPo;
import com.gclife.apply.core.jooq.tables.pojos.BaseUnderwriteDecisionPo;
import com.gclife.apply.dao.app.ApplyPlanBusinessDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.ModelConstantEnum;
import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.request.BaseApplyUnderwriteDecisionRequest;
import com.gclife.apply.model.request.PreImageAttachmentRequest;
import com.gclife.apply.model.response.ApplyListResponse;
import com.gclife.apply.model.response.app.AppAttachmentResponse;
import com.gclife.apply.model.response.group.ApplyReviewResponse;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.business.group.GroupInvoiceService;
import com.gclife.apply.service.business.group.GroupUnderWriteService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.parameter.group.GroupInputParameterValidate;
import com.gclife.apply.validate.parameter.transform.ApplyChangeTransData;
import com.gclife.apply.validate.parameter.transform.ApplyPaymentTransData;
import com.gclife.apply.validate.parameter.transform.GroupApplyTransData;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.response.UnderwriteInfoResponse;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.GroupErrorConfigEnum.GROUP_APPLY_QUERY_ARTIFICIAL_UNDERWRITING_ERROR;

/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
@Service
public class GroupUnderWriteServiceImpl extends BaseBusinessServiceImpl implements GroupUnderWriteService {

    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private ApplyBaseService applyBaseService;

    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private ApplyQueryBaseService applyQueryBaseService;

    @Autowired
    private GroupApplyTransData groupApplyTransData;

    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private GroupInvoiceService groupInvoiceService;

    @Autowired
    private ApplyPaymentTransData applyPaymentTransData;

    @Autowired
    private MessageBusinessService messageBusinessService;

    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private ApplyPlanBusinessDao applyPlanBusinessDao;
    @Autowired
    private GroupInputParameterValidate groupInputParameterValidate;
    @Autowired
    private GroupUnderWriteService groupUnderWriteService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyPaymentBaseService applyPaymentBaseService;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private ApplyChangeTransData applyChangeTransData;
    @Autowired
    private MessageBaseBusinessService messageBaseBusinessService;
    @Autowired
    private AgentApi agentApi;


    @Override
    public ResultObject underWriteSignPost(Users currentLoginUsers, String applyId) {

        ResultObject resultObject = new ResultObject();
        try {
            //签收
            String userId = currentLoginUsers.getUserId();
            resultObject = workFlowApi.claimTask(userId, applyId, ModelConstantEnum.WORKFLOW_STATUS.GROUP_UNDERWRITING_TASK.name(), null);
            AssertUtils.isResultObjectError(this.getLogger(), resultObject);
            //判断在人工核保任务表中是否有核保任务，没有就新建任务
            ApplyUnderwriteTaskPo applyUnderwriteTaskPo = applyUnderwriteBaseService.queryApplyUnderwriteTaskPo(applyId);
            if (!AssertUtils.isNotNull(applyUnderwriteTaskPo)) {
                applyUnderwriteBaseService.saveApplyUnderwriteTaskPo(userId, applyId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_APPLY_UNDER_WRITE_TASK_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<ApplyReviewResponse>> getArtificialUnderwriting(Users currentLoginUsers, ApplyListRequest applyListVo) {
        ResultObject<BasePageResponse<ApplyReviewResponse>> resultObject = new ResultObject<>();
        try {
            WaitingTaskRequest waitingTaskRequest = new WaitingTaskRequest();
            waitingTaskRequest.setWorkflowItemType(ModelConstantEnum.WORKFLOW_STATUS.GROUP_UNDERWRITING_TASK.name());
            waitingTaskRequest.setWorkflowType(ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
            waitingTaskRequest.setUserId(currentLoginUsers.getUserId());

            List<ApplyListBo> applyListBos = applyQueryBaseService.loadApplyListByWorkFlow(waitingTaskRequest, applyListVo, ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name());
            if (!AssertUtils.isNotEmpty(applyListBos)) {
                return resultObject;
            }
            List<ApplyListResponse> applyListResponses = groupApplyTransData.transApplyList(applyListBos, TerminologyTypeEnum.APPLY_UNDERWRITE_WORK_FLOW_STATUS.name(), currentLoginUsers);

            List<ApplyReviewResponse> applyReviewResponses = (List<ApplyReviewResponse>) this.converterList(applyListResponses, new TypeToken<List<ApplyReviewResponse>>() {
            }.getType());

            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;

            BasePageResponse<ApplyReviewResponse> basePageResponse = BasePageResponse.getData(applyListVo.getCurrentPage(), applyListVo.getPageSize(), totalLine, applyReviewResponses);

            resultObject.setData(basePageResponse);

        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(GROUP_APPLY_QUERY_ARTIFICIAL_UNDERWRITING_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject postSendUnderwriting(Users users, AppRequestHeads appRequestHandler, BaseApplyUnderwriteDecisionRequest applyUnderwriteDecisionRequest) {
        ResultObject resultObject = new ResultObject();
        this.getLogger().info("团险人工核保下发核保决定 start");
        try {
            String applyId = applyUnderwriteDecisionRequest.getApplyId();
            String underwriteDecisionCode = applyUnderwriteDecisionRequest.getUnderWriteDecisionCode();
            String reviewComment = applyUnderwriteDecisionRequest.getReviewComment();
            //参数验证
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_APPLYID_ERROR);
            AssertUtils.isNotEmpty(this.getLogger(), underwriteDecisionCode, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_UNDERWRITEDECISIONCODE_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), reviewComment, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_REVIEW_COMMENT_IS_NOT_NULL);

            //代理人签约状态判断
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            ResultObject<AgentSignBaseResponse> respFcResultObject = baseAgentApi.queryOneAgentSignedAgentId(applyBo.getApplyAgentBo().getAgentId());
            AssertUtils.isResultObjectError(this.getLogger(), respFcResultObject);
            if (AssertUtils.isResultObjectError(respFcResultObject) ||
                    !ApplyTermEnum.SIGN_STATUS.SIGN_COMPLETE.name().equals(respFcResultObject.getData().getSignStatus())) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_PLAN_AGENT_NO_SIGN);
            }

            List<String> underWriteDecisionCodes = applyUnderwriteBaseService.queryBaseUnderwriteDecisionPo().stream().map(BaseUnderwriteDecisionPo::getUnderwriteDecisionCode).collect(Collectors.toList());
            //校验underWriteDecisionCode是否正确
            if (!underWriteDecisionCodes.contains(underwriteDecisionCode)) {
                throw new RequestException(ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_CODE_FORMAT_ERROR);
            }
            List<String> isApprovedHc = Arrays.asList(ApplyTermEnum.APPROVED_HC.EFFECTIVE_IMMEDIATELY.name(), ApplyTermEnum.APPROVED_HC.APPOINT_DATE.name());
            if (isApprovedHc.contains(applyUnderwriteDecisionRequest.getPreUnderwritingFlag())) {
                boolean isAppointDate = ApplyTermEnum.APPROVED_HC.APPOINT_DATE.name().equals(applyUnderwriteDecisionRequest.getPreUnderwritingFlag());
                // 指定日期：需填写暂予承保开始日期字段（必填），且日期选择需大于等于当前日期；
                if (isAppointDate) {
                    AssertUtils.isNotEmpty(getLogger(), applyUnderwriteDecisionRequest.getPreUnderwritingStartDate(), ApplyErrorConfigEnum.APPLY_DATE_MUST_GE_TO_THE_CURRENT_DATE);
                    if (DateUtils.timeToTimeLow(DateUtils.getCurrentTime()) > DateUtils.timeToTimeLow(DateUtils.stringToTime(applyUnderwriteDecisionRequest.getPreUnderwritingStartDate()))) {
                        throwsException(getLogger(), ApplyErrorConfigEnum.APPLY_DATE_MUST_GE_TO_THE_CURRENT_DATE);
                    }
                }
                ResultObject resultObject1 = groupUnderWriteService.savePreUnderwritingImageAttachment(applyId, applyUnderwriteDecisionRequest.getAppAttachmentRequests());
                AssertUtils.isResultObjectError(this.getLogger(), resultObject1);
            }

            //下发核保决定
            applyUnderwriteBaseService.saveApplyUnderwriteDecision(applyBo, users.getUserId(), applyUnderwriteDecisionRequest);

            //异步调用微服务保存发票数据
            this.getLogger().info("=*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*==*=savePaymentInvoice异步调用微服务保存发票数据");
            groupInvoiceService.savePaymentInvoice(users, applyId);

            ApplyPaymentUWBo applyPaymentUWBo = new ApplyPaymentUWBo();
            applyPaymentUWBo.setPrepaidPremiumFlag(false);
            //17号产品发送暂予承保消息，不需要再发核保通过消息
            Optional<ApplyCoverageBo> first = applyBo.getListInsuredCoverage().stream().filter(applyCoverageBo ->
                    applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).distinct().findFirst();
            if (first.isPresent() && ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(first.get().getProductId())
                    && isApprovedHc.contains(applyUnderwriteDecisionRequest.getPreUnderwritingFlag())) {
                applyPaymentUWBo.setNewGroupApplyUWFlag(false);
            } else {
                applyPaymentUWBo.setNewGroupApplyUWFlag(true);
            }

            if (ApplyTermEnum.DECISION_TYPE.STANDARD.name().equalsIgnoreCase(underwriteDecisionCode)
                    || ApplyTermEnum.DECISION_TYPE.SUBSTANDARD.name().equalsIgnoreCase(underwriteDecisionCode)) {
                applyPaymentTransData.initiatePayment(applyBo, users, appRequestHandler, applyPaymentUWBo, null);

                //暂予承保投保单状态设置为暂予承保Approved (HC)
                /*Optional<ApplyCoverageBo> first = applyBo.getListInsuredCoverage().stream().filter(applyCoverageBo ->
                        applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).distinct().findFirst();*/
                ApplyPo applyPoNew = applyBaseService.queryApplyPo(applyId);
                AssertUtils.isNotNull(this.getLogger(), applyPoNew, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
                if (first.isPresent() && ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(first.get().getProductId()) && isApprovedHc.contains(applyUnderwriteDecisionRequest.getPreUnderwritingFlag())) {
                    Long preUnderwritingStartDate = DateUtils.getCurrentTime();
                    if (ApplyTermEnum.APPROVED_HC.APPOINT_DATE.name().equals(applyUnderwriteDecisionRequest.getPreUnderwritingFlag())) {
                        preUnderwritingStartDate = DateUtils.timeToTimeLow(DateUtils.stringToTime(applyUnderwriteDecisionRequest.getPreUnderwritingStartDate()));
                    }
                    applyPoNew.setPreUnderwritingFlag(ApplyTermEnum.YES_NO.YES.name());
                    applyPoNew.setPreUnderwritingStartDate(preUnderwritingStartDate);
                    applyBaseService.saveApply(users.getUserId(), applyPoNew);
                    // 立即生效或者是今天生效，立即处理生效
                    if (ApplyTermEnum.APPROVED_HC.EFFECTIVE_IMMEDIATELY.name().equals(applyUnderwriteDecisionRequest.getPreUnderwritingFlag()) ||
                            DateUtils.timeToTimeLow(DateUtils.getCurrentTime()) == DateUtils.timeToTimeLow(DateUtils.stringToTime(applyUnderwriteDecisionRequest.getPreUnderwritingStartDate()))
                    ) {
                        applyPoNew.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPROVED_HC.name());
                        applyBaseService.saveApply(users.getUserId(), applyPoNew);
                        applyDataTransform.transferPreUnderwritingApplyToPolicy(applyId, users.getUserId(), ApplyTermEnum.YES_NO.YES.name());
                        try {
                            ApplyBo applyBo1 = applyBaseService.queryApply(applyId);
                            //设置代理人信息
                            AgentResponse agentResponse = agentApi.agentByIdGet(applyBo1.getApplyAgentBo().getAgentId()).getData();

                            this.getLogger().info("团单暂予承保提醒业务员app");
                            messageBusinessService.pushGroupPreMessageAgent(ApplyTermEnum.MSG_BUSINESS_TYPE.PRE_UNDERWRITING_TO_APP.name(), applyBo1, agentResponse.getUserId());

                            this.getLogger().info("团单暂予承保提醒客户");
                            messageBusinessService.pushGroupPreMessageCustomerSMS(ApplyTermEnum.MSG_BUSINESS_TYPE.PRE_UNDERWRITING_TO_CUSTOMER.name(), applyBo1);

                            this.getLogger().info("团单暂予承保提醒钉钉微信");
                            messageBusinessService.pushGroupPreMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.PRE_UNDERWRITING_ALL.name(), applyBo1);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                //核保通过 初始化投保单变更数据
                if (AssertUtils.isNotNull(applyPoNew) &&
                        Arrays.asList(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name(), ApplyTermEnum.APPLY_STATUS_FLAG.APPROVED_HC.name()).contains(applyPoNew.getApplyStatus())) {
                    applyChangeTransData.initApplyChangeData(users, applyId);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_UNDERWRITE_DECISION_SAVE_ERROR);
        }
        this.getLogger().info("团险人工核保下发核保决定 end");
        return resultObject;
    }

    @Override
    public ResultObject savePreUnderwritingImageAttachment(String applyId, List<PreImageAttachmentRequest> appAttachmentRequests) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            groupInputParameterValidate.validImagePreUnderwritingAttachment(appAttachmentRequests);

            groupInputParameterValidate.saveImagePreUnderwritingAttachmentData(applyId, appAttachmentRequests);
        } catch (Exception e) {
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_SAVE_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AppAttachmentResponse>> getPreUnderwritingImageAttachment(String applyId, Users users) {
        ResultObject<List<AppAttachmentResponse>> resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            List<AppAttachmentResponse> applyAttachmentRespList = new ArrayList<>();
            List<ApplyAttachmentBo> applyAttachmentBos = applyPlanBusinessDao.getIdAttachment(applyId, ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.PRE_UNDERWRITING_ATTACHMENT_TYPE.name());
            if (AssertUtils.isNotEmpty(applyAttachmentBos)) {
                applyAttachmentBos.forEach(applyAttachmentBo -> {
                    AppAttachmentResponse appAttachmentResponse = (AppAttachmentResponse) this.converterObject(applyAttachmentBo, AppAttachmentResponse.class);
                    SyscodeResponse syscodeResponse = platformInternationalBaseApi.queryOneInternational("CERTIFY_ATTACHMENT_TYPE",
                            ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.PRE_UNDERWRITING_ATTACHMENT_TYPE.name(), users.getLanguage()).getData();
                    if (AssertUtils.isNotNull(syscodeResponse)) {
                        appAttachmentResponse.setAttachmentTypeName(syscodeResponse.getCodeName());
                    }
                    String attachmentId = applyAttachmentBo.getAttachmentId();
                    ResultObject<AttachmentResponse> mediaUrl = attachmentApi.attachmentGet(attachmentId);
                    if (!AssertUtils.isResultObjectDataNull(mediaUrl)) {
                        appAttachmentResponse.setUrl(mediaUrl.getData().getUrl());
                    }
                    applyAttachmentRespList.add(appAttachmentResponse);
                });
            } else {
                AppAttachmentResponse appAttachmentResponse = new AppAttachmentResponse();
                applyAttachmentRespList.add(appAttachmentResponse);
            }
            resultObject.setData(applyAttachmentRespList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 暂予承保生效处理
     *
     * @param users           当前用户
     * @param basePageRequest 分页
     * @return YES
     */
    @Override
    @Transactional
    public String dealApprovedHcEffective(Users users, BasePageRequest basePageRequest) {
        List<ApplyHcBo> applyHcBos = applyQueryBaseService.queryApprovedHcEffective(basePageRequest);
        if (AssertUtils.isNotEmpty(applyHcBos)) {
            applyHcBos.forEach(applyHcBo -> {
                // 暂予承保已经缴费成功的单，直接承保回调处理
                if (ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyHcBo.getPremiumStatus())) {
                    this.dealPolicyEffective(applyHcBo);
                } else {
                    // 未缴费，暂予承保生效处理
                    this.dealHcEffective(users, applyHcBo);
                }
            });
        }

        if (AssertUtils.isNotEmpty(applyHcBos) && applyHcBos.size() == basePageRequest.getPageSize()) {
            this.getLogger().info("===============暂予承保生效处理结束 return : NO ===============");
            return TerminologyConfigEnum.WHETHER.NO.name();
        } else {
            this.getLogger().info("===============暂予承保生效处理结束 return : YES ===============");
            return TerminologyConfigEnum.WHETHER.YES.name();
        }
    }

    private void dealPolicyEffective(ApplyPo applyPo) {
        String userId = "GM_USER_101";
        String applyId = applyPo.getApplyId();
        boolean errorFlag = false;
        try {
            //团险投保单转保单
            applyPaymentBaseService.transferApplyToPolicy(applyId, userId, applyPo.getPreUnderwritingStartDate() + "");

            //工作流调用
            this.getLogger().info("handPaymentNotify start workflow " + DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            ResultObject resultObject1 = workFlowApi.paymentNotify(applyId, ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
            this.getLogger().info("handPaymentNotify workflow end time " + DateUtils.timeStrToString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            AssertUtils.isResultObjectError(this.getLogger(), resultObject1);
        } catch (Exception e) {
            e.printStackTrace();
            errorFlag = true;
            this.getLogger().info("[大中华人寿-" + DateUtils.dateToString(new Date()) + "]-支付回调error:" + JSON.toJSONString(e.getMessage()));
            throwsTransactionalException(this.getLogger(), e, ApplyErrorConfigEnum.APPLY_PAYMENT_HAND_PAYMENT_NOTIFY_ERROR);
        } finally {
            if (errorFlag) {
                TransactionStatus finallyTransactionStatus = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
                try {
                    //承保失败
                    if (AssertUtils.isNotEmpty(applyId)) {
                        //查询投保单
                        ApplyBo applyBo = applyBaseService.queryApply(applyId);
                        if (AssertUtils.isNotNull(applyBo)) {
                            //发送承保失败消息
                            messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.APPLY_TRANFER_POLICY_FAIL.name(), applyBo);
                            applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_FAILED.name());
                            applyBaseService.saveApply(userId, applyBo);
                        }
                        // POLICY回滚
                        policyApi.applyToPolicyRollback(applyId);
                    }
                    //提交事物
                    platformTransactionManager.commit(finallyTransactionStatus);
                } catch (Exception e) {
                    platformTransactionManager.rollback(finallyTransactionStatus);
                }
            }
        }
    }

    private void dealHcEffective(Users users, ApplyPo applyPo) {
        String applyId = applyPo.getApplyId();
        this.getLogger().info("===============暂予承保生效处理开始=============== applyId:{}", applyId);
        applyPo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPROVED_HC.name());
        applyBaseService.saveApply(users.getUserId(), applyPo);
        applyDataTransform.transferPreUnderwritingApplyToPolicy(applyId, users.getUserId(), ApplyTermEnum.YES_NO.YES.name());
        try {
            ApplyBo applyBo1 = applyBaseService.queryApply(applyId);
            //设置代理人信息
            AgentResponse agentResponse = agentApi.agentByIdGet(applyBo1.getApplyAgentBo().getAgentId()).getData();

            this.getLogger().info("团单暂予承保提醒业务员app");
            messageBusinessService.pushGroupPreMessageAgent(ApplyTermEnum.MSG_BUSINESS_TYPE.PRE_UNDERWRITING_TO_APP.name(), applyBo1, agentResponse.getUserId());

            this.getLogger().info("团单暂予承保提醒客户");
            messageBusinessService.pushGroupPreMessageCustomerSMS(ApplyTermEnum.MSG_BUSINESS_TYPE.PRE_UNDERWRITING_TO_CUSTOMER.name(), applyBo1);

            this.getLogger().info("团单暂予承保提醒钉钉微信");
            messageBusinessService.pushGroupPreMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.PRE_UNDERWRITING_ALL.name(), applyBo1);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public ResultObject<UnderwriteInfoResponse> getGroupUWInfo(String applyId) {
        ResultObject<UnderwriteInfoResponse> resultObject = new ResultObject<>();
        UnderwriteInfoResponse underwriteInfoResponse = groupApplyTransData.transferGroupApplyUW(applyId);
        resultObject.setData(underwriteInfoResponse);
        return resultObject;
    }


}
