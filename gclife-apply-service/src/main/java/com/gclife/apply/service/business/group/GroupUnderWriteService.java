package com.gclife.apply.service.business.group;

import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.request.BaseApplyUnderwriteDecisionRequest;
import com.gclife.apply.model.request.PreImageAttachmentRequest;
import com.gclife.apply.model.request.app.AppAttachmentRequest;
import com.gclife.apply.model.response.app.AppAttachmentResponse;
import com.gclife.apply.model.response.group.ApplyReviewResponse;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.policy.model.response.UnderwriteInfoResponse;

import java.util.List;

public interface GroupUnderWriteService {
    /**
     * 人工核保签收接口
     *
     * @param currentLoginUsers 当前用户
     * @param applyId           投保单ID
     * @return ResultObject
     */
    ResultObject underWriteSignPost(Users currentLoginUsers, String applyId);

    /**
     * 人工核保列表
     *
     * @param currentLoginUsers 当前用户
     * @param applyListVo       列表请求
     * @return ResultObject
     */
    ResultObject<BasePageResponse<ApplyReviewResponse>> getArtificialUnderwriting(Users currentLoginUsers, ApplyListRequest applyListVo);

    /**
     * 下发核保决定
     *
     * @param currentLoginUsers              当前用户
     * @param appRequestHandler
     * @param applyUnderwriteDecisionRequest 核保请求
     * @return ResultObject
     */
    ResultObject postSendUnderwriting(Users currentLoginUsers, AppRequestHeads appRequestHandler, BaseApplyUnderwriteDecisionRequest applyUnderwriteDecisionRequest);

    /**
     * 上传暂予承保申请书影像
     *
     * @param applyId
     * @param appAttachmentRequests
     * @return
     */
    ResultObject savePreUnderwritingImageAttachment(String applyId, List<PreImageAttachmentRequest> appAttachmentRequests);

    /**
     * 获取暂予承保申请书影像
     *
     * @param applyId
     * @return
     */
    ResultObject<List<AppAttachmentResponse>> getPreUnderwritingImageAttachment(String applyId, Users users);

    /**
     * 暂予承保生效处理
     *
     * @param users           当前用户
     * @param basePageRequest 分页
     * @return YES
     */
    String dealApprovedHcEffective(Users users, BasePageRequest basePageRequest);

    ResultObject<UnderwriteInfoResponse> getGroupUWInfo(String applyId);
}
