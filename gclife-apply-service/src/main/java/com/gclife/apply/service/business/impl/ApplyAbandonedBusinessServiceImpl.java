package com.gclife.apply.service.business.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentListResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.daos.ApplyMessageNotifyDao;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyAbandonedExtDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.config.ciq.CiqApplyTermEnum;
import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.response.ApplyAbandonedListResponse;
import com.gclife.apply.service.ApplyBaseService;
import com.gclife.apply.service.ApplyChangeBaseService;
import com.gclife.apply.service.ApplyPaymentBaseService;
import com.gclife.apply.service.ApplyPaymentTransactionBaseService;
import com.gclife.apply.service.business.ApplyAbandonedBusinessService;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.validate.parameter.transform.LanguageCodeTransData;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.payment.api.PaymentApi;
import com.gclife.platform.api.PlatformBaseInternationServiceApi;
import com.gclife.platform.api.PlatformBranchBaseApi;
import com.gclife.platform.api.PlatformInternationalBaseApi;
import com.gclife.platform.model.response.BranchResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.policy.api.PolicyApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.thirdparty.api.ThirdpartyShortUrlApi;
import com.gclife.thirdparty.model.config.ThirdpartyTermEnum;
import com.gclife.thirdparty.model.request.ShortUrlRequest;
import com.gclife.thirdparty.model.response.ShortUrlResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.request.TerminationTaskRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyErrorConfigEnum.*;
import static com.gclife.apply.model.config.ApplyTermEnum.*;

/**
 * <AUTHOR>
 * create 18-8-1
 * description:
 */
@Service
public class ApplyAbandonedBusinessServiceImpl extends BaseBusinessServiceImpl implements ApplyAbandonedBusinessService {
    @Autowired
    private ApplyAbandonedExtDao applyAbandonedExtDao;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private AgentBaseAgentApi baseAgentApi;
    @Autowired
    private LanguageCodeTransData languageCodeTransData;
    @Autowired
    private ApplyMessageNotifyDao applyMessageNotifyDao;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private ThirdpartyShortUrlApi thirdpartyShortUrlApi;
    @Autowired
    private WorkFlowApi workFlowApi;
    @Autowired
    private PaymentApi paymentApi;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private ApplyPaymentBaseService applyPaymentBaseService;
    @Autowired
    private ApplyPaymentTransactionBaseService applyPaymentTransactionBaseService;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private ApplyChangeBaseService applyChangeBaseService;

    @Override
    public ResultObject<BasePageResponse<ApplyAbandonedListResponse>> getAbandonedList(Users currentLoginUsers, AppRequestHeads appRequestHandler, ApplyListRequest applyListRequest) {
        ResultObject<BasePageResponse<ApplyAbandonedListResponse>> resultObject = new ResultObject<>();
        List<ApplyAbandonedListResponse> applyAbandonedListResponses = new ArrayList<>();
        try {
            List<BranchResponse> branchResponses = platformBranchBaseApi.queryUserOptionBranchTreeLeaf(currentLoginUsers.getUserId()).getData();
            if (!AssertUtils.isNotEmpty(branchResponses)) {
                return resultObject;
            }
            List<String> branchIds = branchResponses.stream().map(BranchResponse::getBranchId).filter(AssertUtils::isNotEmpty).collect(Collectors.toList());
            List<String> applyStatuses = this.getApplyStatuses();
            List<ApplyListBo> applyListBos = applyAbandonedExtDao.queryAbandonedList(branchIds, applyListRequest, applyStatuses);
            if (!AssertUtils.isNotEmpty(applyListBos)) {
                return resultObject;
            }
            List<String> agentIds = applyListBos.stream().map(ApplyListBo::getAgentId).filter(AssertUtils::isNotEmpty).distinct().collect(Collectors.toList());
            List<String> salesBranchIds = applyListBos.stream().map(ApplyListBo::getSalesBranchId).filter(AssertUtils::isNotEmpty).distinct().collect(Collectors.toList());

            List<AgentListResponse> agentListResponses = baseAgentApi.queryOneAgentById(agentIds).getData();
            AssertUtils.isNotEmpty(this.getLogger(), agentListResponses, APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);

            List<BranchResponse> salesBranchBases = platformBranchBaseApi.queryBranchByIds(salesBranchIds).getData();
            AssertUtils.isNotEmpty(this.getLogger(), salesBranchBases, APPLY_BUSINESS_AGENT_BRANCH_IS_NOT_FOUND_OBJECT);

            List<SyscodeRespFc> channelTypes = platformBaseInternationServiceApi.queryInternationalTerminology(TerminologyTypeEnum.CHANNEL_TYPE.name(), currentLoginUsers.getLanguage()).getData();
            List<SyscodeRespFc> premiumFrequencies = platformBaseInternationServiceApi.queryInternationalTerminology(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(), currentLoginUsers.getLanguage()).getData();
            List<SyscodeRespFc> coveragePeriodUnits = platformBaseInternationServiceApi.queryInternationalTerminology(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), currentLoginUsers.getLanguage()).getData();

            applyListBos.forEach(applyListBo -> {
                ApplyAbandonedListResponse applyAbandonedListResponse = (ApplyAbandonedListResponse) this.converterObject(applyListBo, ApplyAbandonedListResponse.class);
                //代理人数据
                agentListResponses.stream().filter(agentListResponse -> agentListResponse.getAgentId().equals(applyListBo.getAgentId())).findFirst().ifPresent((agent) -> {
                    applyAbandonedListResponse.setAgentNameCode(agent.getAgentName() + "/" + agent.getAgentCode());
                    applyAbandonedListResponse.setAgentMobile(agent.getMobile());
                });

                //国际化机构
                salesBranchBases.stream().filter(branchResponse -> branchResponse.getBranchId().equals(applyListBo.getSalesBranchId())).findFirst().ifPresent((branch) -> {
                    applyAbandonedListResponse.setSalesBranchChannelType(branch.getBranchName());
                });

                //国际化渠道类型
                if (AssertUtils.isNotEmpty(channelTypes)) {
                    channelTypes.stream().filter(syscodeRespFc -> syscodeRespFc.getCodeKey().equals(applyListBo.getChannelTypeCode())).findFirst().ifPresent((channelType) -> {
                        applyAbandonedListResponse.setSalesBranchChannelType(applyAbandonedListResponse.getSalesBranchChannelType() + "/" + channelType.getCodeName());
                    });
                }
                if (AssertUtils.isNotEmpty(premiumFrequencies)) {
                    premiumFrequencies.stream().filter(syscodeRespFc -> syscodeRespFc.getCodeKey().equals(applyListBo.getPremiumFrequency())).findFirst().ifPresent((premiumFrequency) -> {
                        applyAbandonedListResponse.setPremiumFrequency(premiumFrequency.getCodeName());
                    });
                }
                if (AssertUtils.isNotEmpty(coveragePeriodUnits)) {
                    coveragePeriodUnits.stream().filter(syscodeRespFc -> syscodeRespFc.getCodeKey().equals(applyListBo.getCoveragePeriodUnit())).findFirst().ifPresent((coveragePeriodUnit) -> {
                        applyAbandonedListResponse.setCoveragePeriod(applyListBo.getCoveragePeriod() + coveragePeriodUnit.getCodeName());
                    });
                }
                if (AssertUtils.isNotEmpty(applyListBo.getApplicantIdNo())) {
                    applyAbandonedListResponse.setApplicantIdTypeNo(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.ID_TYPE.name(), applyListBo.getApplicantIdType(), currentLoginUsers.getLanguage())
                            + "/" + applyListBo.getApplicantIdNo());
                }
                if (AssertUtils.isNotNull(applyListBo.getApplyDate())) {
                    applyAbandonedListResponse.setApplyDateFormat(DateUtils.timeStrToString(applyListBo.getApplyDate(), DateUtils.FORMATE6));
                }
                if (AssertUtils.isNotNull(applyListBo.getAbandonedDate())) {
                    applyAbandonedListResponse.setAbandonedDateFormat(DateUtils.timeStrToString(applyListBo.getAbandonedDate(), DateUtils.FORMATE6));
                    if (AssertUtils.isNotEmpty(applyListBo.getAbandonedRemark())) {
                        applyAbandonedListResponse.setAbandonedFlag(TerminologyConfigEnum.WHETHER.NO.name());
                    }
                }
                applyAbandonedListResponses.add(applyAbandonedListResponse);
            });

            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;
            //数据返回
            BasePageResponse<ApplyAbandonedListResponse> basePageResponse = BasePageResponse.getData(applyListRequest.getCurrentPage(), applyListRequest.getPageSize(), totalLine, applyAbandonedListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, APPLY_QUERY_APPLY_ABANDONED_ERROR);
        }
        return resultObject;
    }

    private List<String> getApplyStatuses() {
        return Arrays.asList(APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name(), APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE.name(),
                APPLY_STATUS_FLAG.APPLY_STATUS_ACCEPT.name(), APPLY_STATUS_FLAG.APPLY_STATUS_ACCEPT_COMPLETE.name(),
                APPLY_STATUS_FLAG.APPLY_STATUS_ACCEPT_PROBLEM.name(), APPLY_STATUS_FLAG.APPLY_STATUS_IMAGE_UPLOAD.name(),
                APPLY_STATUS_FLAG.APPLY_STATUS_IMAGE_UPLOAD_COMPLETE.name(), APPLY_STATUS_FLAG.APPLY_STATUS_IMAGE_UPLOAD_PROBLEM.name(),
                APPLY_STATUS_FLAG.APPLY_STATUS_INPUT.name(), APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_COMPLETE.name(),
                APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_PROBLEM.name(), APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW.name(),
                APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW_COMPLETE.name(), APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW_PROBLEM.name(),
                APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_AUTO.name(), APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_ARTIFICIAL.name(), APPLY_STATUS_FLAG.APPLY_STATUS_PAID_PENDING_ON_UW.name());
    }

    @Override
    @Transactional
    public ResultObject commitAbandoned(Users users, AppRequestHeads appRequestHandler, List<String> applyIds) {
        ResultObject resultObject = new ResultObject();
        try {
            AssertUtils.isNotEmpty(this.getLogger(), applyIds, APPLY_APPLY_ID_IS_NOT_NULL);
            applyIds.forEach(applyId -> {
                ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
                AssertUtils.isNotNull(this.getLogger(), applyPo, APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

                ApplyExtBo applyExtBo = (ApplyExtBo) this.converterObject(applyPo, ApplyExtBo.class);
//                if (!this.getApplyStatuses().contains(applyExtBo.getApplyStatus())) {
//                    throw new RequestException(APPLY_BUSINESS_APPLY_CAN_NOT_ABANDONED);
//                }
                ApplyAbandonedPo applyAbandonedPo = applyBaseService.queryApplyAbandoned(applyId);
                if (AssertUtils.isNotNull(applyAbandonedPo) || APPLY_STATUS_FLAG.APPLY_STATUS_ABANDONED.name().equals(applyExtBo.getApplyStatus())) {
                    throw new RequestException(APPLY_SAVE_APPLY_ABANDONED_IS_EXIST);
                }
                //数据保存
                this.transAbandoned(applyExtBo, users, ABANDONED_REMARK.ARTIFICIAL_ABANDONED.desc(), ABANDONED_TYPE.MANUAL.name(), DateUtils.getCurrentTime(), false);
            });
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, APPLY_SAVE_APPLY_ABANDONED_ERROR);
        }
        return resultObject;
    }

    private void transAbandoned(ApplyExtBo applyPo, Users users, String abandonedRemark, String abandonedType, Long abandonedDate, boolean messageFlag) {
        String applyStatus = applyPo.getApplyStatus();
        String applyId = applyPo.getApplyId();
        ApplyAbandonedPo applyAbandonedPo = applyBaseService.queryApplyAbandoned(applyId);
        if (!AssertUtils.isNotNull(applyAbandonedPo)) {
            applyAbandonedPo = new ApplyAbandonedPo();
        }
        applyAbandonedPo.setApplyId(applyId);
        applyAbandonedPo.setApplyDate(applyPo.getApplyDate());
        applyAbandonedPo.setApplyNo(applyPo.getApplyNo());
        if (AssertUtils.isNotNull(applyPo.getApplyDate())) {
            applyAbandonedPo.setApplyDateStr(DateUtils.timeStrToString(applyPo.getApplyDate(), DateUtils.FORMATE6));
        }
        String abandonedDateStr = DateUtils.timeStrToString(abandonedDate, DateUtils.FORMATE6);
        this.getLogger().info("正在作废投保单,时间{}", abandonedDateStr);
        applyAbandonedPo.setInvalidDate(abandonedDate);
        applyAbandonedPo.setInvalidDateStr(abandonedDateStr);
        applyAbandonedPo.setAbandonedDate(abandonedDate);
        applyAbandonedPo.setAbandonedRemark(abandonedRemark);
        applyAbandonedPo.setAbandonedType(abandonedType);
        String userId = users.getUserId();
        applyBaseService.saveApplyAbandoned(userId, applyAbandonedPo);

        applyPo.setApplyStatus(APPLY_STATUS_FLAG.APPLY_STATUS_ABANDONED.name());
        applyPo.setInvalidDate(DateUtils.getCurrentTime());
        applyBaseService.saveApply(userId, applyPo);

        //手动作废才结束此单工作流任务
        if (!APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name().equals(applyStatus) && ABANDONED_TYPE.MANUAL.name().equals(abandonedType)) {
            TerminationTaskRequest terminationTaskRequest = new TerminationTaskRequest();
            terminationTaskRequest.setBusinessNo(applyPo.getApplyId());
            terminationTaskRequest.setTerminationMessage(null);
            this.getLogger().info("正在结束工作流,时间{}", abandonedDateStr);
            ResultObject resultObject1 = workFlowApi.terminationTask(terminationTaskRequest);
//            AssertUtils.isResultObjectError(this.getLogger(), resultObject1);
        }
        if (messageFlag) {
            sendAbandonedMessage(applyPo.getApplyId());
        }

        //当此单已预缴保费再作废时，需要退费
        ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransactionByType(applyId, ApplyTermEnum.CHARGE_TYPE.PREPAID_PREMIUM.name());
        if (AssertUtils.isNotNull(applyPaymentTransactionBo) && ApplyTermEnum.PAYMENT_STATUS.PAYMENT_SUCCESS.name().equals(applyPaymentTransactionBo.getPaymentStatus())) {
            applyAbandonedPo.setAbandonedType(ABANDONED_TYPE.PRE_PAYMENT_REFUND.name());
            applyAbandonedPo.setAbandonedRemark(ABANDONED_REMARK.PRE_PAYMENT_REFUND.desc());
            applyBaseService.saveApplyAbandoned(userId, applyAbandonedPo);

            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AppRequestHeads appRequestHeads = new AppRequestHeads();
            appRequestHeads.setDeviceChannel("gclife_agent_app");
            applyPaymentBaseService.initiateReceipt(users, appRequestHeads, applyBo, applyPaymentTransactionBo.getPaymentAmount());
        }

        //作废掉原先未支付成功的缴费记录
        List<ApplyPaymentTransactionBo> applyPaymentTransactionBos = applyPaymentTransactionBaseService.queryApplyPaymentTransactions(applyId);
        if (AssertUtils.isNotEmpty(applyPaymentTransactionBos)) {
            List<String> paymentTypes = Arrays.asList(PAYMENT_TYPE.PAYMENT.name(), PAYMENT_TYPE.SUSPENSE_PREMIUM.name());
            applyPaymentTransactionBos.stream().filter(applyPaymentTransactionBo1 -> paymentTypes.contains(applyPaymentTransactionBo1.getFeeType())
                            && Arrays.asList(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_WAITTING.name(), PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_FINISHED.name()).contains(applyPaymentTransactionBo1.getPaymentStatus()))
                    .forEach(applyPaymentTransactionBo1 -> {
                        //作废发起支付的支付记录
                        ResultObject<Void> voidResultObject = paymentApi.updatePaymentStatus(applyPaymentTransactionBo1.getPaymentId(), PAYMENT_STATUS.PAYMENT_INVALID.name());
                        this.getLogger().info("作废发起支付的支付记录：{}", JackSonUtils.toJson(voidResultObject));
                        applyPaymentTransactionBo1.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                        applyPaymentTransactionBo1.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                        applyPaymentTransactionBo1.getApplyPaymentTransactionItemBos().forEach(applyPaymentTransactionItemBo -> {
                            applyPaymentTransactionItemBo.setPaymentStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INVALID.name());
                            applyPaymentTransactionItemBo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                        });
                        applyPaymentTransactionBaseService.saveApplyPaymentTransaction(userId, applyPaymentTransactionBo1);
                    });
        }
    }

    public void sendAbandonedMessage(String applyId) {
        try {
            //发送消息
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            //代理人
            //投保单作废时提醒
            AgentResponse applyAgentRespFc = agentApi.agentByIdGet(applyBo.getApplyAgentBo().getAgentId()).getData();
            applyBo.getApplyAgentBo().setAgentName(applyAgentRespFc.getAgentName());
            messageBusinessService.pushApplyMessageSingle(
                    MSG_BUSINESS_TYPE.NOTICE_WHEN_THE_INSURANCE_POLICY_IS_INVALID.name(),
                    applyBo, applyBo.getApplyAgentBo().getAgentId()
            );

            //推荐人
            //投保单作废时提醒推荐人
            AgentResponse applyAgentRespFc2 = agentApi.agentByIdGet(applyAgentRespFc.getRecommendAgentId()).getData();
            if (AssertUtils.isNotNull(applyAgentRespFc2)) {
                applyBo.getApplyAgentBo().setRecommendAgentName(applyAgentRespFc2.getAgentName());
                messageBusinessService.pushApplyMessageSingle(
                        MSG_BUSINESS_TYPE.NOTICE_WHEN_THE_INSURANCE_POLICY_IS_INVALID_RECOMMEND.name(),
                        applyBo, applyAgentRespFc.getRecommendAgentId());
            }
        } catch (Exception e) {
            this.getLogger().info("作废时发送消息失败");
        }
    }

    @Override
    @Transactional
    public String autoAbandoned(Users users, BasePageRequest basePageRequest) {
        try {
            //分页查出核保任务表中所有“核保通过”和“支付失败”且为标准体、次标准体的数据,是否超过7天未支付
            List<ApplyExtBo> applyExtBos = applyAbandonedExtDao.listTimeOutUnpaidApplyPo(basePageRequest);

            if (AssertUtils.isNotEmpty(applyExtBos)) {
                applyExtBos.stream().distinct().forEach(applyExtBo -> {
                    //无支付数据时 执行作废  有支付数据时由财务回调作废
                    this.getLogger().info("正在执行投保单作废");
                    long abandonedDate = this.getAbandonedDate(applyExtBo.getApplyId(), applyExtBo.getApplyUnderwriteDecisionDate());
                    if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyExtBo.getProductId())) {
                        abandonedDate = DateUtils.addStringDayRT(applyExtBo.getApplyUnderwriteDecisionDate(), 30);
                    }
                    this.transAbandoned(applyExtBo, users,
                            ABANDONED_REMARK.OVERDUE_PAYMENT.desc(),
                            ABANDONED_TYPE.AUTO.name(),
                            abandonedDate,
                            true
                    );

                    // 查询投保单数据
                    ApplyBo applyBo = applyBaseService.queryApply(applyExtBo.getApplyId());
                    Optional<ApplyCoverageBo> first = applyBo.getListInsuredCoverage().stream().filter(applyCoverageBo ->
                            applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())).distinct().findFirst();
                    if (first.isPresent() && ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(first.get().getProductId())) {
                        policyApi.updatePrePolicyStatus(applyExtBo.getApplyId(), YES_NO.NO.name());
                    }
                });

            }

            if (AssertUtils.isNotEmpty(applyExtBos) && applyExtBos.size() == basePageRequest.getPageSize()) {
                return TerminologyConfigEnum.WHETHER.NO.name();
            } else {
                return TerminologyConfigEnum.WHETHER.YES.name();
            }
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return null;
    }

    public long getAbandonedDate(String applyId, long applyUnderwriteDecisionDate) {
        //作废时间默认+7天   5.8.3 作废时间默认14天
        final long[] abandonedDate = {DateUtils.addStringDayRT(applyUnderwriteDecisionDate, 14)};
        ApplyChangePo applyChangePo = applyChangeBaseService.queryApplyChangePo(null, applyId);
        if (AssertUtils.isNotNull(applyChangePo)) {
            List<ApplyChangeRecordPo> applyChangeRecordPos = applyChangeBaseService.queryApplyChangeRecordPo(applyChangePo.getApplyChangeId());
            if (AssertUtils.isNotEmpty(applyChangeRecordPos)) {
                //若存在已变更的投保单变更延长支付时间的数据，则设置对应的作废时间
                applyChangeRecordPos.stream().filter(applyChangeRecordPo -> CHANGE_STATUS.CHANGED.name().equals(applyChangeRecordPo.getChangeStatus())
                        && ApplyTermEnum.CHANGE_TYPE.EXTENDED_PAYMENT_DUE_TIME.name().equals(applyChangeRecordPo.getChangeType()))
                        .findFirst().ifPresent(applyChangeRecordPo -> {
                    if (ApplyTermEnum.EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_ONE.name().equals(applyChangeRecordPo.getChangeTypeValue())) {
                        abandonedDate[0] = DateUtils.addStringDayRT(abandonedDate[0], 7);
                    }
                    if (EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_TWO.name().equals(applyChangeRecordPo.getChangeTypeValue())) {
                        abandonedDate[0] = DateUtils.addStringDayRT(abandonedDate[0], 14);
                    }
                });
            }
        }
        return abandonedDate[0];
    }

    @Override
    @Transactional
    public ResultObject paymentCommitAbandoned(Users users, String applyId) {
        ResultObject resultObject = new ResultObject();

        ApplyExtBo applyExtBo = applyAbandonedExtDao.oneTimeOutUnpaidApplyPo(applyId);
        if (AssertUtils.isNotNull(applyExtBo)) {
            this.getLogger().info("正在执行作废投保单（财务服务调用）,投保单ID{}", applyId);
            long abandonedDate = this.getAbandonedDate(applyExtBo.getApplyId(), applyExtBo.getApplyUnderwriteDecisionDate());
            if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyExtBo.getProductId())) {
                abandonedDate = DateUtils.addStringDayRT(applyExtBo.getApplyUnderwriteDecisionDate(), 30);
            }
            this.transAbandoned(applyExtBo, users, ABANDONED_REMARK.OVERDUE_PAYMENT.desc(), ABANDONED_TYPE.AUTO.name(),
                    abandonedDate, true);
        }

        return resultObject;
    }


    @Override
    public String applyInvalidRemindMessage(BasePageRequest basePageRequest) {

        List<ApplyExtBo> applyPos = applyAbandonedExtDao.applyInvalidRemindMessage(basePageRequest);
        if (!AssertUtils.isNotEmpty(applyPos)) {
            return TerminologyConfigEnum.WHETHER.YES.name();
        }
        //代理人
        List<String> agentIdList = applyPos.stream().map(ApplyExtBo::getAgentId).distinct().collect(Collectors.toList());
        ResultObject<List<AgentListResponse>> agentListResultObject = baseAgentApi.queryOneAgentById(agentIdList);
        AssertUtils.isResultObjectError(this.getLogger(), agentListResultObject);
        List<AgentListResponse> agentList = agentListResultObject.getData();

        List<ApplyMessageNotifyPo> applyMessageNotifyPoList = new ArrayList<>();
        applyPos.forEach(applyExtBo -> {
            //数据组装
            Map<String, String> messageParamMap = new HashMap<>();
            messageParamMap.put("applyNo", applyExtBo.getApplyNo());
            messageParamMap.put("productName", applyExtBo.getProductName());
            messageParamMap.put("applicantName", applyExtBo.getApplicantName());
            String applyId = applyExtBo.getApplyId();
            String abandonedDateFormat = DateUtils.timeStrToString(this.getAbandonedDate(applyId, applyExtBo.getApplyUnderwriteDecisionDate()), DateUtils.FORMATE18);
            if (Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(applyExtBo.getProductId())) {
                abandonedDateFormat = DateUtils.timeStrToString(DateUtils.addStringDayRT(applyExtBo.getApplyUnderwriteDecisionDate(), 30), DateUtils.FORMATE18);
            }
            messageParamMap.put("abandonedDate", abandonedDateFormat);
            Optional<AgentListResponse> optionalAgent = agentList.stream().filter(agentListRespFc -> applyExtBo.getAgentId().equals(agentListRespFc.getAgentId())).findFirst();
            if (optionalAgent.isPresent()) {
                AgentListResponse agentListResponse = optionalAgent.get();
                messageParamMap.put("agentName", agentListResponse.getAgentName());

                ApplyPaymentTransactionBo applyPaymentTransactionBo = applyPaymentTransactionBaseService.queryApplyPaymentTransaction(applyId);
                ShortUrlRequest shortUrlRequest = new ShortUrlRequest();
                shortUrlRequest.setShortType(ThirdpartyTermEnum.SHORT_TYPE.PAYMENT_GUIDE.name());
                if (AssertUtils.isNotNull(applyPaymentTransactionBo)) {
                    messageParamMap.put("receivablePremium", applyPaymentTransactionBo.getPaymentAmount() + "");
                    if (AssertUtils.isNotNull(applyPaymentTransactionBo.getPaymentId())) {
                        shortUrlRequest.setBusinessId(applyPaymentTransactionBo.getPaymentId());
                    }
                }

                getLogger().info("投保单ID：{}，获取短链接请求参数：{}", applyId, JSON.toJSONString(shortUrlRequest));
                ResultObject<ShortUrlResponse> resultObject = thirdpartyShortUrlApi.queryShortUrl(shortUrlRequest);
                getLogger().info("投保单ID：{}，获取短链接返回结果：{}", applyId, JSON.toJSONString(resultObject));
                if (!AssertUtils.isResultObjectDataNull(resultObject)) {
                    messageParamMap.put("paymentUrl", resultObject.getData().getFullUrl());
                }

                //发送消息 代理人
                messageBusinessService.applyInvalidRemindMessage(
                        MSG_BUSINESS_TYPE.REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_AGENT.name(),
                        agentListResponse.getAgentId(), messageParamMap
                );
                //发送消息 客户
                messageBusinessService.applyInvalidRemindMessageCustomer(
                        MSG_BUSINESS_TYPE.REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_CUSTOMER.name(),
                        applyExtBo.getApplicantMobile(), messageParamMap
                );

                this.packageApplyMessageNotifyPo(applyMessageNotifyPoList, applyExtBo, messageParamMap, agentListResponse);
            }
        });


        //保存发送记录
        applyMessageNotifyDao.insert(applyMessageNotifyPoList);

        if (AssertUtils.isNotEmpty(applyPos) && applyPos.size() == basePageRequest.getPageSize()) {
            return TerminologyConfigEnum.WHETHER.NO.name();
        } else {
            return TerminologyConfigEnum.WHETHER.YES.name();
        }
    }

    /**
     * @param applyMessageNotifyPoList
     * @param applyExtBo
     * @param messageParamMap
     * @param agentListResponse
     */
    private void packageApplyMessageNotifyPo(List<ApplyMessageNotifyPo> applyMessageNotifyPoList, ApplyExtBo applyExtBo, Map<String, String> messageParamMap, AgentListResponse agentListResponse) {
        ApplyMessageNotifyPo agentApplyMessageNotifyPo = new ApplyMessageNotifyPo();
        agentApplyMessageNotifyPo.setApplyMessageNotifyId(UUIDUtils.getUUIDShort());
        agentApplyMessageNotifyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        agentApplyMessageNotifyPo.setApplyId(applyExtBo.getApplyId());
        agentApplyMessageNotifyPo.setNotifyDate(DateUtils.getCurrentTime());
        agentApplyMessageNotifyPo.setNotifyStatus(CiqApplyTermEnum.APPLY_STATUS.SUCCESS.name());
        agentApplyMessageNotifyPo.setNotifyCustomerId(agentListResponse.getAgentId());
        agentApplyMessageNotifyPo.setNotifyType(MSG_BUSINESS_TYPE.REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_AGENT.name());
        agentApplyMessageNotifyPo.setNotifyData(JSON.toJSONString(messageParamMap));
        applyMessageNotifyPoList.add(agentApplyMessageNotifyPo);

        ApplyMessageNotifyPo recommendAgentApplyMessageNotifyPo = new ApplyMessageNotifyPo();
        recommendAgentApplyMessageNotifyPo.setApplyMessageNotifyId(UUIDUtils.getUUIDShort());
        recommendAgentApplyMessageNotifyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        recommendAgentApplyMessageNotifyPo.setApplyId(applyExtBo.getApplyId());
        recommendAgentApplyMessageNotifyPo.setNotifyDate(DateUtils.getCurrentTime());
        recommendAgentApplyMessageNotifyPo.setNotifyStatus(CiqApplyTermEnum.APPLY_STATUS.SUCCESS.name());
        recommendAgentApplyMessageNotifyPo.setNotifyCustomerId(applyExtBo.getApplicantMobile());
        recommendAgentApplyMessageNotifyPo.setNotifyType(MSG_BUSINESS_TYPE.REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_CUSTOMER.name());
        recommendAgentApplyMessageNotifyPo.setNotifyData(JSON.toJSONString(messageParamMap));
        applyMessageNotifyPoList.add(recommendAgentApplyMessageNotifyPo);
    }

    @Override
    public ResultObject<List<SyscodeResponse>> getApplyAbandonedTypeList(Users users) {
        ResultObject<List<SyscodeResponse>> resultObject = new ResultObject<>();
        List<SyscodeResponse> responseList = platformInternationalBaseApi.queryInternational(TerminologyTypeEnum.ABANDONED_TYPE.name(), users.getLanguage()).getData();
        resultObject.setData(responseList);
        return resultObject;
    }
}
