package com.gclife.apply.service.business.group.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.request.AgentApplyQueryRequest;
import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentKeyWordResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.daos.ApplyInsuredPrintDetailDao;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyBaseDao;
import com.gclife.apply.model.ApplyInsuredDoneBo;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.group.*;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.GroupErrorConfigEnum;
import com.gclife.apply.model.request.GroupApplyInsuredPrintRequest;
import com.gclife.apply.model.request.group.GroupInsuredRequest;
import com.gclife.apply.model.request.group.ImportInsuredRequest;
import com.gclife.apply.model.request.group.InsuredRequest;
import com.gclife.apply.model.response.*;
import com.gclife.apply.model.response.group.*;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.group.InsuredService;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.InsuredInventoryValidate;
import com.gclife.apply.validate.parameter.group.GroupInputParameterValidate;
import com.gclife.apply.validate.parameter.group.LanguageUtils;
import com.gclife.apply.validate.parameter.transform.GroupApplyTransData;
import com.gclife.apply.validate.parameter.transform.LanguageCodeTransData;
import com.gclife.attachment.api.AttachmentApi;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.policy.policy.group.GroupAttachInsuredCoverageLevelBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.InternationalTypeEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.*;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.model.response.PaymentStatusResponse;
import com.gclife.platform.api.*;
import com.gclife.platform.model.response.AreaNameResponse;
import com.gclife.platform.model.response.CareerResponse;
import com.gclife.platform.model.response.SyscodeResponse;
import com.gclife.product.model.config.ProductTermEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyErrorConfigEnum.*;
import static com.gclife.apply.model.config.ApplyTermEnum.PRODUCT_LEVEL_29.*;
import static com.gclife.apply.model.config.GroupErrorConfigEnum.*;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.GROUP_INSURED_LIST;

/**
 * <AUTHOR>
 * create 18-5-4
 * description:
 */
@Slf4j
@Service
public class InsuredServiceImpl extends BaseBusinessServiceImpl implements InsuredService {
    @Autowired
    private AttachmentApi attachmentApi;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private GroupInputParameterValidate groupInputParameterValidate;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyInsuredBaseService applyInsuredBaseService;
    @Autowired
    private ApplyBaseDao applyBaseDao;
    @Autowired
    private ApplyRemarkBaseService applyRemarkBaseService;
    @Autowired
    private InsuredInventoryValidate insuredInventoryValidate;
    @Autowired
    private GroupApplyTransData groupApplyTransData;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private ApplyInsuredPrintDetailDao applyInsuredPrintDetailDao;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyBeneficiaryBaseService applyBeneficiaryBaseService;
    @Autowired
    private ApplyAgentBaseService applyAgentBaseService;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private LanguageCodeTransData languageCodeTransData;
    @Autowired
    private PlatformTerminologyBaseApi platformTerminologyBaseApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private PaymentBaseApi paymentBaseApi;

    @Override
    public ResultObject<InsuredInventoryResponse> getInsured(String applyId, Users users) {
        ResultObject resultObject = this.getApplyInsured(applyId, null, users);
        return resultObject;
    }

    /**
     * 批次导入被保人清单
     *
     * @param importInsuredRequest
     * @param users
     * @return
     */
    @Override
    @Transactional
    public ResultObject<ImportInsuredResponse> importInsured(ImportInsuredRequest importInsuredRequest, Users users) {
        ResultObject<ImportInsuredResponse> resultObject = new ResultObject<>();
        try {
            getLogger().info("importInsuredResponse" + JSON.toJSONString(importInsuredRequest));
            String applyId = importInsuredRequest.getApplyId();
            String attachmentId = importInsuredRequest.getAttachmentId();
            AssertUtils.isNotEmpty(getLogger(), applyId, APPLY_APPLY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(getLogger(), attachmentId, GROUP_APPLY_SAVE_INSURED_ATTACHMENT_ID_IS_NOT_NULL);
            AttachmentByteResponse attachmentByteRespFc = attachmentApi.attachmentByteGet(attachmentId).getData();
            AssertUtils.isNotNull(getLogger(), attachmentByteRespFc, GROUP_APPLY_QUERY_ATTACHMENT_ERROR);
            byte[] fileByte = attachmentByteRespFc.getFileByte();
            //由输入流得到工作簿
            InputStream inputStream = new ByteArrayInputStream(fileByte);
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            List<ApplyInsuredUploadPo> insuredUploadPos = new ArrayList<>();
            List<ApplyContactUploadPo> contactUploadPos = new ArrayList<>();
            List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos = new ArrayList<>();

            String templateCode = groupApplyTransData.getGroupInsuredTemplate(applyId);
            if (ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE_V2021.name().equals(templateCode)) {
                this.trans17InsuredData(workbook, insuredUploadPos, contactUploadPos, beneficiaryUploadPos);
            } else if (ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE_PRO29.name().equals(templateCode)) {
                this.trans29InsuredData(workbook, insuredUploadPos, contactUploadPos, beneficiaryUploadPos);
            } else {
                this.trans1PlusInsuredData(workbook, insuredUploadPos, beneficiaryUploadPos);
            }

            inputStream.close();
            //EXCEL表格无数据
            AssertUtils.isNotEmpty(this.getLogger(), insuredUploadPos, GROUP_APPLY_SAVE_IMPORT_INSURED_IS_NOT_NULL);

            // 保存批次表
            ApplyInsuredBatchPo applyInsuredBatchPo = new ApplyInsuredBatchPo();
            applyInsuredBatchPo.setApplyId(applyId);
            applyInsuredBatchPo.setAttachmentId(attachmentId);
            applyInsuredBaseService.saveApplyInsuredBatch(users.getUserId(), applyInsuredBatchPo);
            // 校验导入的数据并设置校验结果
            insuredInventoryValidate.insuredValidate(insuredUploadPos, beneficiaryUploadPos, templateCode);

            insuredUploadPos.forEach(insuredUploadPo -> {
                insuredUploadPo.setApplyId(applyId);
                insuredUploadPo.setInsuredBatchId(applyInsuredBatchPo.getInsuredBatchId());
            });
            // 批量新增被保人上传表
            applyInsuredBaseService.addApplyInsuredUpload(insuredUploadPos, users.getUserId());
            // 批量新增受益人上传表
            beneficiaryUploadPos.forEach(beneficiaryUploadPo -> {
                beneficiaryUploadPo.setApplyId(applyId);
                beneficiaryUploadPo.setInsuredBatchId(applyInsuredBatchPo.getInsuredBatchId());
            });
            applyBeneficiaryBaseService.addApplyBeneficiaryUpload(beneficiaryUploadPos, users.getUserId());

            //批量保存合同信息上传表
            contactUploadPos.forEach(applyContactUploadPo -> {
                applyContactUploadPo.setApplyId(applyId);
                applyContactUploadPo.setInsuredBatchId(applyInsuredBatchPo.getInsuredBatchId());
            });
            applyInsuredBaseService.saveApplyContactUpload(contactUploadPos, users.getUserId());

            // 组装返回数据
            int successNum = 0;
            int failureNum = 0;
            for (ApplyInsuredUploadPo applyInsuredUploadPo : insuredUploadPos) {
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(applyInsuredUploadPo.getCheckResult())) {
                    successNum++;
                } else {
                    failureNum++;
                }
            }
            ImportInsuredResponse importInsuredResponse = new ImportInsuredResponse();
            importInsuredResponse.setErrorNum(failureNum);
            importInsuredResponse.setSuccessNum(successNum);
            importInsuredResponse.setInsuredBatchId(applyInsuredBatchPo.getInsuredBatchId());

            resultObject.setData(importInsuredResponse);
        } catch (Exception e) {
            e.printStackTrace();
            this.setResultObjectException(this.getLogger(), resultObject, e, GROUP_APPLY_SAVE_IMPORT_INSURED_ERROR);
        }
        return resultObject;
    }

    private int getLastNotBlankRowNum(XSSFSheet xssfSheet) {
        int rowNum = xssfSheet.getLastRowNum();
        int lastNotBlankRowNum = 3;
        for (int i = 3; i < rowNum; i++) {
            //得到行,0表示第一行
            Row row = xssfSheet.getRow(i);
            // 第一列从0开始计算, 被保人姓名和证件号码都为空则读取结束
            if (!AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 1)) && !AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 2))) {
                break;
            }
            lastNotBlankRowNum++;
        }
        return lastNotBlankRowNum;
    }

    private void trans1PlusInsuredData(XSSFWorkbook workbook, List<ApplyInsuredUploadPo> insuredUploadPos, List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos) {
        //得到工作表
        XSSFSheet sheet = workbook.getSheetAt(0);
        Row head = sheet.getRow(2);
        int lastCellNum = head.getLastCellNum();
        int lastNotBlankRowNum = this.getLastNotBlankRowNum(sheet);
        for (int i = 3; i <= lastNotBlankRowNum; i++) {
            //得到行,0表示第一行
            Row row = sheet.getRow(i);
            // 第一列从0开始计算, 被保人姓名和证件号码都为空则读取结束
            if (!AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 1)) && !AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 3))) {
                break;
            }
            // 设置被保人数据
            ApplyInsuredUploadPo insuredUploadPo = groupApplyTransData.getInsuredUploadData(row);
            insuredUploadPo.setInsuredUploadId(UUIDUtils.getUUIDShort());
            insuredUploadPo.setForceSave(true);
            insuredUploadPos.add(insuredUploadPo);
            // 设置受益人数据
            List<ApplyBeneficiaryUploadPo> beneficiaryUploads = groupApplyTransData.getBeneficiaryUploadData(row, lastCellNum, insuredUploadPo.getInsuredUploadId(), 35);
            if (AssertUtils.isNotEmpty(beneficiaryUploads)) {
                beneficiaryUploadPos.addAll(beneficiaryUploads);
            }
        }
    }

    private void trans29InsuredData(XSSFWorkbook workbook, List<ApplyInsuredUploadPo> insuredUploadPos, List<ApplyContactUploadPo> contactUploadPos, List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos) {
        //第一个sheet页  合同信息
        XSSFSheet sheet1 = workbook.getSheetAt(0);
        ApplyContactUploadPo contactUploadData = groupApplyTransData.get29ContactUploadData(sheet1);
        contactUploadPos.add(contactUploadData);

        //第二个sheet页  被保人及受益人信息
        XSSFSheet sheet2 = workbook.getSheetAt(1);
        Row head = sheet2.getRow(2);
        int lastCellNum = head.getLastCellNum();
        int lastNotBlankRowNum = this.getLastNotBlankRowNum(sheet2);
        for (int i = 3; i <= lastNotBlankRowNum; i++) {
            //得到行,0表示第一行
            Row row = sheet2.getRow(i);
            // 第一列从0开始计算, 被保人姓名和证件号码都为空则读取结束
            if (!AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 1)) && !AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 2))) {
                break;
            }
            // 设置被保人数据
            ApplyInsuredUploadPo insuredUploadPo = groupApplyTransData.getInsuredUploadData29(row);
            insuredUploadPo.setInsuredUploadId(UUIDUtils.getUUIDShort());
            insuredUploadPo.setForceSave(true);
            insuredUploadPos.add(insuredUploadPo);
            // 设置受益人数据
            List<ApplyBeneficiaryUploadPo> beneficiaryUploads = groupApplyTransData.getBeneficiaryUploadData29(row, lastCellNum, insuredUploadPo.getInsuredUploadId(), 17);
            if (AssertUtils.isNotEmpty(beneficiaryUploads)) {
                beneficiaryUploadPos.addAll(beneficiaryUploads);
            }
        }
    }

    private void trans17InsuredData(XSSFWorkbook workbook, List<ApplyInsuredUploadPo> insuredUploadPos, List<ApplyContactUploadPo> contactUploadPos, List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos) {
        //第一个sheet页  合同信息
        XSSFSheet sheet1 = workbook.getSheetAt(0);
        ApplyContactUploadPo contactUploadData = groupApplyTransData.get17ContactUploadData(sheet1);
        contactUploadPos.add(contactUploadData);

        //第二个sheet页  被保人及受益人信息
        XSSFSheet sheet2 = workbook.getSheetAt(1);
        Row head = sheet2.getRow(2);
        int lastCellNum = head.getLastCellNum();
        int lastNotBlankRowNum = this.getLastNotBlankRowNum(sheet2);
        for (int i = 3; i <= lastNotBlankRowNum; i++) {
            //得到行,0表示第一行
            Row row = sheet2.getRow(i);
            // 第一列从0开始计算, 被保人姓名和证件号码都为空则读取结束
            if (!AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 1)) && !AssertUtils.isNotEmpty(groupApplyTransData.getCellData(row, 2))) {
                break;
            }
            // 设置被保人数据
            ApplyInsuredUploadPo insuredUploadPo = groupApplyTransData.getInsuredUploadData17(row);
            insuredUploadPo.setInsuredUploadId(UUIDUtils.getUUIDShort());
            insuredUploadPo.setForceSave(true);
            insuredUploadPos.add(insuredUploadPo);
            // 设置受益人数据
            List<ApplyBeneficiaryUploadPo> beneficiaryUploads = groupApplyTransData.getBeneficiaryUploadData(row, lastCellNum, insuredUploadPo.getInsuredUploadId(), 29);
            if (AssertUtils.isNotEmpty(beneficiaryUploads)) {
                beneficiaryUploadPos.addAll(beneficiaryUploads);
            }
        }
    }

    /**
     * 导出上传被保人清单
     *
     * @param response
     * @param insuredBatchId 上传批次
     * @return
     */
    @SneakyThrows
    @Override
    public void exportInsured(HttpServletResponse response, String insuredBatchId) {
        getLogger().info("insuredBatchId" + insuredBatchId);
        List<ApplyInsuredUploadBo> applyInsuredUploadBos = applyInsuredBaseService.listApplyInsuredUploadByInsuredBatchId(insuredBatchId);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredUploadBos, GROUP_APPLY_BUSINESS_IMPORT_INSURED_NOT_FOUND);
        String applyId = applyInsuredUploadBos.get(0).getApplyId();

        String templateCode = groupApplyTransData.getGroupInsuredTemplate(applyId);
        //由输入流得到工作簿
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet(templateCode);
        URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
        InputStream inputStream = url.openStream();
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        // #17模板
        if (ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE_V2021.name().equals(templateCode)) {
            this.get17Excel(insuredBatchId, applyInsuredUploadBos, workbook);
        } else if (ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE_PRO29.name().equals(templateCode)) {
            // #29模板
            this.get29Excel(insuredBatchId, applyInsuredUploadBos, workbook);
        } else {
            this.get1PlusExcel(insuredBatchId, applyInsuredUploadBos, workbook);
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/x-download");
        response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("被保人清单.xlsm", "UTF-8"));
        OutputStream outputStream = response.getOutputStream();
        outputStream.write(byteArrayOutputStream.toByteArray());
        outputStream.close();
        inputStream.close();
        byteArrayOutputStream.close();
    }

    private void get17Excel(String insuredBatchId, List<ApplyInsuredUploadBo> applyInsuredUploadBos, XSSFWorkbook workbook) {
        try {
            //查询合同信息
            ApplyContactUploadPo applyContactUploadPo = applyInsuredBaseService.getApplyContactUploadPo(insuredBatchId);
            if (AssertUtils.isNotNull(applyContactUploadPo)) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                groupApplyTransData.setContactData17(applyContactUploadPo, sheet);
            }

            //得到被保人清单工作表
            XSSFSheet sheet1 = workbook.getSheetAt(1);
            // 根据批次ID查询受益人导入清单
            List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos = applyBeneficiaryBaseService.listApplyBeneficiaryUploadByBatchId(insuredBatchId);
            Map<String, List<ApplyBeneficiaryUploadPo>> beneficiaryMap = beneficiaryUploadPos.stream().collect(Collectors.groupingBy(ApplyBeneficiaryUploadPo::getInsuredUploadId));
            applyInsuredUploadBos.forEach(insuredUploadBo -> insuredUploadBo.setListBeneficiaryUpload(beneficiaryMap.get(insuredUploadBo.getInsuredUploadId())));
            // 一个被保人最大受益人数
            long maxBeneficiaryNum = 5;
            if (AssertUtils.isNotEmpty(beneficiaryUploadPos)) {
                maxBeneficiaryNum = beneficiaryUploadPos.stream().mapToLong(ApplyBeneficiaryUploadPo::getBeneficiaryIndex).max().getAsLong();
            }
            int templateColumn = 74;
            // 设置表头
            groupApplyTransData.setHeadTitle(sheet1, maxBeneficiaryNum, templateColumn);
            for (int i = 0; i < applyInsuredUploadBos.size(); i++) {
                ApplyInsuredUploadBo insuredUploadBo = applyInsuredUploadBos.get(i);
                Row writeRow = sheet1.getRow(i + 3);
                if (!AssertUtils.isNotNull(writeRow)) {
                    writeRow = sheet1.createRow(i + 3);
                }
                // 设置行数据
                groupApplyTransData.setRowDataV2021(insuredUploadBo, writeRow, maxBeneficiaryNum, templateColumn);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void get29Excel(String insuredBatchId, List<ApplyInsuredUploadBo> applyInsuredUploadBos, XSSFWorkbook workbook) {
        try {
            //查询合同信息
            ApplyContactUploadPo applyContactUploadPo = applyInsuredBaseService.getApplyContactUploadPo(insuredBatchId);
            if (AssertUtils.isNotNull(applyContactUploadPo)) {
                XSSFSheet sheet = workbook.getSheetAt(0);
                groupApplyTransData.setContactData29(applyContactUploadPo, sheet, workbook);
            }

            //得到被保人清单工作表
            XSSFSheet sheet1 = workbook.getSheetAt(1);
            // 根据批次ID查询受益人导入清单
            List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos = applyBeneficiaryBaseService.listApplyBeneficiaryUploadByBatchId(insuredBatchId);
            Map<String, List<ApplyBeneficiaryUploadPo>> beneficiaryMap = beneficiaryUploadPos.stream().collect(Collectors.groupingBy(ApplyBeneficiaryUploadPo::getInsuredUploadId));
            applyInsuredUploadBos.forEach(insuredUploadBo -> insuredUploadBo.setListBeneficiaryUpload(beneficiaryMap.get(insuredUploadBo.getInsuredUploadId())));
            // 一个被保人最大受益人数
            long maxBeneficiaryNum = 2;
            if (AssertUtils.isNotEmpty(beneficiaryUploadPos)) {
                maxBeneficiaryNum = beneficiaryUploadPos.stream().mapToLong(ApplyBeneficiaryUploadPo::getBeneficiaryIndex).max().getAsLong();
            }
            for (int i = 0; i < applyInsuredUploadBos.size(); i++) {
                ApplyInsuredUploadBo insuredUploadBo = applyInsuredUploadBos.get(i);
                Row writeRow = sheet1.getRow(i + 3);
                if (!AssertUtils.isNotNull(writeRow)) {
                    writeRow = sheet1.createRow(i + 3);
                }
                // 设置行数据
                groupApplyTransData.setRowData29(insuredUploadBo, writeRow, maxBeneficiaryNum, 23);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void get1PlusExcel(String insuredBatchId, List<ApplyInsuredUploadBo> applyInsuredUploadBos, XSSFWorkbook workbook) {
        //得到工作表
        XSSFSheet sheet = workbook.getSheetAt(0);
        try {
            // 根据批次ID查询受益人导入清单
            List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos = applyBeneficiaryBaseService.listApplyBeneficiaryUploadByBatchId(insuredBatchId);
            Map<String, List<ApplyBeneficiaryUploadPo>> beneficiaryMap = beneficiaryUploadPos.stream().collect(Collectors.groupingBy(ApplyBeneficiaryUploadPo::getInsuredUploadId));
            applyInsuredUploadBos.forEach(insuredUploadBo -> insuredUploadBo.setListBeneficiaryUpload(beneficiaryMap.get(insuredUploadBo.getInsuredUploadId())));
            // 一个被保人最大受益人数
            long maxBeneficiaryNum = 3;
            if (AssertUtils.isNotEmpty(beneficiaryUploadPos)) {
                maxBeneficiaryNum = beneficiaryUploadPos.stream().mapToLong(ApplyBeneficiaryUploadPo::getBeneficiaryIndex).max().getAsLong();
            }
            int templateColumn = 62;
            // 设置表头
            groupApplyTransData.setHeadTitle(sheet, maxBeneficiaryNum, templateColumn);
            for (int i = 0; i < applyInsuredUploadBos.size(); i++) {
                ApplyInsuredUploadBo insuredUploadBo = applyInsuredUploadBos.get(i);
                Row writeRow = sheet.getRow(i + 3);
                if (!AssertUtils.isNotNull(writeRow)) {
                    writeRow = sheet.createRow(i + 3);
                }
                // 设置行数据
                groupApplyTransData.setRowData(insuredUploadBo, writeRow, maxBeneficiaryNum, templateColumn);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取去编号
     *
     * @param no
     * @return
     */
    private String getNo(int no) {
        String stringNo = no + "";
        int length = 3 - stringNo.length();
        for (int i = 0; i < length; i++) {
            stringNo = "0" + stringNo;
        }
        return stringNo;
    }


    /**
     * 添加被保人信息(2019-08-13 by liaobinbin)
     *
     * @param users               用户
     * @param groupInsuredRequest 请求数据
     * @return
     */
    @Override
    @Transactional
    public ResultObject saveInsured(Users users, GroupInsuredRequest groupInsuredRequest) {
        ResultObject resultObject = new ResultObject();
        try {
            getLogger().info("groupInsuredRequest:::::" + JSON.toJSONString(groupInsuredRequest));

            String applyId = groupInsuredRequest.getApplyId();
            // 查询投保单
            ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
            AssertUtils.isNotNull(getLogger(), applyPo, GROUP_APPLY_QUERY_APPLY_IS_NOT_NULL);
            // 查询投保单公共险种
            List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.listApplyCoverage(applyId);
            AssertUtils.isNotEmpty(getLogger(), applyCoveragePos, GROUP_APPLY_BUSINESS_COVERAGE_IS_NOT_NULL);
            String productId = applyCoveragePos.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                    .map(ApplyCoveragePo::getProductId).findFirst().get();

            // 参数校验
            groupInputParameterValidate.validParameterInsuredRequests(groupInsuredRequest, productId);
            List<InsuredRequest> listInsured = groupInsuredRequest.getListInsured();
            String insuredBatchId = groupInsuredRequest.getInsuredBatchId();

            if (AssertUtils.isNotEmpty(insuredBatchId)) {
                // 模板导入
                this.saveInsuredOfTemplateImport(insuredBatchId, applyCoveragePos, applyPo, users);
            } else if (AssertUtils.isNotEmpty(listInsured)) {
                // 手工录入
                saveInsuredManualInput(listInsured, applyCoveragePos, applyId, users);
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, GROUP_APPLY_SAVE_INSURED_ERROR);
        }
        return resultObject;
    }

    /**
     * 保存模板导入被保人
     *
     * @param insuredBatchId   导入批次
     * @param applyCoveragePos 公共险种
     * @param applyPo          投保单信息
     * @param users            用户
     */
    private void saveInsuredOfTemplateImport(String insuredBatchId, List<ApplyCoveragePo> applyCoveragePos, ApplyPo applyPo, Users users) {
        // 1.查询当前投保单被保人清单
        String applyId = applyPo.getApplyId();
        List<ApplyInsuredBo> originalInsuredBos = applyInsuredBaseService.listApplyInsured(applyId);
        // 待新增健康告知
        List<ApplyGroupHealthQuestionnaireAnswerPo> newHealthAnswerPos = new ArrayList<>();
        // 将原有的公共险种对应产品ID暂存
        List<String> productIds = applyCoveragePos.stream().map(ApplyCoveragePo::getProductId).collect(Collectors.toList());

        // 根据批次ID查询被保人清单上传表
        List<ApplyInsuredUploadBo> applyInsuredUploadBos = applyInsuredBaseService.listApplyInsuredUploadByInsuredBatchId(insuredBatchId);
        AssertUtils.isNotEmpty(getLogger(), applyInsuredUploadBos, GROUP_APPLY_BUSINESS_IMPORT_INSURED_NOT_FOUND);
        // 根据批次ID查询受益人导入清单
        List<ApplyBeneficiaryUploadPo> beneficiaryUploadPos = applyBeneficiaryBaseService.listApplyBeneficiaryUploadByBatchId(insuredBatchId);
        AssertUtils.isNotEmpty(getLogger(), beneficiaryUploadPos, GROUP_APPLY_BUSINESS_IMPORT_BENEFICIARY_NOT_FOUND);
        // 组装被保人、受益人完成数据
        List<ApplyInsuredDoneBo> insuredDoneBos = groupApplyTransData.transApplyInsuredDone(applyInsuredUploadBos, beneficiaryUploadPos);

        //查询合同信息
        ApplyContactUploadPo applyContactUploadPo = applyInsuredBaseService.getApplyContactUploadPo(insuredBatchId);
        ApplyContactDonePo applyContactDonePo = null;
        if (AssertUtils.isNotNull(applyContactUploadPo)) {
            applyContactDonePo = new ApplyContactDonePo();
            ClazzUtils.copyPropertiesIgnoreNull(applyContactUploadPo, applyContactDonePo);
            for (ApplyTermEnum.DISCOUNT_TYPE discount_type : ApplyTermEnum.DISCOUNT_TYPE.values()) {
                if (discount_type.code().equalsIgnoreCase(applyContactUploadPo.getDiscountType())) {
                    applyContactDonePo.setDiscountType(discount_type.name());
                }
            }

            for (ApplyTermEnum.COMPANY_TYPE company_type : ApplyTermEnum.COMPANY_TYPE.values()) {
                if (company_type.code().equalsIgnoreCase(applyContactUploadPo.getCompanyType())) {
                    applyContactDonePo.setCompanyType(company_type.name());
                }
            }

            if (AssertUtils.isNotEmpty(applyContactUploadPo.getMultipleOfCurrentSalary()) && "Yes".equals(applyContactUploadPo.getMultipleOfCurrentSalary())) {
                applyContactDonePo.setBasisOfSumInsured(ApplyTermEnum.BASIS_OF_SUM_INSURED.MULTIPLE_OF_CURRENT_SALARY.name());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getRankAndPositionInTheCompany()) && "Yes".equals(applyContactUploadPo.getRankAndPositionInTheCompany())) {
                applyContactDonePo.setBasisOfSumInsured(ApplyTermEnum.BASIS_OF_SUM_INSURED.RANK_AND_POSITION_IN_THE_COMPANY.name());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getFlatSumInsuredForAllEmployees()) && "Yes".equals(applyContactUploadPo.getFlatSumInsuredForAllEmployees())) {
                applyContactDonePo.setBasisOfSumInsured(ApplyTermEnum.BASIS_OF_SUM_INSURED.FLAT_SUM_INSURED_FOR_ALL_EMPLOYEES.name());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getYearsOfServiceWithTheCompany()) && "Yes".equals(applyContactUploadPo.getYearsOfServiceWithTheCompany())) {
                applyContactDonePo.setBasisOfSumInsured(ApplyTermEnum.BASIS_OF_SUM_INSURED.YEARS_OF_SERVICE_WITH_THE_COMPANY.name());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getOtherCategorySpecify())) {
                applyContactDonePo.setBasisOfSumInsured(ApplyTermEnum.BASIS_OF_SUM_INSURED.OTHER_CATEGORY.name());
                applyContactDonePo.setOtherCategorySpecify(applyContactUploadPo.getOtherCategorySpecify());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getSalesPlan())) {
                applyContactDonePo.setSalesPlan(Arrays.stream(ApplyTermEnum.SALES_PLAN.values()).filter(salesPlan -> salesPlan.value().equals(applyContactUploadPo.getSalesPlan())).findFirst().get().name());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getSchoolProperties())) {
                applyContactDonePo.setSchoolProperties(applyContactUploadPo.getSchoolProperties().toUpperCase());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getDelegateSex())) {
                applyContactDonePo.setDelegateSex(applyContactUploadPo.getDelegateSex().toUpperCase());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getDelegateIdType())) {
                applyContactDonePo.setDelegateIdType(applyContactUploadPo.getDelegateIdType().toUpperCase());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getDelegateNationality())) {
                applyContactDonePo.setDelegateNationality(Arrays.stream(ApplyTermEnum.NATIONALITY.values()).filter(nationality -> nationality.value().equals(applyContactUploadPo.getDelegateNationality())).findFirst().get().name());
            }
            if (AssertUtils.isNotEmpty(applyContactUploadPo.getParticipationPercentage()) && applyContactUploadPo.getParticipationPercentage().contains(".")) {
                applyContactDonePo.setParticipationPercentage(new BigDecimal(applyContactUploadPo.getParticipationPercentage()).multiply(new BigDecimal("100")).stripTrailingZeros().toPlainString() + "%");
            }
            applyInsuredBaseService.saveApplyContactDone(applyContactDonePo, users.getUserId());
        }

        // 保存被保人、受益人完成数据
        List<ApplyInsuredDonePo> insuredDonePos = new ArrayList<>();
        List<ApplyBeneficiaryDonePo> beneficiaryDonePos = new ArrayList<>();
        insuredDoneBos.forEach(insuredDoneBo -> {
            insuredDonePos.add(insuredDoneBo);
            beneficiaryDonePos.addAll(insuredDoneBo.getListBeneficiaryDone());
        });
        applyInsuredBaseService.addApplyInsuredDone(insuredDonePos, users.getUserId());
        applyBeneficiaryBaseService.addApplyBeneficiaryDone(beneficiaryDonePos, users.getUserId());

        // 组装被保人及险种、档次信息及健康告知信息
        List<ApplyInsuredBo> newInsuredBos = groupApplyTransData.transInsuredOfTemplateImport(originalInsuredBos, insuredDoneBos, applyCoveragePos, newHealthAnswerPos, applyPo, users);

        String discountType = null;
        String discountModel = null;
        BigDecimal specialDiscount = null;// 折扣系数
        //合同信息就是团险公司信息，投保人信息
        if (AssertUtils.isNotNull(applyContactDonePo)) {
            getLogger().info("applyContactDonePo:::::" + JSON.toJSONString(applyContactDonePo));
            // 先查投保人
            ApplyApplicantPo applyApplicantPo = applyApplicantBaseService.queryApplyApplicant(applyId);
            if (!AssertUtils.isNotNull(applyApplicantPo)) {
                applyApplicantPo = new ApplyApplicantPo();
            }
            ClazzUtils.copyPropertiesIgnoreNull(applyContactDonePo, applyApplicantPo);
            if (AssertUtils.isNotEmpty(applyContactDonePo.getDelegateBirthday())) {
                applyApplicantPo.setDelegateBirthday(DateUtils.stringToTime(applyContactDonePo.getDelegateBirthday(), DateUtils.FORMATE18));
            }
            applyApplicantPo.setApplyId(applyId);
            applyApplicantPo.setName(applyContactDonePo.getCompanyName());
            applyApplicantBaseService.saveApplyApplicant(users.getUserId(), applyApplicantPo);
            //保费折扣信息
            if (AssertUtils.isNotEmpty(applyContactDonePo.getDiscountType()) && AssertUtils.isNotNull(applyContactDonePo.getSpecialDiscount())) {
                discountType = applyContactDonePo.getDiscountType();
                specialDiscount = new BigDecimal(applyContactDonePo.getSpecialDiscount()).divide(new BigDecimal("100"), 6, BigDecimal.ROUND_UP);
                discountModel = ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name();
            }
        }

        // 保存新增加的公共险种
        applyCoveragePos.forEach(applyCoveragePo -> {
            if (!productIds.contains(applyCoveragePo.getProductId())) {
                applyCoverageBaseService.saveApplyCoverage(applyCoveragePo, users.getUserId());
            }
        });
        // 新增健康告知
        if (AssertUtils.isNotEmpty(newHealthAnswerPos)) {
            applyBaseService.saveApplyGroupHealthQuestionnaireAnswer(newHealthAnswerPos, users.getUserId());
        }

        // 查询保费记录
        ApplyPremiumBo applyPremiumBo = applyPremiumBaseService.queryApplyPremium(applyId);
        if (!AssertUtils.isNotNull(applyPremiumBo)) {
            applyPremiumBo = new ApplyPremiumBo();
            applyPremiumBo.setApplyId(applyId);
        }
        applyPremiumBo.setPremiumStatus(ApplyTermEnum.PAYMENT_STATUS.PAYMENT_INITIAL.name());
        applyPremiumBo.setPolicyYear("1");
        applyPremiumBo.setPolicyPeriod("1");
        // 新增特殊折扣系数 公司折扣系数 业务员折扣系数 折扣类型字段
        applyPremiumBo.setSpecialDiscount(specialDiscount);
        applyPremiumBo.setDiscountType(discountType);
        applyPremiumBo.setDiscountModel(discountModel);
        // 折扣前保费
        applyPremiumBo.setPremiumBeforeDiscount(null);

        // 计算保费
        List<ApplyCoverageBo> newCoverageBos = groupApplyTransData.calculatePremium(applyPo.getSalesBranchId(), newInsuredBos, applyPremiumBo);

        groupApplyTransData.updateApplyPremium(applyPo, applyPremiumBo.getTotalPremium(), specialDiscount, applyPremiumBo);
        // 更新保费
        applyPremiumBaseService.saveApplyPremium(users.getUserId(), applyPremiumBo);

        // 模板导入的所有被保人均作为新增被保人，数据库中的被保人均作为需删除被保人
        if (AssertUtils.isNotEmpty(originalInsuredBos)) {
            groupApplyTransData.handleInsuredDelete(originalInsuredBos);
        }
        if (AssertUtils.isNotEmpty(newInsuredBos)) {
            //处理折扣信息
            groupApplyTransData.updateInsuranceFees(newCoverageBos, specialDiscount);

            groupApplyTransData.handleInsuredAdd(newInsuredBos, newCoverageBos, users.getUserId());
        }
        // 保存被保人统计信息
        ApplyInsuredCollectPo applyInsuredCollectPo = applyInsuredBaseService.queryApplyInsuredCollect(applyId);
        if (!AssertUtils.isNotNull(applyInsuredCollectPo)) {
            applyInsuredCollectPo = new ApplyInsuredCollectPo();
            applyInsuredCollectPo.setApplyId(applyId);
        }
        applyInsuredCollectPo.setTotalQuantity((long) newInsuredBos.size());
        applyInsuredCollectPo.setEffectiveQuantity((long) newInsuredBos.size());
        applyInsuredBaseService.saveApplyInsuredCollect(users.getUserId(), applyInsuredCollectPo);
    }

    /**
     * 保存手工录入被保人信息
     *
     * @param listInsured      被保人信息
     * @param applyCoveragePos 公共险种
     * @param applyId          投保单ID
     * @param users            用户
     */
    private void saveInsuredManualInput(List<InsuredRequest> listInsured, List<ApplyCoveragePo> applyCoveragePos, String applyId, Users users) {
        // 被保人信息
        List<ApplyInsuredBo> newInsuredBos = new ArrayList<>();
        // 待修改被保人
        List<ApplyInsuredBo> updateApplyInsureds = new ArrayList<>();
        // 待新增险种
        List<ApplyCoverageBo> newCoverageBos = new ArrayList<>();

        // 1.查询当前投保单被保人清单
        List<ApplyInsuredBo> originalInsuredBos = applyInsuredBaseService.listApplyInsured(applyId);
        groupApplyTransData.transInsuredManualInput(listInsured, newInsuredBos, applyId, users);
        // 组装被保人险种
        newInsuredBos.forEach(applyInsuredBo -> {
            applyCoveragePos.stream()
                    .filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                    .findFirst().ifPresent(applyCoveragePo -> {
                        ApplyCoverageBo insuredCoverageBo = new ApplyCoverageBo();
                        ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, insuredCoverageBo);
                        insuredCoverageBo.setCoveragePeriod("1");
                        insuredCoverageBo.setPremiumPeriod("1");
                        insuredCoverageBo.setInsuredId(applyInsuredBo.getInsuredId());
                        insuredCoverageBo.setCoverageId(null);
                        newCoverageBos.add(insuredCoverageBo);
                    });
        });
        // 先假设页面传入的都是新增被保人
        List<ApplyInsuredBo> addApplyInsureds = new ArrayList<>(newInsuredBos);
        // 先假设数据库中的被保人都是要删除的
        List<ApplyInsuredBo> deleteApplyInsureds = new ArrayList<>(originalInsuredBos);

        // 遍历集合，判断新增、修改
        if (AssertUtils.isNotEmpty(originalInsuredBos)) {
            newInsuredBos.forEach(newInsuredBo -> {
                originalInsuredBos.stream()
                        .filter(originalInsuredBo -> newInsuredBo.getIdNo().equals(originalInsuredBo.getIdNo()))
                        .findFirst().ifPresent(originalInsuredBo -> {
                            addApplyInsureds.removeIf(insured -> newInsuredBo.getInsuredId().equals(insured.getInsuredId()));
                            String insuredId = originalInsuredBo.getInsuredId();
                            ClazzUtils.copyPropertiesIgnoreNull(newInsuredBo, originalInsuredBo);
                            originalInsuredBo.setInsuredId(insuredId);
                            updateApplyInsureds.add(originalInsuredBo);
                            // 移除非新增被保人的险种
                            newCoverageBos.removeIf(coverage -> newInsuredBo.getInsuredId().equals(coverage.getInsuredId()));
                        });
            });
            // 判断删除
            originalInsuredBos.forEach(originalInsuredBo -> {
                newInsuredBos.stream()
                        .filter(newInsuredBo -> originalInsuredBo.getIdNo().equals(newInsuredBo.getIdNo()))
                        .findFirst().ifPresent(newInsuredBo -> {
                            deleteApplyInsureds.remove(originalInsuredBo);
                        });
            });
        }
        // 删除被保人
        if (AssertUtils.isNotEmpty(deleteApplyInsureds)) {
            groupApplyTransData.handleInsuredDelete(deleteApplyInsureds);
        }
        // 新增被保人
        if (AssertUtils.isNotEmpty(addApplyInsureds)) {
            groupApplyTransData.handleInsuredAdd(addApplyInsureds, newCoverageBos, users.getUserId());
        }
        // 更新被保人
        if (AssertUtils.isNotEmpty(updateApplyInsureds)) {
            groupApplyTransData.handleInsuredUpdate(updateApplyInsureds, users.getUserId());
        }

        // 保存被保人统计信息
        ApplyInsuredCollectPo applyInsuredCollectPo = applyInsuredBaseService.queryApplyInsuredCollect(applyId);
        if (AssertUtils.isNotNull(applyInsuredCollectPo)) {
            // 修改
            applyInsuredCollectPo.setTotalQuantity(applyInsuredCollectPo.getTotalQuantity() + addApplyInsureds.size() - deleteApplyInsureds.size());
            applyInsuredCollectPo.setEffectiveQuantity(applyInsuredCollectPo.getEffectiveQuantity() + addApplyInsureds.size() - deleteApplyInsureds.size());
        } else {
            applyInsuredCollectPo = new ApplyInsuredCollectPo();
            applyInsuredCollectPo.setApplyId(applyId);
            applyInsuredCollectPo.setTotalQuantity((long) addApplyInsureds.size());
            applyInsuredCollectPo.setEffectiveQuantity((long) addApplyInsureds.size());
        }
        applyInsuredBaseService.saveApplyInsuredCollect(users.getUserId(), applyInsuredCollectPo);
    }

    @Override
    public ResultObject<InsuredInventoryResponse> getApplyInsured(String applyId, String keyword, Users users) {
        ResultObject resultObject = new ResultObject();
        try {
            InsuredInventoryResponse insuredInventoryResponse = new InsuredInventoryResponse();
            List<InsuredResponse> listInsured = new ArrayList<>();
            getLogger().info("applyId" + applyId);

            // 查询投保单
            ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
            AssertUtils.isNotNull(getLogger(), applyPo, GROUP_APPLY_QUERY_APPLY_IS_NOT_NULL);

            //健康告知备注
            if (ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name().equals(applyPo.getApplyStatus())) {
                insuredInventoryResponse.setShowHealthFlag(TerminologyConfigEnum.WHETHER.NO.name());
            }
            List<ApplyHealthQuestionnaireRemarkPo> applyHealthQuestionnaireRemarkPos = applyRemarkBaseService.listApplyHealthQuestionnaireRemark(applyId);
            if (AssertUtils.isNotEmpty(applyHealthQuestionnaireRemarkPos)) {
                List<ApplyHealthQuestionnaireRemarkResponse> listHealthRemark = new ArrayList<>();
                applyHealthQuestionnaireRemarkPos.forEach(applyHealthQuestionnaireRemarkPo -> {
                    ApplyHealthQuestionnaireRemarkResponse healthRemark = new ApplyHealthQuestionnaireRemarkResponse();
                    healthRemark.setCustomerType(applyHealthQuestionnaireRemarkPo.getCustomerType());
                    healthRemark.setRemark(applyHealthQuestionnaireRemarkPo.getRemark());
                    if (AssertUtils.isNotNull(applyHealthQuestionnaireRemarkPo.getCreatedDate())) {
                        healthRemark.setRemarkDate(DateUtils.timeStrToString(applyHealthQuestionnaireRemarkPo.getCreatedDate(), DateUtils.FORMATE3));
                    }
                    listHealthRemark.add(healthRemark);
                });
                insuredInventoryResponse.setListHealthRemark(listHealthRemark);
            }

            //获取投保单 应收保费
            List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.listApplyCoverage(applyId);
            AssertUtils.isNotEmpty(getLogger(), applyCoveragePos, GROUP_APPLY_QUERY_COVERAGE_IS_NOT_NULL);
            double originalPremiumSum = applyCoveragePos.stream().filter(applyCoveragePo -> AssertUtils.isNotNull(applyCoveragePo.getOriginalPremium())).mapToDouble(applyCoveragePo ->
                    applyCoveragePo.getOriginalPremium().doubleValue()
            ).sum();
            String productId = applyCoveragePos.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                    .map(ApplyCoveragePo::getProductId).findFirst().get();
            if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(productId)) {
                insuredInventoryResponse.setStudentFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            insuredInventoryResponse.setOriginalPremium(BigDecimal.valueOf(originalPremiumSum).setScale(2, BigDecimal.ROUND_HALF_UP));
            //获取 被保人清单列表
            List<ApplyInsuredBo> applyInsuredBos = applyInsuredBaseService.listApplyInsured(applyId, keyword);
            if (!AssertUtils.isNotEmpty(applyInsuredBos)) {
                resultObject.setData(insuredInventoryResponse);
                return resultObject;
            }
            //性别国际化
            List<SyscodeRespFc> genderList = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.GENDER.name()).getData();
            //证件类型国际化
            List<SyscodeRespFc> idTypeList = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.ID_TYPE.name()).getData();
            //国籍国际化
            List<SyscodeRespFc> nationalityList = platformBaseInternationServiceApi.getTerminologyList(TerminologyTypeEnum.NATIONALITY.name()).getData();
            //职业国际化
            List<String> occupationCodeList = applyInsuredBos.stream().filter(applyInsuredBo -> AssertUtils.isNotEmpty(applyInsuredBo.getOccupationCode()))
                    .map(applyInsuredBo -> applyInsuredBo.getOccupationCode()).collect(Collectors.toList());
            List<CareerResponse> careerList = null;
            if (AssertUtils.isNotEmpty(occupationCodeList)) {
                careerList = platformCareerApi.careerInfoPost(occupationCodeList, users.getLanguage()).getData();
            }
            int i = 1;
            int invalidNum = 0;
            List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos = applyBaseService.queryApplyGroupHealthQuestionnaireAnswer(applyId, null);

            for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
                InsuredResponse insuredResponse = new InsuredResponse();
                insuredResponse.setNo(this.getNo(i));
                i++;
                insuredResponse.setName(applyInsuredBo.getName());
                insuredResponse.setSex(applyInsuredBo.getSex());
                insuredResponse.setSexName(LanguageUtils.getCodeName(genderList, applyInsuredBo.getSex()));
                insuredResponse.setIdType(applyInsuredBo.getIdType());
                insuredResponse.setIdTypeName(LanguageUtils.getCodeName(idTypeList, applyInsuredBo.getIdType()));
                insuredResponse.setNationality(applyInsuredBo.getNationality());
                insuredResponse.setNationalityName(LanguageUtils.getCodeName(nationalityList, applyInsuredBo.getNationality()));
                insuredResponse.setIdNo(applyInsuredBo.getIdNo());
                insuredResponse.setMobile(applyInsuredBo.getMobile());
                insuredResponse.setInsuredId(applyInsuredBo.getInsuredId());
                if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
                    insuredResponse.setBirthdayFormat(DateUtils.timeStrToString(applyInsuredBo.getBirthday()));
                }
                insuredResponse.setOccupationCode(applyInsuredBo.getOccupationCode());
                insuredResponse.setOccupationType(applyInsuredBo.getOccupationType());
                if (AssertUtils.isNotEmpty(careerList) && AssertUtils.isNotEmpty(applyInsuredBo.getOccupationCode())) {
                    careerList.stream()
                            .filter(careerResponse -> careerResponse.getCareerId().equals(applyInsuredBo.getOccupationCode()))
                            .findFirst().ifPresent(careerResponse -> insuredResponse.setOccupationCodeName(careerResponse.getCareerName()));
                }
                insuredResponse.setMult(applyInsuredBo.getMult());
                if (AssertUtils.isNotNull(applyInsuredBo.getEffectiveDate())) {
                    insuredResponse.setEffectiveDate(DateUtils.timeStrToString(applyInsuredBo.getEffectiveDate()));
                }
                if (AssertUtils.isNotNull(applyInsuredBo.getInvalidDate())) {
                    insuredResponse.setInvalidDate(DateUtils.timeStrToString(applyInsuredBo.getInvalidDate()));
                    invalidNum++;
                }
                if (AssertUtils.isNotNull(applyInsuredBo.getAddDate())) {
                    insuredResponse.setAddDate(DateUtils.timeStrToString(applyInsuredBo.getAddDate()));
                }
                List<ApplyGroupHealthQuestionnaireAnswerPo> collect = applyGroupHealthQuestionnaireAnswerPos.stream().filter(applyGroupHealthQuestionnaireAnswerPo -> applyGroupHealthQuestionnaireAnswerPo.getInsuredId().equals(applyInsuredBo.getInsuredId()))
                        .collect(Collectors.toList());
                if (AssertUtils.isNotEmpty(collect)) {
                    insuredResponse.setAnswerRemark(collect.get(0).getAnswerRemark());
                }
                listInsured.add(insuredResponse);
            }
            int insuredSum = applyInsuredBos.size();
            int effectiveSum = insuredSum - invalidNum;
            insuredInventoryResponse.setInsuredSum(insuredSum);
            insuredInventoryResponse.setEffectiveSum(effectiveSum);
            insuredInventoryResponse.setInvalidNum(invalidNum);
            insuredInventoryResponse.setListInsured(listInsured);
            resultObject.setData(insuredInventoryResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(GROUP_APPLY_QUERY_INSURED_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public void exportApplyInsured(HttpServletResponse response, String applyId, String keyword, Users users) {
        ResultObject<InsuredInventoryResponse> resultObject = this.getApplyInsured(applyId, keyword, users);
        try {
            //由输入流得到工作簿
            ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet(ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE.name());
            URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
            InputStream inputStream = url.openStream();
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            //得到工作表
            XSSFSheet sheet = workbook.getSheetAt(0);
            try {
                AssertUtils.isResultObjectDataNull(this.getLogger(), resultObject);
                InsuredInventoryResponse insuredInventoryResponse = resultObject.getData();
                List<InsuredResponse> listInsured = insuredInventoryResponse.getListInsured();
                int num = 0;
                for (int i = 4; i < sheet.getLastRowNum(); i++) {
                    if (!(listInsured.size() > num && AssertUtils.isNotNull(listInsured.get(num)))) {
                        break;
                    }
                    InsuredResponse insuredResponse = listInsured.get(num);
                    num++;
                    Row writeRow = sheet.getRow(i);
                    String insuredName = insuredResponse.getName();
                    writeRow.getCell(1).setCellValue(insuredName);
                    String idType = insuredResponse.getIdType();
                    writeRow.getCell(2).setCellValue(idType);
                    String idNo = insuredResponse.getIdNo();
                    writeRow.getCell(3).setCellValue(idNo);
                    String birthday = insuredResponse.getBirthdayFormat();
                    writeRow.getCell(4).setCellValue(birthday);
                    String sex = insuredResponse.getSex();
                    writeRow.getCell(5).setCellValue(sex);
                    String occupationCode = insuredResponse.getOccupationCode();
                    writeRow.getCell(6).setCellValue(occupationCode);
                    String mult = insuredResponse.getMult();
                    writeRow.getCell(7).setCellValue(mult);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            workbook.write(byteArrayOutputStream);
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/x-download");
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("被保人清单.xlsx", "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(byteArrayOutputStream.toByteArray());
            outputStream.close();
            inputStream.close();
            byteArrayOutputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                throw new RequestException(error.getiEnum());
            } else {
                throw new RequestException(GROUP_APPLY_EXPORT_INSURED_ERROR);
            }

        }
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse httpServletResponse, String businessId) {
        String templateCode = groupApplyTransData.getGroupInsuredTemplate(businessId);
        ResultObject<AttachmentResponse> attachmentRespFcResultObject = attachmentApi.templateGet(templateCode);
        URL url = new URL(attachmentRespFcResultObject.getData().getUrl());
        InputStream inputStream = url.openStream();
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        if (Arrays.asList(ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE_V2021.name(), ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE_PRO29.name()).contains(templateCode)) {
            XSSFSheet sheetAt = workbook.getSheetAt(0);
            ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(businessId);
            if (AssertUtils.isNotNull(applyAgentPo)) {
                ResultObject<AgentBaseResponse> agentBaseRespFcResultObject = agentBaseAgentApi.queryOneAgentById(applyAgentPo.getAgentId());
                AssertUtils.isResultObjectError(this.getLogger(), agentBaseRespFcResultObject);
                AgentBaseResponse agent = agentBaseRespFcResultObject.getData();
                int index = 0;
                if (ApplyTermEnum.IMPORT_EXPORT_TEMPLATE.GROUP_INSURED_TEMPLATE_PRO29.name().equals(templateCode)) {
                    index = 1;
                }
                sheetAt.getRow(index).getCell(1).setCellValue(agent.getAgentName());
                sheetAt.getRow(++index).getCell(1).setCellValue(agent.getAgentCode());
                sheetAt.getRow(++index).getCell(1).setCellValue(agent.getAgentDetail().getMobile());
            }
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("application/x-download");
        httpServletResponse.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("被保人清单模板.xlsm", "UTF-8"));
        OutputStream outputStream = httpServletResponse.getOutputStream();
        outputStream.write(byteArrayOutputStream.toByteArray());
        outputStream.close();
        inputStream.close();
        byteArrayOutputStream.close();
    }

//    @Override
//    public ResultObject<GroupInsuredInfoResponse> getInsuredProductInfo(String applyId, String insuredId) {
//        ResultObject<GroupInsuredInfoResponse> resultObject = new ResultObject<>();
//        GroupInsuredInfoResponse groupInsuredInfoResponse = new GroupInsuredInfoResponse();
//        AssertUtils.isNotEmpty(this.getLogger(), applyId, APPLY_APPLY_ID_IS_NOT_NULL);
//        AssertUtils.isNotEmpty(this.getLogger(), insuredId, APPLY_INPUT_INSURED_INFO_IS_NOT_NULL);
//        // 查询投保单
//        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
//        AssertUtils.isNotNull(getLogger(), applyPo, GROUP_APPLY_QUERY_APPLY_IS_NOT_NULL);
//
//        if (ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name().equals(applyPo.getApplyStatus())) {
//            groupInsuredInfoResponse.setShowHealthFlag(TerminologyConfigEnum.WHETHER.NO.name());
//        }
//
//        //健康告知信息
//        List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos = applyBaseService.queryApplyGroupHealthQuestionnaireAnswer(applyId, insuredId);
//        if (AssertUtils.isNotEmpty(applyGroupHealthQuestionnaireAnswerPos)) {
//            groupInsuredInfoResponse.setHealthNoticeAnswerResponses((List<GroupHealthNoticeAnswerResponse>) this.converterList(applyGroupHealthQuestionnaireAnswerPos, new TypeToken<List<GroupHealthNoticeAnswerResponse>>() {
//            }.getType()));
//        }
//
//        //产品信息
//        List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.getGroupApplyCoverageList(applyId, insuredId);
//
//        List<ApplyCoverageDutyBo> applyCoverageDutyBos = applyCoverageBaseService.getApplyCoverageDutyList(applyId);
//
//        if (AssertUtils.isNotEmpty(applyCoveragePos)) {
//            List<InsuredProductInfoResponse> insuredProductInfoResponses = (List<InsuredProductInfoResponse>) this.converterList(
//                    applyCoveragePos, new TypeToken<List<InsuredProductInfoResponse>>() {
//                    }.getType()
//            );
//            //查询投保单险种信息
//            if (AssertUtils.isNotEmpty(insuredProductInfoResponses)) {
//                insuredProductInfoResponses.forEach(insuredProductInfoResponse -> {
//                    if (TerminologyConfigEnum.WHETHER.YES.name().equals(insuredProductInfoResponse.getDutyChooseFlag())) {
//                        //开始进行分组拆分
//                        if (AssertUtils.isNotEmpty(applyCoverageDutyBos)) {
//                            List<ApplyCoverageDutyBo> applyCoverageDutyBosTemp = applyCoverageDutyBos.stream()
//                                    .filter(applyCoverageDutyBo -> insuredProductInfoResponse.getCoverageId().equals(applyCoverageDutyBo.getCoverageId()))
//                                    .collect(Collectors.toList());
//                            if (AssertUtils.isNotEmpty(applyCoverageDutyBosTemp)) {
//                                List<ApplyCoverageDutyResponse> applyCoverageDutyResponses = (List<ApplyCoverageDutyResponse>) this.converterList(applyCoverageDutyBosTemp, new TypeToken<List<ApplyCoverageDutyResponse>>() {
//                                }.getType());
//                                insuredProductInfoResponse.setListCoverageDuty(applyCoverageDutyResponses);
//                            }
//                        }
//                    } else {
//                        List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.getApplyCoverageLevel(insuredProductInfoResponse.getCoverageId());
//                        List<ApplyCoverageLevelResponse> coverageLevelResponses = (List<ApplyCoverageLevelResponse>) this.converterList(
//                                applyCoverageLevelPos, new TypeToken<List<ApplyCoverageLevelResponse>>() {
//                                }.getType()
//                        );
//                        insuredProductInfoResponse.setListCoverageLevel(coverageLevelResponses);
//                    }
//                });
//            }
//            groupInsuredInfoResponse.setListProductInfo(insuredProductInfoResponses);
//        }
//        //查询受益人信息
//        List<ApplyBeneficiaryInfoBo> originListBeneficiary = applyBaseDao.queryApplyBeneficiaryListByApplyId(applyId);
//        //设置受益人
//        List<ApplyBeneficiaryInfoBo> listBeneficiary = new ArrayList<>();
//        List<ApplyBeneficiaryResponse> applyBeneficiaryResponses = new ArrayList<>();
//        if (AssertUtils.isNotEmpty(originListBeneficiary)) {
//            listBeneficiary = originListBeneficiary.stream().filter(beneficiaryInfoBo -> insuredId.equals(beneficiaryInfoBo.getInsuredId())).collect(Collectors.toList());
//
//            if (AssertUtils.isNotEmpty(listBeneficiary)) {
//                groupInsuredInfoResponse.setIsLegalBeneficial(TerminologyConfigEnum.WHETHER.NO.name());
//                listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
//                    ApplyBeneficiaryResponse applyBeneficiaryResponse = new ApplyBeneficiaryResponse();
//                    ClazzUtils.copyPropertiesIgnoreNull(applyBeneficiaryInfoBo, applyBeneficiaryResponse);
//
//                    ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
//                    if (AssertUtils.isNotNull(applyBeneficiaryBo)) {
//                        ClazzUtils.copyPropertiesIgnoreNull(applyBeneficiaryBo, applyBeneficiaryResponse);
//                    }
//                    applyBeneficiaryResponses.add(applyBeneficiaryResponse);
//                });
//            } else {
//                groupInsuredInfoResponse.setIsLegalBeneficial(TerminologyConfigEnum.WHETHER.YES.name());
//            }
//        }
//        groupInsuredInfoResponse.setListBeneficiary(applyBeneficiaryResponses);
//        resultObject.setData(groupInsuredInfoResponse);
//        return resultObject;
//    }


    @Override
    public ResultObject<GroupInsuredInfoResponse> getInsuredProductInfo(String applyId, String insuredId) {
        ResultObject<GroupInsuredInfoResponse> resultObject = new ResultObject<>();
        GroupInsuredInfoResponse groupInsuredInfoResponse = new GroupInsuredInfoResponse();
        AssertUtils.isNotEmpty(this.getLogger(), applyId, APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), insuredId, APPLY_INPUT_INSURED_INFO_IS_NOT_NULL);
        List<ApplyCoverageExt> applyCoverageExtList = new ArrayList<>();
        // 查询投保单
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(getLogger(), applyPo, GROUP_APPLY_QUERY_APPLY_IS_NOT_NULL);

        if (ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name().equals(applyPo.getApplyStatus())) {
            groupInsuredInfoResponse.setShowHealthFlag(TerminologyConfigEnum.WHETHER.NO.name());
        }
        //产品信息
        List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.getGroupApplyCoverageList(applyId, insuredId);
        if (AssertUtils.isNotEmpty(applyCoveragePos)) {
            List<String> coverageIds = applyCoveragePos.stream().map(ApplyCoveragePo::getCoverageId).collect(Collectors.toList());

            List<ApplyCoverageBo> applyCoverageBos = (List<ApplyCoverageBo>) this.converterList(
                    applyCoveragePos, new TypeToken<List<ApplyCoverageBo>>() {
                    }.getType()
            );
            // 查询险种档次信息
            List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.listApplyCoverageLevel(coverageIds);
            if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
                // 按险种ID分组
                Map<String, List<ApplyCoverageLevelPo>> applyCoverageLevelPoMap =
                        applyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(ApplyCoverageLevelPo::getCoverageId));
                // 设置险种档次数据
                applyCoverageBos.forEach(applyCoverageBo ->
                        applyCoverageBo.setListCoverageLevel(applyCoverageLevelPoMap.get(applyCoverageBo.getCoverageId())));
            }
            //查询责任信息
//            List<String> dutyCoverageIds = applyCoveragePos.stream().filter(coveragePo -> TerminologyConfigEnum.WHETHER.YES.name().equals(coveragePo.getDutyChooseFlag())).map(ApplyCoveragePo::getCoverageId).collect(Collectors.toList());
            List<ApplyCoverageDutyBo> applyCoverageDutyBos = applyCoverageBaseService.getApplyCoverageDutyList(applyId);
            if (AssertUtils.isNotEmpty(applyCoverageDutyBos)) {
                // 按险种ID分组
                Map<String, List<ApplyCoverageDutyBo>> endorseCoverageDutyMaps =
                        applyCoverageDutyBos.parallelStream().collect(Collectors.groupingBy(ApplyCoverageDutyBo::getCoverageId));
                // 设置险种档次数据
                applyCoverageBos.forEach(applyCoverageBo -> {
                    if (TerminologyConfigEnum.WHETHER.YES.name().equals(applyCoverageBo.getDutyChooseFlag())) {
                        applyCoverageBo.setListCoverageDuty(endorseCoverageDutyMaps.get(applyCoverageBo.getCoverageId()));
                    }
                });
            }
            //转换险种信息
            List<ApplyCoverageBo> listCoverage = applyDataTransform.transSummaryCoverage(applyCoverageBos);
            if (AssertUtils.isNotEmpty(listCoverage)) {
                // 按产品分组
                Map<String, List<ApplyCoverageBo>> productMap = listCoverage.stream().collect(Collectors.groupingBy(ApplyCoverageBo::getProductId));
                for (String productId : productMap.keySet()) {
                    List<ApplyCoverageBo> applyCoverageBos1 = productMap.get(productId);
                    if (AssertUtils.isNotEmpty(applyCoverageBos1)) {
                        ApplyCoverageExt applyCoverageExt = new ApplyCoverageExt();
                        applyCoverageExt.setDutyChooseFlag(applyCoverageBos1.get(0).getDutyChooseFlag());
                        applyCoverageExt.setProductId(productId);
                        applyCoverageExt.setPremiumFrequency(applyCoverageBos1.get(0).getPremiumFrequency());
                        applyCoverageExt.setPrimaryFlag(applyCoverageBos1.get(0).getPrimaryFlag());
                        applyCoverageExt.setProductName(applyCoverageBos1.get(0).getProductName());
                        applyCoverageExt.setProductCode(applyCoverageBos1.get(0).getProductCode());
                        List<ApplyCoverageExtResponse> applyCoverageExtResponseList = new ArrayList<>();
                        applyCoverageBos1.forEach(applyCoverageBo -> {
                            ApplyCoverageExtResponse applyCoverageExtResponse = (ApplyCoverageExtResponse) this.converterObject(applyCoverageBo, ApplyCoverageExtResponse.class);
                            if (TerminologyConfigEnum.WHETHER.YES.name().equals(applyCoverageBo.getDutyChooseFlag()) && AssertUtils.isNotEmpty(applyCoverageBo.getListCoverageDuty())) {
                                applyCoverageExtResponse.setDutyId(applyCoverageBo.getListCoverageDuty().get(0).getDutyId());
                            }
                            applyCoverageExtResponseList.add(applyCoverageExtResponse);
                        });
                        applyCoverageExt.setCoverageExtResponses(applyCoverageExtResponseList);
                        applyCoverageExtList.add(applyCoverageExt);
                    }
                }
            }
            //排序
            if (AssertUtils.isNotEmpty(applyCoverageExtList)) {
                applyCoverageExtList.sort(Comparator.comparing(ApplyCoverageExt::getPrimaryFlag, Comparator.nullsLast(String::compareTo)).reversed()
                        .thenComparing(ApplyCoverageExt::getProductId, Comparator.nullsLast(String::compareTo)));
            }
            groupInsuredInfoResponse.setListCoverage(applyCoverageExtList);
        }
        //健康告知信息
        List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos = applyBaseService.queryApplyGroupHealthQuestionnaireAnswer(applyId, insuredId);
        if (AssertUtils.isNotEmpty(applyGroupHealthQuestionnaireAnswerPos)) {
            groupInsuredInfoResponse.setAnswerRemark(applyGroupHealthQuestionnaireAnswerPos.get(0).getAnswerRemark());
            groupInsuredInfoResponse.setHealthNoticeAnswerResponses((List<GroupHealthNoticeAnswerResponse>) this.converterList(applyGroupHealthQuestionnaireAnswerPos, new TypeToken<List<GroupHealthNoticeAnswerResponse>>() {
            }.getType()));
        }

        //查询受益人信息
        List<ApplyBeneficiaryInfoBo> originListBeneficiary = applyBaseDao.queryApplyBeneficiaryListByApplyId(applyId);
        //设置受益人
        List<ApplyBeneficiaryInfoBo> listBeneficiary = new ArrayList<>();
        List<ApplyBeneficiaryResponse> applyBeneficiaryResponses = new ArrayList<>();
        if (AssertUtils.isNotEmpty(originListBeneficiary)) {
            listBeneficiary = originListBeneficiary.stream().filter(beneficiaryInfoBo -> insuredId.equals(beneficiaryInfoBo.getInsuredId())).collect(Collectors.toList());

            if (AssertUtils.isNotEmpty(listBeneficiary)) {
                groupInsuredInfoResponse.setIsLegalBeneficial(TerminologyConfigEnum.WHETHER.NO.name());
                listBeneficiary.forEach(applyBeneficiaryInfoBo -> {
                    ApplyBeneficiaryResponse applyBeneficiaryResponse = new ApplyBeneficiaryResponse();
                    ClazzUtils.copyPropertiesIgnoreNull(applyBeneficiaryInfoBo, applyBeneficiaryResponse);

                    ApplyBeneficiaryBo applyBeneficiaryBo = applyBeneficiaryInfoBo.getApplyBeneficiaryBo();
                    if (AssertUtils.isNotNull(applyBeneficiaryBo)) {
                        ClazzUtils.copyPropertiesIgnoreNull(applyBeneficiaryBo, applyBeneficiaryResponse);
                        applyBeneficiaryResponse.setIdExpDate(DateUtils.timeStrToString(applyBeneficiaryBo.getIdExpDate(), DateUtils.FORMATE3));
                    }
                    applyBeneficiaryResponses.add(applyBeneficiaryResponse);
                });
            } else {
                groupInsuredInfoResponse.setIsLegalBeneficial(TerminologyConfigEnum.WHETHER.YES.name());
            }
        }
        groupInsuredInfoResponse.setListBeneficiary(applyBeneficiaryResponses);
        resultObject.setData(groupInsuredInfoResponse);
        return resultObject;
    }

    @SneakyThrows
    @Override
    public ResultObject printInsureds(Users currentLoginUsers, PrintInsuredsResponse printInsuredsResponse) throws IOException {
        String applyId = printInsuredsResponse.getApplyId();
        String companyName = printInsuredsResponse.getCompanyName();
        String language = printInsuredsResponse.getLanguage();
        AssertUtils.isNotEmpty(this.getLogger(), applyId, APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), companyName, APPLY_COMPANY_NAME_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), language, APPLY_PARAMETER_LANGUAGE_IS_NOT_NULL);
        ResultObject resultObject = new ResultObject();
        PrintApplyInsuredBo printApplyInsuredBo = applyBaseService.queryPrintApplyData(applyId);
        AssertUtils.isNotNull(log, printApplyInsuredBo, APPLY_BASE_PARAMETER_APPLY_NOT_EXISTENT);
        ApplyHealthQuestionnaireRemarkPo applyHealthQuestionnaireRemark = applyRemarkBaseService.getApplyHealthQuestionnaireRemark(applyId, ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
        printApplyInsuredBo.setRemark(AssertUtils.isNotNull(applyHealthQuestionnaireRemark) ? applyHealthQuestionnaireRemark.getRemark() : null);
        printApplyInsuredBo.setCompanyName(companyName);
        printApplyInsuredBo.setPrintDate(DateUtils.getCurrentTime());
        AssertUtils.isNotEmpty(log, printApplyInsuredBo.getAgentId(), APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
        ResultObject<AgentResponse> agentInfoByCode = agentApi.agentByIdGet(printApplyInsuredBo.getAgentId());
        AssertUtils.isResultObjectError(log, agentInfoByCode);
        AgentResponse agentInfoByCodeData = agentInfoByCode.getData();
        printApplyInsuredBo.setAgentCode(agentInfoByCodeData.getAgentCode());
        printApplyInsuredBo.setAgentMobile(agentInfoByCodeData.getMobile());
        printApplyInsuredBo.setAgentName(agentInfoByCodeData.getAgentName());
        //性别国际化
        List<SyscodeRespFc> genderList = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.GENDER.name(), language).getData();
        //证件类型国际化
        List<SyscodeRespFc> idTypeList = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.ID_TYPE.name(), language).getData();
        // 与投保人关系
        List<SyscodeRespFc> relationshipWithTheApplicantSyscodes = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_APPLICANT.name(), language).getData();
        // 险种档次国际化
        List<SyscodeRespFc> productLevelSyscodes = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.PRODUCT_PRODUCT_LEVEL.name(), language).getData();

        List<ApplyInsuredBo> applyInsuredBos = applyInsuredBaseService.listApplyInsured(applyId);
        AssertUtils.isNotEmpty(log, applyInsuredBos, APPLY_PRINT_INSURED_LIST_IS_NOT_FOUND_ERROR);
        // 职业国际化
        List<String> occupationCodeList = applyInsuredBos.stream().map(ApplyInsuredBo::getOccupationCode)
                .filter(AssertUtils::isNotEmpty).collect(Collectors.toList());
        List<CareerResponse> careerList = null;
        if (AssertUtils.isNotEmpty(occupationCodeList)) {
            careerList = platformCareerApi.careerInfoPost(occupationCodeList, language).getData();
        }
        List<CareerResponse> finalCareerList = careerList;
        applyInsuredBos.forEach(applyInsuredBo -> {
            applyInsuredBo.setSexName(LanguageUtils.getCodeName(genderList, applyInsuredBo.getSex()));
            applyInsuredBo.setOccupationName(LanguageUtils.getCareerName(finalCareerList, applyInsuredBo.getOccupationCode()));
            applyInsuredBo.setIdTypeName(LanguageUtils.getCodeName(idTypeList, applyInsuredBo.getIdType()));
        });
        printApplyInsuredBo.setApplyInsuredList(applyInsuredBos);
        List<ApplyBeneficiaryBo> applyBeneficiaryBoList = applyBaseService.queryApplyBeneficiaryByApplyId(applyId);
        if (AssertUtils.isNotEmpty(applyBeneficiaryBoList)) {
            applyBeneficiaryBoList.forEach(applyBeneficiaryBo -> {
                applyBeneficiaryBo.setSexName(LanguageUtils.getCodeName(genderList, applyBeneficiaryBo.getSex()));
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getIdType())) {
                    applyBeneficiaryBo.setIdTypeName(LanguageUtils.getCodeName(idTypeList, applyBeneficiaryBo.getIdType()));
                }
                applyBeneficiaryBo.setRelationshipName(LanguageUtils.getCodeName(relationshipWithTheApplicantSyscodes, applyBeneficiaryBo.getRelationship()));
            });
        }
        printApplyInsuredBo.setApplyBeneficiaryBoList(applyBeneficiaryBoList);
        List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos = applyBaseService.queryApplyGroupHealthQuestionnaireAnswer(applyId, null);
        printApplyInsuredBo.setAghqaList(applyGroupHealthQuestionnaireAnswerPos);
        List<ApplyCoveragePo> applyCoveragePos = applyBaseDao.getApplyCoverageList(applyId, null);
        AssertUtils.isNotEmpty(log, applyCoveragePos, APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        printApplyInsuredBo.setApplyCoverageList(applyCoveragePos);
        List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.listApplyCoverageLevel(applyId);
        List<GroupAttachInsuredCoverageLevelBo> groupAttachInsuredCoverageLevelBoList = (List<GroupAttachInsuredCoverageLevelBo>) this.converterList(
                applyCoverageLevelPos, new TypeToken<List<GroupAttachInsuredCoverageLevelBo>>() {
                }.getType()
        );
        // 险种档次 国际化
        if (AssertUtils.isNotEmpty(groupAttachInsuredCoverageLevelBoList)) {
            groupAttachInsuredCoverageLevelBoList.forEach(groupAttachInsuredCoverageLevelBo -> {
                groupAttachInsuredCoverageLevelBo.setProductLevelI18n(LanguageUtils.getCodeName(productLevelSyscodes, groupAttachInsuredCoverageLevelBo.getProductLevel()));
            });
        }
        printApplyInsuredBo.setApplyCoverageLevelList(groupAttachInsuredCoverageLevelBoList);

        List<ApplyCoverageDutyBo> applyCoverageDutyList = applyCoverageBaseService.getApplyCoverageDutyList(applyId);
        printApplyInsuredBo.setApplyCoverageDutyList(applyCoverageDutyList);

        // 设置投保人信息
        ApplyApplicantPo applyApplicantPo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(getLogger(), applyApplicantPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        GroupAttachApplyApplicantBo groupAttachApplyApplicantBo = new GroupAttachApplyApplicantBo();
        ClazzUtils.copyPropertiesIgnoreNull(applyApplicantPo, groupAttachApplyApplicantBo);
        // 单位地区
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyAreaCode())) {
            ResultObject<AreaNameResponse> areaNameResultObject = platformAreaApi.areaNameGet(groupAttachApplyApplicantBo.getCompanyAreaCode(), language);
            if (!AssertUtils.isResultObjectDataNull(areaNameResultObject) && AssertUtils.isNotEmpty(areaNameResultObject.getData().getAreaName())) {
                groupAttachApplyApplicantBo.setCompanyAreaName(areaNameResultObject.getData().getAreaName());
                groupAttachApplyApplicantBo.setCompanyAddressWhole(areaNameResultObject.getData().getAreaName() + groupAttachApplyApplicantBo.getCompanyAddress());
            }
        }
        // 所属行业
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyIndustry())) {
            ResultObject<CareerResponse> careerResultObject = platformCareerApi.careerInfoGet(groupAttachApplyApplicantBo.getCompanyIndustry(), language);
            if (!AssertUtils.isResultObjectDataNull(careerResultObject) && AssertUtils.isNotEmpty(careerResultObject.getData().getCareerName())) {
                groupAttachApplyApplicantBo.setCompanyIndustryName(careerResultObject.getData().getCareerName());
            }
        }
        //法人国籍国际化
        groupAttachApplyApplicantBo.setCompanyLegalPersonNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyLegalPersonNationality(), language));
        groupAttachApplyApplicantBo.setCompanyContractNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyContractNationality(), language));

        groupAttachApplyApplicantBo.setCompanyTypeName(languageCodeTransData.queryOneInternational(com.gclife.common.TerminologyTypeEnum.COMPANY_TYPE.name(), groupAttachApplyApplicantBo.getCompanyType(), language));

        printApplyInsuredBo.setGroupApplicant(groupAttachApplyApplicantBo);

        // 设置代理人信息
        ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(applyId);
        AssertUtils.isNotNull(getLogger(), applyAgentPo, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
        // 调代理人服务
        AgentBaseResponse agentBaseResponse = agentBaseAgentApi.queryOneAgentById(applyAgentPo.getAgentId()).getData();
        GroupAttachApplyAgentBo groupAttachApplyAgentBo = new GroupAttachApplyAgentBo();
        groupAttachApplyAgentBo.setAgentId(applyAgentPo.getAgentId());
        groupAttachApplyAgentBo.setAgentCode(applyAgentPo.getAgentCode());
        groupAttachApplyAgentBo.setAgentName(agentBaseResponse.getAgentName());
        groupAttachApplyAgentBo.setMobile(agentBaseResponse.getAgentDetail().getMobile());
        printApplyInsuredBo.setGroupAgent(groupAttachApplyAgentBo);

        // 设置被保险人统计信息
        ApplyInsuredCollectPo applyInsuredCollect = applyBaseDao.getApplyInsuredCollect(applyId);
        AssertUtils.isNotNull(getLogger(), applyInsuredCollect, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_COLLECT_IS_NOT_FOUND_OBJECT);
        GroupAttachApplyInsuredCollectBo groupAttachApplyInsuredCollectBo = (GroupAttachApplyInsuredCollectBo) this.converterObject(applyInsuredCollect, GroupAttachApplyInsuredCollectBo.class);
        // 主险人数、附加险人数
        long mainQuantity = 0;
        long additionQuantity = 0;
        long additionQuantity18 = 0;
        long additionQuantity26 = 0;
        long additionQuantity27 = 0;
        long additionQuantity33 = 0;
        final long[] quantityPreSchool29 = {0};
        final long[] quantityPreSchool33 = {0};
        final long[] quantityPrimarySchool29 = {0};
        final long[] quantityPrimarySchool33 = {0};
        final long[] quantitySecondarySchool29 = {0};
        final long[] quantitySecondarySchool33 = {0};
        final long[] quantityUniversity29 = {0};
        final long[] quantityUniversity33 = {0};
        final BigDecimal[] quantityPreSchoolAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantityPreSchoolAmount33 = {new BigDecimal(0)};
        final BigDecimal[] quantityPrimarySchoolAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantityPrimarySchoolAmount33 = {new BigDecimal(0)};
        final BigDecimal[] quantitySecondarySchoolAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantitySecondarySchoolAmount33 = {new BigDecimal(0)};
        final BigDecimal[] quantityUniversityAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantityUniversityAmount33 = {new BigDecimal(0)};

        for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
            for (ApplyCoverageBo applyCoverageBo : applyInsuredBo.getListCoverage()) {
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    mainQuantity++;
                }
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    additionQuantity++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_18.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity18++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_26.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity26++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_27.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity27++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity33++;
                }

                //29号和33号产品投保清单人数统计
                List<ApplyCoverageLevelPo> listCoverageLevel = applyCoverageBo.getListCoverageLevel();
                if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                    listCoverageLevel.forEach(applyCoverageLevelPo -> {
                        String productLevel = applyCoverageLevelPo.getProductLevel();
                        BigDecimal amount = applyCoverageLevelPo.getAmount();
                        if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool29[0]++;
                                quantityPreSchoolAmount29[0] = quantityPreSchoolAmount29[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool29[0]++;
                                quantityPrimarySchoolAmount29[0] = quantityPrimarySchoolAmount29[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool29[0]++;
                                quantitySecondarySchoolAmount29[0] = quantitySecondarySchoolAmount29[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity29[0]++;
                                quantityUniversityAmount29[0] = quantityUniversityAmount29[0].add(amount);
                            }

                        }
                        if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool33[0]++;
                                quantityPreSchoolAmount33[0] = quantityPreSchoolAmount33[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool33[0]++;
                                quantityPrimarySchoolAmount33[0] = quantityPrimarySchoolAmount33[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool33[0]++;
                                quantitySecondarySchoolAmount33[0] = quantitySecondarySchoolAmount33[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity33[0]++;
                                quantityUniversityAmount33[0] = quantityUniversityAmount33[0].add(amount);
                            }
                        }
                    });
                }
            }
        }
        groupAttachApplyInsuredCollectBo.setMainQuantity(mainQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity(additionQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity18(additionQuantity18);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity26(additionQuantity26);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity27(additionQuantity27);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity33(additionQuantity33);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool29(quantityPreSchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool33(quantityPreSchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool29(quantityPrimarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool33(quantityPrimarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool29(quantitySecondarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool33(quantitySecondarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity29(quantityUniversity29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity33(quantityUniversity33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount29(quantityPreSchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount33(quantityPreSchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount29(quantityPrimarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount33(quantityPrimarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount29(quantitySecondarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount33(quantitySecondarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount29(quantityUniversityAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount33(quantityUniversityAmount33[0]);
        printApplyInsuredBo.setApplyInsuredCollect(groupAttachApplyInsuredCollectBo);

        // =====================投保单数据封装=====================
        // 查询投保单基础信息
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        ClazzUtils.copyPropertiesIgnoreNull(applyPo, printApplyInsuredBo);
        getLogger().info("=====================apply end=======================");

        /********************************** 查询投保单代理人信息********************************************/
        applyAgentPo = applyAgentBaseService.queryApplyAgent(applyId);
        AssertUtils.isNotNull(getLogger(), applyAgentPo, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
        // 调代理人服务
        agentBaseResponse = agentBaseAgentApi.queryOneAgentById(applyAgentPo.getAgentId()).getData();
        groupAttachApplyAgentBo = new GroupAttachApplyAgentBo();
        groupAttachApplyAgentBo.setAgentId(applyAgentPo.getAgentId());
        groupAttachApplyAgentBo.setAgentCode(applyAgentPo.getAgentCode());
        groupAttachApplyAgentBo.setAgentName(agentBaseResponse.getAgentName());
        groupAttachApplyAgentBo.setMobile(agentBaseResponse.getAgentDetail().getMobile());
        printApplyInsuredBo.setGroupAgent(groupAttachApplyAgentBo);
        getLogger().info("=====================agent end=======================");
        /********************************** 查询保单投保人信息********************************************/
        applyApplicantPo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(getLogger(), applyApplicantPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        groupAttachApplyApplicantBo = new GroupAttachApplyApplicantBo();
        ClazzUtils.copyPropertiesIgnoreNull(applyApplicantPo, groupAttachApplyApplicantBo);
        // 单位地区
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyAreaCode())) {
            ResultObject<AreaNameResponse> areaNameResultObject = platformAreaApi.areaNameGet(groupAttachApplyApplicantBo.getCompanyAreaCode(), language);
            if (!AssertUtils.isResultObjectDataNull(areaNameResultObject) && AssertUtils.isNotEmpty(areaNameResultObject.getData().getAreaName())) {
                groupAttachApplyApplicantBo.setCompanyAreaName(areaNameResultObject.getData().getAreaName());
                groupAttachApplyApplicantBo.setCompanyAddressWhole(areaNameResultObject.getData().getAreaName() + groupAttachApplyApplicantBo.getCompanyAddress());
            }
        }
        // 所属行业
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyIndustry())) {
            ResultObject<CareerResponse> careerResultObject = platformCareerApi.careerInfoGet(groupAttachApplyApplicantBo.getCompanyIndustry(), language);
            if (!AssertUtils.isResultObjectDataNull(careerResultObject) && AssertUtils.isNotEmpty(careerResultObject.getData().getCareerName())) {
                groupAttachApplyApplicantBo.setCompanyIndustryName(careerResultObject.getData().getCareerName());
            }
        }
        //法人国籍国际化
        groupAttachApplyApplicantBo.setCompanyLegalPersonNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyLegalPersonNationality(), language));
        groupAttachApplyApplicantBo.setCompanyContractNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyContractNationality(), language));

        groupAttachApplyApplicantBo.setCompanyTypeName(languageCodeTransData.queryOneInternational(com.gclife.common.TerminologyTypeEnum.COMPANY_TYPE.name(), groupAttachApplyApplicantBo.getCompanyType(), language));

        //设置投保代表国际化
        groupAttachApplyApplicantBo.setDelegateSexName(languageCodeTransData.queryOneInternational(com.gclife.common.TerminologyTypeEnum.GENDER.name(), groupAttachApplyApplicantBo.getDelegateSex(), language));
        groupAttachApplyApplicantBo.setDelegateNationalityName(languageCodeTransData.queryOneInternational(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getDelegateNationality(), language));
        printApplyInsuredBo.setGroupApplicant(groupAttachApplyApplicantBo);
        getLogger().info("=====================applicant end=======================");
        /********************************** 查询保单险种信息********************************************/
        applyCoveragePos = applyCoverageBaseService.listApplyCoverage(applyId);
        AssertUtils.isNotEmpty(getLogger(), applyCoveragePos, ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        // 缴费期限单位
        List<SyscodeResponse> premiumPeriodUnitSyscodes = platformTerminologyBaseApi.queryTerminologyInternation(com.gclife.common.TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(), language).getData();
        // 保障期限单位
        List<SyscodeResponse> coveragePeriodUnitSyscodes = platformTerminologyBaseApi.queryTerminologyInternation(com.gclife.common.TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), language).getData();

        List<GroupAttachApplyCoverageBo> groupAttachApplyCoverageBos = new ArrayList<>();
        applyCoveragePos.forEach(applyCoveragePo -> {
            GroupAttachApplyCoverageBo applyCoverageBo = new GroupAttachApplyCoverageBo();
            applyInsuredBos.forEach(policyInsuredBo -> {
                List<ApplyCoverageBo> listCoverage = policyInsuredBo.getListCoverage();
                if (AssertUtils.isNotEmpty(listCoverage)) {
                    Optional<ApplyCoverageBo> coverageOptional = listCoverage.stream().filter(coverage -> coverage.getProductId().equals(applyCoveragePo.getProductId())).findFirst();
                    if (coverageOptional.isPresent()) {
                        ApplyCoverageBo coverageBo = coverageOptional.get();
                        ClazzUtils.copyPropertiesIgnoreNull(coverageBo, applyCoverageBo);
                        return;
                    }
                }
            });
            ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, applyCoverageBo);

            applyCoverageBo.setTotalAmount(BigDecimal.ZERO);
            applyCoverageBo.setTotalPremium(BigDecimal.ZERO);

            // 缴费期限单位
            if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnit()) && AssertUtils.isNotEmpty(premiumPeriodUnitSyscodes)) {
                premiumPeriodUnitSyscodes.stream()
                        .filter(syscode -> syscode.getCodeKey().equals(applyCoverageBo.getPremiumPeriodUnit()))
                        .findFirst().ifPresent(syscode -> applyCoverageBo.setPremiumPeriodUnitName(syscode.getCodeName()));
            }
            // 保障期限单位
            if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnit()) && AssertUtils.isNotEmpty(coveragePeriodUnitSyscodes)) {
                coveragePeriodUnitSyscodes.stream()
                        .filter(syscode -> syscode.getCodeKey().equals(applyCoverageBo.getCoveragePeriodUnit()))
                        .findFirst().ifPresent(syscode -> applyCoverageBo.setCoveragePeriodUnitName(syscode.getCodeName()));
            }
            applyCoverageBo.setPrimaryFlag(applyCoveragePo.getPrimaryFlag());
            applyCoverageBo.setProductLevel(applyCoveragePo.getProductLevel());
            groupAttachApplyCoverageBos.add(applyCoverageBo);
        });
        printApplyInsuredBo.setListGroupCoverage(groupAttachApplyCoverageBos);
        getLogger().info("=====================coverage end=======================");
        /********************************************设置被保险人统计信息****************************************/
        applyInsuredCollect = applyBaseDao.getApplyInsuredCollect(applyId);
        AssertUtils.isNotNull(getLogger(), applyInsuredCollect, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_COLLECT_IS_NOT_FOUND_OBJECT);
        groupAttachApplyInsuredCollectBo = (GroupAttachApplyInsuredCollectBo) this.converterObject(applyInsuredCollect, GroupAttachApplyInsuredCollectBo.class);
        // 主险人数、附加险人数
        mainQuantity = 0;
        additionQuantity = 0;
        additionQuantity18 = 0;
        additionQuantity26 = 0;
        additionQuantity27 = 0;
        additionQuantity33 = 0;
        quantityPreSchool29[0] = 0;
        quantityPreSchool33[0] = 0;
        quantityPrimarySchool29[0] = 0;
        quantityPrimarySchool33[0] = 0;
        quantitySecondarySchool29[0] = 0;
        quantitySecondarySchool33[0] = 0;
        quantityUniversity29[0] = 0;
        quantityUniversity33[0] = 0;
        quantityPreSchoolAmount29[0] = new BigDecimal(0);
        quantityPreSchoolAmount33[0] = new BigDecimal(0);
        quantityPrimarySchoolAmount29[0] = new BigDecimal(0);
        quantityPrimarySchoolAmount33[0] = new BigDecimal(0);
        quantitySecondarySchoolAmount29[0] = new BigDecimal(0);
        quantitySecondarySchoolAmount33[0] = new BigDecimal(0);
        quantityUniversityAmount29[0] = new BigDecimal(0);
        quantityUniversityAmount33[0] = new BigDecimal(0);
        for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
            for (ApplyCoverageBo applyCoverageBo : applyInsuredBo.getListCoverage()) {
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    mainQuantity++;
                }
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    additionQuantity++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_18.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity18++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_26.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity26++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_27.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity27++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity33++;
                }

                //29号和33号产品投保清单人数统计
                List<ApplyCoverageLevelPo> listCoverageLevel = applyCoverageBo.getListCoverageLevel();
                if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                    listCoverageLevel.forEach(applyCoverageLevelPo -> {
                        String productLevel = applyCoverageLevelPo.getProductLevel();
                        BigDecimal amount = applyCoverageLevelPo.getAmount();
                        if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool29[0]++;
                                quantityPreSchoolAmount29[0] = quantityPreSchoolAmount29[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool29[0]++;
                                quantityPrimarySchoolAmount29[0] = quantityPrimarySchoolAmount29[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool29[0]++;
                                quantitySecondarySchoolAmount29[0] = quantitySecondarySchoolAmount29[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity29[0]++;
                                quantityUniversityAmount29[0] = quantityUniversityAmount29[0].add(amount);
                            }

                        }
                        if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool33[0]++;
                                quantityPreSchoolAmount33[0] = quantityPreSchoolAmount33[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool33[0]++;
                                quantityPrimarySchoolAmount33[0] = quantityPrimarySchoolAmount33[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool33[0]++;
                                quantitySecondarySchoolAmount33[0] = quantitySecondarySchoolAmount33[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity33[0]++;
                                quantityUniversityAmount33[0] = quantityUniversityAmount33[0].add(amount);
                            }
                        }
                    });
                }
            }
        }
        groupAttachApplyInsuredCollectBo.setMainQuantity(mainQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity(additionQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity18(additionQuantity18);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity26(additionQuantity26);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity27(additionQuantity27);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity33(additionQuantity33);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool29(quantityPreSchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool33(quantityPreSchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool29(quantityPrimarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool33(quantityPrimarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool29(quantitySecondarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool33(quantitySecondarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity29(quantityUniversity29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity33(quantityUniversity33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount29(quantityPreSchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount33(quantityPreSchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount29(quantityPrimarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount33(quantityPrimarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount29(quantitySecondarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount33(quantitySecondarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount29(quantityUniversityAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount33(quantityUniversityAmount33[0]);
        printApplyInsuredBo.setApplyInsuredCollect(groupAttachApplyInsuredCollectBo);
        //如果员工总数为空，则重新计算员工总数
        if (!AssertUtils.isNotEmpty(printApplyInsuredBo.getGroupApplicant().getTotalEmployeeNum())) {
            List<ApplyInsuredBo> numLists = applyInsuredBos.stream().filter(applyInsuredBo -> "EMPLOYEE".equals(applyInsuredBo.getInsuredType())).collect(Collectors.toList());
            int size = numLists.size();
            printApplyInsuredBo.getGroupApplicant().setTotalEmployeeNum(size+"");
        }
        /********************************************查询投保单被保人信息****************************************/
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBos, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        applyInsuredBos.forEach(insuredBo -> {
            // 险种总保险金额、险种总保费
            insuredBo.getListCoverage().forEach(insuredCoverage -> {
                printApplyInsuredBo.getListGroupCoverage().stream()
                        .filter(coverage -> coverage.getProductId().equals(insuredCoverage.getProductId()))
                        .findFirst().ifPresent(coverage -> {
                            BigDecimal totalAmount = (AssertUtils.isNotNull(coverage.getTotalAmount()) ? coverage.getTotalAmount() : new BigDecimal(0)).add(new BigDecimal(AssertUtils.isNotEmpty(insuredCoverage.getAmount()) ? insuredCoverage.getAmount() : "0"));
                            coverage.setTotalAmount(totalAmount);
                            BigDecimal totalPremium = (AssertUtils.isNotNull(coverage.getTotalPremium()) ? coverage.getTotalPremium() : new BigDecimal(0)).add(AssertUtils.isNotNull(insuredCoverage.getTotalPremium()) ? insuredCoverage.getTotalPremium() : new BigDecimal(0));
                            coverage.setTotalPremium(totalPremium);
                        });
            });
        });
        // 投保单总保费
        printApplyInsuredBo.getListGroupCoverage().forEach(coverage -> {
            printApplyInsuredBo.setTotalPremium(printApplyInsuredBo.getTotalPremium().add(coverage.getTotalPremium()));
        });
        getLogger().info("=====================insured end=======================");
        /********************************************账户数据****************************************/
        List<SyscodeResponse> bankSyscodes = platformInternationalBaseApi.queryInternational(InternationalTypeEnum.BANK.name(), language).getData();
        List<ApplyAccountBo> applyAccountBos = applyBaseService.listApplyAccount(applyId);
        if (AssertUtils.isNotEmpty(applyAccountBos)) {
            applyAccountBos.forEach(applyAccountBo -> {
                applyAccountBo.setBankName(LanguageUtils.getNewCodeName(bankSyscodes, applyAccountBo.getBankCode()));
            });
            List<GroupAttachApplyAccountBo> applyAccountList = (List<GroupAttachApplyAccountBo>) this.converterList(applyAccountBos, new TypeToken<List<GroupAttachApplyAccountBo>>() {
            }.getType());
            printApplyInsuredBo.setApplyAccountList(applyAccountList);
        }

        //查询银行转账支付对应的银行
        if ("BANK_TRANSFER".equals(printApplyInsuredBo.getPaymentMode())) {
            ResultObject<PaymentStatusResponse> paymentStatusResponseResultObject = paymentBaseApi.queryOnePaymentDoByBusinessId(applyId);
            if (!AssertUtils.isResultObjectDataNull(paymentStatusResponseResultObject)) {
                PaymentStatusResponse response = paymentStatusResponseResultObject.getData();
                printApplyInsuredBo.setBankCode(response.getBankCode());
            }
        }

        ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest = new ElectronicPolicyGeneratorRequest();
        electronicPolicyGeneratorRequest.setContent(JackSonUtils.toJson(printApplyInsuredBo, language));
        electronicPolicyGeneratorRequest.setPdfType(GROUP_INSURED_LIST.name());
        electronicPolicyGeneratorRequest.setLanguage(language);

        // 主险产品ID
        String productId = applyCoveragePos.stream()
                .filter(coverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()))
                .findFirst().get().getProductId();
        electronicPolicyGeneratorRequest.setProductId(productId);
        ResultObject<List<AttachmentResponse>> attachmentResultObject = attachmentPDFDocumentApi.electronicPolicyGenerator(electronicPolicyGeneratorRequest);
        AssertUtils.isResultObjectError(log, attachmentResultObject);
        AttachmentResponse attachmentResponse = attachmentResultObject.getData().get(0);
        resultObject.setData(attachmentResponse);

        //保存被保人清单打印详情
        ApplyInsuredPrintDetailPo applyInsuredPrintDetailPo = new ApplyInsuredPrintDetailPo();
        applyInsuredPrintDetailPo.setApplyInsuredPrintDetailId(UUIDUtils.getUUIDShort());
        applyInsuredPrintDetailPo.setApplyId(applyId);
        applyInsuredPrintDetailPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        applyInsuredPrintDetailPo.setCreatedUserId(currentLoginUsers.getUserId());
        applyInsuredPrintDetailPo.setCreatedDate(DateUtils.getCurrentTime());
        applyInsuredPrintDetailPo.setApplyInsuredPrintId(printApplyInsuredBo.getApplyInsuredPrintId());
        applyInsuredPrintDetailPo.setApplyId(applyId);
        applyInsuredPrintDetailPo.setCompanyName(companyName);
        applyInsuredPrintDetailPo.setAttachmentId(attachmentResponse.getMediaId());
        applyInsuredPrintDetailPo.setLanguage(language);
        applyInsuredPrintDetailDao.insert(applyInsuredPrintDetailPo);

        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<GroupApplyInsuredPrintResponse>> queryListApplyInsuredPrint(GroupApplyInsuredPrintRequest groupApplyInsuredPrintRequest) {
        ResultObject<BasePageResponse<GroupApplyInsuredPrintResponse>> resultObject = new ResultObject<>();
        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintRequest.getKeyword())) {
            ResultObject<List<AgentKeyWordResponse>> agentsVagueGet = agentApi.agentsVagueGet(groupApplyInsuredPrintRequest.getKeyword());
            if (!AssertUtils.isResultObjectListDataNull(agentsVagueGet)) {
                List<String> agentIds = agentsVagueGet.getData().stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
                groupApplyInsuredPrintRequest.setAgentIds(agentIds);
            }
        }
        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintRequest.getAgentIds())) {
            groupApplyInsuredPrintRequest.setKeyword(null);
        }
        List<AgentResponse> agentResponseList = new ArrayList<>();
        List<ApplyInsuredPrintBo> applyInsuredPrintBoList = applyBaseService.queryApplyInsuredPrint(groupApplyInsuredPrintRequest);
        if (AssertUtils.isNotEmpty(applyInsuredPrintBoList)) {
            List<String> agentIds = applyInsuredPrintBoList.stream().map(ApplyInsuredPrintBo::getAgentId).distinct().collect(Collectors.toList());
            AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
            agentApplyQueryRequest.setListAgentId(agentIds);
            ResultObject<List<AgentResponse>> agentsGet = agentApi.agentsGet(agentApplyQueryRequest);
            if (!AssertUtils.isResultObjectListDataNull(agentsGet)) {
                agentResponseList.addAll(agentsGet.getData());
            }
        }
        List<GroupApplyInsuredPrintResponse> groupApplyInsuredPrintResponses = (List<GroupApplyInsuredPrintResponse>) this.converterList(applyInsuredPrintBoList, new TypeToken<List<GroupApplyInsuredPrintResponse>>() {
        }.getType());
        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintResponses)) {
            groupApplyInsuredPrintResponses.forEach(groupApplyInsuredPrintResponse -> {
                groupApplyInsuredPrintResponse.setApplyDate(DateUtils.timeStrToString(groupApplyInsuredPrintResponse.getApplyDate(), DateUtils.FORMATE5));
                if (AssertUtils.isNotEmpty(agentResponseList)) {
                    agentResponseList.forEach(agentResponse -> {
                        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintResponse.getAgentId())) {
                            if (groupApplyInsuredPrintResponse.getAgentId().equals(agentResponse.getAgentId())) {
                                groupApplyInsuredPrintResponse.setAgentName(agentResponse.getAgentName());
                            }
                        }
                    });
                }
            });
        } else {
            return resultObject;
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(applyInsuredPrintBoList) ? applyInsuredPrintBoList.get(0).getTotalLine() : null;
        BasePageResponse<GroupApplyInsuredPrintResponse> basePageResponse = BasePageResponse.getData(groupApplyInsuredPrintRequest.getCurrentPage(), groupApplyInsuredPrintRequest.getPageSize(), totalLine, groupApplyInsuredPrintResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<GroupApplyInsuredPrintResponse>> queryListApplyInsuredPrintManage(GroupApplyInsuredPrintRequest groupApplyInsuredPrintRequest) {
        ResultObject<BasePageResponse<GroupApplyInsuredPrintResponse>> resultObject = new ResultObject<>();
        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintRequest.getKeyword())) {
            ResultObject<List<AgentKeyWordResponse>> agentsVagueGet = agentApi.agentsVagueGet(groupApplyInsuredPrintRequest.getKeyword());
            if (!AssertUtils.isResultObjectListDataNull(agentsVagueGet)) {
                List<String> agentIds = agentsVagueGet.getData().stream().map(AgentKeyWordResponse::getAgentId).distinct().collect(Collectors.toList());
                groupApplyInsuredPrintRequest.setAgentIds(agentIds);
            }
        }
        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintRequest.getAgentIds())) {
            groupApplyInsuredPrintRequest.setKeyword(null);
        }
        List<AgentResponse> agentResponseList = new ArrayList<>();
        List<ApplyInsuredPrintBo> applyInsuredPrintBoList = applyBaseService.queryApplyInsuredPrintManage(groupApplyInsuredPrintRequest);
        if (AssertUtils.isNotEmpty(applyInsuredPrintBoList)) {
            List<String> agentIds = applyInsuredPrintBoList.stream().map(ApplyInsuredPrintBo::getAgentId).distinct().collect(Collectors.toList());
            AgentApplyQueryRequest agentApplyQueryRequest = new AgentApplyQueryRequest();
            agentApplyQueryRequest.setListAgentId(agentIds);
            ResultObject<List<AgentResponse>> agentsGet = agentApi.agentsGet(agentApplyQueryRequest);
            if (!AssertUtils.isResultObjectListDataNull(agentsGet)) {
                agentResponseList.addAll(agentsGet.getData());
            }
        }
        List<GroupApplyInsuredPrintResponse> groupApplyInsuredPrintResponses = (List<GroupApplyInsuredPrintResponse>) this.converterList(applyInsuredPrintBoList, new TypeToken<List<GroupApplyInsuredPrintResponse>>() {
        }.getType());
        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintResponses)) {
            groupApplyInsuredPrintResponses.forEach(groupApplyInsuredPrintResponse -> {
                groupApplyInsuredPrintResponse.setApplyDate(DateUtils.timeStrToString(groupApplyInsuredPrintResponse.getApplyDate(), DateUtils.FORMATE5));
                if (AssertUtils.isNotEmpty(agentResponseList)) {
                    agentResponseList.forEach(agentResponse -> {
                        if (AssertUtils.isNotEmpty(groupApplyInsuredPrintResponse.getAgentId())) {
                            if (groupApplyInsuredPrintResponse.getAgentId().equals(agentResponse.getAgentId())) {
                                groupApplyInsuredPrintResponse.setAgentName(agentResponse.getAgentName());
                            }
                        }
                    });
                }
            });
        } else {
            return resultObject;
        }
        //获取总页数
        Integer totalLine = AssertUtils.isNotNull(applyInsuredPrintBoList) ? applyInsuredPrintBoList.get(0).getTotalLine() : null;
        BasePageResponse<GroupApplyInsuredPrintResponse> basePageResponse = BasePageResponse.getData(groupApplyInsuredPrintRequest.getCurrentPage(), groupApplyInsuredPrintRequest.getPageSize(), totalLine, groupApplyInsuredPrintResponses);
        resultObject.setData(basePageResponse);
        return resultObject;
    }

    @Transactional
    @Override
    public ResultObject insuredPrintManage(Users users, HttpServletResponse httpServletResponse, String applyId, String languageCode)  {
        ResultObject resultObject = new ResultObject();
        try {
            String achmentId;
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), applyId, GroupErrorConfigEnum.GROUP_APPLY_PARAMETER_APPLY_ID_IS_NOT_NULL);
            ApplyInsuredPrintDetailPo applyInsuredPrintDetailPo = applyBaseService.queryApplyInsuredPrintPoByLanguage(applyId, languageCode);
            if (AssertUtils.isNotNull(applyInsuredPrintDetailPo) && AssertUtils.isNotEmpty(applyInsuredPrintDetailPo.getAttachmentId())) {
                achmentId = applyInsuredPrintDetailPo.getAttachmentId();
            }else {
                achmentId = this.getGenerateAttachmentId(applyId, users, languageCode);
            }
            //下载PDF保单文件
            ResultObject<AttachmentByteResponse> attachmentByteResFcResultObject = attachmentPDFDocumentApi.electronicPolicyDownload(achmentId);
            this.getLogger().info(attachmentByteResFcResultObject.getMessage());
            if (!AssertUtils.isResultObjectDataNull(attachmentByteResFcResultObject)) {
                AttachmentByteResponse data = attachmentByteResFcResultObject.getData();
                // 返回文件流
                byte[] bytesFile = data.getFileByte();
                System.out.println(bytesFile.length);
                httpServletResponse.setHeader("Content-Type", "application/pdf");
                httpServletResponse.addHeader("Content-Disposition", "inline;filename=" + URLEncoder.encode("group-insured.pdf", "UTF-8"));
                httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
                httpServletResponse.setHeader("Access-Control-Allow-Methods", "POST,GET");
                httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
                ServletOutputStream outputStream = httpServletResponse.getOutputStream();
                outputStream.write(bytesFile);
                return resultObject;
            }
            // 其他任何情况都为失败
            throw new RequestException(GROUP_APPLY_ATTACHMENTS_PRINT_ERROR);
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, GroupErrorConfigEnum.GROUP_APPLY_ATTACHMENTS_PRINT_ERROR);
        }
        return resultObject;
    }

    private String getGenerateAttachmentId(String applyId, Users currentLoginUsers, String language) {
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), applyId, GROUP_APPLY_PARAMETER_APPLY_ID_IS_NOT_NULL);
        AssertUtils.isNotEmpty(this.getLogger(), language, APPLY_PARAMETER_LANGUAGE_IS_NOT_NULL);
        // 查询打印数据
        PrintApplyInsuredBo printApplyInsuredBo = applyBaseService.queryPrintApplyDataNew(applyId);
        AssertUtils.isNotNull(log, printApplyInsuredBo, APPLY_BASE_PARAMETER_APPLY_NOT_EXISTENT);
        String companyName = printApplyInsuredBo.getCompanyName();
        ApplyHealthQuestionnaireRemarkPo applyHealthQuestionnaireRemark = applyRemarkBaseService.getApplyHealthQuestionnaireRemark(applyId, ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
        printApplyInsuredBo.setRemark(AssertUtils.isNotNull(applyHealthQuestionnaireRemark) ? applyHealthQuestionnaireRemark.getRemark() : null);
        printApplyInsuredBo.setCompanyName(companyName);
        printApplyInsuredBo.setPrintDate(DateUtils.getCurrentTime());
        AssertUtils.isNotEmpty(log, printApplyInsuredBo.getAgentId(), APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
        ResultObject<AgentResponse> agentInfoByCode = agentApi.agentByIdGet(printApplyInsuredBo.getAgentId());
        AssertUtils.isResultObjectError(log, agentInfoByCode);
        AgentResponse agentInfoByCodeData = agentInfoByCode.getData();
        printApplyInsuredBo.setAgentMobile(agentInfoByCodeData.getMobile());
        printApplyInsuredBo.setAgentName(agentInfoByCodeData.getAgentName());
        //性别国际化
        List<SyscodeRespFc> genderList = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.GENDER.name(), language).getData();
        //证件类型国际化
        List<SyscodeRespFc> idTypeList = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.ID_TYPE.name(), language).getData();
        // 与投保人关系
        List<SyscodeRespFc> relationshipWithTheApplicantSyscodes = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_APPLICANT.name(), language).getData();
        // 险种档次国际化
        List<SyscodeRespFc> productLevelSyscodes = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.PRODUCT_PRODUCT_LEVEL.name(), language).getData();

        List<ApplyInsuredBo> applyInsuredBos = applyInsuredBaseService.listApplyInsured(applyId);
        AssertUtils.isNotEmpty(log, applyInsuredBos, APPLY_QUERY_INSURED_ERROR);
        // 职业国际化
        List<String> occupationCodeList = applyInsuredBos.stream().map(ApplyInsuredBo::getOccupationCode)
                .filter(AssertUtils::isNotEmpty).collect(Collectors.toList());
        List<CareerResponse> careerList = null;
        if (AssertUtils.isNotEmpty(occupationCodeList)) {
            careerList = platformCareerApi.careerInfoPost(occupationCodeList, language).getData();
        }
        List<CareerResponse> finalCareerList = careerList;
        applyInsuredBos.forEach(applyInsuredBo -> {
            applyInsuredBo.setSexName(LanguageUtils.getCodeName(genderList, applyInsuredBo.getSex()));
            applyInsuredBo.setOccupationName(LanguageUtils.getCareerName(finalCareerList, applyInsuredBo.getOccupationCode()));
            applyInsuredBo.setIdTypeName(LanguageUtils.getCodeName(idTypeList, applyInsuredBo.getIdType()));
        });
        printApplyInsuredBo.setApplyInsuredList(applyInsuredBos);
        List<ApplyBeneficiaryBo> applyBeneficiaryBoList = applyBaseService.queryApplyBeneficiaryByApplyId(applyId);
        if (AssertUtils.isNotEmpty(applyBeneficiaryBoList)) {
            applyBeneficiaryBoList.forEach(applyBeneficiaryBo -> {
                applyBeneficiaryBo.setSexName(LanguageUtils.getCodeName(genderList, applyBeneficiaryBo.getSex()));
                if (AssertUtils.isNotEmpty(applyBeneficiaryBo.getIdType())) {
                    applyBeneficiaryBo.setIdTypeName(LanguageUtils.getCodeName(idTypeList, applyBeneficiaryBo.getIdType()));
                }
                applyBeneficiaryBo.setRelationshipName(LanguageUtils.getCodeName(relationshipWithTheApplicantSyscodes, applyBeneficiaryBo.getRelationship()));
            });
        }
        printApplyInsuredBo.setApplyBeneficiaryBoList(applyBeneficiaryBoList);
        List<ApplyGroupHealthQuestionnaireAnswerPo> applyGroupHealthQuestionnaireAnswerPos = applyBaseService.queryApplyGroupHealthQuestionnaireAnswer(applyId, null);
        printApplyInsuredBo.setAghqaList(applyGroupHealthQuestionnaireAnswerPos);
        List<ApplyCoveragePo> applyCoveragePos = applyBaseDao.getApplyCoverageList(applyId, null);
        AssertUtils.isNotEmpty(log, applyCoveragePos, APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        printApplyInsuredBo.setApplyCoverageList(applyCoveragePos);
        List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.listApplyCoverageLevel(applyId);
        List<GroupAttachInsuredCoverageLevelBo> groupAttachInsuredCoverageLevelBoList = (List<GroupAttachInsuredCoverageLevelBo>) this.converterList(
                applyCoverageLevelPos, new TypeToken<List<GroupAttachInsuredCoverageLevelBo>>() {
                }.getType()
        );
        // 险种档次 国际化
        if (AssertUtils.isNotEmpty(groupAttachInsuredCoverageLevelBoList)) {
            groupAttachInsuredCoverageLevelBoList.forEach(groupAttachInsuredCoverageLevelBo -> {
                groupAttachInsuredCoverageLevelBo.setProductLevelI18n(LanguageUtils.getCodeName(productLevelSyscodes, groupAttachInsuredCoverageLevelBo.getProductLevel()));
            });
        }
        printApplyInsuredBo.setApplyCoverageLevelList(groupAttachInsuredCoverageLevelBoList);

        List<ApplyCoverageDutyBo> applyCoverageDutyList = applyCoverageBaseService.getApplyCoverageDutyList(applyId);
        printApplyInsuredBo.setApplyCoverageDutyList(applyCoverageDutyList);

        // 设置投保人信息
        ApplyApplicantPo applyApplicantPo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(getLogger(), applyApplicantPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        GroupAttachApplyApplicantBo groupAttachApplyApplicantBo = new GroupAttachApplyApplicantBo();
        ClazzUtils.copyPropertiesIgnoreNull(applyApplicantPo, groupAttachApplyApplicantBo);
        // 单位地区
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyAreaCode())) {
            ResultObject<AreaNameResponse> areaNameResultObject = platformAreaApi.areaNameGet(groupAttachApplyApplicantBo.getCompanyAreaCode(), language);
            if (!AssertUtils.isResultObjectDataNull(areaNameResultObject) && AssertUtils.isNotEmpty(areaNameResultObject.getData().getAreaName())) {
                groupAttachApplyApplicantBo.setCompanyAreaName(areaNameResultObject.getData().getAreaName());
                groupAttachApplyApplicantBo.setCompanyAddressWhole(areaNameResultObject.getData().getAreaName() + groupAttachApplyApplicantBo.getCompanyAddress());
            }
        }
        // 所属行业
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyIndustry())) {
            ResultObject<CareerResponse> careerResultObject = platformCareerApi.careerInfoGet(groupAttachApplyApplicantBo.getCompanyIndustry(), language);
            if (!AssertUtils.isResultObjectDataNull(careerResultObject) && AssertUtils.isNotEmpty(careerResultObject.getData().getCareerName())) {
                groupAttachApplyApplicantBo.setCompanyIndustryName(careerResultObject.getData().getCareerName());
            }
        }
        //法人国籍国际化
        groupAttachApplyApplicantBo.setCompanyLegalPersonNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyLegalPersonNationality(), language));
        groupAttachApplyApplicantBo.setCompanyContractNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyContractNationality(), language));

        groupAttachApplyApplicantBo.setCompanyTypeName(languageCodeTransData.queryOneInternational(com.gclife.common.TerminologyTypeEnum.COMPANY_TYPE.name(), groupAttachApplyApplicantBo.getCompanyType(), language));

        printApplyInsuredBo.setGroupApplicant(groupAttachApplyApplicantBo);

        // 设置代理人信息
        ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(applyId);
        AssertUtils.isNotNull(getLogger(), applyAgentPo, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
        // 调代理人服务
        AgentBaseResponse agentBaseResponse = agentBaseAgentApi.queryOneAgentById(applyAgentPo.getAgentId()).getData();
        GroupAttachApplyAgentBo groupAttachApplyAgentBo = new GroupAttachApplyAgentBo();
        groupAttachApplyAgentBo.setAgentId(applyAgentPo.getAgentId());
        groupAttachApplyAgentBo.setAgentCode(applyAgentPo.getAgentCode());
        groupAttachApplyAgentBo.setAgentName(agentBaseResponse.getAgentName());
        groupAttachApplyAgentBo.setMobile(agentBaseResponse.getAgentDetail().getMobile());
        printApplyInsuredBo.setGroupAgent(groupAttachApplyAgentBo);

        // 设置被保险人统计信息
        ApplyInsuredCollectPo applyInsuredCollect = applyBaseDao.getApplyInsuredCollect(applyId);
        AssertUtils.isNotNull(getLogger(), applyInsuredCollect, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_COLLECT_IS_NOT_FOUND_OBJECT);
        GroupAttachApplyInsuredCollectBo groupAttachApplyInsuredCollectBo = (GroupAttachApplyInsuredCollectBo) this.converterObject(applyInsuredCollect, GroupAttachApplyInsuredCollectBo.class);
        // 主险人数、附加险人数
        long mainQuantity = 0;
        long additionQuantity = 0;
        long additionQuantity18 = 0;
        long additionQuantity26 = 0;
        long additionQuantity27 = 0;
        long additionQuantity33 = 0;
        final long[] quantityPreSchool29 = {0};
        final long[] quantityPreSchool33 = {0};
        final long[] quantityPrimarySchool29 = {0};
        final long[] quantityPrimarySchool33 = {0};
        final long[] quantitySecondarySchool29 = {0};
        final long[] quantitySecondarySchool33 = {0};
        final long[] quantityUniversity29 = {0};
        final long[] quantityUniversity33 = {0};
        final BigDecimal[] quantityPreSchoolAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantityPreSchoolAmount33 = {new BigDecimal(0)};
        final BigDecimal[] quantityPrimarySchoolAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantityPrimarySchoolAmount33 = {new BigDecimal(0)};
        final BigDecimal[] quantitySecondarySchoolAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantitySecondarySchoolAmount33 = {new BigDecimal(0)};
        final BigDecimal[] quantityUniversityAmount29 = {new BigDecimal(0)};
        final BigDecimal[] quantityUniversityAmount33 = {new BigDecimal(0)};
        for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
            for (ApplyCoverageBo applyCoverageBo : applyInsuredBo.getListCoverage()) {
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    mainQuantity++;
                }
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    additionQuantity++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_18.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity18++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_26.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity26++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_27.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity27++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity33++;
                }

                //29号和33号产品投保清单人数统计
                List<ApplyCoverageLevelPo> listCoverageLevel = applyCoverageBo.getListCoverageLevel();
                if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                    listCoverageLevel.forEach(applyCoverageLevelPo -> {
                        String productLevel = applyCoverageLevelPo.getProductLevel();
                        BigDecimal amount = applyCoverageLevelPo.getAmount();
                        if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool29[0]++;
                                quantityPreSchoolAmount29[0] = quantityPreSchoolAmount29[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool29[0]++;
                                quantityPrimarySchoolAmount29[0] = quantityPrimarySchoolAmount29[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool29[0]++;
                                quantitySecondarySchoolAmount29[0] = quantitySecondarySchoolAmount29[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity29[0]++;
                                quantityUniversityAmount29[0] = quantityUniversityAmount29[0].add(amount);
                            }

                        }
                        if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool33[0]++;
                                quantityPreSchoolAmount33[0] = quantityPreSchoolAmount33[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool33[0]++;
                                quantityPrimarySchoolAmount33[0] = quantityPrimarySchoolAmount33[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool33[0]++;
                                quantitySecondarySchoolAmount33[0] = quantitySecondarySchoolAmount33[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity33[0]++;
                                quantityUniversityAmount33[0] = quantityUniversityAmount33[0].add(amount);
                            }
                        }
                    });
                }
            }
        }
        groupAttachApplyInsuredCollectBo.setMainQuantity(mainQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity(additionQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity18(additionQuantity18);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity26(additionQuantity26);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity27(additionQuantity27);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity33(additionQuantity33);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool29(quantityPreSchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool33(quantityPreSchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool29(quantityPrimarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool33(quantityPrimarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool29(quantitySecondarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool33(quantitySecondarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity29(quantityUniversity29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity33(quantityUniversity33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount29(quantityPreSchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount33(quantityPreSchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount29(quantityPrimarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount33(quantityPrimarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount29(quantitySecondarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount33(quantitySecondarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount29(quantityUniversityAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount33(quantityUniversityAmount33[0]);
        printApplyInsuredBo.setApplyInsuredCollect(groupAttachApplyInsuredCollectBo);

        // =====================投保单数据封装=====================
        // 查询投保单基础信息
        ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
        AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
        ClazzUtils.copyPropertiesIgnoreNull(applyPo, printApplyInsuredBo);
        getLogger().info("=====================apply end=======================");

        /********************************** 查询投保单代理人信息********************************************/
        applyAgentPo = applyAgentBaseService.queryApplyAgent(applyId);
        AssertUtils.isNotNull(getLogger(), applyAgentPo, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
        // 调代理人服务
        agentBaseResponse = agentBaseAgentApi.queryOneAgentById(applyAgentPo.getAgentId()).getData();
        groupAttachApplyAgentBo = new GroupAttachApplyAgentBo();
        groupAttachApplyAgentBo.setAgentId(applyAgentPo.getAgentId());
        groupAttachApplyAgentBo.setAgentCode(applyAgentPo.getAgentCode());
        groupAttachApplyAgentBo.setAgentName(agentBaseResponse.getAgentName());
        groupAttachApplyAgentBo.setMobile(agentBaseResponse.getAgentDetail().getMobile());
        printApplyInsuredBo.setGroupAgent(groupAttachApplyAgentBo);
        getLogger().info("=====================agent end=======================");
        /********************************** 查询保单投保人信息********************************************/
        applyApplicantPo = applyApplicantBaseService.queryApplyApplicant(applyId);
        AssertUtils.isNotNull(getLogger(), applyApplicantPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
        groupAttachApplyApplicantBo = new GroupAttachApplyApplicantBo();
        ClazzUtils.copyPropertiesIgnoreNull(applyApplicantPo, groupAttachApplyApplicantBo);
        // 单位地区
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyAreaCode())) {
            ResultObject<AreaNameResponse> areaNameResultObject = platformAreaApi.areaNameGet(groupAttachApplyApplicantBo.getCompanyAreaCode(), language);
            if (!AssertUtils.isResultObjectDataNull(areaNameResultObject) && AssertUtils.isNotEmpty(areaNameResultObject.getData().getAreaName())) {
                groupAttachApplyApplicantBo.setCompanyAreaName(areaNameResultObject.getData().getAreaName());
                groupAttachApplyApplicantBo.setCompanyAddressWhole(areaNameResultObject.getData().getAreaName() + groupAttachApplyApplicantBo.getCompanyAddress());
            }
        }
        // 所属行业
        if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyIndustry())) {
            ResultObject<CareerResponse> careerResultObject = platformCareerApi.careerInfoGet(groupAttachApplyApplicantBo.getCompanyIndustry(), language);
            if (!AssertUtils.isResultObjectDataNull(careerResultObject) && AssertUtils.isNotEmpty(careerResultObject.getData().getCareerName())) {
                groupAttachApplyApplicantBo.setCompanyIndustryName(careerResultObject.getData().getCareerName());
            }
        }
        //法人国籍国际化
        groupAttachApplyApplicantBo.setCompanyLegalPersonNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyLegalPersonNationality(), language));
        groupAttachApplyApplicantBo.setCompanyContractNationalityName(languageCodeTransData.getCodeNameByKey(com.gclife.common.TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyContractNationality(), language));

        groupAttachApplyApplicantBo.setCompanyTypeName(languageCodeTransData.queryOneInternational(com.gclife.common.TerminologyTypeEnum.COMPANY_TYPE.name(), groupAttachApplyApplicantBo.getCompanyType(), language));

        printApplyInsuredBo.setGroupApplicant(groupAttachApplyApplicantBo);
        getLogger().info("=====================applicant end=======================");
        /********************************** 查询保单险种信息********************************************/
        applyCoveragePos = applyCoverageBaseService.listApplyCoverage(applyId);
        AssertUtils.isNotEmpty(getLogger(), applyCoveragePos, ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        // 缴费期限单位
        List<SyscodeResponse> premiumPeriodUnitSyscodes = platformTerminologyBaseApi.queryTerminologyInternation(com.gclife.common.TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(), language).getData();
        // 保障期限单位
        List<SyscodeResponse> coveragePeriodUnitSyscodes = platformTerminologyBaseApi.queryTerminologyInternation(com.gclife.common.TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), language).getData();

        List<GroupAttachApplyCoverageBo> groupAttachApplyCoverageBos = new ArrayList<>();
        applyCoveragePos.forEach(applyCoveragePo -> {
            GroupAttachApplyCoverageBo applyCoverageBo = new GroupAttachApplyCoverageBo();
            applyInsuredBos.forEach(policyInsuredBo -> {
                List<ApplyCoverageBo> listCoverage = policyInsuredBo.getListCoverage();
                if (AssertUtils.isNotEmpty(listCoverage)) {
                    Optional<ApplyCoverageBo> coverageOptional = listCoverage.stream().filter(coverage -> coverage.getProductId().equals(applyCoveragePo.getProductId())).findFirst();
                    if (coverageOptional.isPresent()) {
                        ApplyCoverageBo coverageBo = coverageOptional.get();
                        ClazzUtils.copyPropertiesIgnoreNull(coverageBo, applyCoverageBo);
                        return;
                    }
                }
            });
            ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, applyCoverageBo);

            applyCoverageBo.setTotalAmount(BigDecimal.ZERO);
            applyCoverageBo.setTotalPremium(BigDecimal.ZERO);

            // 缴费期限单位
            if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnit()) && AssertUtils.isNotEmpty(premiumPeriodUnitSyscodes)) {
                premiumPeriodUnitSyscodes.stream()
                        .filter(syscode -> syscode.getCodeKey().equals(applyCoverageBo.getPremiumPeriodUnit()))
                        .findFirst().ifPresent(syscode -> applyCoverageBo.setPremiumPeriodUnitName(syscode.getCodeName()));
            }
            // 保障期限单位
            if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnit()) && AssertUtils.isNotEmpty(coveragePeriodUnitSyscodes)) {
                coveragePeriodUnitSyscodes.stream()
                        .filter(syscode -> syscode.getCodeKey().equals(applyCoverageBo.getCoveragePeriodUnit()))
                        .findFirst().ifPresent(syscode -> applyCoverageBo.setCoveragePeriodUnitName(syscode.getCodeName()));
            }
            applyCoverageBo.setPrimaryFlag(applyCoveragePo.getPrimaryFlag());
            applyCoverageBo.setProductLevel(applyCoveragePo.getProductLevel());
            groupAttachApplyCoverageBos.add(applyCoverageBo);
        });
        printApplyInsuredBo.setListGroupCoverage(groupAttachApplyCoverageBos);
        getLogger().info("=====================coverage end=======================");
        /********************************************设置被保险人统计信息****************************************/
        applyInsuredCollect = applyBaseDao.getApplyInsuredCollect(applyId);
        AssertUtils.isNotNull(getLogger(), applyInsuredCollect, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_COLLECT_IS_NOT_FOUND_OBJECT);
        groupAttachApplyInsuredCollectBo = (GroupAttachApplyInsuredCollectBo) this.converterObject(applyInsuredCollect, GroupAttachApplyInsuredCollectBo.class);
        // 主险人数、附加险人数
        mainQuantity = 0;
        additionQuantity = 0;
        additionQuantity18 = 0;
        additionQuantity26 = 0;
        additionQuantity27 = 0;
        additionQuantity33 = 0;
        quantityPreSchool29[0] = 0;
        quantityPreSchool33[0] = 0;
        quantityPrimarySchool29[0] = 0;
        quantityPrimarySchool33[0] = 0;
        quantitySecondarySchool29[0] = 0;
        quantitySecondarySchool33[0] = 0;
        quantityUniversity29[0] = 0;
        quantityUniversity33[0] = 0;
        quantityPreSchoolAmount29[0] = new BigDecimal(0);
        quantityPreSchoolAmount33[0] = new BigDecimal(0);
        quantityPrimarySchoolAmount29[0] = new BigDecimal(0);
        quantityPrimarySchoolAmount33[0] = new BigDecimal(0);
        quantitySecondarySchoolAmount29[0] = new BigDecimal(0);
        quantitySecondarySchoolAmount33[0] = new BigDecimal(0);
        quantityUniversityAmount29[0] = new BigDecimal(0);
        quantityUniversityAmount33[0] = new BigDecimal(0);
        for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
            for (ApplyCoverageBo applyCoverageBo : applyInsuredBo.getListCoverage()) {
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    mainQuantity++;
                }
                if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(applyCoverageBo.getPrimaryFlag())) {
                    additionQuantity++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_18.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity18++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_26.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity26++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_27.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity27++;
                }
                if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                    additionQuantity33++;
                }

                //29号和33号产品投保清单人数统计
                List<ApplyCoverageLevelPo> listCoverageLevel = applyCoverageBo.getListCoverageLevel();
                if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                    listCoverageLevel.forEach(applyCoverageLevelPo -> {
                        String productLevel = applyCoverageLevelPo.getProductLevel();
                        BigDecimal amount = applyCoverageLevelPo.getAmount();
                        if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool29[0]++;
                                quantityPreSchoolAmount29[0] = quantityPreSchoolAmount29[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool29[0]++;
                                quantityPrimarySchoolAmount29[0] = quantityPrimarySchoolAmount29[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool29[0]++;
                                quantitySecondarySchoolAmount29[0] = quantitySecondarySchoolAmount29[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity29[0]++;
                                quantityUniversityAmount29[0] = quantityUniversityAmount29[0].add(amount);
                            }

                        }
                        if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                            if (PRE_SCHOOL.name().equals(productLevel)) {
                                quantityPreSchool33[0]++;
                                quantityPreSchoolAmount33[0] = quantityPreSchoolAmount33[0].add(amount);
                            }
                            if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                quantityPrimarySchool33[0]++;
                                quantityPrimarySchoolAmount33[0] = quantityPrimarySchoolAmount33[0].add(amount);
                            }
                            if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                quantitySecondarySchool33[0]++;
                                quantitySecondarySchoolAmount33[0] = quantitySecondarySchoolAmount33[0].add(amount);
                            }
                            if (UNIVERSITY.name().equals(productLevel)) {
                                quantityUniversity33[0]++;
                                quantityUniversityAmount33[0] = quantityUniversityAmount33[0].add(amount);
                            }
                        }
                    });
                }
            }
        }
        groupAttachApplyInsuredCollectBo.setMainQuantity(mainQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity(additionQuantity);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity18(additionQuantity18);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity26(additionQuantity26);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity27(additionQuantity27);
        groupAttachApplyInsuredCollectBo.setAdditionQuantity33(additionQuantity33);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool29(quantityPreSchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchool33(quantityPreSchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool29(quantityPrimarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool33(quantityPrimarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool29(quantitySecondarySchool29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool33(quantitySecondarySchool33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity29(quantityUniversity29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversity33(quantityUniversity33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount29(quantityPreSchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount33(quantityPreSchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount29(quantityPrimarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount33(quantityPrimarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount29(quantitySecondarySchoolAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount33(quantitySecondarySchoolAmount33[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount29(quantityUniversityAmount29[0]);
        groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount33(quantityUniversityAmount33[0]);
        printApplyInsuredBo.setApplyInsuredCollect(groupAttachApplyInsuredCollectBo);
        //如果员工总数为空，则重新计算员工总数
        if (!AssertUtils.isNotEmpty(printApplyInsuredBo.getGroupApplicant().getTotalEmployeeNum())) {
            List<ApplyInsuredBo> numLists = applyInsuredBos.stream().filter(applyInsuredBo -> "EMPLOYEE".equals(applyInsuredBo.getInsuredType())).collect(Collectors.toList());
            int size = numLists.size();
            printApplyInsuredBo.getGroupApplicant().setTotalEmployeeNum(size+"");
        }
        /********************************************查询投保单被保人信息****************************************/
        AssertUtils.isNotEmpty(getLogger(), applyInsuredBos, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
        applyInsuredBos.forEach(insuredBo -> {
            // 险种总保险金额、险种总保费
            insuredBo.getListCoverage().forEach(insuredCoverage -> {
                printApplyInsuredBo.getListGroupCoverage().stream()
                        .filter(coverage -> coverage.getProductId().equals(insuredCoverage.getProductId()))
                        .findFirst().ifPresent(coverage -> {
                            BigDecimal totalAmount = (AssertUtils.isNotNull(coverage.getTotalAmount()) ? coverage.getTotalAmount() : new BigDecimal(0)).add(new BigDecimal(AssertUtils.isNotEmpty(insuredCoverage.getAmount()) ? insuredCoverage.getAmount() : "0"));
                            coverage.setTotalAmount(totalAmount);
                            BigDecimal totalPremium = (AssertUtils.isNotNull(coverage.getTotalPremium()) ? coverage.getTotalPremium() : new BigDecimal(0)).add(AssertUtils.isNotNull(insuredCoverage.getTotalPremium()) ? insuredCoverage.getTotalPremium() : new BigDecimal(0));
                            coverage.setTotalPremium(totalPremium);
                        });
            });
        });
        // 投保单总保费
        printApplyInsuredBo.getListGroupCoverage().forEach(coverage -> {
            printApplyInsuredBo.setTotalPremium(printApplyInsuredBo.getTotalPremium().add(coverage.getTotalPremium()));
        });
        getLogger().info("=====================insured end=======================");
        /********************************************账户数据****************************************/
        List<SyscodeResponse> bankSyscodes = platformInternationalBaseApi.queryInternational(InternationalTypeEnum.BANK.name(), language).getData();
        List<ApplyAccountBo> applyAccountBos = applyBaseService.listApplyAccount(applyId);
        if (AssertUtils.isNotEmpty(applyAccountBos)) {
            applyAccountBos.forEach(applyAccountBo -> {
                applyAccountBo.setBankName(LanguageUtils.getNewCodeName(bankSyscodes, applyAccountBo.getBankCode()));
            });
            List<GroupAttachApplyAccountBo> applyAccountList = (List<GroupAttachApplyAccountBo>) this.converterList(applyAccountBos, new TypeToken<List<GroupAttachApplyAccountBo>>() {
            }.getType());
            printApplyInsuredBo.setApplyAccountList(applyAccountList);
        }

        ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest = new ElectronicPolicyGeneratorRequest();
        electronicPolicyGeneratorRequest.setContent(JackSonUtils.toJson(printApplyInsuredBo, language));
        electronicPolicyGeneratorRequest.setPdfType(GROUP_INSURED_LIST.name());
        electronicPolicyGeneratorRequest.setLanguage(language);

        // 主险产品ID
        String productId = applyCoveragePos.stream()
                .filter(coverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()))
                .findFirst().get().getProductId();
        electronicPolicyGeneratorRequest.setProductId(productId);
        ResultObject<List<AttachmentResponse>> attachmentResultObject = attachmentPDFDocumentApi.electronicPolicyGenerator(electronicPolicyGeneratorRequest);
        AssertUtils.isResultObjectError(log, attachmentResultObject);
        AttachmentResponse attachmentResponse = attachmentResultObject.getData().get(0);
        //resultObject.setData(attachmentResponse);

        //保存被保人清单打印详情
        ApplyInsuredPrintDetailPo applyInsuredPrintDetailPo = new ApplyInsuredPrintDetailPo();
        applyInsuredPrintDetailPo.setApplyInsuredPrintDetailId(UUIDUtils.getUUIDShort());
        applyInsuredPrintDetailPo.setApplyId(applyId);
        applyInsuredPrintDetailPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.effective.name());
        applyInsuredPrintDetailPo.setCreatedUserId(currentLoginUsers.getUserId());
        applyInsuredPrintDetailPo.setCreatedDate(DateUtils.getCurrentTime());
        applyInsuredPrintDetailPo.setApplyInsuredPrintId(printApplyInsuredBo.getApplyInsuredPrintId());
        applyInsuredPrintDetailPo.setApplyId(applyId);
        applyInsuredPrintDetailPo.setCompanyName(companyName);
        applyInsuredPrintDetailPo.setAttachmentId(attachmentResponse.getMediaId());
        applyInsuredPrintDetailPo.setLanguage(language);
        applyInsuredPrintDetailDao.insert(applyInsuredPrintDetailPo);
        return attachmentResponse.getMediaId();
    }


    @Override
    public ResultObject insuredPrintEnd(Users users, String applyInsuredPrintId) {
        ResultObject resultObject = new ResultObject();
        // 参数校验
        AssertUtils.isNotEmpty(this.getLogger(), applyInsuredPrintId, GroupErrorConfigEnum.GROUP_APPLY_INSURED_PRINT_ID_IS_NOT_NULL);
        ApplyInsuredPrintPo applyInsuredPrintPo = applyBaseService.queryApplyInsuredPrintPo(applyInsuredPrintId);
        // 数据校验
        AssertUtils.isNotNull(this.getLogger(), applyInsuredPrintPo, GroupErrorConfigEnum.GROUP_APPLY_INSURED_PRINT_IS_NOT_NULL);
        // 更新保单打印表
        applyInsuredPrintPo.setPrintEndFlag(TerminologyConfigEnum.WHETHER.YES.name());
        applyBaseService.saveApplyInsuredPrint(users.getUserId(), applyInsuredPrintPo);
        return resultObject;
    }

}
