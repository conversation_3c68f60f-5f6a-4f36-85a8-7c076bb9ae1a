package com.gclife.apply.service.business.group.impl;

import com.alibaba.fastjson.JSON;
import com.gclife.agent.api.AgentApi;
import com.gclife.agent.api.AgentBaseAgentApi;
import com.gclife.agent.model.response.AgentBaseResponse;
import com.gclife.agent.model.response.AgentResponse;
import com.gclife.apply.core.jooq.tables.daos.ApplyPremiumDao;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyBaseDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.group.*;
import com.gclife.apply.model.config.*;
import com.gclife.apply.model.feign.ApplyAttachmentResp;
import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.apply.model.request.group.ContractAttachmentRequest;
import com.gclife.apply.model.request.group.GroupInputRequest;
import com.gclife.apply.model.response.ApplyListResponse;
import com.gclife.apply.model.response.AttachmentTypeResp;
import com.gclife.apply.model.response.GroupInputDictionariesResponse;
import com.gclife.apply.model.response.group.*;
import com.gclife.apply.service.*;
import com.gclife.apply.service.business.MessageBusinessService;
import com.gclife.apply.service.business.group.GroupApplyBusinessService;
import com.gclife.apply.service.data.impl.GroupApplyDataSaveService;
import com.gclife.apply.validate.parameter.group.GroupInputParameterValidate;
import com.gclife.apply.validate.parameter.group.LanguageUtils;
import com.gclife.apply.validate.parameter.transform.GroupApplyTransData;
import com.gclife.apply.validate.parameter.transform.LanguageCodeTransData;
import com.gclife.attachment.api.AttachmentPDFDocumentApi;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.InternationalTypeEnum;
import com.gclife.common.TerminologyTypeEnum;
import com.gclife.common.error.AppErrorConfigEnum;
import com.gclife.common.exception.RequestException;
import com.gclife.common.model.BasePageResponse;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.base.Users;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.JackSonUtils;
import com.gclife.payment.api.PaymentBaseApi;
import com.gclife.payment.model.response.PaymentStatusResponse;
import com.gclife.platform.api.*;
import com.gclife.platform.model.request.SyscodeRequest;
import com.gclife.platform.model.response.*;
import com.gclife.policy.api.PolicyApi;
import com.gclife.policy.model.vo.PolicyAttachmentVo;
import com.gclife.product.api.ProductApi;
import com.gclife.product.api.ProductSalesApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import com.gclife.product.model.response.sales.ProductDiscountActivityResponse;
import com.gclife.workflow.api.WorkFlowApi;
import com.gclife.workflow.model.config.ProcessParam;
import com.gclife.workflow.model.request.StartProcessRequest;
import com.gclife.workflow.model.request.WaitingTaskRequest;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.apply.model.config.ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL;
import static com.gclife.apply.model.config.ApplyTermEnum.CERTIFY_ATTACHMENT_TYPE.*;
import static com.gclife.apply.model.config.ApplyTermEnum.PRODUCT_LEVEL_29.*;
import static com.gclife.apply.model.config.ApplyTermEnum.PRODUCT_LEVEL_29.UNIVERSITY;
import static com.gclife.apply.model.config.GroupErrorConfigEnum.*;
import static com.gclife.apply.model.config.ModelConstantEnum.WORKFLOW_STATUS.GROUP_NEW_ENTRY_TASK;
import static com.gclife.common.TerminologyTypeEnum.BENEFICIARY_NO;

/**
 * <AUTHOR>
 * create 18-5-2
 * description:
 */
@Service
public class GroupApplyBusinessServiceImpl extends BaseBusinessServiceImpl implements GroupApplyBusinessService {
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private GroupApplyTransData groupApplyTransData;
    @Autowired
    private ApplyQueryBaseService applyQueryBaseService;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private PlatformBankApi platformBankApi;
    @Autowired
    private ApplyBaseDao applyBaseDao;
    @Autowired
    private GroupInputParameterValidate groupInputParameterValidate;
    @Autowired
    private GroupApplyDataSaveService groupApplyDataSaveService;
    @Autowired
    private WorkFlowBaseService workFlowBaseService;
    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private AgentBaseAgentApi agentBaseAgentApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private PlatformTerminologyBaseApi platformTerminologyBaseApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    @Autowired
    private AttachmentPDFDocumentApi attachmentPDFDocumentApi;
    @Autowired
    private LanguageCodeTransData languageCodeTransData;
    @Autowired
    private ApplyApplicantBaseService applyApplicantBaseService;
    @Autowired
    private ApplyAttachmentBaseService applyAttachmentBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;
    @Autowired
    private ApplyInsuredBaseService applyInsuredBaseService;
    @Autowired
    private ApplyAgentBaseService applyAgentBaseService;
    @Autowired
    private ApplyPremiumBaseService applyPremiumBaseService;
    @Autowired
    private MessageBusinessService messageBusinessService;
    @Autowired
    private AgentApi agentApi;
    @Autowired
    private ApplyPremiumDao applyPremiumDao;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private ProductSalesApi productSalesApi;
    @Autowired
    private ProductApi productApi;
    @Autowired
    private PlatformBranchApi platformBranchApi;
    @Autowired
    private PaymentBaseApi paymentBaseApi;
    @Autowired
    private WorkFlowApi workFlowApi;

    /**
     * 生成投保单PDF附件
     *
     * @param applyId  投保单ID
     * @param language 语言
     * @return
     */
    @Override
    public ResultObject<List<AttachmentResponse>> applyPdfGenerate(String applyId, String language) {
        ResultObject<List<AttachmentResponse>> resultObject = new ResultObject<>();
        try {
            // 参数校验
            AssertUtils.isNotEmpty(this.getLogger(), applyId, ApplyErrorConfigEnum.APPLY_PARAMETER_APPLY_APPLY_ID_IS_NOT_NULL);
            AssertUtils.isNotEmpty(this.getLogger(), language, ApplyErrorConfigEnum.APPLY_PARAMETER_LANGUAGE_IS_NOT_NULL);

            getLogger().info("=====================投保单打印开始=======================");

            // 查询投保单基础信息
            ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
            AssertUtils.isNotNull(getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            GroupAttachApplyBo groupAttachApplyBo = new GroupAttachApplyBo();
            ClazzUtils.copyPropertiesIgnoreNull(applyPo, groupAttachApplyBo);
            getLogger().info("=====================apply end=======================");

            /********************************** 查询投保单代理人信息********************************************/
            ApplyAgentPo applyAgentPo = applyAgentBaseService.queryApplyAgent(applyId);
            AssertUtils.isNotNull(getLogger(), applyAgentPo, ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            // 调代理人服务
            AgentBaseResponse agentBaseResponse = agentBaseAgentApi.queryOneAgentById(applyAgentPo.getAgentId()).getData();
            GroupAttachApplyAgentBo groupAttachApplyAgentBo = new GroupAttachApplyAgentBo();
            groupAttachApplyAgentBo.setAgentId(applyAgentPo.getAgentId());
            groupAttachApplyAgentBo.setAgentCode(applyAgentPo.getAgentCode());
            groupAttachApplyAgentBo.setAgentName(agentBaseResponse.getAgentName());
            groupAttachApplyAgentBo.setMobile(agentBaseResponse.getAgentDetail().getMobile());
            groupAttachApplyBo.setGroupAgent(groupAttachApplyAgentBo);
            getLogger().info("=====================agent end=======================");

            //查询银行转账支付对应的银行
            ResultObject<PaymentStatusResponse> paymentStatusResponseResultObject = paymentBaseApi.queryOnePaymentDoByBusinessId(applyId);
            getLogger().info("=====================查询银行转账支付对应的银行数据=======================" + JSON.toJSON(paymentStatusResponseResultObject));
            if (!AssertUtils.isResultObjectDataNull(paymentStatusResponseResultObject)) {
                PaymentStatusResponse response = paymentStatusResponseResultObject.getData();
                groupAttachApplyBo.setBankCode(response.getBankCode());
                groupAttachApplyBo.setPaymentMode(response.getPaymentMethodCode());
            }

            getLogger().info("=====================查询银行转账支付对应的银行bankCode=======================" + groupAttachApplyBo.getBankCode());
            getLogger().info("=====================查询银行转账支付对应的银行paymentMode=======================" + groupAttachApplyBo.getPaymentMode());

            /********************************** 查询保单投保人信息********************************************/
            ApplyApplicantPo applyApplicantPo = applyApplicantBaseService.queryApplyApplicant(applyId);
            AssertUtils.isNotNull(getLogger(), applyApplicantPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);
            GroupAttachApplyApplicantBo groupAttachApplyApplicantBo = new GroupAttachApplyApplicantBo();
            ClazzUtils.copyPropertiesIgnoreNull(applyApplicantPo, groupAttachApplyApplicantBo);
            // 单位地区
            if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyAreaCode())) {
                ResultObject<AreaNameResponse> areaNameResultObject = platformAreaApi.areaNameGet(groupAttachApplyApplicantBo.getCompanyAreaCode(), language);
                if (!AssertUtils.isResultObjectDataNull(areaNameResultObject) && AssertUtils.isNotEmpty(areaNameResultObject.getData().getAreaName())) {
                    groupAttachApplyApplicantBo.setCompanyAreaName(areaNameResultObject.getData().getAreaName());
                    groupAttachApplyApplicantBo.setCompanyAddressWhole(areaNameResultObject.getData().getAreaName() + groupAttachApplyApplicantBo.getCompanyAddress());
                }
            }
            // 所属行业
            if (AssertUtils.isNotEmpty(groupAttachApplyApplicantBo.getCompanyIndustry())) {
                ResultObject<CareerResponse> careerResultObject = platformCareerApi.careerInfoGet(groupAttachApplyApplicantBo.getCompanyIndustry(), language);
                if (!AssertUtils.isResultObjectDataNull(careerResultObject) && AssertUtils.isNotEmpty(careerResultObject.getData().getCareerName())) {
                    groupAttachApplyApplicantBo.setCompanyIndustryName(careerResultObject.getData().getCareerName());
                }
            }
            //法人国籍国际化
            groupAttachApplyApplicantBo.setCompanyLegalPersonNationalityName(languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyLegalPersonNationality(), language));
            groupAttachApplyApplicantBo.setCompanyContractNationalityName(languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getCompanyContractNationality(), language));
            groupAttachApplyApplicantBo.setDelegateNationalityName(languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.NATIONALITY.name(), groupAttachApplyApplicantBo.getDelegateNationality(), language));
            groupAttachApplyApplicantBo.setCompanyTypeName(languageCodeTransData.queryOneInternational(TerminologyTypeEnum.COMPANY_TYPE.name(), groupAttachApplyApplicantBo.getCompanyType(), language));
            groupAttachApplyApplicantBo.setDelegateSexName(languageCodeTransData.getCodeNameByKey(TerminologyTypeEnum.GENDER.name(), groupAttachApplyApplicantBo.getDelegateSex(), language));

            groupAttachApplyBo.setGroupApplicant(groupAttachApplyApplicantBo);
            getLogger().info("=====================applicant end=======================");
            /********************************** 查询保单险种信息********************************************/
            List<ApplyInsuredBo> applyInsuredBos = applyInsuredBaseService.listApplyInsured(applyId);
            List<ApplyCoveragePo> applyCoveragePos = applyCoverageBaseService.listApplyCoverage(applyId);
            AssertUtils.isNotEmpty(getLogger(), applyCoveragePos, ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
            // 缴费期限单位
            List<SyscodeResponse> premiumPeriodUnitSyscodes = platformTerminologyBaseApi.queryTerminologyInternation(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(), language).getData();
            // 保障期限单位
            List<SyscodeResponse> coveragePeriodUnitSyscodes = platformTerminologyBaseApi.queryTerminologyInternation(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), language).getData();

            List<GroupAttachApplyCoverageBo> groupAttachApplyCoverageBos = new ArrayList<>();
            applyCoveragePos.forEach(applyCoveragePo -> {
                GroupAttachApplyCoverageBo applyCoverageBo = new GroupAttachApplyCoverageBo();
                applyInsuredBos.forEach(policyInsuredBo -> {
                    List<ApplyCoverageBo> listCoverage = policyInsuredBo.getListCoverage();
                    if (AssertUtils.isNotEmpty(listCoverage)) {
                        Optional<ApplyCoverageBo> coverageOptional = listCoverage.stream().filter(coverage -> coverage.getProductId().equals(applyCoveragePo.getProductId())).findFirst();
                        if (coverageOptional.isPresent()) {
                            ApplyCoverageBo coverageBo = coverageOptional.get();
                            ClazzUtils.copyPropertiesIgnoreNull(coverageBo, applyCoverageBo);
                            return;
                        }
                    }
                });
                ClazzUtils.copyPropertiesIgnoreNull(applyCoveragePo, applyCoverageBo);

                applyCoverageBo.setTotalAmount(BigDecimal.ZERO);
                applyCoverageBo.setTotalPremium(BigDecimal.ZERO);

                // 缴费期限单位
                if (AssertUtils.isNotEmpty(applyCoverageBo.getPremiumPeriodUnit()) && AssertUtils.isNotEmpty(premiumPeriodUnitSyscodes)) {
                    premiumPeriodUnitSyscodes.stream()
                            .filter(syscode -> syscode.getCodeKey().equals(applyCoverageBo.getPremiumPeriodUnit()))
                            .findFirst().ifPresent(syscode -> applyCoverageBo.setPremiumPeriodUnitName(syscode.getCodeName()));
                }
                // 保障期限单位
                if (AssertUtils.isNotEmpty(applyCoverageBo.getCoveragePeriodUnit()) && AssertUtils.isNotEmpty(coveragePeriodUnitSyscodes)) {
                    coveragePeriodUnitSyscodes.stream()
                            .filter(syscode -> syscode.getCodeKey().equals(applyCoverageBo.getCoveragePeriodUnit()))
                            .findFirst().ifPresent(syscode -> applyCoverageBo.setCoveragePeriodUnitName(syscode.getCodeName()));
                }
                applyCoverageBo.setPrimaryFlag(applyCoveragePo.getPrimaryFlag());
                applyCoverageBo.setProductLevel(applyCoveragePo.getProductLevel());
                groupAttachApplyCoverageBos.add(applyCoverageBo);
            });
            groupAttachApplyBo.setListGroupCoverage(groupAttachApplyCoverageBos);
            getLogger().info("=====================coverage end=======================");
            /********************************************设置被保险人统计信息****************************************/
            ApplyInsuredCollectPo applyInsuredCollect = applyBaseDao.getApplyInsuredCollect(applyId);
            AssertUtils.isNotNull(getLogger(), applyInsuredCollect, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_COLLECT_IS_NOT_FOUND_OBJECT);
            GroupAttachApplyInsuredCollectBo groupAttachApplyInsuredCollectBo = (GroupAttachApplyInsuredCollectBo) this.converterObject(applyInsuredCollect, GroupAttachApplyInsuredCollectBo.class);
            // 主险人数、附加险人数
            long mainQuantity = 0;
            long additionQuantity = 0;
            long additionQuantity18 = 0;
            long additionQuantity26 = 0;
            long additionQuantity27 = 0;
            long additionQuantity33 = 0;
            final long[] quantityPreSchool29 = {0};
            final long[] quantityPreSchool33 = {0};
            final long[] quantityPrimarySchool29 = {0};
            final long[] quantityPrimarySchool33 = {0};
            final long[] quantitySecondarySchool29 = {0};
            final long[] quantitySecondarySchool33 = {0};
            final long[] quantityUniversity29 = {0};
            final long[] quantityUniversity33 = {0};
            final BigDecimal[] quantityPreSchoolAmount29 = {new BigDecimal(0)};
            final BigDecimal[] quantityPreSchoolAmount33 = {new BigDecimal(0)};
            final BigDecimal[] quantityPrimarySchoolAmount29 = {new BigDecimal(0)};
            final BigDecimal[] quantityPrimarySchoolAmount33 = {new BigDecimal(0)};
            final BigDecimal[] quantitySecondarySchoolAmount29 = {new BigDecimal(0)};
            final BigDecimal[] quantitySecondarySchoolAmount33 = {new BigDecimal(0)};
            final BigDecimal[] quantityUniversityAmount29 = {new BigDecimal(0)};
            final BigDecimal[] quantityUniversityAmount33 = {new BigDecimal(0)};
            for (ApplyInsuredBo applyInsuredBo : applyInsuredBos) {
                for (ApplyCoverageBo applyCoverageBo : applyInsuredBo.getListCoverage()) {
                    if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())) {
                        mainQuantity++;
                    }
                    if (ApplyTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL.name().equals(applyCoverageBo.getPrimaryFlag())) {
                        additionQuantity++;
                    }
                    if (ProductTermEnum.PRODUCT.PRODUCT_18.id().equals(applyCoverageBo.getProductId())) {
                        additionQuantity18++;
                    }
                    if (ProductTermEnum.PRODUCT.PRODUCT_26.id().equals(applyCoverageBo.getProductId())) {
                        additionQuantity26++;
                    }
                    if (ProductTermEnum.PRODUCT.PRODUCT_27.id().equals(applyCoverageBo.getProductId())) {
                        additionQuantity27++;
                    }
                    if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                        additionQuantity33++;
                    }

                    //29号和33号产品投保清单人数统计
                    List<ApplyCoverageLevelPo> listCoverageLevel = applyCoverageBo.getListCoverageLevel();
                    if (AssertUtils.isNotEmpty(listCoverageLevel)) {
                        listCoverageLevel.forEach(applyCoverageLevelPo -> {
                            String productLevel = applyCoverageLevelPo.getProductLevel();
                            BigDecimal amount = applyCoverageLevelPo.getAmount();
                            if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(applyCoverageBo.getProductId())) {
                                if (PRE_SCHOOL.name().equals(productLevel)) {
                                    quantityPreSchool29[0]++;
                                    quantityPreSchoolAmount29[0] = quantityPreSchoolAmount29[0].add(amount);
                                }
                                if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                    quantityPrimarySchool29[0]++;
                                    quantityPrimarySchoolAmount29[0] = quantityPrimarySchoolAmount29[0].add(amount);
                                }
                                if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                    quantitySecondarySchool29[0]++;
                                    quantitySecondarySchoolAmount29[0] = quantitySecondarySchoolAmount29[0].add(amount);
                                }
                                if (UNIVERSITY.name().equals(productLevel)) {
                                    quantityUniversity29[0]++;
                                    quantityUniversityAmount29[0] = quantityUniversityAmount29[0].add(amount);
                                }

                            }
                            if (ProductTermEnum.PRODUCT.PRODUCT_33.id().equals(applyCoverageBo.getProductId())) {
                                if (PRE_SCHOOL.name().equals(productLevel)) {
                                    quantityPreSchool33[0]++;
                                    quantityPreSchoolAmount33[0] = quantityPreSchoolAmount33[0].add(amount);
                                }
                                if (PRIMARY_SCHOOL.name().equals(productLevel)) {
                                    quantityPrimarySchool33[0]++;
                                    quantityPrimarySchoolAmount33[0] = quantityPrimarySchoolAmount33[0].add(amount);
                                }
                                if (SECONDARY_SCHOOL.name().equals(productLevel)) {
                                    quantitySecondarySchool33[0]++;
                                    quantitySecondarySchoolAmount33[0] = quantitySecondarySchoolAmount33[0].add(amount);
                                }
                                if (UNIVERSITY.name().equals(productLevel)) {
                                    quantityUniversity33[0]++;
                                    quantityUniversityAmount33[0] = quantityUniversityAmount33[0].add(amount);
                                }
                            }
                        });
                    }
                }
            }
            groupAttachApplyInsuredCollectBo.setMainQuantity(mainQuantity);
            groupAttachApplyInsuredCollectBo.setAdditionQuantity(additionQuantity);
            groupAttachApplyInsuredCollectBo.setAdditionQuantity18(additionQuantity18);
            groupAttachApplyInsuredCollectBo.setAdditionQuantity26(additionQuantity26);
            groupAttachApplyInsuredCollectBo.setAdditionQuantity27(additionQuantity27);
            groupAttachApplyInsuredCollectBo.setAdditionQuantity33(additionQuantity33);
            groupAttachApplyInsuredCollectBo.setQuantityPreSchool29(quantityPreSchool29[0]);
            groupAttachApplyInsuredCollectBo.setQuantityPreSchool33(quantityPreSchool33[0]);
            groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool29(quantityPrimarySchool29[0]);
            groupAttachApplyInsuredCollectBo.setQuantityPrimarySchool33(quantityPrimarySchool33[0]);
            groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool29(quantitySecondarySchool29[0]);
            groupAttachApplyInsuredCollectBo.setQuantitySecondarySchool33(quantitySecondarySchool33[0]);
            groupAttachApplyInsuredCollectBo.setQuantityUniversity29(quantityUniversity29[0]);
            groupAttachApplyInsuredCollectBo.setQuantityUniversity33(quantityUniversity33[0]);
            groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount29(quantityPreSchoolAmount29[0]);
            groupAttachApplyInsuredCollectBo.setQuantityPreSchoolAmount33(quantityPreSchoolAmount33[0]);
            groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount29(quantityPrimarySchoolAmount29[0]);
            groupAttachApplyInsuredCollectBo.setQuantityPrimarySchoolAmount33(quantityPrimarySchoolAmount33[0]);
            groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount29(quantitySecondarySchoolAmount29[0]);
            groupAttachApplyInsuredCollectBo.setQuantitySecondarySchoolAmount33(quantitySecondarySchoolAmount33[0]);
            groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount29(quantityUniversityAmount29[0]);
            groupAttachApplyInsuredCollectBo.setQuantityUniversityAmount33(quantityUniversityAmount33[0]);
            groupAttachApplyBo.setApplyInsuredCollect(groupAttachApplyInsuredCollectBo);
            /********************************************查询投保单被保人信息****************************************/
            AssertUtils.isNotEmpty(getLogger(), applyInsuredBos, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
            applyInsuredBos.forEach(insuredBo -> {
                // 险种总保险金额、险种总保费
                insuredBo.getListCoverage().forEach(insuredCoverage -> {
                    groupAttachApplyBo.getListGroupCoverage().stream()
                            .filter(coverage -> coverage.getProductId().equals(insuredCoverage.getProductId()))
                            .findFirst().ifPresent(coverage -> {
                        BigDecimal totalAmount = (AssertUtils.isNotNull(coverage.getTotalAmount()) ? coverage.getTotalAmount() : new BigDecimal(0)).add(new BigDecimal(AssertUtils.isNotEmpty(insuredCoverage.getAmount()) ? insuredCoverage.getAmount() : "0"));
                        coverage.setTotalAmount(totalAmount);
                        BigDecimal totalPremium = (AssertUtils.isNotNull(coverage.getTotalPremium()) ? coverage.getTotalPremium() : new BigDecimal(0)).add(AssertUtils.isNotNull(insuredCoverage.getTotalPremium()) ? insuredCoverage.getTotalPremium() : new BigDecimal(0));
                        coverage.setTotalPremium(totalPremium);
                    });
                });
            });
            // 投保单总保费
            groupAttachApplyBo.getListGroupCoverage().forEach(coverage -> {
                groupAttachApplyBo.setTotalPremium(groupAttachApplyBo.getTotalPremium().add(coverage.getTotalPremium()));
            });
            getLogger().info("=====================insured end=======================");
            getLogger().info("投保单打印|groupAttachApplyBo:::::" + JSON.toJSONString(groupAttachApplyBo));
            /********************************************账户数据****************************************/
            List<SyscodeResponse> bankSyscodes = platformInternationalBaseApi.queryInternational(InternationalTypeEnum.BANK.name(), language).getData();
            List<ApplyAccountBo> applyAccountBos = applyBaseService.listApplyAccount(applyId);
            if (AssertUtils.isNotEmpty(applyAccountBos)) {
                applyAccountBos.forEach(applyAccountBo -> {
                    applyAccountBo.setBankName(LanguageUtils.getNewCodeName(bankSyscodes, applyAccountBo.getBankCode()));
                });
                List<GroupAttachApplyAccountBo> applyAccountList = (List<GroupAttachApplyAccountBo>) this.converterList(applyAccountBos, new TypeToken<List<GroupAttachApplyAccountBo>>() {
                }.getType());
                groupAttachApplyBo.setApplyAccountList(applyAccountList);
            }


            /********************************************开始打印****************************************/
            // 主险产品ID
            String productId = applyCoveragePos.stream()
                    .filter(coverageBo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(coverageBo.getPrimaryFlag()))
                    .findFirst().get().getProductId();
            ElectronicPolicyGeneratorRequest generatorRequest = new ElectronicPolicyGeneratorRequest();
            generatorRequest.setPdfType(ApplyTermEnum.PDF_TYPE.APPLY.name());
            generatorRequest.setProductId(productId);
            generatorRequest.setLanguage(language);
            generatorRequest.setContent(JackSonUtils.toJson(groupAttachApplyBo, language));
            getLogger().info("投保单打印|generatorRequest:::::" + JSON.toJSONString(generatorRequest));
            // 生成PDF
            ResultObject<List<AttachmentResponse>> attachmentObject = attachmentPDFDocumentApi.electronicPolicyGenerator(generatorRequest);
            getLogger().info("投保单打印|attachmentObject:::::" + JSON.toJSONString(attachmentObject));
            AssertUtils.isResultObjectError(this.getLogger(), attachmentObject);
            List<AttachmentResponse> attachmentResponses = attachmentObject.getData();
            // 保存保单附件
            attachmentResponses.forEach(attachmentRespFc -> {
                PolicyAttachmentVo policyAttachmentVo = new PolicyAttachmentVo();
                policyAttachmentVo.setPolicyId(applyId);
                policyAttachmentVo.setAttachmentId(attachmentRespFc.getMediaId());
                policyAttachmentVo.setAttachmentTypeCode(attachmentRespFc.getTemplateType());
                policyAttachmentVo.setLanguage(language);
                policyApi.saveAttachment(policyAttachmentVo);
            });
            resultObject.setData(attachmentResponses);

            getLogger().info("=====================投保单打印结束=======================");
        } catch (Exception e) {
            getLogger().info("=====================投保单打印出错=======================");
            e.printStackTrace();
            setResultObjectException(getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_GENERATE_APPLY_PDF_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<ApplyListResponse>> getGroupApplyList(Users users, ApplyListRequest applyListRequest) {
        ResultObject<BasePageResponse<ApplyListResponse>> resultObject = new ResultObject<>();
        try {
            List<ApplyListBo> applyListBos;
            if (!AssertUtils.isNotNull(applyListRequest.getApplyStatus())) {
                //查询团险投保单数据
                applyListBos = applyQueryBaseService.loadApplyListByUserId(users.getUserId(), applyListRequest, ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name(), ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
                if (!AssertUtils.isNotEmpty(applyListBos)) {
                    return resultObject;
                }
            } else {
                //查询团险投保单数据
                applyListBos = applyQueryBaseService.loadApplyListByUserId(users.getUserId(), applyListRequest, ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name(), applyListRequest.getApplyStatus());
                if (!AssertUtils.isNotEmpty(applyListBos)) {
                    return resultObject;
                }
            }

            //列表数据拼装
            List<ApplyListResponse> applyListResponses = groupApplyTransData.transApplyList(applyListBos, "", users);
            if (!AssertUtils.isNotEmpty(applyListResponses)) {
                return resultObject;
            }
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;

            BasePageResponse<ApplyListResponse> basePageResponse = BasePageResponse.getData(applyListRequest.getCurrentPage(), applyListRequest.getPageSize(), totalLine, applyListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }


    @Override
    public ResultObject getGroupInitApplyStatus(Users users) {
        ResultObject resultObject = new ResultObject();
        List<SyscodeRespFc> syscodeRespFcs = new ArrayList<>();
        List<SyscodeRespFc> applyStatusList = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.APPLY_STATUS.name(), users.getLanguage()).getData();
        applyStatusList.forEach(applyStatus -> {
            if (applyStatus.getCodeKey().equals("APPLY_STATUS_INITIAL")
                    || applyStatus.getCodeKey().equals("APPLY_STATUS_INITIAL_COMPLETE")
                    || applyStatus.getCodeKey().equals("APPLY_STATUS_UNDERWRITE_ARTIFICIAL")
                    || applyStatus.getCodeKey().equals("APPLY_STATUS_UNDERWRITE_PASS")
                    || applyStatus.getCodeKey().equals("APPLY_STATUS_PAYMENT_COMPLETE")
                    || applyStatus.getCodeKey().equals("APPLY_STATUS_ABANDONED")) {
                syscodeRespFcs.add(applyStatus);
            }
        });
        resultObject.setData(syscodeRespFcs);
        return resultObject;
    }

    /**
     * 获取投保单基本信息
     *
     * @param users   当前用户
     * @param applyId 投保单ID
     * @return GroupInputResponse
     */
    @Override
    public ResultObject<GroupInputResponse> getGroupApplyInputInfo(Users users, String applyId) {
        ResultObject<GroupInputResponse> resultObject = new ResultObject<>();
        try {
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            resultObject.setData(groupApplyTransData.transGroupInput(users, applyBo));
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<GroupInputDictionariesResponse> getInputDictionaries(Users users, String applyId) {
        ResultObject<GroupInputDictionariesResponse> resultObject = new ResultObject<>();
        GroupInputDictionariesResponse inputDictionariesResponse = new GroupInputDictionariesResponse();
        // 所属行业
        inputDictionariesResponse.setCompanyIndustry(platformCareerApi.careerGet("0000", "PRO8888888888888").getData());
        String language = users.getLanguage();

        List<String> codeTypes = Arrays.asList(
                BENEFICIARY_NO.name(),
                TerminologyTypeEnum.ID_TYPE.name(),
                TerminologyTypeEnum.COMPANY_TYPE.name(),
                TerminologyTypeEnum.COMPANY_ID_TYPE.name(),
                TerminologyTypeEnum.GENDER.name(),
                TerminologyTypeEnum.SPECIAL_CONTRACT_TYPE.name(),
                TerminologyTypeEnum.NATIONALITY.name(),
                TerminologyTypeEnum.RELATIONSHIP_WITH_THE_INSURED.name(),
                TerminologyTypeEnum.INVOICE_TYPE.name(),
                TerminologyTypeEnum.DISCOUNT_TYPE.name(),
                "APPROVED_HC",
                "SALES_PLAN",
                "SCHOOL_PROPERTIES",
                "SCHOOL_TYPE",
                "BASIS_OF_SUM_INSURED",
                "REFERRAL_SOURCES"
        );
        Map<String, List<SyscodeResponse>> data = platformInternationalBaseApi.queryBatchInternationalByCodeKeys(language, codeTypes).getData();

        List<SyscodeResponse> idTypeSyscodeResponses = data.get(TerminologyTypeEnum.ID_TYPE.name());
        if (AssertUtils.isNotEmpty(idTypeSyscodeResponses)) {
            idTypeSyscodeResponses.removeIf(syscodeResponse -> ApplyTermEnum.ID_TYPE.HOUSEHOLD_REGISTER.name().equals(syscodeResponse.getCodeKey()));
        }
        inputDictionariesResponse.setBeneficiaryNo(data.get(BENEFICIARY_NO.name()));
        inputDictionariesResponse.setIdType(idTypeSyscodeResponses);
        inputDictionariesResponse.setCompanyType(data.get(TerminologyTypeEnum.COMPANY_TYPE.name()));
        inputDictionariesResponse.setCompanyIdType(data.get(TerminologyTypeEnum.COMPANY_ID_TYPE.name()));
        inputDictionariesResponse.setSex(data.get(TerminologyTypeEnum.GENDER.name()));
        inputDictionariesResponse.setSpecialContractType(data.get(TerminologyTypeEnum.SPECIAL_CONTRACT_TYPE.name()));
        inputDictionariesResponse.setNationality(data.get(TerminologyTypeEnum.NATIONALITY.name()));
        inputDictionariesResponse.setRelationship(data.get(TerminologyTypeEnum.RELATIONSHIP_WITH_THE_INSURED.name()));
        inputDictionariesResponse.setInvoiceType(data.get(TerminologyTypeEnum.INVOICE_TYPE.name()));
        inputDictionariesResponse.setDiscountType(data.get(TerminologyTypeEnum.DISCOUNT_TYPE.name()));
        inputDictionariesResponse.setBasisOfSumInsured(data.get("BASIS_OF_SUM_INSURED"));
        inputDictionariesResponse.setReferralSources(data.get("REFERRAL_SOURCES"));
        inputDictionariesResponse.setApprovedHc(data.get("APPROVED_HC"));
        inputDictionariesResponse.setSalesPlan(data.get("SALES_PLAN"));
        inputDictionariesResponse.setSchoolProperties(data.get("SCHOOL_PROPERTIES"));
        inputDictionariesResponse.setSchoolType(data.get("SCHOOL_TYPE"));

        List<ProductDiscountActivityResponse> productDiscountActivityResponses = new ArrayList<>();
        ProductDiscountActivityResponse productDiscountActivityResponse = new ProductDiscountActivityResponse();
        productDiscountActivityResponse.setPromotionType(ApplyTermEnum.PROMOTION_TYPE.CUSTOM_DISCOUNT_OFFER.name());
        productDiscountActivityResponse.setDiscountModel(ProductTermEnum.DISCOUNT_MODEL.PERCENTAGE.name());
        productDiscountActivityResponses.add(productDiscountActivityResponse);
        if (AssertUtils.isNotEmpty(applyId)) {
            ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
            AssertUtils.isNotNull(getLogger(), applyPo, GROUP_APPLY_QUERY_APPLY_IS_NOT_NULL);
            // 查询公共险种
            final String[] mainProductId = {null};
            List<ApplyCoveragePo> applyCoverages = applyCoverageBaseService.listApplyCoverage(applyId);
            applyCoverages.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag()))
                    .findFirst().ifPresent(applyCoveragePo -> mainProductId[0] = applyCoveragePo.getProductId());
            //优惠活动下拉选
            SyscodeRequest syscodeRequest = new SyscodeRequest();
            syscodeRequest.setCodeType(ApplyTermEnum.PROMOTION_TYPE.PROMOTION_TYPE.name());
            Long discountDate = AssertUtils.isNotNull(applyPo.getApplyDate()) ? applyPo.getApplyDate() : DateUtils.getCurrentTime();
            ResultObject<List<ProductDiscountActivityResponse>> productSimpleInfo = productSalesApi.queryProductDiscountActivity(mainProductId[0], applyPo.getSalesBranchId(), discountDate);
            if (!AssertUtils.isResultObjectListDataNull(productSimpleInfo)) {
                List<ProductDiscountActivityResponse> collect = productSimpleInfo.getData().stream()
                        .filter(promotionType -> !ApplyTermEnum.PROMOTION_TYPE.CUSTOM_DISCOUNT_OFFER.name().equals(promotionType.getPromotionType())).collect(Collectors.toList());
                productDiscountActivityResponses.addAll(collect);
            }
        }
        inputDictionariesResponse.setPromotionType(productDiscountActivityResponses);

        resultObject.setData(inputDictionariesResponse);
        return resultObject;
    }

    /**
     * 保存投保单基本信息
     *
     * @param users             用户
     * @param groupInputRequest 请求参数
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject saveGroupApplyInputInfo(Users users, GroupInputRequest groupInputRequest) {
        ResultObject resultObject = new ResultObject<>();
        try {
            getLogger().info("groupInputRequest:" + JSON.toJSONString(groupInputRequest));
            //验证请求参数
            String applyId = groupInputRequest.getApplyId();
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            String groupMainProductId = groupApplyTransData.getGroupMainProductId(applyId);
            groupInputRequest.setMainProductId(groupMainProductId);
            if (ProductTermEnum.PRODUCT.PRODUCT_29.id().equals(groupMainProductId)) {
                groupInputRequest.setStudentFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            groupInputParameterValidate.validGroupInputRequest(groupInputRequest);

            //保存数据
            groupApplyDataSaveService.saveInputApplyData(users, groupInputRequest);
        } catch (Exception e) {
            e.printStackTrace();
            //事务回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_APPLY_SAVE_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject saveContractAttachment(Users currentLoginUsers, ContractAttachmentRequest contractAttachmentRequest) {
        ResultObject<GroupInputResponse> resultObject = new ResultObject<>();
        try {
            getLogger().info("contractAttachmentRequest" + JSON.toJSONString(contractAttachmentRequest));
            String applyId = contractAttachmentRequest.getApplyId();
            AssertUtils.isNotNull(this.getLogger(), applyId, GroupErrorConfigEnum.GROUP_APPLY_SAVE_CONTRACT_ATTACHMENT_ERROR);

            ApplyPo applyPo = applyBaseService.queryApplyPo(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyPo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);

            ApplyPremiumBo applyPremiumBo = applyBaseDao.getApplyPremium(applyId);
            // 如果存在折扣系数 则折扣申请表必须上传
            if ((AssertUtils.isNotNull(applyPremiumBo) && AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount()) && BigDecimal.ZERO.compareTo(applyPremiumBo.getSpecialDiscount()) < 0)
                    && (contractAttachmentRequest.getListAttachmentType()
                    .stream()
                    .noneMatch(attachmentTypeResp -> DISCOUNT_REQUEST_FORM.name().equals(attachmentTypeResp.getAttachmentTypeCode()))
                    || contractAttachmentRequest.getListAttachmentType()
                    .stream()
                    .anyMatch(attachmentTypeResp -> DISCOUNT_REQUEST_FORM.name().equals(attachmentTypeResp.getAttachmentTypeCode()) && attachmentTypeResp.getListAttachment().isEmpty()))) {
                // 若未找到折扣申请表附件
                throw new RequestException(GROUP_APPLY_DISCOUNT_CONFIRMATION_BOOK_IS_NOT_NULL);
            }
/*          4.2.1 团险折扣二期 折扣确认表 未实现
            // 如果存在折扣系数 则折扣申请表必须上传
            List<AttachmentTypeResp> discountAttachmentType = contractAttachmentRequest.getDiscountAttachmentType();
            if ((AssertUtils.isNotNull(applyPremiumBo) && AssertUtils.isNotNull(applyPremiumBo.getSpecialDiscount()) && BigDecimal.ZERO.compareTo(applyPremiumBo.getSpecialDiscount()) < 0)
                    && (discountAttachmentType.isEmpty() || discountAttachmentType.get(0).getListAttachment().isEmpty())) {
                // 若未找到折扣申请表附件
                throw new RequestException(GROUP_APPLY_DISCOUNT_CONFIRMATION_BOOK_IS_NOT_NULL);
            } else if (discountAttachmentType.get(0).getListAttachment().size() > 1) {
                // 折扣申请表只能传一张
                throw new RequestException(GROUP_APPLY_TOO_MANY_ATTACHMENTS_ERROR);
            }*/

            //若是年缴和趸交之外的投保单，需要上传缴费周期变更申请书
            boolean b = this.isPaymentModeRequest(applyId);
            List<AttachmentTypeResp> collect = contractAttachmentRequest.getListAttachmentType().stream().filter(attachmentTypeResp -> PAYMENT_MODE_MODIFICATION_REQUEST_FORM.name().equals(attachmentTypeResp.getAttachmentTypeCode())).collect(Collectors.toList());
            if (b && (!AssertUtils.isNotEmpty(collect) || collect.stream().anyMatch(attachmentTypeResp -> !AssertUtils.isNotEmpty(attachmentTypeResp.getListAttachment())))) {
                throwsException(GROUP_APPLY_PAYMENT_MODE_MODIFICATION_REQUEST_FORM_IS_NOT_NULL);
            }
            //  删除上次的附件
            applyAttachmentBaseService.deleteApplyAttachment(applyId, GROUP_APPLY_CONTRACT.name());
            applyAttachmentBaseService.deleteApplyAttachment(applyId, GROUP_APPLY_CONTRACT_INSURED_INVENTORY.name());
            applyAttachmentBaseService.deleteApplyAttachment(applyId, DISCOUNT_REQUEST_FORM.name());
            applyAttachmentBaseService.deleteApplyAttachment(applyId, HEALTH_DISCLOSURE.name());
            applyAttachmentBaseService.deleteApplyAttachment(applyId, PAYMENT_MODE_MODIFICATION_REQUEST_FORM.name());

            List<ApplyAttachmentPo> applyAttachmentPos = new ArrayList<>();
            List<AttachmentTypeResp> listAttachmentType = contractAttachmentRequest.getListAttachmentType();
/*          4.2.1 团险折扣二期 折扣确认表 未实现
            // 保存折扣申请表附件
            listAttachmentType.add(discountAttachmentType.get(0));*/
            if (AssertUtils.isNotEmpty(listAttachmentType)) {
                listAttachmentType.forEach(attachmentTypeRespConsumer -> {
                    final long[] index = {1};
                    List<ApplyAttachmentResp> listApplyAttachmentResp = attachmentTypeRespConsumer.getListAttachment();
                    if (!AssertUtils.isNotEmpty(listApplyAttachmentResp)) {
                        return;
                    }
                    listApplyAttachmentResp.forEach(attachmentResp -> {
                        //20190816 去掉合同上传限制
                        if (!AssertUtils.isNotEmpty(attachmentResp.getAttachmentId())) {
                            return;
                        }
                        ApplyAttachmentPo applyAttachmentPo = new ApplyAttachmentPo();
                        applyAttachmentPo.setApplyId(applyId);
                        applyAttachmentPo.setAttachmentId(attachmentResp.getAttachmentId());
                        applyAttachmentPo.setAttachmentTypeCode(attachmentTypeRespConsumer.getAttachmentTypeCode());
                        applyAttachmentPo.setAttachmentSeq(index[0]);
                        applyAttachmentPos.add(applyAttachmentPo);
                        index[0]++;
                    });
                });
                applyAttachmentBaseService.saveApplyAttachment(applyAttachmentPos);
            }
        } catch (Exception e) {
            e.printStackTrace();
            setTransactionalResultObjectException(getLogger(), resultObject, e, GroupErrorConfigEnum.GROUP_APPLY_SAVE_CONTRACT_ATTACHMENT_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<GroupApplyDetailResponse> getGroupApplyDetail(Users users, String applyId) {
        ResultObject<GroupApplyDetailResponse> resultObject = new ResultObject<>();
        try {
            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            // 查询险种档次
            List<ApplyCoverageLevelPo> applyCoverageLevelPos = applyCoverageBaseService.listApplyCoverageLevel(applyId);
            if (AssertUtils.isNotEmpty(applyCoverageLevelPos)) {
                // 按险种ID分组
                Map<String, List<ApplyCoverageLevelPo>> applyCoverageLevelPoMap =
                        applyCoverageLevelPos.parallelStream().collect(Collectors.groupingBy(ApplyCoverageLevelPo::getCoverageId));
                // 设置险种档次数据
                if (AssertUtils.isNotEmpty(applyBo.getListInsured())) {
                    applyBo.getListInsured().forEach(applyInsuredBo -> {
                        applyInsuredBo.getListCoverage().forEach(applyCoverageBo -> {
                            applyCoverageBo.setListCoverageLevel(applyCoverageLevelPoMap.get(applyCoverageBo.getCoverageId()));
                        });
                    });
                }
                if (AssertUtils.isNotEmpty(applyBo.getListInsuredCoverage())) {
                    applyBo.getListInsuredCoverage().forEach(applyCoverageBo ->
                            applyCoverageBo.setListCoverageLevel(applyCoverageLevelPoMap.get(applyCoverageBo.getCoverageId())));
                }
            }
            AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            resultObject.setData(groupApplyTransData.transGroupApplyDetail(users, applyBo));
        } catch (Exception e) {
            e.printStackTrace();
            setResultObjectException(this.getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
        }
        return resultObject;
    }

    /**
     * 提交投保单录入信息
     *
     * @param currentLoginUsers 当前用户
     * @param applyId           投保單ID
     * @return ResultObject
     */
    @Override
    @Transactional
    public ResultObject submitGroupApplyInputInfo(Users currentLoginUsers, String applyId) {
        ResultObject resultObject = new ResultObject<>();
        try {
            AssertUtils.isNotNull(this.getLogger(), applyId, GroupErrorConfigEnum.GROUP_APPLY_SAVE_CONTRACT_ATTACHMENT_ERROR);

            ApplyBo applyBo = applyBaseService.queryApply(applyId);
            AssertUtils.isNotNull(this.getLogger(), applyBo, ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_IS_NOT_FOUND_OBJECT);
            //1.判断产品信息是否填写完,查险种
            //2.判断被保人主表是否存在
            AssertUtils.isNotEmpty(this.getLogger(), applyBo.getListInsured(), ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_INSURED_IS_NOT_FOUND_OBJECT);
            // 存放被保人购买的产品ID
            List<String> insuredProductIds = new ArrayList<>();
            applyBo.getListInsured().forEach(applyInsuredBo -> {
                AssertUtils.isNotEmpty(this.getLogger(), applyInsuredBo.getListCoverage(), ApplyErrorConfigEnum.APPLY_INPUT_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
                // 收集所有被保人选择的产品
                applyInsuredBo.getListCoverage().forEach(coverageBo -> {
                    if (!insuredProductIds.contains(coverageBo.getProductId())) {
                        insuredProductIds.add(coverageBo.getProductId());
                    }
                });
            });
            //3.判断基本信息是否存在，apply，附件，投保人
            AssertUtils.isNotNull(this.getLogger(), applyBo.getApplyAgentBo(), ApplyErrorConfigEnum.APPLY_BUSINESS_AGENT_IS_NOT_FOUND_OBJECT);
            AssertUtils.isNotEmpty(this.getLogger(), applyBo.getListAttachment(), ApplyErrorConfigEnum.APPLY_QUERY_IMAGE_ATTACHMENT_IS_NOT_FOUND_OBJECT);
            AssertUtils.isNotNull(this.getLogger(), applyBo.getApplicant(), ApplyErrorConfigEnum.APPLY_BUSINESS_APPLY_APPLICANT_IS_NOT_FOUND_OBJECT);

            // 将多余的公共险种删除
            applyBo.getListCoverage().forEach(coverageBo -> {
                if (!insuredProductIds.contains(coverageBo.getProductId())) {
                    // 将所有被保人都没选择的产品删除
                    applyCoverageBaseService.deleteApplyCoverage(coverageBo);
                }
            });

            //如果该单来源于代理人
            if (ApplyTermEnum.APPLY_SOURCE.AGENT_INPUT.name().equals(applyBo.getApplySource())) {
                //20190816 去掉合同上传限制
                //4.判断合同影像是否存在，指定附件类型
//                List<ApplyAttachmentPo> applyInsuredAttachmentList = applyBaseService.listApplyAttachment(applyId, GROUP_APPLY_CONTRACT_INSURED_INVENTORY.name());
//                List<ApplyAttachmentPo> applyAttachmentList = applyBaseService.listApplyAttachment(applyId, GROUP_APPLY_CONTRACT.name());
//                AssertUtils.isNotEmpty(this.getLogger(), applyInsuredAttachmentList, ApplyErrorConfigEnum.APPLY_QUERY_CONTRACT_ATTACHMENT_IS_NOT_FOUND_OBJECT);
//                AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentList, ApplyErrorConfigEnum.APPLY_QUERY_CONTRACT_ATTACHMENT_IS_NOT_FOUND_OBJECT);
//                applyInsuredAttachmentList.forEach(applyAttachmentPo -> {
//                    AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentPo.getAttachmentId(), ApplyErrorConfigEnum.APPLY_QUERY_CONTRACT_ATTACHMENT_IS_NOT_FOUND_OBJECT);
//                });
//                applyAttachmentList.forEach(applyAttachmentPo -> {
//                    AssertUtils.isNotEmpty(this.getLogger(), applyAttachmentPo.getAttachmentId(), ApplyErrorConfigEnum.APPLY_QUERY_CONTRACT_ATTACHMENT_IS_NOT_FOUND_OBJECT);
//                });
                //上述信息如果都存在，调用开启工作流并完成接口，写入投保单状态
                //开启工作流
                /*StartProcessRequest startProcessRequest = new StartProcessRequest();
                startProcessRequest.setBusinessNo(applyId);
                startProcessRequest.setCreateUserId(currentLoginUsers.getUserId());
                startProcessRequest.setWorkflowType(ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
                startProcessRequest.setIsSignFlag("true");
                startProcessRequest.setIsCompleteFlag("true");
                startProcessRequest.setSignUserId("GM_USER_101");
                startProcessRequest.setWorkflowItemType(ModelConstantEnum.WORKFLOW_STATUS.GROUP_AGENT_INPUT_TASK.name());
                ProcessParam processParam = new ProcessParam();
                processParam.setBranchId(applyBo.getSalesBranchId());
                processParam.setBranchEnabledFlag(ModelConstantEnum.BRANCH_ENABLED_FLAG.BRANCH_ENABLED.name());
                processParam.setSourceType(ApplyTermEnum.APPLY_SOURCE.AGENT_INPUT.name());
                startProcessRequest.setData(processParam);
                ResultObject startWorkflow = workFlowApi.startProcess(startProcessRequest);
                AssertUtils.isResultObjectError(this.getLogger(), startWorkflow);*/
                workFlowBaseService.getTaskComplete(applyId, ModelConstantEnum.WORKFLOW_STATUS.GROUP_AGENT_INPUT_TASK.name());
                //workFlowBaseService.startWorkflow(currentLoginUsers.getUserId(), applyId, applyBo.getSalesBranchId(), ApplyTermEnum.APPLY_SOURCE.AGENT_INPUT.name());

            }
            if (ApplyTermEnum.APPLY_SOURCE.ACCEPT_INPUT.name().equals(applyBo.getApplySource())) {
                workFlowBaseService.getTaskComplete(applyId, GROUP_NEW_ENTRY_TASK.name());
            }

            applyBo.setApplyStatus(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE.name());

            //团险销售报表提交承保日期取值调整为提交复核的日期
            applyBo.setAppSubmitUnderwritingDate(DateUtils.getCurrentTime());
            applyBaseService.saveApply(currentLoginUsers.getUserId(), applyBo);
            //产生保费记录
            applyPremiumBaseService.saveApplyPremium(currentLoginUsers.getUserId(), applyId, PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_INITIAL.name());

            //发送复核消息
            messageBusinessService.pushApplyMessageBatch(ApplyTermEnum.MSG_BUSINESS_TYPE.GROUP_APPLY_REVIEW_REMINDER.name(), applyBo);
        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_APPLY_INFO_SUBMIT_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<AttachmentTypeResp>> getContractAttachment(Users users, String applyId) {
        ResultObject<List<AttachmentTypeResp>> resultObject = new ResultObject<>();
        try {
            List<AttachmentTypeResp> contractAttachmentList = new ArrayList<>();

            // 折扣系数有值，则展示折扣确认书上传窗口
            List<ApplyPremiumPo> applyPremiumPos = applyPremiumDao.fetchByApplyId(applyId);
            if (!applyPremiumPos.isEmpty()) {
                ApplyPremiumPo applyPremiumPo = applyPremiumPos.get(0);
                if (AssertUtils.isNotNull(applyPremiumPo)) {
                    // 折扣系数
                    BigDecimal specialDiscount = applyPremiumPo.getSpecialDiscount();
                    if (AssertUtils.isNotNull(specialDiscount) && BigDecimal.ZERO.compareTo(specialDiscount) != 0) {
                        // 添加折扣申请表
                        AttachmentTypeResp discountConfirmationBook = new AttachmentTypeResp();
                        discountConfirmationBook.setAttachmentTypeCode(DISCOUNT_REQUEST_FORM.name());
                        List<ApplyAttachmentPo> discountConfirmationBooks = applyAttachmentBaseService.listApplyAttachment(applyId, DISCOUNT_REQUEST_FORM.name());
                        if (AssertUtils.isNotEmpty(discountConfirmationBooks)) {
                            List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(discountConfirmationBooks, new TypeToken<List<ApplyAttachmentResp>>() {
                            }.getType());
                            discountConfirmationBook.setListAttachment(listApplyAttachmentResp);
                        }
                        contractAttachmentList.add(discountConfirmationBook);
                    }
                }
            }

            AttachmentTypeResp applyAttachmentResp = new AttachmentTypeResp();
            applyAttachmentResp.setAttachmentTypeCode(GROUP_APPLY_CONTRACT.name());
            List<ApplyAttachmentPo> applyAttachmentList = applyAttachmentBaseService.listApplyAttachment(applyId, GROUP_APPLY_CONTRACT.name());
            if (AssertUtils.isNotEmpty(applyAttachmentList)) {
                List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(applyAttachmentList, new TypeToken<List<ApplyAttachmentResp>>() {
                }.getType());
                applyAttachmentResp.setListAttachment(listApplyAttachmentResp);
            }
            contractAttachmentList.add(applyAttachmentResp);

            AttachmentTypeResp applyInsuredAttachmentResp = new AttachmentTypeResp();
            applyInsuredAttachmentResp.setAttachmentTypeCode(GROUP_APPLY_CONTRACT_INSURED_INVENTORY.name());
            List<ApplyAttachmentPo> applyInsuredAttachmentList = applyAttachmentBaseService.listApplyAttachment(applyId, GROUP_APPLY_CONTRACT_INSURED_INVENTORY.name());
            if (AssertUtils.isNotEmpty(applyInsuredAttachmentList)) {
                List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(applyInsuredAttachmentList, new TypeToken<List<ApplyAttachmentResp>>() {
                }.getType());
                applyInsuredAttachmentResp.setListAttachment(listApplyAttachmentResp);
            }
            contractAttachmentList.add(applyInsuredAttachmentResp);

            if (ProductTermEnum.PRODUCT.PRODUCT_17.id().equals(groupApplyTransData.getGroupMainProductId(applyId))) {
                //17.18增加健康告知书上传
                AttachmentTypeResp healthDisclosure = new AttachmentTypeResp();
                healthDisclosure.setAttachmentTypeCode(HEALTH_DISCLOSURE.name());
                List<ApplyAttachmentPo> healthDisclosureAttachmentList = applyAttachmentBaseService.listApplyAttachment(applyId, HEALTH_DISCLOSURE.name());
                if (AssertUtils.isNotEmpty(healthDisclosureAttachmentList)) {
                    List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(healthDisclosureAttachmentList, new TypeToken<List<ApplyAttachmentResp>>() {
                    }.getType());
                    healthDisclosure.setListAttachment(listApplyAttachmentResp);
                }
                contractAttachmentList.add(healthDisclosure);
            }

            //若是年缴和趸交之外的投保单，需要上传缴费周期变更申请书
            boolean b = this.isPaymentModeRequest(applyId);
            if (b) {
                AttachmentTypeResp paymentModeModification = new AttachmentTypeResp();
                paymentModeModification.setAttachmentTypeCode(PAYMENT_MODE_MODIFICATION_REQUEST_FORM.name());
                List<ApplyAttachmentPo> paymentModeModificationAttachmentList = applyAttachmentBaseService.listApplyAttachment(applyId, PAYMENT_MODE_MODIFICATION_REQUEST_FORM.name());
                if (AssertUtils.isNotEmpty(paymentModeModificationAttachmentList)) {
                    List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(paymentModeModificationAttachmentList, new TypeToken<List<ApplyAttachmentResp>>() {
                    }.getType());
                    paymentModeModification.setListAttachment(listApplyAttachmentResp);
                }
                contractAttachmentList.add(paymentModeModification);
            }

            resultObject.setData(contractAttachmentList);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(GroupErrorConfigEnum.GROUP_APPLY_QUERY_CONTRACT_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    private boolean isPaymentModeRequest(String applyId) {
        List<ApplyCoveragePo> listApplyCoveragePo = applyCoverageBaseService.listApplyCoverageOfInsured(applyId);
        AssertUtils.isNotEmpty(getLogger(), listApplyCoveragePo, ApplyErrorConfigEnum.APPLY_BASE_BUSINESS_APPLY_COVERAGE_IS_NOT_FOUND_OBJECT);
        Optional<ApplyCoveragePo> first = listApplyCoveragePo.stream().filter(applyCoveragePo -> ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoveragePo.getPrimaryFlag())).findFirst();
        if (first.isPresent()) {
            ApplyCoveragePo mainApplyCoveragePo = first.get();
            return !Arrays.asList(ApplyTermEnum.PRODUCT_PAY_FREQUENCY.YEAR.name(), ApplyTermEnum.PRODUCT_PAY_FREQUENCY.SINGLE.name()).contains(mainApplyCoveragePo.getPremiumFrequency());
        }
        return false;
    }

    @Override
    public ResultObject<ContractAttachmentResponse> getGroupApplyAttachment(Users users, String applyId) {
        ResultObject<ContractAttachmentResponse> resultObject = new ResultObject<>();
        ContractAttachmentResponse groupApplyAttachmentTypeResponse = new ContractAttachmentResponse();
        try {
            List<AttachmentTypeResp> groupApplyAttachmentTypes = new ArrayList<>();
            List<AttachmentTypeResp> discountAttachmentType = new ArrayList<>();

            // 折扣系数有值，则展示折扣确认书上传窗口
            List<ApplyPremiumPo> applyPremiumPos = applyPremiumDao.fetchByApplyId(applyId);
            if (!applyPremiumPos.isEmpty()) {
                ApplyPremiumPo applyPremiumPo = applyPremiumPos.get(0);
                if (AssertUtils.isNotNull(applyPremiumPo)) {
                    // 折扣系数
                    BigDecimal specialDiscount = applyPremiumPo.getSpecialDiscount();
                    if (AssertUtils.isNotNull(specialDiscount) && BigDecimal.ZERO.compareTo(specialDiscount) != 0) {
                        // 添加折扣申请表
                        AttachmentTypeResp discountConfirmationBook = new AttachmentTypeResp();
                        discountConfirmationBook.setAttachmentTypeCode(DISCOUNT_REQUEST_FORM.name());
                        List<ApplyAttachmentPo> discountConfirmationBooks = applyAttachmentBaseService.listApplyAttachment(applyId, DISCOUNT_REQUEST_FORM.name());
                        if (AssertUtils.isNotEmpty(discountConfirmationBooks)) {
                            List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(discountConfirmationBooks, new TypeToken<List<ApplyAttachmentResp>>() {
                            }.getType());
                            discountConfirmationBook.setListAttachment(listApplyAttachmentResp);
                        }
                        discountAttachmentType.add(discountConfirmationBook);
                    }
                }
            }

            AttachmentTypeResp applyAttachmentResp = new AttachmentTypeResp();
            applyAttachmentResp.setAttachmentTypeCode(GROUP_APPLY_CONTRACT.name());
            List<ApplyAttachmentPo> applyAttachmentList = applyAttachmentBaseService.listApplyAttachment(applyId, GROUP_APPLY_CONTRACT.name());
            if (AssertUtils.isNotEmpty(applyAttachmentList)) {
                List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(applyAttachmentList, new TypeToken<List<ApplyAttachmentResp>>() {
                }.getType());
                applyAttachmentResp.setListAttachment(listApplyAttachmentResp);
            }
            groupApplyAttachmentTypes.add(applyAttachmentResp);

            AttachmentTypeResp applyInsuredAttachmentResp = new AttachmentTypeResp();
            applyInsuredAttachmentResp.setAttachmentTypeCode(GROUP_APPLY_CONTRACT_INSURED_INVENTORY.name());
            List<ApplyAttachmentPo> applyInsuredAttachmentList = applyAttachmentBaseService.listApplyAttachment(applyId, GROUP_APPLY_CONTRACT_INSURED_INVENTORY.name());
            if (AssertUtils.isNotEmpty(applyInsuredAttachmentList)) {
                List<ApplyAttachmentResp> listApplyAttachmentResp = (List<ApplyAttachmentResp>) this.converterList(applyInsuredAttachmentList, new TypeToken<List<ApplyAttachmentResp>>() {
                }.getType());
                applyInsuredAttachmentResp.setListAttachment(listApplyAttachmentResp);
            }
            groupApplyAttachmentTypes.add(applyInsuredAttachmentResp);

            groupApplyAttachmentTypeResponse.setGroupApplyAttachmentTypes(groupApplyAttachmentTypes);
            groupApplyAttachmentTypeResponse.setDiscountAttachmentType(discountAttachmentType);
            resultObject.setData(groupApplyAttachmentTypeResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(GroupErrorConfigEnum.GROUP_APPLY_QUERY_CONTRACT_ATTACHMENT_ERROR);
            }
        }
        return resultObject;
    }

    /**
     * 获取团险推荐信息下拉选
     *
     * @param users     用户
     * @param agentCode 业务员CODE
     * @param productId 产品ID
     * @return GroupReferralResponse
     */
    @Override
    public ResultObject<GroupReferralResponse> getGroupApplyReferral(Users users, String agentCode, String productId) {
        ResultObject<GroupReferralResponse> resultObject = new ResultObject<>();
        GroupReferralResponse groupReferralResponse = new GroupReferralResponse();
        AssertUtils.isNotEmpty(this.getLogger(), productId, ApplyErrorConfigEnum.APPLY_INPUT_PRODUCT_ID_IS_NOT_NULL);

        //获取代理人信息
        String agentId = "";
        if (!AssertUtils.isNotEmpty(agentCode)) {
            agentId = users.getUserId();
        } else {
            ResultObject<AgentResponse> agentResponseResultObject = agentApi.agentGet(agentCode);
            AssertUtils.isResultObjectDataNull(getLogger(), agentResponseResultObject, AppErrorConfigEnum.APP_AGENT_IS_NOT_NULL);
            agentId = agentResponseResultObject.getData().getAgentId();
        }
        ResultObject<AgentResponse> agentRespFcResultObject = agentApi.agentByIdGet(agentId);
        AssertUtils.isResultObjectDataNull(getLogger(), agentRespFcResultObject, AppErrorConfigEnum.APP_AGENT_IS_NOT_NULL);
        AgentResponse agentResponse = agentRespFcResultObject.getData();
        ResultObject<ProductDetailedInfoResponse> productDetailedInfoResponseResultObject = productApi.getProductDetailInfo(productId, agentResponse.getBranchId());
        if (TerminologyConfigEnum.WHETHER.YES.name().equals(productDetailedInfoResponseResultObject.getData().getReferralInfoFlag())) {
            //推荐信息所需的银行分支机构ID
            groupReferralResponse.setBankBranchId(agentResponse.getBranchId());
            ResultObject<List<BranchResponse>> branchSameLevel = platformBranchApi.getBranchSameLevel(agentResponse.getBranchId());
            if (!AssertUtils.isResultObjectListDataNull(branchSameLevel)) {
                groupReferralResponse.setBankBranchIds((List<BranchSimpleResponse>) this.converterList(branchSameLevel.getData(), new TypeToken<List<BranchSimpleResponse>>() {
                }.getType()));
            }
            resultObject.setData(groupReferralResponse);
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<ApplyListResponse>> getInputApplyList(Users currentLoginUsers, ApplyListRequest applyListRequest) {
        ResultObject<BasePageResponse<ApplyListResponse>> resultObject = new ResultObject<>();
        try {

            WaitingTaskRequest waitingTaskRequest = new WaitingTaskRequest();
            waitingTaskRequest.setWorkflowItemType(GROUP_NEW_ENTRY_TASK.name());
            waitingTaskRequest.setWorkflowType(ModelConstantEnum.WORKFLOW_STATUS.NEW_CONTRACT_GROUP.name());
            waitingTaskRequest.setUserId(currentLoginUsers.getUserId());
            //查询团险投保单数据
            List<ApplyListBo> applyListBos = applyQueryBaseService.loadApplyListByWorkFlow(waitingTaskRequest, applyListRequest, ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name());
            if (!AssertUtils.isNotEmpty(applyListBos)) {
                return resultObject;
            }
            //列表数据拼装
            List<ApplyListResponse> applyListResponses = groupApplyTransData.transApplyList(applyListBos, TerminologyTypeEnum.APPLY_INPUT_WORK_FLOW_STATUS.name(), currentLoginUsers);
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;

            BasePageResponse<ApplyListResponse> basePageResponse = BasePageResponse.getData(applyListRequest.getCurrentPage(), applyListRequest.getPageSize(), totalLine, applyListResponses);

            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    @Transactional
    public ResultObject deleteApply(Users currentLoginUsers, List<String> applyIds) {
        ResultObject resultObject = new ResultObject<>();
        try {
            List<ApplyPo> applyPos = applyBaseService.queryApplyByApplyId(applyIds);
            if (!AssertUtils.isNotEmpty(applyPos)) {
                throw new RequestException(GROUP_APPLY_QUERY_APPLY_IS_NOT_NULL);
            }
            applyPos.forEach(applyPo -> {
                if (AssertUtils.isNotEmpty(applyPo.getApplyStatus()) && !APPLY_STATUS_INITIAL.name().equals(applyPo.getApplyStatus())) {
                    throw new RequestException(GROUP_APPLY_DELETE_APPLY_IS_NOT_GROUP_NEW_ENTRY_TASK);
                }
                applyPo.setValidFlag(TerminologyConfigEnum.VALID_FLAG.invalid.name());
                applyBaseService.saveApply(currentLoginUsers.getUserId(), applyPo);
            });

        } catch (Exception e) {
            e.printStackTrace();
            this.setTransactionalResultObjectException(this.getLogger(), resultObject, e, ApplyErrorConfigEnum.APPLY_DELETE_APPLY_ERROR);
        }
        return resultObject;
    }

    @Override
    public ResultObject<BasePageResponse<GroupApplyResponse>> queryGroupApplyList(Users users, ApplyListRequest applyListRequest) {
        ResultObject<BasePageResponse<GroupApplyResponse>> resultObject = new ResultObject<>();
        try {
            List<ApplyListBo> applyListBos;
            applyListBos = applyQueryBaseService.loadGroupApplyListByUserId(users.getUserId(), applyListRequest, ApplyTermEnum.APPLY_TYPE.LIFE_INSURANCE_GROUP.name());
            if (!AssertUtils.isNotEmpty(applyListBos)) {
                return resultObject;
            }
            //列表数据拼装
            List<GroupApplyResponse> groupApplyResponses = (List<GroupApplyResponse>) converterList(applyListBos, new TypeToken<List<GroupApplyResponse>>() {
            }.getType());
            groupApplyResponses.forEach(groupApplyResponse -> {
                if (AssertUtils.isNotEmpty(groupApplyResponse.getApplyId())) {
                    ApplyUnderwriteDecisionPo applyUnderwriteDecisionPo = applyUnderwriteBaseService.queryApplyUnderwriteDecisionPo(groupApplyResponse.getApplyId());
                    if (AssertUtils.isNotNull(applyUnderwriteDecisionPo)) {
                        groupApplyResponse.setCommitApplyDate(applyUnderwriteDecisionPo.getCreatedDate());
                    }
                }
            });

            if (!AssertUtils.isNotEmpty(groupApplyResponses)) {
                return resultObject;
            }
            //获取总页数
            Integer totalLine = AssertUtils.isNotNull(applyListBos) ? applyListBos.get(0).getTotalLine() : null;
            BasePageResponse<GroupApplyResponse> basePageResponse = BasePageResponse.getData(applyListRequest.getCurrentPage(), applyListRequest.getPageSize(), totalLine, groupApplyResponses);
            resultObject.setData(basePageResponse);
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_QUERY_APPLY_ERROR);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<List<SyscodeRespFc>> getApplyStatusList(Users users) {
        ResultObject<List<SyscodeRespFc>> resultObject = new ResultObject<>();
        try {
            ResultObject<List<SyscodeRespFc>> syscodeRespFcs = platformBaseInternationServiceApi.queryInternational(TerminologyTypeEnum.APPLY_STATUS.name(), users.getLanguage());
            if (!AssertUtils.isResultObjectListDataNull(syscodeRespFcs)) {
                //过滤八种状态：初始化、待复核、复核完成待核保、拒保、待支付、承保中、已承保、废弃
                List<String> codeKeyList = new ArrayList<>();
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INITIAL_COMPLETE.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_INPUT_REVIEW_COMPLETE.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_REFUND.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_FAILED.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_APPROVE_SUCCESS.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPLY_STATUS_ABANDONED.name());
                codeKeyList.add(ApplyTermEnum.APPLY_STATUS_FLAG.APPROVED_HC.name());
                List<SyscodeRespFc> syscodeRespFcList = syscodeRespFcs.getData().stream().filter(syscodeRespFc -> codeKeyList.contains(syscodeRespFc.getCodeKey())).collect(Collectors.toList());
                resultObject.setData(syscodeRespFcList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof RequestException) {
                RequestException error = (RequestException) e;
                //错误设置
                resultObject.setIenum(error.getiEnum());
            } else {
                resultObject.setIenum(ApplyErrorConfigEnum.APPLY_FEIGN_CLIENT_PLATFORM_IS_NOT_NULL);
            }
        }
        return resultObject;
    }

    @Override
    public ResultObject<ApplyPremiumResponse> queryApplyPremiumDetails(Users currentLoginUsers, String applyId) {
        AssertUtils.isNotEmpty(this.getLogger(), applyId, GROUP_APPLY_PARAMETER_APPLY_ID_IS_NOT_NULL);

        ResultObject<ApplyPremiumResponse> resultObject = new ResultObject<>();
        List<ApplyPremiumPo> applyPremiumPos = applyPremiumDao.fetchByApplyId(applyId);
        if (!applyPremiumPos.isEmpty()) {
            ApplyPremiumPo applyPremiumPo = applyPremiumPos.get(0);
            if (AssertUtils.isNotNull(applyPremiumPo)) {
                ApplyPremiumResponse applyPremiumResponse = (ApplyPremiumResponse) converterObject(applyPremiumPo, ApplyPremiumResponse.class);
                BigDecimal hundred = new BigDecimal("100");
                // 折扣系数
                BigDecimal specialDiscount = applyPremiumPo.getSpecialDiscount();
                // 公司折扣
                BigDecimal companyDiscount = applyPremiumPo.getCompanyDiscount();
                // 业务员折扣
                BigDecimal agentDiscount = applyPremiumPo.getAgentDiscount();
                if (AssertUtils.isNotNull(specialDiscount)) {
                    applyPremiumResponse.setSpecialDiscount(new BigDecimal(hundred.multiply(specialDiscount).stripTrailingZeros().toPlainString()));
                }
                if (AssertUtils.isNotNull(companyDiscount)) {
                    applyPremiumResponse.setCompanyDiscount(new BigDecimal(hundred.multiply(companyDiscount).stripTrailingZeros().toPlainString()));
                }
                if (AssertUtils.isNotNull(agentDiscount)) {
                    applyPremiumResponse.setAgentDiscount(new BigDecimal(hundred.multiply(agentDiscount).stripTrailingZeros().toPlainString()));
                }
                resultObject.setData(applyPremiumResponse);
                return resultObject;
            }
        }
        return resultObject;
    }

}
