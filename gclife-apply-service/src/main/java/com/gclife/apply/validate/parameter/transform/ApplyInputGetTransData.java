package com.gclife.apply.validate.parameter.transform;

import com.alibaba.fastjson.JSON;
import com.gclife.apply.core.jooq.tables.pojos.*;
import com.gclife.apply.dao.ApplyExtDao;
import com.gclife.apply.model.bo.*;
import com.gclife.apply.model.bo.app.ApplyPlanBo;
import com.gclife.apply.model.config.ApplyErrorConfigEnum;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.feign.ApplyAttachmentResp;
import com.gclife.apply.model.response.*;
import com.gclife.apply.service.*;
import com.gclife.apply.transform.ApplyDataTransform;
import com.gclife.apply.validate.ClazzBusinessService;
import com.gclife.common.InternationalTypeEnum;
import com.gclife.common.TerminologyConfigEnum;
import com.gclife.common.function.GcFactorFunction;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.BaseTermEnum;
import com.gclife.common.model.config.TerminologyTypeEnum;
import com.gclife.common.model.feign.SyscodeRespFc;
import com.gclife.common.model.pojo.AppRequestHeads;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ClazzUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.platform.api.*;
import com.gclife.platform.model.response.*;
import com.gclife.product.api.ProductApi;
import com.gclife.product.api.ProductHealthPaperApi;
import com.gclife.product.model.config.ProductTermEnum;
import com.gclife.product.model.response.HealthPaperResponse;
import com.gclife.product.model.response.duty.DutyResponse;
import com.gclife.product.model.response.manager.ProductDetailedInfoResponse;
import com.gclife.product.model.response.paramter.ParameterFieldResponse;
import com.gclife.product.model.response.paramter.ParameterValueResponse;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create 17-9-18
 * description
 */
@Component
public class ApplyInputGetTransData extends BaseBusinessServiceImpl {
    @Autowired
    private ProductApi productApi;
    @Autowired
    private PlatformBaseInternationServiceApi platformBaseInternationServiceApi;
    @Autowired
    private PlatformCareerApi platformCareerApi;
    @Autowired
    private PlatformBankApi platformBankApi;
    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private ApplyExtDao applyExtDao;
    @Autowired
    private ClazzBusinessService clazzBusinessService;
    @Autowired
    private ApplyParameterTransData applyParameterTransData;
    @Autowired
    private ProductHealthPaperApi productHealthPaperApi;
    @Autowired
    private PlatformInternationalBaseApi platformInternationalBaseApi;
    private List<SyscodeResponse> listCoverageTitle = new ArrayList<>();
    @Autowired
    private ApplyRemarkBaseService applyRemarkBaseService;
    @Autowired
    private ApplyBaseService applyBaseService;
    @Autowired
    private PlatformBranchBaseApi platformBranchBaseApi;
    @Autowired
    private ApplyOtherInfoBaseService applyOtherInfoBaseService;
    @Autowired
    private ApplyDataTransform applyDataTransform;
    @Autowired
    private ApplyUnderwriteBaseService applyUnderwriteBaseService;
    @Autowired
    private ApplyCustomerMergerTransData applyCustomerMergerTransData;
    @Autowired
    private ApplyPlanBaseService applyPlanBaseService;
    @Autowired
    private ApplyCoverageBaseService applyCoverageBaseService;


    public ApplyInputResponse transApplyInputResponse(ApplyBo applyBo, String type, AppRequestHeads appRequestHeads) {

        //附件数据特殊处理 方便前端数据处理
        List<String> attachmentIds = new ArrayList<>();
        if (AssertUtils.isNotEmpty(applyBo.getListAttachment())) {
            attachmentIds = applyBo.getListAttachment().stream().map(ApplyAttachmentBo::getAttachmentId).collect(Collectors.toList());
        }


        //数据转换
        ApplyInputResponse applyResponse = (ApplyInputResponse) this.converterObject(applyBo, ApplyInputResponse.class);
        applyResponse.setBackTrackDateFormat(DateUtils.timeStrToString(applyResponse.getBackTrackDate(), DateUtils.FORMATE3));
        //转换投保人信息
        this.transApplyInputApplicantResponse(applyResponse, appRequestHeads);
        //转换保单联系人
        this.transApplyInputContactResponse(applyResponse);
        //转换被保人信息
        this.transApplyInputInsuredResponse(applyResponse, applyBo, appRequestHeads);
        ////设置投保告知书
        GcFactorFunction gcFuction = () -> {
            transApplyInputListQuestion(applyResponse, applyBo);
        };
        GcFactorFunction thFuction = () -> {
            if (ApplyTermEnum.APPLY_SOURCE.APP.name().equals(applyBo.getApplySource())) {
                transApplyInputListQuestion(applyResponse, applyBo);
            }
        };
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(BaseTermEnum.BASE_FACTOR_CONFIG_VALUE.GC.name(), gcFuction);
        map.put(BaseTermEnum.BASE_FACTOR_CONFIG_VALUE.TH.name(), thFuction);
        this.handleDifferent(map, ApplyTermEnum.BASE_FACTOR_CONFIG_CODE.APPLY_HEALTH_NOTICE_BOOK_VERIFICATION.name());
        List<ImageManageAttachmentResponse> attachmentTypeResponseList = new ArrayList<>();
        List<ApplyAttachmentBo> applyAttachmentBos = applyBo.getListAttachment();
        applyAttachmentBos.forEach(attachment -> {
            ImageManageAttachmentResponse applyAttachmentTypeResponse = attachmentTypeResponseList.stream()
                    .filter(attachmentType -> attachmentType.getAttachmentTypeCode().equals(attachment.getAttachmentTypeCode()))
                    .findFirst().orElse(new ImageManageAttachmentResponse());

            if (!AssertUtils.isNotEmpty(applyAttachmentTypeResponse.getAttachmentTypeCode())) {
                applyAttachmentTypeResponse.setAttachmentTypeCode(attachment.getAttachmentTypeCode());
                applyAttachmentTypeResponse.setAttachmentSeq(attachment.getAttachmentSeq());
                applyAttachmentTypeResponse.setTotalPage(0L);
                attachmentTypeResponseList.add(applyAttachmentTypeResponse);
            }

            //统计总记录数
            applyAttachmentTypeResponse.setTotalPage(applyAttachmentTypeResponse.getTotalPage() + 1);
            //附件
            ApplyAttachmentResp applyAttachmentResponse = new ApplyAttachmentResp();
            applyAttachmentResponse.setAttachmentId(attachment.getApplyAttachmentId());
            applyAttachmentResponse.setAttachmentId(attachment.getAttachmentId());
            applyAttachmentResponse.setAttachmentSeq(attachment.getAttachmentSeq());
            applyAttachmentTypeResponse.getListApplyAttachment().add(applyAttachmentResponse);
        });
        //设置附件
        applyResponse.setListAttachmentId(attachmentTypeResponseList);
        //账户
        if (!AssertUtils.isNotEmpty(applyResponse.getListApplyAccount())) {
            List<ApplyAccountResponse> responses = new ArrayList<>();
            responses.add(transApplyAccount(applyBo, new ApplyAccountResponse()));
            applyResponse.setListApplyAccount(responses);
        } else {
            applyResponse.getListApplyAccount().forEach(applyAccountResponse -> {
                //投保人地址拼接
                if (AssertUtils.isNotEmpty(applyAccountResponse.getAreaCode())) {
                    ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyAccountResponse.getAreaCode());
                    if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                        applyAccountResponse.setAreaName(respFcResultObject.getData().getAreaName());
                    }
                    ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(applyAccountResponse.getAreaCode());
                    if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                        applyAccountResponse.setListAreaName(areaResultObject.getData());
                    }
                }
                transApplyAccount(applyBo, applyAccountResponse);
            });
        }
        //判断是否有未处理的客户合并  其中包括 需要合并但没有保存合并数据 或者 存在合并结论为NOT_MERGED的数据
        if ("REVIEW".equals(type) && applyCustomerMergerTransData.isUnprocessedSuspectedCustomers(applyBo.getApplyId())) {
            applyResponse.setDoubtfulClientFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        if (ApplyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(applyBo.getChannelTypeCode())) {
            applyResponse.setOnlineFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        if (AssertUtils.isNotEmpty(applyBo.getActivationCode())) {
            applyResponse.setScratchCardFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }

        List<ApplyUnderwriteProblemPo> applyUnderwriteProblemPos = applyUnderwriteBaseService.queryApplyUnderwriteProblemPo(applyResponse.getApplyId());
        if (AssertUtils.isNotEmpty(applyUnderwriteProblemPos)) {
            Optional<ApplyUnderwriteProblemPo> first = applyUnderwriteProblemPos.stream().filter(applyUnderwriteProblemPo -> ApplyTermEnum.AUTO_UNDERWRITE_PROBLEM.REAL_CLIENT.name().equals(applyUnderwriteProblemPo.getProblemCode()))
                    .findFirst();
            if (first.isPresent()) {
                applyResponse.setRealClientFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
        }

        //设置贷款合同信息录入标识
        Optional<ApplyCoverageResponse> first = applyResponse.getListInsured().get(0).getListCoverage().stream().filter(applyCoverageResponse ->
                ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageResponse.getPrimaryFlag())
        ).findFirst();
        if (first.isPresent()) {
            String productId = first.get().getProductId();
//            Long discountDate = AssertUtils.isNotNull(applyBo.getAppSubmitUnderwritingDate()) ? applyBo.getAppSubmitUnderwritingDate() : DateUtils.getCurrentTime();
//            ResultObject<ProductSimpleInfoResponse> productSimpleInfo = productApi.getProductSimpleInfo(productId, applyBo.getSalesBranchId(), discountDate);
            ApplyPlanBo applyPlanBo = applyPlanBaseService.queryApplyPlan(applyBo.getApplyId());
            if (AssertUtils.isNotNull(applyPlanBo) && AssertUtils.isNotNull(applyPlanBo.getSpecialDiscount()) && AssertUtils.isNotNull(applyPlanBo.getDiscountObject())
            ) {
                applyResponse.setBankDiscountFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            ResultObject<ProductDetailedInfoResponse> productDetailInfo = productApi.getProductDetailInfo(productId, applyBo.getSalesBranchId());
            if (!AssertUtils.isResultObjectDataNull(productDetailInfo)) {
                ProductDetailedInfoResponse productDetailInfoData = productDetailInfo.getData();
                applyResponse.setContractLoanFlag(productDetailInfoData.getContractLoanFlag());
                applyResponse.setReferralInfoFlag(productDetailInfoData.getReferralInfoFlag());
                applyResponse.setLegalBeneficialFlag(productDetailInfoData.getLegalBeneficialFlag());
                //计划书录入职业的产品，不能再次更改职业
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(productDetailInfoData.getOccupationPlanFlag())) {
                    applyResponse.setOccupationEditableFlag(TerminologyConfigEnum.WHETHER.NO.name());
                }
                //2021.12.03  职业性质必填
                applyResponse.setOccupationNatureFlag(TerminologyConfigEnum.WHETHER.YES.name());
                applyResponse.setInsuredOccupationNatureFlag(TerminologyConfigEnum.WHETHER.YES.name());
                if (AssertUtils.isNotEmpty(applyResponse.getListInsured())
                        && AssertUtils.isNotNull(applyResponse.getListInsured().get(0).getBirthday())
                        && applyDataTransform.getAgeByBirthday(applyResponse.getListInsured().get(0).getBirthday()) < 18) {
                    applyResponse.setInsuredOccupationNatureFlag(TerminologyConfigEnum.WHETHER.NO.name());
                }
                //产品折扣信息
                applyResponse.setDiscountPremium(applyDataTransform.transApplyDiscountPremium(applyBo));
                applyResponse.setBackTrackFlag(productDetailInfoData.getBackTrackFlag());

                //推荐信息
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(applyResponse.getReferralInfoFlag())) {
                    ApplyReferralInfoResponse applyReferralInfoResponse = new ApplyReferralInfoResponse();
                    if (AssertUtils.isNotNull(applyBo.getReferralInfo())) {
                        ClazzUtils.copyPropertiesIgnoreNull(applyBo.getReferralInfo(), applyReferralInfoResponse);
                    } else {
                        applyReferralInfoResponse.setBankBranchId(applyBo.getSalesBranchId());
                    }
                    applyResponse.setReferralInfo(applyReferralInfoResponse);
                }
                //返回贷款合同信息
                if (TerminologyConfigEnum.WHETHER.YES.name().equals(productDetailInfoData.getContractLoanFlag())) {
                    if (AssertUtils.isNotNull(applyBo.getLoanContract())) {
                        ApplyLoanResponse applyLoanResponse = new ApplyLoanResponse();
                        ClazzUtils.copyPropertiesIgnoreNull(applyBo.getLoanContract(), applyLoanResponse);
                        applyLoanResponse.setExchangeRate(TerminologyConfigEnum.CURRENCY.USD.name().equals(applyBo.getLoanContract().getCurrency()) ? "1" : applyBo.getLoanContract().getExchangeRate().toString());
                        applyResponse.setLoanContract(applyLoanResponse);
                    }
                    //第一机构受益人信息
                    ApplyLoanBeneficiaryResponse loanBeneficiary = new ApplyLoanBeneficiaryResponse();
                    List<ApplyBeneficiaryInfoBo> applyBeneficiaryInfoBos = applyBaseService.queryApplyLoanBeneficiary(applyBo.getApplyId(), TerminologyConfigEnum.WHETHER.NO.name());
                    if (AssertUtils.isNotEmpty(applyBeneficiaryInfoBos)) {
                        loanBeneficiary.setBeneficiaryBranchId(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchId());
                        loanBeneficiary.setBeneficiaryBranchCode(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchCode());
                        loanBeneficiary.setBeneficiaryBranchName(applyBeneficiaryInfoBos.get(0).getApplyBeneficiaryBo().getBeneficiaryBranchName());
                        loanBeneficiary.setBeneficiaryNoOrder(applyBeneficiaryInfoBos.get(0).getBeneficiaryNoOrder());
                        loanBeneficiary.setBeneficiaryProportion(applyBeneficiaryInfoBos.get(0).getBeneficiaryProportion());
                    } else {
                        // 查询机构信息
                        ResultObject<List<BranchResponse>> branchParentList = platformBranchBaseApi.queryBranchParentListById(applyBo.getSalesBranchId());
                        AssertUtils.isResultObjectError(getLogger(), branchParentList);
                        for (BranchResponse branchResponse : branchParentList.getData()) {
                            if (AssertUtils.isNotNull(branchResponse.getBranchLicense())) {
                                loanBeneficiary.setBeneficiaryBranchCode(branchResponse.getBranchLicense().getLicenseNo());
                                loanBeneficiary.setBeneficiaryBranchId(branchResponse.getBranchId());
                                break;
                            }
                        }
                        loanBeneficiary.setBeneficiaryNoOrder(ApplyTermEnum.BENEFICIARY_NO.ORDER_ONE.name());
                        loanBeneficiary.setBeneficiaryProportion(BigDecimal.valueOf(100));
                    }
                    applyResponse.setLoanBeneficiary(loanBeneficiary);
                }
            }
        }

        if (ApplyTermEnum.CHANNEL_TYPE.ONLINE.name().equals(applyBo.getChannelTypeCode()) && AssertUtils.isNotNull(applyBo.getApplyPremiumBo().getSpecialDiscount())) {
            applyResponse.setPromotionalCodeShowFlag(TerminologyConfigEnum.WHETHER.YES.name());
        }
        return applyResponse;
    }

    private void transApplyInputListQuestion(ApplyInputResponse applyResponse, ApplyBo applyBo) {
        //查询投保告知书
        AppApplyBo appApplyBo = (AppApplyBo) this.converterObject(applyBo, AppApplyBo.class);
        appApplyBo.setBranchId(applyBo.getSalesBranchId());
        List<ProductHealthNoticeResponse>
                applyQuestionResponses = transApplyApplyHealthNotice(appApplyBo, null);
        if (!AssertUtils.isNotEmpty(applyQuestionResponses)) {
            return;
        }
        applyResponse.setListQuestion(getQuestionnaireResponseList(applyQuestionResponses, applyBo.getApplyId()));
    }

    public List<ApplyQuestionnaireResponse> getQuestionnaireResponseList(List<ProductHealthNoticeResponse> applyQuestionResponses, String applyId) {
        List<ApplyQuestionnaireResponse> questionnaireResponseList = new ArrayList<>();
        List<ProductHealthNoticeResponse> insuredHealthNoticeResponseList = new ArrayList<>();
        List<ProductHealthNoticeResponse> applicantHealthNoticeResponseList = new ArrayList<>();
        applyQuestionResponses.forEach(productHealthNoticeResponse -> {
            if (ApplyTermEnum.CUSTOMER_TYPE.INSURED.name().equals(productHealthNoticeResponse.getCustomerType())) {
                insuredHealthNoticeResponseList.add(productHealthNoticeResponse);
            } else if (ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name().equals(productHealthNoticeResponse.getCustomerType())) {
                applicantHealthNoticeResponseList.add(productHealthNoticeResponse);
            }
        });
        if (AssertUtils.isNotEmpty(insuredHealthNoticeResponseList)) {
            ApplyHealthQuestionnaireRemarkPo applyHealthQuestionnaireRemark = applyRemarkBaseService.getApplyHealthQuestionnaireRemark(applyId, ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            questionnaireResponseList.add(new ApplyQuestionnaireResponse(
                    ApplyTermEnum.CUSTOMER_TYPE.INSURED.name(), insuredHealthNoticeResponseList, AssertUtils.isNotNull(applyHealthQuestionnaireRemark) ? applyHealthQuestionnaireRemark.getRemark() : null
            ));
        }
        if (AssertUtils.isNotEmpty(applicantHealthNoticeResponseList)) {
            ApplyHealthQuestionnaireRemarkPo applyHealthQuestionnaireRemark = applyRemarkBaseService.getApplyHealthQuestionnaireRemark(applyId, ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
            questionnaireResponseList.add(new ApplyQuestionnaireResponse(
                    ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name(), applicantHealthNoticeResponseList, AssertUtils.isNotNull(applyHealthQuestionnaireRemark) ? applyHealthQuestionnaireRemark.getRemark() : null
            ));
        }
        return questionnaireResponseList;
    }

    private ApplyAccountResponse transApplyAccount(ApplyBo applyBo, ApplyAccountResponse applyAccountResponse) {
//        applyAccountResponse.setInitialPaymentMode(applyBo.getInitialPaymentMode());
        return applyAccountResponse;
    }


    private void transApplyInputContactResponse(ApplyInputResponse applyResponse) {
        ApplyContactResponse applyContactResponse = applyResponse.getApplyContact();
        //保单寄送地址拼接
        if (AssertUtils.isNotEmpty(applyContactResponse.getSendAddrAreaCode())) {
            ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyContactResponse.getSendAddrAreaCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                applyContactResponse.setSendAddrAreaName(respFcResultObject.getData().getAreaName());
            }
            ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(applyContactResponse.getSendAddrAreaCode());
            if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                applyContactResponse.setListAreaName(areaResultObject.getData());
            }
        }
    }

    private void transApplyInputApplicantResponse(ApplyInputResponse applyResponse, AppRequestHeads appRequestHeads) {
        ApplyApplicantResponse applyApplicantResponse = applyResponse.getApplicant();
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getExpectedPremiumSources())) {
            applyApplicantResponse.setListExpectedPremiumSources(JSON.parseArray(applyApplicantResponse.getExpectedPremiumSources(), String.class));
        }
        ApplyOccupationNaturePo applyOccupationNaturePo = applyOtherInfoBaseService.queryApplyOccupationNaturePo(applyResponse.getApplyId(), ApplyTermEnum.CUSTOMER_TYPE.APPLICANT.name());
        if (AssertUtils.isNotNull(applyOccupationNaturePo)) {
            ApplyOccupationNatureResponse applyOccupationNatureResponse = new ApplyOccupationNatureResponse();
            ClazzUtils.copyPropertiesIgnoreNull(applyOccupationNaturePo, applyOccupationNatureResponse);
            applyApplicantResponse.setOccupationNature(applyOccupationNatureResponse);
        }

        //投保人地址拼接
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getHomeAreaCode())) {
            ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(applyApplicantResponse.getHomeAreaCode());
            if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                applyApplicantResponse.setHomeAreaName(respFcResultObject.getData().getAreaName());
            }
            ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(applyApplicantResponse.getHomeAreaCode());
            if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                applyApplicantResponse.setListAreaName(areaResultObject.getData());
            }
        }
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getDoctorAreaCode())) {
            ResultObject<List<AreaTreeResponse>> getDoctorAreaCode = platformAreaApi.areaTreeGet(applyApplicantResponse.getDoctorAreaCode());
            if (!AssertUtils.isResultObjectDataNull(getDoctorAreaCode)) {
                applyApplicantResponse.setListDoctorAreaName(getDoctorAreaCode.getData());
            }
        }

        if (AssertUtils.isNotEmpty(applyApplicantResponse.getCompanyAreaCode())) {
            ResultObject<List<AreaTreeResponse>> getCompanyAreaCode = platformAreaApi.areaTreeGet(applyApplicantResponse.getCompanyAreaCode());
            if (!AssertUtils.isResultObjectDataNull(getCompanyAreaCode)) {
                applyApplicantResponse.setListCompanyAreaName(getCompanyAreaCode.getData());
            }
        }

        //投保人职业
        if (AssertUtils.isNotEmpty(applyApplicantResponse.getOccupationCode())) {
            ResultObject<CareerNameResponse> careerNameResponseResultObject = platformCareerApi.careerNameGet(applyApplicantResponse.getOccupationCode());
            if (!AssertUtils.isResultObjectDataNull(careerNameResponseResultObject)) {
                applyApplicantResponse.setOccupationName(careerNameResponseResultObject.getData().getCareerName());
            }
        }

        //针对20A网销产品国籍国际化特殊处理
        applyApplicantResponse.setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyResponse.getApplyId(), applyApplicantResponse.getNationality(), appRequestHeads.getLanguage()));

        //投保單複核展示bmi，有值直接展示，沒值根據身高體重算，在重新設置，反之，直接返回空
        if (!AssertUtils.isNotEmpty(applyApplicantResponse.getBmi())) {
            if (AssertUtils.isNotEmpty(applyApplicantResponse.getStature())
                    && AssertUtils.isNotEmpty(applyApplicantResponse.getAvoirdupois())) {
                BigDecimal powStature = new BigDecimal(applyApplicantResponse.getStature()).divide(new BigDecimal("100")).pow(2);
                BigDecimal bmi = new BigDecimal(applyApplicantResponse.getAvoirdupois()).divide(powStature, 2, BigDecimal.ROUND_HALF_UP);
                applyApplicantResponse.setBmi(bmi+"");
            }
        }

        //設置投保年齡
        Integer applyAge = getAgeYear(applyApplicantResponse.getBirthday(), applyResponse.getApplyDate());
        applyApplicantResponse.setApplyAge(applyAge+"");
    }

    private Integer getAgeYear(Long insuredBirthday, Long approveDate) {
        try {
            if (AssertUtils.isNotNull(insuredBirthday) && AssertUtils.isNotNull(approveDate)) {
                return DateUtils.getAgeYear(new Date(insuredBirthday), new Date(approveDate));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


//    public List<ApplyCoverageResponse> transApplyInputCalculateResponse(ApplyBo applyBo) {
//        List<ApplyCoverageResponse> listApplyCoverageResponse = new ArrayList<>();
//        //转换险种信息
//        listApplyCoverageResponse.add(this.transApplyInputCoverageResponse(applyBo));
//        return listApplyCoverageResponse;
//    }

    public List<ApplyCoverageResponse> transApplyInputCoverageResponse(ApplyBo applyBo) {
        List<ApplyCoverageResponse> listApplyCoverageResponse = new ArrayList<>();
        //获取险种字段属性
        List<Field> coverageFields = clazzBusinessService.getFiledList(ApplyCoveragePo.class);
        List<Field> dutyFields = clazzBusinessService.getFiledList(ApplyCoverageDutyPo.class);

        applyBo.getListInsured().get(0).getListCoverage().forEach(applyCoverageBo -> {
            ApplyCoverageResponse applyCoverageResponse = new ApplyCoverageResponse();
            applyCoverageResponse.setProductId(applyCoverageBo.getProductId());
            applyCoverageResponse.setPrimaryFlag(applyCoverageBo.getPrimaryFlag());
            applyCoverageResponse.setProductCode(applyCoverageBo.getProductCode());
            //录入复核受理投保单险种信息
            transferApplyCoverageList(applyBo.getSalesBranchId(), coverageFields, dutyFields, applyCoverageResponse, applyCoverageBo);
            listApplyCoverageResponse.add(applyCoverageResponse);
        });
        return listApplyCoverageResponse;
    }


    /**
     * 转换被保人以及险种信息
     *
     * @param applyResponse
     * @param applyBo
     */
    public void transApplyInputInsuredResponse(ApplyInputResponse applyResponse, ApplyBo applyBo, AppRequestHeads appRequestHeads) {
        /**
         * 被保人返回集合
         */
        List<ApplyInsuredResponse> applyInsuredResponses = Optional.ofNullable(applyResponse.getListInsured()).orElse(new ArrayList<ApplyInsuredResponse>());
        //被保人集合为空的时候新建一个默认的被保人:为了存放集合
        if (applyInsuredResponses.size() == 0) {
            ApplyInsuredResponse applyInsuredResponse = new ApplyInsuredResponse();
            applyInsuredResponses.add(applyInsuredResponse);
            applyResponse.setListInsured(applyInsuredResponses);
        }
        //循环被保人
        applyInsuredResponses.forEach(insuredRes -> {
            //投保單複核展示bmi，有值直接展示，沒值根據身高體重算，在重新設置，反之，直接返回空
            if (!AssertUtils.isNotEmpty(insuredRes.getBmi())) {
                if (AssertUtils.isNotEmpty(insuredRes.getStature())
                        && AssertUtils.isNotEmpty(insuredRes.getAvoirdupois())) {
                    BigDecimal powStature = new BigDecimal(insuredRes.getStature()).divide(new BigDecimal("100")).pow(2);
                    BigDecimal bmi = new BigDecimal(insuredRes.getAvoirdupois()).divide(powStature, 2, BigDecimal.ROUND_HALF_UP);
                    insuredRes.setBmi(bmi+"");
                }
            }

            //設置投保年齡
            Integer applyAge = getAgeYear(insuredRes.getBirthday(), applyResponse.getApplyDate());
            insuredRes.setApplyAge(applyAge+"");

            if (AssertUtils.isNotEmpty(insuredRes.getExpectedPremiumSources())) {
                insuredRes.setListExpectedPremiumSources(JSON.parseArray(insuredRes.getExpectedPremiumSources(), String.class));
            }
            if (AssertUtils.isNotNull(insuredRes.getBirthday()) && applyDataTransform.getAgeByBirthday(insuredRes.getBirthday()) < 18) {
                applyResponse.setHolderFlag(TerminologyConfigEnum.WHETHER.YES.name());
            }
            ApplyOccupationNaturePo applyOccupationNaturePo = applyOtherInfoBaseService.queryApplyOccupationNaturePo(applyResponse.getApplyId(), ApplyTermEnum.CUSTOMER_TYPE.INSURED.name());
            if (AssertUtils.isNotNull(applyOccupationNaturePo)) {
                ApplyOccupationNatureResponse applyOccupationNatureResponse = new ApplyOccupationNatureResponse();
                ClazzUtils.copyPropertiesIgnoreNull(applyOccupationNaturePo, applyOccupationNatureResponse);
                insuredRes.setOccupationNature(applyOccupationNatureResponse);
            }
            //被保人地址拼接
            if (AssertUtils.isNotEmpty(insuredRes.getHomeAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(insuredRes.getHomeAreaCode());
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject)) {
                    insuredRes.setHomeAreaName(respFcResultObject.getData().getAreaName());
                }
                ResultObject<List<AreaTreeResponse>> areaResultObject = platformAreaApi.areaTreeGet(insuredRes.getHomeAreaCode());
                if (!AssertUtils.isResultObjectDataNull(areaResultObject)) {
                    insuredRes.setListAreaName(areaResultObject.getData());
                }
            }

            if (AssertUtils.isNotEmpty(insuredRes.getDoctorAreaCode())) {
                ResultObject<List<AreaTreeResponse>> getDoctorAreaCode = platformAreaApi.areaTreeGet(insuredRes.getDoctorAreaCode());
                if (!AssertUtils.isResultObjectDataNull(getDoctorAreaCode)) {
                    insuredRes.setListDoctorAreaName(getDoctorAreaCode.getData());
                }
            }

            if (AssertUtils.isNotEmpty(insuredRes.getCompanyAreaCode())) {
                ResultObject<List<AreaTreeResponse>> getCompanyAreaCode = platformAreaApi.areaTreeGet(insuredRes.getCompanyAreaCode());
                if (!AssertUtils.isResultObjectDataNull(getCompanyAreaCode)) {
                    insuredRes.setListCompanyAreaName(getCompanyAreaCode.getData());
                }
            }

            //针对20A网销产品国籍国际化特殊处理
            insuredRes.setNationalityName(applyDataTransform.getNationalityCodeTypeName(applyBo.getApplyId(), insuredRes.getNationality(), appRequestHeads.getLanguage()));

            //被保人职业
            if (AssertUtils.isNotEmpty(insuredRes.getOccupationCode())) {
                ResultObject<CareerNameResponse> careerNameResponseResultObject = platformCareerApi.careerNameGet(insuredRes.getOccupationCode());
                if (!AssertUtils.isResultObjectDataNull(careerNameResponseResultObject)) {
                    insuredRes.setOccupationName(careerNameResponseResultObject.getData().getCareerName());
                }
            }

            //受益人
            if (Optional.ofNullable(insuredRes.getInsuredId()).isPresent() && Optional.ofNullable(insuredRes.getListBeneficiary()).filter(beneRes -> beneRes.size() > 0).isPresent()) {
                applyBo.getListInsured().stream().filter(insuredBo -> insuredBo.getInsuredId().equals(insuredRes.getInsuredId())).map(insuredBo -> {
                    //受益人转换
                    Optional.ofNullable(insuredRes.getListBeneficiary()).filter(listBene -> listBene.size() > 0).ifPresent(listBene -> {
                        listBene.stream().map(beneRes -> {
                            insuredBo.getListBeneficiary().stream().filter(beneBo -> beneBo.getBeneficiaryId().equals(beneRes.getBeneficiaryId())).findFirst().ifPresent(beneBo -> {
                                Optional.ofNullable(beneBo.getApplyBeneficiaryBo()).ifPresent(beneBoInfo -> {
                                    beneRes.setName(beneBoInfo.getName());
                                    if (AssertUtils.isNotEmpty(beneBoInfo.getIdType())) {
                                        beneRes.setIdType(beneBoInfo.getIdType());
                                    }
                                    if (AssertUtils.isNotEmpty(beneBoInfo.getIdNo())) {
                                        beneRes.setIdNo(beneBoInfo.getIdNo());
                                    }
                                    beneRes.setSex(beneBoInfo.getSex());
                                    if (AssertUtils.isNotNull(beneBoInfo.getBirthday())) {
                                        beneRes.setBirthday(beneBoInfo.getBirthday());
                                    }
                                });
                            });

                            return null;
                        }).collect(Collectors.toList());
                    });
                    return null;
                }).collect(Collectors.toList());
            }

            //获取险种字段属性
            List<Field> coverageFields = clazzBusinessService.getFiledList(ApplyCoveragePo.class);
            List<Field> dutyFields = clazzBusinessService.getFiledList(ApplyCoverageDutyPo.class);
            List<Field> acceptCoverageFields = clazzBusinessService.getFiledList(ApplyCoverageAcceptPo.class);

            //判断被保人ID不为空 且下面有险种 :使用自身险种
            if (Optional.ofNullable(insuredRes.getInsuredId()).isPresent() && Optional.ofNullable(insuredRes.getListCoverage()).filter(list -> list.size() > 0).isPresent()) {

                applyBo.getListInsured()
                        .stream()
                        .filter(insuredBo -> insuredBo.getInsuredId().equals(insuredRes.getInsuredId()))
                        .forEach(insuredBo -> {
                            //险种转换
                            insuredRes.getListCoverage().forEach(coverageRes -> {
                                insuredBo.getListCoverage().stream().filter(coverageBo -> coverageBo.getCoverageId().equals(coverageRes.getCoverageId())).findFirst().ifPresent(coverageBo -> {
                                    //录入复核受理投保单险种信息
                                    transferApplyCoverageList(applyBo.getSalesBranchId(), coverageFields, dutyFields, coverageRes, coverageBo);
                                });
                            });
                        });

            } else {
                //为空造险种 :从受理表中取险种
                List<ApplyCoverageResponse> listCoverageResponse = new ArrayList<>();
                applyBo.getListApplyCoverageAcceptBo().stream().forEach(coverageBo -> {
                    //转换受理投保单险种信息
                    transferApplyAcceptCoverages(applyBo, acceptCoverageFields, listCoverageResponse, coverageBo);
                });
                //放入别保险人中
                insuredRes.setListCoverage(listCoverageResponse);
            }
        });
    }

    /**
     * 转换受理投保单信息
     *
     * @param applyBo              　投保单对象
     * @param acceptCoverageFields 　受理险种字段
     * @param listCoverageResponse 　受理险种集合
     * @param coverageBo           　受理险种对象
     */
    private void transferApplyAcceptCoverages(ApplyBo applyBo, List<Field> acceptCoverageFields, List<ApplyCoverageResponse> listCoverageResponse, ApplyCoverageAcceptBo coverageBo) {
        //新建对象
        ApplyCoverageResponse applyCoverageResponse = new ApplyCoverageResponse();
        applyCoverageResponse.setProductId(coverageBo.getProductId());
        applyCoverageResponse.setProductCode(coverageBo.getProductCode());

        //查询产品信息
        ProductDetailedInfoResponse productDetailedInfoResponse = productApi.getProductDetailInfo(coverageBo.getProductId(), applyBo.getSalesBranchId()).getData();
        AssertUtils.isNotNull(getLogger(), productDetailedInfoResponse, ApplyErrorConfigEnum.APPLY_ACCEPT_PRODUCT_ERROR);
        applyCoverageResponse.setPrimaryFlag(productDetailedInfoResponse.getMainProductFlag());
        //产品字段集合
        List<ApplyProductFieldResponse> applyProductFieldResponses = new ArrayList<>();
        //添加产品基本字段信息
        addBaseProductField(productDetailedInfoResponse, applyProductFieldResponses);
        //险种参数
        if (AssertUtils.isNotEmpty(productDetailedInfoResponse.getParameterFields())) {
            productDetailedInfoResponse.getParameterFields().forEach(parameterFieldRespFc -> {
                transferAcceptCoverageFields(acceptCoverageFields, coverageBo, parameterFieldRespFc, applyProductFieldResponses);
            });
        }
        //险种责任参数
        if (AssertUtils.isNotEmpty(productDetailedInfoResponse.getDutys())) {
            //循环责任
            productDetailedInfoResponse.getDutys()
                    .forEach(dutyRespFc -> {
                        //全部添加
                        dutyRespFc.getParameterFields()
                                .stream()
                                .forEach(parameterFieldRespFc -> {
                                    ApplyProductFieldResponse applyProductFieldResponse4 =
                                            new ApplyProductFieldResponse(
                                                    dutyRespFc.getDutyName() + parameterFieldRespFc.getFieldLabel(),
                                                    parameterFieldRespFc.getFieldName(),
                                                    parameterFieldRespFc.getInputFlag(),
                                                    parameterFieldRespFc.getControlType(),
                                                    dutyRespFc.getDutyId());
                                    applyProductFieldResponses.add(applyProductFieldResponse4);
                                });
                    });
        }
        //添加产品固定字段
        addFixedAcceptProductFields(coverageBo, applyProductFieldResponses);
        //字段排序
        Collections.sort(applyProductFieldResponses, new Comparator<ApplyProductFieldResponse>() {
            @Override
            public int compare(ApplyProductFieldResponse o1, ApplyProductFieldResponse o2) {
                return o1.getIndex() - o2.getIndex();
            }
        });
        applyCoverageResponse.setListProductFields(applyProductFieldResponses);
        applyCoverageResponse.setProductDetailedInfoRespFc(productDetailedInfoResponse);
        listCoverageResponse.add(applyCoverageResponse);
    }

    /**
     * 转换投保单对象集合
     *
     * @param saleBranchId   销售机构
     * @param coverageFields 　险种字段
     * @param dutyFields     　责任字段
     * @param coverageRes    　返回险种对象
     * @param coverageBo     　业务险种对象
     */
    public void transferApplyCoverageList(String saleBranchId, List<Field> coverageFields, List<Field> dutyFields, ApplyCoverageResponse coverageRes, ApplyCoverageBo coverageBo) {

        listCoverageTitle = platformInternationalBaseApi.queryInternational(InternationalTypeEnum.COVERAGE_TITLE.name(), null).getData();
        //查询产品信息
        ProductDetailedInfoResponse productDetailedInfoResponse = productApi.getProductDetailInfo(coverageBo.getProductId(), saleBranchId).getData();
        AssertUtils.isNotNull(getLogger(), productDetailedInfoResponse, ApplyErrorConfigEnum.APPLY_ACCEPT_PRODUCT_ERROR);
        coverageRes.setPrimaryFlag(productDetailedInfoResponse.getMainProductFlag());
        //产品字段集合
        List<ApplyProductFieldResponse> applyProductFieldResponses = new ArrayList<>();
        //添加基础的险种字段
        addBaseProductField(productDetailedInfoResponse, applyProductFieldResponses);
        //险种参数
        if (AssertUtils.isNotEmpty(productDetailedInfoResponse.getParameterFields())) {

            productDetailedInfoResponse.getParameterFields().forEach(parameterFieldRespFc -> {
                transferCoverageFields(coverageFields, coverageBo, parameterFieldRespFc, applyProductFieldResponses);
            });
        }

        if (TerminologyConfigEnum.WHETHER.YES.name().equals(coverageBo.getDutyChooseFlag())) {
            String coveragePlan = "选项";
            Optional<SyscodeResponse> optionalCoveragePlan = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_PLAN")).findFirst();
            if (optionalCoveragePlan.isPresent()) {
                coveragePlan = optionalCoveragePlan.get().getCodeName();
            }
            String coveragePlanValue = coverageBo.getListCoverageDuty().get(0).getDutyName();
            SyscodeResponse dutySyscodeRespFc = platformInternationalBaseApi.queryOneInternational("DUTY", coverageBo.getDutyId(), null).getData();
            if (AssertUtils.isNotNull(dutySyscodeRespFc)) {
                coveragePlanValue = dutySyscodeRespFc.getCodeName();
            }
            // 11号产品
            ApplyProductFieldResponse applyProductFieldResponse =
                    new ApplyProductFieldResponse(
                            coveragePlan, "coveragePlan", "DEFAULT", "P", null, coveragePlanValue
                    );
            applyProductFieldResponse.setIndex(130);
            applyProductFieldResponses.add(applyProductFieldResponse);

            // 动态字段
            if (AssertUtils.isNotEmpty(productDetailedInfoResponse.getDutys())) {
                productDetailedInfoResponse.getDutys().forEach(dutyResponse -> {
                    dutyResponse.getParameterFields().forEach(parameterField -> {
                        String value = clazzBusinessService.getFieldValueByName(parameterField.getFieldName(), coverageBo) + "";
                        SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational("PRODUCT_" + parameterField.getFieldCode(), value, null).getData();

                        String fieldName;
                        if (AssertUtils.isNotNull(syscodeRespFc)) {
                            //需要国际化
                            fieldName = syscodeRespFc.getCodeName();
                        } else {
                            fieldName = value;
                        }
                        final Optional<ApplyProductFieldResponse> optional = applyProductFieldResponses.stream()
                                .filter(productFieldResponse -> parameterField.getFieldName().equals(productFieldResponse.getFieldName()))
                                .findFirst();
                        if (optional.isPresent()) {
                            optional.ifPresent(productFieldResponse -> {
                                productFieldResponse.setParameterName(fieldName);
                                productFieldResponse.setParameterValue(value);
                            });
                        } else {
                            ApplyProductFieldResponse productFieldResponse = new ApplyProductFieldResponse(
                                    parameterField.getFieldLabel(),
                                    parameterField.getFieldName(),
                                    parameterField.getInputFlag(),
                                    parameterField.getControlType(),
                                    dutyResponse.getDutyId(),
                                    fieldName
                            );
                            productFieldResponse.setIndex(applyProductFieldResponses.size() + 130);
                            applyProductFieldResponses.add(productFieldResponse);
                        }
                    });
                });
            }
        }

        //险种责任参数
        else if (AssertUtils.isNotEmpty(productDetailedInfoResponse.getDutys())) {
            //循环责任
            productDetailedInfoResponse.getDutys()
                    .forEach(dutyResponse -> {
                        if (AssertUtils.isNotEmpty(coverageBo.getListCoverageDuty())) {
                            //判断责任是否匹配上
                            coverageBo.getListCoverageDuty()
                                    .stream()
                                    .filter(dutyBo -> dutyBo.getDutyId().equals(dutyResponse.getDutyId()))
                                    .forEach(dutyBo -> {
                                        //循环责任字段
                                        transferDutyFields(dutyFields, dutyResponse, dutyBo, applyProductFieldResponses);
                                    });
                        } else {
                            //全部添加
                            dutyResponse.getParameterFields()
                                    .stream()
                                    .forEach(parameterFieldRespFc -> {
                                        ApplyProductFieldResponse applyProductFieldResponse4 =
                                                new ApplyProductFieldResponse(
                                                        dutyResponse.getDutyName() + parameterFieldRespFc.getFieldLabel(),
                                                        parameterFieldRespFc.getFieldName(),
                                                        parameterFieldRespFc.getInputFlag(),
                                                        parameterFieldRespFc.getControlType(),
                                                        dutyResponse.getDutyId());
                                        applyProductFieldResponses.add(applyProductFieldResponse4);
                                    });
                        }
                    });
        }
        //添加固定的产品字段
        addFixedProductFields(coverageBo, applyProductFieldResponses, true);

        //字段排序
        Collections.sort(applyProductFieldResponses, new Comparator<ApplyProductFieldResponse>() {
            @Override
            public int compare(ApplyProductFieldResponse o1, ApplyProductFieldResponse o2) {
                return o1.getIndex() - o2.getIndex();
            }
        });
        coverageRes.setListProductFields(applyProductFieldResponses);
        coverageRes.setProductDetailedInfoRespFc(productDetailedInfoResponse);
    }


    /**
     * 添加险种固定字段
     *
     * @param coverageBo                 　险种对象
     * @param applyProductFieldResponses 　产品字段集合
     */
    private void addFixedAcceptProductFields(ApplyCoverageAcceptBo coverageBo, List<ApplyProductFieldResponse> applyProductFieldResponses) {
        /***
         * 判断险种字段中是否有
         * 1.(缴费周期(缴别)(年缴、月缴)，premiumFrequency
         * 2.缴费期限(具体数值，如缴至60岁))　premiumPeriodUnit
         * 3.缴费期限类型（年、周岁、终身）　　premiumPeriod
         * 4.保障年期类型（年、周岁、终身）　coveragePeriodUnit
         * 5.保障年期(具体数值，如缴至60岁)　coveragePeriod
         * 6.保额　totalPremium
         * 7.保费　amount
         */
        //premiumFrequency
        ApplyProductFieldResponse premiumFrequency = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "premiumFrequency".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse("缴费周期", "premiumFrequency", "DEFAULT", "P", ""));
        premiumFrequency.setFieldCode("PREMIUM_FREQUENCY");
        if (!applyProductFieldResponses.contains(premiumFrequency)) {
            if (AssertUtils.isNotEmpty(coverageBo.getPremiumFrequency())) {
                SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(), coverageBo.getPremiumFrequency(), null).getData();
                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    premiumFrequency.setFieldParam(syscodeRespFc.getCodeName(), null, coverageBo.getPremiumFrequency());
                } else {
                    premiumFrequency.setFieldParam(coverageBo.getPremiumFrequency(), null, coverageBo.getPremiumFrequency());
                }
            }
            applyProductFieldResponses.add(premiumFrequency);
        }
        premiumFrequency.setIndex(105);

        //缴费期限
        ApplyProductFieldResponse premiumPeriod = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "premiumPeriod".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse("缴费期限", "premiumPeriod", "DEFAULT", "P", ""));
        premiumPeriod.setFieldCode("PREMIUM_PERIOD");
        if (!applyProductFieldResponses.contains(premiumPeriod)) {
            if (AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnit()) && AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod())) {
                //需要国际化
                SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(), coverageBo.getPremiumPeriodUnit(), null).getData();
                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    //一次性缴费做特殊处理
                    if (ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(coverageBo.getPremiumPeriodUnit())) {
                        //缴费期限为趸交时,name显示为--
                        premiumPeriod.setFieldParam("--", coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    } else {
                        premiumPeriod.setFieldParam(coverageBo.getPremiumPeriod() + syscodeRespFc.getCodeName(), coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    }
                } else {
                    if (ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(coverageBo.getPremiumPeriodUnit())) {
                        premiumPeriod.setFieldParam("--", coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    } else {
                        premiumPeriod.setFieldParam(coverageBo.getPremiumPeriod() + coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    }
                }
            } else {
                premiumPeriod.setFieldParam(coverageBo.getPremiumPeriod(), null, coverageBo.getPremiumPeriod());
            }
            applyProductFieldResponses.add(premiumPeriod);
        }
        premiumPeriod.setIndex(104);


        //保障年期
        ApplyProductFieldResponse coveragePeriod = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "coveragePeriod".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse("保险期间", "coveragePeriod", "DEFAULT", "P", ""));
        coveragePeriod.setFieldCode("COVERAGE_PERIOD");
        if (!applyProductFieldResponses.contains(coveragePeriod)) {
            if (AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnit()) && AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod())) {
                //需要国际化
                SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), coverageBo.getCoveragePeriodUnit(), null).getData();
                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    coveragePeriod.setFieldParam(coverageBo.getCoveragePeriod() + syscodeRespFc.getCodeName(), coverageBo.getCoveragePeriodUnit(), coverageBo.getCoveragePeriod());
                } else {
                    coveragePeriod.setFieldParam(coverageBo.getCoveragePeriod() + coverageBo.getCoveragePeriodUnit(), coverageBo.getCoveragePeriodUnit(), coverageBo.getCoveragePeriod());
                }
            } else {
                coveragePeriod.setFieldParam(coverageBo.getCoveragePeriod(), null, coverageBo.getCoveragePeriod());
            }
            applyProductFieldResponses.add(coveragePeriod);
        }
        coveragePeriod.setIndex(106);

        //amount
        ApplyProductFieldResponse amount = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "amount".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse("保额", "amount", "DEFAULT", "P", ""));
        amount.setFieldCode("AMOUNT");
        if (!applyProductFieldResponses.contains(amount)) {
            amount.setFieldParam("", null, "");
            applyProductFieldResponses.add(amount);
        }
        amount.setIndex(107);

        //mult
        ApplyProductFieldResponse multField = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "mult".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse("份数", "mult", "DEFAULT", "P", ""));
        multField.setFieldCode("MULT");
        if (!applyProductFieldResponses.contains(multField)) {
            long mult = 1;
            multField.setFieldParam(mult + "", null, mult + "");
            applyProductFieldResponses.add(multField);
        }
        multField.setIndex(108);

        //totalPremium
        ApplyProductFieldResponse totalPremium = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "totalPremium".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse("总保费", "totalPremium", "DEFAULT", "P", ""));
        totalPremium.setFieldCode("PREMIUM");
        if (!applyProductFieldResponses.contains(totalPremium)) {
            if (AssertUtils.isNotNull(coverageBo.getTotalPremium())) {
                totalPremium.setFieldParam(coverageBo.getTotalPremium() + "", null, coverageBo.getTotalPremium() + "");
            }
            applyProductFieldResponses.add(totalPremium);
        }
        totalPremium.setIndex(109);

    }

    /**
     * 添加险种固定字段
     *
     * @param coverageBo                 　险种对象
     * @param applyProductFieldResponses 　产品字段集合
     * @param notUnderWritingFlag
     */
    private void addFixedProductFields(ApplyCoverageBo coverageBo, List<ApplyProductFieldResponse> applyProductFieldResponses, boolean notUnderWritingFlag) {
        /***
         * 判断险种字段中是否有
         * 1.(缴费周期(缴别)(年缴、月缴)，premiumFrequency
         * 2.缴费期限(具体数值，如缴至60岁))　premiumPeriodUnit
         * 3.缴费期限类型（年、周岁、终身）　　premiumPeriod
         * 4.保障年期类型（年、周岁、终身）　coveragePeriodUnit
         * 5.保障年期(具体数值，如缴至60岁)　coveragePeriod
         * 6.保额　totalPremium
         * 7.保费　amount
         */
        //premiumFrequency
        String premiumFrequencyName = "缴费周期";
        Optional<SyscodeResponse> optionalPremiumFrequency = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("PREMIUM_FREQUENCY")).findFirst();
        if (optionalPremiumFrequency.isPresent()) {
            premiumFrequencyName = optionalPremiumFrequency.get().getCodeName();
        }
        ApplyProductFieldResponse premiumFrequency = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "premiumFrequency".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse(premiumFrequencyName, "premiumFrequency", "DEFAULT", "P", ""));
        premiumFrequency.setFieldCode("PREMIUM_FREQUENCY");
        if (AssertUtils.isNotEmpty(coverageBo.getPremiumFrequency())) {
            SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_FREQUENCY.name(), coverageBo.getPremiumFrequency(), null).getData();
            if (AssertUtils.isNotNull(syscodeRespFc)) {
                premiumFrequency.setFieldParam(syscodeRespFc.getCodeName(), null, coverageBo.getPremiumFrequency());
            } else {
                premiumFrequency.setFieldParam(coverageBo.getPremiumFrequency(), null, coverageBo.getPremiumFrequency());
            }
        }
        premiumFrequency.setIndex(105);

        //缴费期限
        String premiumPeriodName = "缴费期限";
        Optional<SyscodeResponse> optionalPremiumPeriod = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("PREMIUM_PERIOD")).findFirst();
        if (optionalPremiumPeriod.isPresent()) {
            premiumPeriodName = optionalPremiumPeriod.get().getCodeName();
        }
        ApplyProductFieldResponse premiumPeriod = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "premiumPeriod".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse(premiumPeriodName, "premiumPeriod", "DEFAULT", "P", ""));
        premiumPeriod.setFieldCode("PREMIUM_PERIOD");
        if (!applyProductFieldResponses.contains(premiumPeriod)) {
            if (AssertUtils.isNotEmpty(coverageBo.getPremiumPeriodUnit()) && AssertUtils.isNotEmpty(coverageBo.getPremiumPeriod())) {
                //需要国际化
                SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_PREMIUM_PERIOD_UNIT.name(), coverageBo.getPremiumPeriodUnit(), null).getData();

                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    //一次性缴费做特殊处理
                    if (ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(coverageBo.getPremiumPeriodUnit())) {
                        //缴费期限为趸交时,name显示为--
                        premiumPeriod.setFieldParam("--", coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    } else {
                        premiumPeriod.setFieldParam(coverageBo.getPremiumPeriod() + syscodeRespFc.getCodeName(), coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    }
                } else {
                    if (ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(coverageBo.getPremiumPeriodUnit())) {
                        premiumPeriod.setFieldParam("--", coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    } else {
                        premiumPeriod.setFieldParam(coverageBo.getPremiumPeriod() + coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriodUnit(), coverageBo.getPremiumPeriod());
                    }
                }
            } else {
                premiumPeriod.setFieldParam(coverageBo.getPremiumPeriod(), null, coverageBo.getPremiumPeriod());
            }
            applyProductFieldResponses.add(premiumPeriod);
        }
        premiumPeriod.setIndex(104);

        //保障年期
        String coveragePeriodName = "保险期间";
        Optional<SyscodeResponse> optionalCoveragePeriod = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_PERIOD")).findFirst();
        if (optionalCoveragePeriod.isPresent()) {
            coveragePeriodName = optionalCoveragePeriod.get().getCodeName();
        }
        ApplyProductFieldResponse coveragePeriod = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "coveragePeriod".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse(coveragePeriodName, "coveragePeriod", "DEFAULT", "P", ""));
        coveragePeriod.setFieldCode("COVERAGE_PERIOD");
        if (!applyProductFieldResponses.contains(coveragePeriod)) {
            if (AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnit()) && AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod())) {
                //需要国际化
                SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational(TerminologyTypeEnum.PRODUCT_COVERAGE_PERIOD_UNIT.name(), coverageBo.getCoveragePeriodUnit(), null).getData();
                if (AssertUtils.isNotNull(syscodeRespFc)) {
                    coveragePeriod.setFieldParam(coverageBo.getCoveragePeriod() + syscodeRespFc.getCodeName(), coverageBo.getCoveragePeriodUnit(), coverageBo.getCoveragePeriod());
                } else {
                    coveragePeriod.setFieldParam(coverageBo.getCoveragePeriod() + coverageBo.getCoveragePeriodUnit(), coverageBo.getCoveragePeriodUnit(), coverageBo.getCoveragePeriod());
                }
            } else {
                coveragePeriod.setFieldParam(coverageBo.getCoveragePeriod(), null, coverageBo.getCoveragePeriod());
            }
            applyProductFieldResponses.add(coveragePeriod);
        }
        coveragePeriod.setIndex(106);

        //amount
        String amountName = "保险金额";
        Optional<SyscodeResponse> optionalAmount = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("AMOUNT")).findFirst();
        if (optionalAmount.isPresent()) {
            amountName = optionalAmount.get().getCodeName();
        }
        ApplyProductFieldResponse amount = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "amount".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse(amountName, "amount", "DEFAULT", "P", "", coverageBo.getAmount()));
        amount.setFieldCode("AMOUNT");
        if (!applyProductFieldResponses.contains(amount)) {
            amount.setFieldParam(coverageBo.getAmount(), null, coverageBo.getAmount());
            applyProductFieldResponses.add(amount);
        }
        if (AssertUtils.isNotNull(amount) && Arrays.asList(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()).contains(coverageBo.getProductId()) && AssertUtils.isNotNull(coverageBo.getBaseAmount()) && notUnderWritingFlag) {
            amount.setParameterName(coverageBo.getBaseAmount().toString());
            amount.setParameterValue(coverageBo.getBaseAmount().toString());
        }
        amount.setIndex(107);

        //financingMethod
        String financingMethodName = "加上保费";
        Optional<SyscodeResponse> optionalFinancingMethod = this.listCoverageTitle.stream().filter(syscodeResponse -> "FINANCING_METHOD".equals(syscodeResponse.getCodeKey())).findFirst();
        if (optionalFinancingMethod.isPresent()) {
            financingMethodName = optionalFinancingMethod.get().getCodeName();
        }
        Optional<ApplyProductFieldResponse> first = applyProductFieldResponses.stream().filter(applyProductFieldResponse -> "financingMethod".equals(applyProductFieldResponse.getFieldName())).findFirst();
        if (first.isPresent()) {
            SyscodeResponse financingMethod = platformInternationalBaseApi.queryOneInternational("PRODUCT_FINANCING_METHOD", coverageBo.getFinancingMethod(), null).getData();
            String parameterName = AssertUtils.isNotNull(financingMethod) ? financingMethod.getCodeName() : coverageBo.getFinancingMethod();
            ApplyProductFieldResponse applyProductFieldResponse = first.get();
            applyProductFieldResponse.setFieldCode("FINANCING_METHOD");
            applyProductFieldResponse.setFieldLabel(financingMethodName);
            applyProductFieldResponse.setFieldParam(parameterName, null, coverageBo.getFinancingMethod());
            applyProductFieldResponse.setIndex(108);
        }

        //mult
        String multName = "份数";
        Optional<SyscodeResponse> optionalMult = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("MULT")).findFirst();
        if (optionalMult.isPresent()) {
            multName = optionalMult.get().getCodeName();
        }
        ApplyProductFieldResponse multField = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "mult".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse(multName, "mult", "DEFAULT", "P", "", coverageBo.getMult()));
        multField.setFieldCode("MULT");
        if (!applyProductFieldResponses.contains(multField)) {
            long mult = 1;
            if (AssertUtils.isNotNull(coverageBo.getMult())) {
                mult = Long.parseLong(coverageBo.getMult());
            }
            multField.setFieldParam(mult + "", null, mult + "");
            applyProductFieldResponses.add(multField);
        }
        multField.setIndex(108);

        String accSiMultipleName = "额外意外保额倍数";
        Optional<SyscodeResponse> optionalAccSiMultipleFirst = this.listCoverageTitle.stream().filter(syscodeResponse -> "ACC_SI_MULTIPLE".equals(syscodeResponse.getCodeKey())).findFirst();
        if (optionalAccSiMultipleFirst.isPresent()) {
            accSiMultipleName = optionalAccSiMultipleFirst.get().getCodeName();
        }
        Optional<ApplyProductFieldResponse> accSiMultipleFirst = applyProductFieldResponses.stream().filter(applyProductFieldResponse -> "accSiMultiple".equals(applyProductFieldResponse.getFieldName())).findFirst();
        if (accSiMultipleFirst.isPresent()) {
            ApplyProductFieldResponse applyProductFieldResponse = accSiMultipleFirst.get();
            applyProductFieldResponse.setFieldCode("ACC_SI_MULTIPLE");
            applyProductFieldResponse.setFieldLabel(accSiMultipleName);
            applyProductFieldResponse.setFieldParam(coverageBo.getAccSiMultiple() + "", null, coverageBo.getAccSiMultiple() + "");
            applyProductFieldResponse.setIndex(109);
        }

        //totalPremium
        String premiumName = "期缴保险费";
        Optional<SyscodeResponse> optionalPremium = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("PREMIUM")).findFirst();
        if (optionalPremium.isPresent()) {
            premiumName = optionalPremium.get().getCodeName();
        }
        ApplyProductFieldResponse totalPremium = applyProductFieldResponses.stream()
                .filter(applyProductFieldResponse -> "totalPremium".equals(applyProductFieldResponse.getFieldName()))
                .findFirst()
                .orElse(new ApplyProductFieldResponse(premiumName, "totalPremium", "DEFAULT", "P", ""));
        totalPremium.setFieldCode("PREMIUM");
        if (!applyProductFieldResponses.contains(totalPremium)) {
            totalPremium.setFieldParam(coverageBo.getTotalPremium() + "", null, coverageBo.getTotalPremium() + "");
            applyProductFieldResponses.add(totalPremium);
        }
        totalPremium.setIndex(110);
    }


    /**
     * 转换投保单对象集合
     *
     * @param saleBranchId 销售机构
     * @param coverageRes  　险种返回对象
     * @param coverageBo   　业务险种对象
     */
    public void transferUnderWritingApplyCoverageList(String saleBranchId, UnderWriteCoverageResponse coverageRes, ApplyCoverageBo coverageBo) {
        //获取险种字段属性
        List<Field> coverageFields = clazzBusinessService.getFiledList(ApplyCoveragePo.class);
        List<Field> dutyFields = clazzBusinessService.getFiledList(ApplyCoverageDutyPo.class);
        listCoverageTitle = platformInternationalBaseApi.queryInternational(InternationalTypeEnum.COVERAGE_TITLE.name(), null).getData();

        //查询产品信息
        ProductDetailedInfoResponse productDetailedInfoResponse = productApi.getProductDetailInfo(coverageBo.getProductId(), saleBranchId).getData();
        //产品字段集合
        List<ApplyProductFieldResponse> applyProductFieldResponses = new ArrayList<>();
        //添加基础的险种字段
        addBaseProductField(productDetailedInfoResponse, applyProductFieldResponses);
        //险种参数
        if (AssertUtils.isNotEmpty(productDetailedInfoResponse.getParameterFields())) {
            productDetailedInfoResponse.getParameterFields().forEach(parameterFieldResponse -> {
                transferCoverageFields(coverageFields, coverageBo, parameterFieldResponse, applyProductFieldResponses);
                //设置主险缴费周期变更标识
                transferPremiumFrequencyChoiceFlag(coverageRes, coverageBo, parameterFieldResponse);
            });
        }

        //险种责任参数
        if (AssertUtils.isNotEmpty(productDetailedInfoResponse.getDutys())) {
            //循环责任
            productDetailedInfoResponse.getDutys()
                    .forEach(dutyRespFc -> {
                        if (AssertUtils.isNotEmpty(coverageBo.getListCoverageDuty())) {
                            //判断责任是否匹配上
                            coverageBo.getListCoverageDuty()
                                    .stream()
                                    .filter(dutyBo -> dutyBo.getDutyId().equals(dutyRespFc.getDutyId()))
                                    .forEach(dutyBo -> {
                                        //循环责任字段
                                        transferDutyFields(dutyFields, dutyRespFc, dutyBo, applyProductFieldResponses);
                                    });
                        } else {
                            //全部添加
                            dutyRespFc.getParameterFields()
                                    .stream()
                                    .forEach(parameterFieldRespFc -> {
                                        ApplyProductFieldResponse applyProductFieldResponse4 =
                                                new ApplyProductFieldResponse(
                                                        dutyRespFc.getDutyName() + parameterFieldRespFc.getFieldLabel(),
                                                        parameterFieldRespFc.getFieldName(),
                                                        parameterFieldRespFc.getInputFlag(),
                                                        parameterFieldRespFc.getControlType(),
                                                        dutyRespFc.getDutyId());
                                        applyProductFieldResponses.add(applyProductFieldResponse4);
                                    });
                        }
                    });
        }

        //添加固定字段
        this.addFixedProductFields(coverageBo, applyProductFieldResponses, false);
        //字段排序
        Collections.sort(applyProductFieldResponses, new Comparator<ApplyProductFieldResponse>() {
            @Override
            public int compare(ApplyProductFieldResponse o1, ApplyProductFieldResponse o2) {
                return o1.getIndex() - o2.getIndex();
            }
        });
        coverageRes.setListProductFields(applyProductFieldResponses);
        //设置到返回数据
        coverageRes.setCoverageId(coverageBo.getCoverageId());
        coverageRes.setProductId(productDetailedInfoResponse.getProductId());
        coverageRes.setProductCode(productDetailedInfoResponse.getProductCode());
        coverageRes.setPrimaryFlag(productDetailedInfoResponse.getMainProductFlag());
        coverageRes.setProductName(productDetailedInfoResponse.getProductName());
    }

    private void transferPremiumFrequencyChoiceFlag(UnderWriteCoverageResponse underWriteCoverageResponse, ApplyCoverageBo applyCoverageBo, ParameterFieldResponse parameterFieldResponse) {
        if (!AssertUtils.isNotEmpty(applyCoverageBo.getPrimaryFlag())
                || !applyCoverageBo.getPrimaryFlag().equals(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())
                || !ApplyTermEnum.COVERAGE_TITLE.PREMIUM_FREQUENCY.name().equals(parameterFieldResponse.getFieldCode())
        ) {
            return;
        }
        List<ParameterValueResponse> parameterValueResponses = parameterFieldResponse.getParameterValues().stream()
                .filter(parameterValueResponse -> !parameterValueResponse.getParameterValue().equals(applyCoverageBo.getPremiumFrequency()))
                .collect(Collectors.toList());
        List<String> premiumFrequencyList = parameterFieldResponse.getParameterValues().stream().map(ParameterValueResponse::getParameterValue).distinct().collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(parameterValueResponses) && premiumFrequencyList.contains(applyCoverageBo.getPremiumFrequency())) {
            underWriteCoverageResponse.setPremiumFrequencyChoiceFlag(TerminologyConfigEnum.WHETHER.YES.name());
            underWriteCoverageResponse.setListPremiumFrequency(parameterFieldResponse.getParameterValues());
        }
    }

    /**
     * 转换责任字段
     *
     * @param dutyFields   　责任字段集合
     * @param dutyResponse 　产品责任对象
     * @param dutyBo       　投保单责任对象
     */
    private void transferDutyFields(List<Field> dutyFields, DutyResponse dutyResponse, ApplyCoverageDutyBo dutyBo, List<ApplyProductFieldResponse> applyProductFieldResponses) {
        //责任字段
        dutyResponse.getParameterFields()
                .stream()
                .forEach(parameterFieldRespFc -> {
                    //新建字段(设置字段的基本属性)
                    ApplyProductFieldResponse applyProductFieldResponse4 =
                            new ApplyProductFieldResponse(
                                    dutyResponse.getDutyName() + parameterFieldRespFc.getFieldLabel(),
                                    parameterFieldRespFc.getFieldName(),
                                    parameterFieldRespFc.getInputFlag(),
                                    parameterFieldRespFc.getControlType(),
                                    dutyResponse.getDutyId());
                    //字段相同
                    dutyFields.stream()
                            .filter(dutyField -> dutyField.getName().equals(parameterFieldRespFc.getFieldName()))
                            .forEach(dutyField -> {
                                String value = null;
                                if (AssertUtils.isNotNull(clazzBusinessService.getFieldValueByName(dutyField.getName(), dutyBo))) {
                                    value = clazzBusinessService.getFieldValueByName(dutyField.getName(), dutyBo) + "";
                                }
                                if (AssertUtils.isNotEmpty(value)) {
                                    //选择框(单位)
                                    if (ApplyTermEnum.PRODUCT_FIELD_INPUT.SELECT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        if (AssertUtils.isNotEmpty(parameterFieldRespFc.getParameterValues().get(0).getParameterUnit())) {
                                            String valueUnit = clazzBusinessService.getFieldValueByName(dutyField.getName() + "Unit", dutyResponse) + "";
                                            //获取相应的参数值
                                            transferParameterSelect(parameterFieldRespFc, applyProductFieldResponse4, value, valueUnit);
                                        } else {
                                            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                                        }

                                    }
                                    //默认框
                                    if (ApplyTermEnum.PRODUCT_FIELD_INPUT.DEFAULT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        //获取相应的参数值
                                        transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                                    }
                                    //输入框,
                                    if (ApplyTermEnum.PRODUCT_FIELD_INPUT.INPUT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        if (AssertUtils.isNotNull(parameterFieldRespFc.getUnitObj())) {
                                            //TODO:未处理
                                        } else {
                                            applyProductFieldResponse4.setFieldParam(value, null, value);
                                        }

                                    }
                                    //不录入
                                    if (ApplyTermEnum.PRODUCT_FIELD_INPUT.NOT.name().equals(parameterFieldRespFc.getInputFlag())) {
                                        applyProductFieldResponse4.setFieldParam(value, null, value);
                                    }
                                }
                            });
                    applyProductFieldResponses.add(applyProductFieldResponse4);
                });
    }


    /**
     * 受理转换险种字段参数
     *
     * @param coverageFields       　险种字段集合
     * @param coverageBo           　投保单险种对象
     * @param parameterFieldRespFc 　产品参数字段对象
     */
    private void transferAcceptCoverageFields(List<Field> coverageFields, ApplyCoverageAcceptBo coverageBo, ParameterFieldResponse parameterFieldRespFc, List<ApplyProductFieldResponse> applyProductFieldResponses) {
        //新建字段(设置字段的基本属性)
        ApplyProductFieldResponse applyProductFieldResponse4 =
                new ApplyProductFieldResponse(
                        parameterFieldRespFc.getFieldLabel(),
                        parameterFieldRespFc.getFieldName(),
                        parameterFieldRespFc.getInputFlag(),
                        parameterFieldRespFc.getControlType(),
                        null);
        applyProductFieldResponse4.setIndex(applyProductFieldResponses.size() + 120);
        //字段相同
        coverageFields.stream()
                .filter(coveragefield -> coveragefield.getName().equals(parameterFieldRespFc.getFieldName()))
                .forEach(coverageField -> {
                    String value = null;
                    if (AssertUtils.isNotNull(clazzBusinessService.getFieldValueByName(coverageField.getName(), coverageBo))) {
                        value = clazzBusinessService.getFieldValueByName(coverageField.getName(), coverageBo) + "";
                    }
                    String unit = null;
                    if (AssertUtils.isNotNull(clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo))) {
                        unit = clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo) + "";
                    }
                    if (AssertUtils.isNotEmpty(value)) {
                        //选择框(单位)
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.SELECT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            if (AssertUtils.isNotEmpty(parameterFieldRespFc.getParameterValues().get(0).getParameterUnit())) {
                                String valueUnit = clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo) + "";
                                //获取相应的参数值
                                transferParameterSelect(parameterFieldRespFc, applyProductFieldResponse4, value, valueUnit);
                            } else {
                                transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                            }
                        }
                        //默认框
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.DEFAULT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            //获取相应的参数值
                            transferParameterDefault(parameterFieldRespFc, applyProductFieldResponse4, value);
                        }
                        //输入框
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.INPUT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            //判断是否有单位
                            if (AssertUtils.isNotNull(parameterFieldRespFc.getUnitObj())) {
                                //TODO:未处理
                            } else {
                                String fieldName = value;
                                String unitName = unit;
                                if (ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(unit)) {
                                    fieldName = "--";
                                }
                                if (AssertUtils.isNotNull(value) && AssertUtils.isNotNull(unit) && !ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(unit)) {
                                    //需要国际化
                                    SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational("PRODUCT_" + parameterFieldRespFc.getFieldCode() + "_UNIT", unit, null).getData();
                                    if (AssertUtils.isNotNull(syscodeRespFc)) {
                                        fieldName = value + syscodeRespFc.getCodeName();
                                        unitName = syscodeRespFc.getCodeName();
                                    } else {
                                        fieldName = value + unit;
                                    }
                                    applyProductFieldResponse4.setParameterUnitName(unitName);
                                }
                                applyProductFieldResponse4.setFieldParam(fieldName, unit, value);
                            }
                        }
                        //不录入
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.NOT.name().equals(parameterFieldRespFc.getInputFlag())) {
                            applyProductFieldResponse4.setFieldParam(value, null, value);
                        }

                    }
                });
        applyProductFieldResponses.add(applyProductFieldResponse4);
    }


    /**
     * 转换险种字段参数
     *
     * @param coverageFields         　险种字段集合
     * @param coverageBo             　投保单险种对象
     * @param parameterFieldResponse 　产品参数字段对象
     */
    private void transferCoverageFields(List<Field> coverageFields, ApplyCoverageBo coverageBo, ParameterFieldResponse parameterFieldResponse, List<ApplyProductFieldResponse> applyProductFieldResponses) {
        //新建字段(设置字段的基本属性)
        ApplyProductFieldResponse applyProductFieldResponse4 =
                new ApplyProductFieldResponse(
                        parameterFieldResponse.getFieldLabel(),
                        parameterFieldResponse.getFieldName(),
                        parameterFieldResponse.getInputFlag(),
                        parameterFieldResponse.getControlType(),
                        null);
        applyProductFieldResponse4.setIndex(applyProductFieldResponses.size() + 120);
        //动态字段相同
        coverageFields.stream()
                .filter(coveragefield -> coveragefield.getName().equals(parameterFieldResponse.getFieldName()))
                .forEach(coverageField -> {
                    String value = null;
                    String unit = null;
                    if (AssertUtils.isNotNull(clazzBusinessService.getFieldValueByName(coverageField.getName(), coverageBo))) {
                        value = clazzBusinessService.getFieldValueByName(coverageField.getName(), coverageBo) + "";
                    }
                    if (AssertUtils.isNotNull(clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo))) {
                        unit = clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo) + "";
                    }


                    if (AssertUtils.isNotEmpty(value)) {
                        //选择框(单位)
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.SELECT.name().equals(parameterFieldResponse.getInputFlag())) {
                            //判断单位是否为空
                            if (AssertUtils.isNotEmpty(parameterFieldResponse.getParameterValues().get(0).getParameterUnit())) {
                                String valueUnit = clazzBusinessService.getFieldValueByName(coverageField.getName() + "Unit", coverageBo) + "";
                                transferParameterSelect(parameterFieldResponse, applyProductFieldResponse4, value, valueUnit);
                            } else {
                                transferParameterDefault(parameterFieldResponse, applyProductFieldResponse4, value);
                            }
                        }
                        //默认框,
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.DEFAULT.name().equals(parameterFieldResponse.getInputFlag())) {
                            //获取相应的参数值
                            transferParameterDefault(parameterFieldResponse, applyProductFieldResponse4, value);
                        }
                        //输入框,
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.INPUT.name().equals(parameterFieldResponse.getInputFlag())) {
                            //判断是否有单位
                            if (AssertUtils.isNotNull(parameterFieldResponse.getUnitObj())) {
                                //TODO:未处理
                            } else {
                                String fieldName = value;
                                String unitName = unit;
                                if (ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(unit)) {
                                    fieldName = "--";
                                }
                                if (AssertUtils.isNotNull(value) && AssertUtils.isNotNull(unit) && !ApplyTermEnum.PRODUCT_PREMIUM_PERIOD_UNIT.SINGLE.name().equals(unit)) {
                                    //需要国际化
                                    SyscodeRespFc syscodeRespFc = platformBaseInternationServiceApi.queryOneInternational("PRODUCT_" + parameterFieldResponse.getFieldCode() + "_UNIT", unit, null).getData();
                                    if (AssertUtils.isNotNull(syscodeRespFc)) {
                                        fieldName = value + syscodeRespFc.getCodeName();
                                        unitName = syscodeRespFc.getCodeName();
                                    } else {
                                        fieldName = value + unit;
                                    }
                                    applyProductFieldResponse4.setParameterUnitName(unitName);
                                }
                                applyProductFieldResponse4.setFieldParam(fieldName, unit, value);
                            }
                        }
                        //不录入
                        if (ApplyTermEnum.PRODUCT_FIELD_INPUT.NOT.name().equals(parameterFieldResponse.getInputFlag())) {
                            applyProductFieldResponse4.setFieldParam(value, null, value);
                        }
                    }
                });

        applyProductFieldResponses.add(applyProductFieldResponse4);
    }


    /**
     * 匹配默框参数
     *
     * @param parameterFieldRespFc       字段对象
     * @param applyProductFieldResponse4 　字段返回对象
     * @param value                      　值
     */
    private void transferParameterDefault(ParameterFieldResponse parameterFieldRespFc, ApplyProductFieldResponse applyProductFieldResponse4, String value) {
        if ("amount".equals(parameterFieldRespFc.getFieldName()) || "totalPremium".equals(parameterFieldRespFc.getFieldName())) {
            parameterFieldRespFc
                    .getParameterValues()
                    .stream()
                    .filter(parameterValueRespFc -> Double.parseDouble(parameterValueRespFc.getParameterValue()) == Double.parseDouble(value))
                    .forEach(parameterValueRespFc -> {
                        applyProductFieldResponse4.setFieldParam(parameterValueRespFc.getParameterName(), parameterValueRespFc.getParameterUnit(), parameterValueRespFc.getParameterValue());
                    });
        } else {
            parameterFieldRespFc
                    .getParameterValues()
                    .stream()
                    .filter(parameterValueRespFc -> parameterValueRespFc.getParameterValue().equals(value))
                    .forEach(parameterValueRespFc -> {
                        applyProductFieldResponse4.setFieldParam(parameterValueRespFc.getParameterName(), parameterValueRespFc.getParameterUnit(), parameterValueRespFc.getParameterValue());
                    });
        }
    }


    /**
     * 　匹配选择框参数
     *
     * @param parameterFieldRespFc       　字段对象
     * @param applyProductFieldResponse4 　字段返回对象
     * @param value                      　值
     * @param valueUnit                  　单位
     */
    private void transferParameterSelect(ParameterFieldResponse parameterFieldRespFc, ApplyProductFieldResponse applyProductFieldResponse4, String value, String valueUnit) {
        //保费保额特殊处理
        if ("amount".equals(parameterFieldRespFc.getFieldName()) || "totalPremium".equals(parameterFieldRespFc.getFieldName())) {
            parameterFieldRespFc
                    .getParameterValues()
                    .stream()
                    .filter(parameterValueRespFc -> Double.parseDouble(parameterValueRespFc.getParameterValue()) == Double.parseDouble(value) && parameterValueRespFc.getParameterUnit().equals(valueUnit))
                    .forEach(parameterValueRespFc -> {
                        applyProductFieldResponse4.setFieldParam(parameterValueRespFc.getParameterName(), parameterValueRespFc.getParameterUnit(), parameterValueRespFc.getParameterValue());
                    });
        } else {
            parameterFieldRespFc
                    .getParameterValues()
                    .stream()
                    .filter(parameterValueRespFc -> parameterValueRespFc.getParameterValue().equals(value) && parameterValueRespFc.getParameterUnit().equals(valueUnit))
                    .forEach(parameterValueRespFc -> {
                        applyProductFieldResponse4.setFieldParam(parameterValueRespFc.getParameterName(), parameterValueRespFc.getParameterUnit(), parameterValueRespFc.getParameterValue());
                    });
        }
    }


    /**
     * 　匹配选择框录入框带参数
     *
     * @param parameterFieldRespFc       　字段对象
     * @param applyProductFieldResponse4 　字段返回对象
     * @param value                      　值
     * @param valueUnit                  　单位
     */
    private void transferParameterInputUnit(ParameterFieldResponse parameterFieldRespFc, ApplyProductFieldResponse applyProductFieldResponse4, String value, String valueUnit) {
        parameterFieldRespFc
                .getParameterValues()
                .stream()
                .filter(parameterValueRespFc -> parameterValueRespFc.getParameterValue().equals(value))
                .filter(parameterValueRespFc -> parameterValueRespFc.getParameterUnit().equals(valueUnit))
                .forEach(parameterValueRespFc -> {
                    applyProductFieldResponse4.setFieldParam(parameterValueRespFc.getParameterName(), parameterValueRespFc.getParameterUnit(), parameterValueRespFc.getParameterValue());
                });
    }


    /**
     * 添加险种的基础信息
     *
     * @param productDetailedInfoResponse 　产品详情
     * @param applyProductFieldResponses  　字段集合
     */
    private void addBaseProductField(ProductDetailedInfoResponse productDetailedInfoResponse, List<ApplyProductFieldResponse> applyProductFieldResponses) {
        String coverageName = "险种名称";
        Optional<SyscodeResponse> optionalCoverageName = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_NAME")).findFirst();
        if (optionalCoverageName.isPresent()) {
            coverageName = optionalCoverageName.get().getCodeName();
        }
        String coverageCode = "险种编号";
        Optional<SyscodeResponse> optionalCoverageCode = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_CODE")).findFirst();
        if (optionalCoverageCode.isPresent()) {
            coverageCode = optionalCoverageCode.get().getCodeName();
        }
        String coverageType = "险种类型";
        Optional<SyscodeResponse> optionalCoverageType = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("COVERAGE_TYPE")).findFirst();
        if (optionalCoverageType.isPresent()) {
            coverageType = optionalCoverageType.get().getCodeName();
        }
        String curency = "币种";
        Optional<SyscodeResponse> optionalCurency = this.listCoverageTitle.stream().filter(syscodeResponse -> syscodeResponse.getCodeKey().equals("CURRENCY")).findFirst();
        if (optionalCurency.isPresent()) {
            curency = optionalCurency.get().getCodeName();
        }
        //添加产品信息
        ApplyProductFieldResponse applyProductFieldResponse = new ApplyProductFieldResponse(coverageName, "coverageName", "DEFAULT", "P", null, productDetailedInfoResponse.getProductName());
        applyProductFieldResponse.setIndex(100);
        applyProductFieldResponse.setFieldCode("COVERAGE_NAME");
        applyProductFieldResponses.add(applyProductFieldResponse);
        // 产品编码
        ApplyProductFieldResponse applyProductFieldResponse1 = new ApplyProductFieldResponse(coverageCode, "coverageCode", "DEFAULT", "P", null, productDetailedInfoResponse.getProductCode());
        applyProductFieldResponse1.setIndex(101);
        applyProductFieldResponse1.setFieldCode("COVERAGE_CODE");
        applyProductFieldResponses.add(applyProductFieldResponse1);
        //险种类型
        ApplyProductFieldResponse applyProductFieldResponse2 = new ApplyProductFieldResponse(coverageType, null, "DEFAULT", "P", null, productDetailedInfoResponse.getMainProductFlagName());
        applyProductFieldResponse2.setIndex(102);
        applyProductFieldResponse2.setFieldCode("COVERAGE_TYPE");
        applyProductFieldResponses.add(applyProductFieldResponse2);
        //币种
        ApplyProductFieldResponse applyProductFieldResponse5 = new ApplyProductFieldResponse(curency, null, "DEFAULT", "P", null, productDetailedInfoResponse.getCurrencyName());
        applyProductFieldResponse5.setIndex(103);
        applyProductFieldResponse5.setFieldCode("CURRENCY");
        applyProductFieldResponses.add(applyProductFieldResponse5);
    }


    public List<ProductHealthNoticeResponse> transApplyApplyHealthNotice(AppApplyBo appApplyBo, String language) {
        List<ProductHealthNoticeResponse> productHealthNoticeResponses;
        //查询投保告知书
        List<ApplyHealthQuestionnaireAnswerBo> healthQuestionnaireBos = applyExtDao.loadHealthQuestionnaire(appApplyBo.getApplyId());
        //当健康告知存在时,用已有的健康告知
        if (AssertUtils.isNotEmpty(healthQuestionnaireBos)) {
            productHealthNoticeResponses = this.getProductHealthNoticeResponses(appApplyBo, language, healthQuestionnaireBos);
        } else {
            productHealthNoticeResponses = this.transApplyApplyHealthNoticeFromProduct(appApplyBo, language);
        }
        return productHealthNoticeResponses;
    }

    public List<ProductHealthNoticeResponse> getProductHealthNoticeResponses(AppApplyBo appApplyBo, String language, List<ApplyHealthQuestionnaireAnswerBo> healthQuestionnaireBos) {
        List<ProductHealthNoticeResponse> productHealthNoticeResponses;
        System.out.println("health appApplyBo:" + JSON.toJSONString(appApplyBo));
        com.gclife.product.model.request.calculate.ApplyRequest applyRequest1 = (com.gclife.product.model.request.calculate.ApplyRequest) converterObject(appApplyBo, com.gclife.product.model.request.calculate.ApplyRequest.class);
        ResultObject<List<HealthPaperResponse>> productHealthPaperGet = productHealthPaperApi.productHealthPaperGet(applyRequest1, language);
        //将数据库已有的健康告知按照产品给的顺序排序
        //在每个节点上设置值
        productHealthNoticeResponses = this.transferHealthTree(productHealthPaperGet.getData(), healthQuestionnaireBos);
        for (ApplyHealthQuestionnaireAnswerBo applyHealthQuestionnaireAnswerBo : healthQuestionnaireBos) {
            for (ProductHealthNoticeResponse productHealthNoticeResponse : productHealthNoticeResponses) {
                if (applyHealthQuestionnaireAnswerBo.getQuestionCode().equals(productHealthNoticeResponse.getQuestionCode())) {
                    productHealthNoticeResponse.setAnswer(applyHealthQuestionnaireAnswerBo.getAnswer());
                    productHealthNoticeResponse.setAnswerDesc(applyHealthQuestionnaireAnswerBo.getAnswerDesc());
                }
            }
        }
        return productHealthNoticeResponses;
    }

    /**
     * 数据转换，转换机构树
     *
     * @param healthPaperResponses   树机构所有机构集合
     * @param healthQuestionnaireBos 产品机构
     * @return List<BranchTreeDo>　机构树
     */
    public List<ProductHealthNoticeResponse> transferHealthTree(List<HealthPaperResponse> healthPaperResponses, List<ApplyHealthQuestionnaireAnswerBo> healthQuestionnaireBos) {
        List<ProductHealthNoticeResponse> productHealthNoticeResponses = new ArrayList<>();
        List<String> rootQuestionCodes = healthPaperResponses.stream().map(HealthPaperResponse::getQuestionCode).collect(Collectors.toList());

        for (HealthPaperResponse healthPaperResponse : healthPaperResponses) {
            List<String> recursionQuestionCodes = new ArrayList<>();
            //递归
            this.recursion(healthPaperResponse.getChilds(), rootQuestionCodes, recursionQuestionCodes, healthQuestionnaireBos);
            recursionQuestionCodes.add(healthPaperResponse.getQuestionCode());

            productHealthNoticeResponses.add((ProductHealthNoticeResponse) this.converterObject(healthPaperResponse, ProductHealthNoticeResponse.class));
            if (rootQuestionCodes.size() == recursionQuestionCodes.size()) {
                productHealthNoticeResponses = new ArrayList<>();
                productHealthNoticeResponses.add((ProductHealthNoticeResponse) this.converterObject(healthPaperResponse, ProductHealthNoticeResponse.class));
                return productHealthNoticeResponses;
            }
        }
        return productHealthNoticeResponses;
    }

    private void recursion(List<HealthPaperResponse> chileHealthPaperResponses, List<String> rootQuestionCodes, List<String> recursionQuestionCodes, List<ApplyHealthQuestionnaireAnswerBo> applyHealthQuestionnaireAnswerBos) {
        if (AssertUtils.isNotEmpty(chileHealthPaperResponses)) {
            for (HealthPaperResponse chileHealthPaperResponse : chileHealthPaperResponses) {
                Optional<ApplyHealthQuestionnaireAnswerBo> first1 = applyHealthQuestionnaireAnswerBos.stream().filter(applyHealthQuestionnaireAnswerBo -> applyHealthQuestionnaireAnswerBo.getQuestionCode().equals(chileHealthPaperResponse.getQuestionCode())).findFirst();
                if (first1.isPresent()) {
                    ApplyHealthQuestionnaireAnswerBo applyHealthQuestionnaireAnswerBo = first1.get();
                    chileHealthPaperResponse.setAnswer(applyHealthQuestionnaireAnswerBo.getAnswer());
                    chileHealthPaperResponse.setAnswerDesc(applyHealthQuestionnaireAnswerBo.getAnswerDesc());
                }

                Optional<String> first = rootQuestionCodes.stream().filter(s -> s.equals(chileHealthPaperResponse.getQuestionCode())).findFirst();
                if (first.isPresent()) {
                    recursionQuestionCodes.add(first.get());
                } else {
                    this.recursion(chileHealthPaperResponse.getChilds(), rootQuestionCodes, recursionQuestionCodes, applyHealthQuestionnaireAnswerBos);
                }
            }
        }
    }

    public List<ProductHealthNoticeResponse> transApplyApplyHealthNoticeFromProduct(AppApplyBo appApplyBo, String language) {
        List<ProductHealthNoticeResponse> productHealthNoticeResponses = new ArrayList<>();
        System.out.println(JSON.toJSON(appApplyBo));
        com.gclife.product.model.request.calculate.ApplyRequest applyRequest1 = (com.gclife.product.model.request.calculate.ApplyRequest) converterObject(appApplyBo, com.gclife.product.model.request.calculate.ApplyRequest.class);
        ResultObject<List<HealthPaperResponse>> productHealthPaperRespFcs = productHealthPaperApi.productHealthPaperGet(applyRequest1, language);
        if (!AssertUtils.isResultObjectListDataNull(productHealthPaperRespFcs)) {
            productHealthNoticeResponses = (List<ProductHealthNoticeResponse>) this.converterList(productHealthPaperRespFcs.getData(), new TypeToken<List<ProductHealthNoticeResponse>>() {
            }.getType());
        }
        return productHealthNoticeResponses;
    }

    /**
     * 拆分健康告知树结构
     *
     * @param answers
     * @param applyHealthQuestionnaireAnswerPos
     * @param applyId
     */
    public void splitHealthNoticeTreeStructure(List<ProductHealthNoticeResponse> answers, List<ApplyHealthQuestionnaireAnswerPo> applyHealthQuestionnaireAnswerPos, String applyId) {
        for (ProductHealthNoticeResponse tree : answers) {
            ApplyHealthQuestionnaireAnswerPo applyHealthQuestionnaireAnswerPo = new ApplyHealthQuestionnaireAnswerPo();
            ClazzUtils.copyPropertiesIgnoreNull(tree, applyHealthQuestionnaireAnswerPo);
            applyHealthQuestionnaireAnswerPo.setApplyId(applyId);
            applyHealthQuestionnaireAnswerPos.add(applyHealthQuestionnaireAnswerPo);
            if (AssertUtils.isNotEmpty(tree.getChilds())) {
                splitHealthNoticeTreeStructure(tree.getChilds(), applyHealthQuestionnaireAnswerPos, applyId);
            }
        }
    }

}
