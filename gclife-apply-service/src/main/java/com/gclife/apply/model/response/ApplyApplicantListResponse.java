package com.gclife.apply.model.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ApplyApplicantListResponse {
    @ApiModelProperty(value = "投保单id")
    private String applyId;

    @ApiModelProperty(value = "保单号")
    private String policyNo;

    @ApiModelProperty(value = "投保时间")
    private Long applyDate;

    @ApiModelProperty(value = "核保时间")
    private Long approveDate;

    @ApiModelProperty(value = "投保人姓名")
    private String name;

    @ApiModelProperty(value = "投保人生日")
    private Long  birthday;

    @ApiModelProperty(value = "投保人证件号码")
    private String idNo;

    @ApiModelProperty(value = "投保人性别")
    private String sex;

    @ApiModelProperty(value = "国籍")
    private String nationality;

    @ApiModelProperty(value = "职业")
    private String occupationCode;

    @ApiModelProperty(value = "地址")
    private String homeAddress;

    @ApiModelProperty(value = "电话号码")
    private String mobile;
}
