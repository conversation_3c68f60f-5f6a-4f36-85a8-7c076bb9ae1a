package com.gclife.apply.dao.impl;

import com.gclife.apply.core.jooq.tables.Apply;
import com.gclife.apply.dao.ApplyAbandonedExtDao;
import com.gclife.apply.model.bo.ApplyExtBo;
import com.gclife.apply.model.bo.ApplyListBo;
import com.gclife.apply.model.config.ApplyTermEnum;
import com.gclife.apply.model.config.PayNotifyTermEnum;
import com.gclife.apply.model.config.ciq.CiqApplyTermEnum;
import com.gclife.apply.model.request.ApplyListRequest;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.model.BasePageRequest;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.product.model.config.ProductTermEnum;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.gclife.apply.core.jooq.Tables.*;
import static com.gclife.apply.model.config.ApplyTermEnum.*;
import static com.gclife.apply.model.config.ApplyTermEnum.MSG_BUSINESS_TYPE.REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_AGENT;
import static com.gclife.apply.model.config.ApplyTermEnum.MSG_BUSINESS_TYPE.TWO_DAYS_NOTICE_BEFORE_THE_INSURANCE_POLICY_IS_VOIDED;

/**
 * <AUTHOR>
 * create 18-8-1
 * description:
 */
@Component
public class ApplyAbandonedExtDaoImpl extends BaseDaoImpl implements ApplyAbandonedExtDao {

    @Override
    public List<ApplyListBo> queryAbandonedList(List<String> branchIds, ApplyListRequest applyListRequest, List<String> applyStatuses) {

        //-------------------------------------失效作废表------------------------------
        SelectJoinStep selectJoinStepAbandoned = this.getDslContext()
                .select(APPLY.fields())
                .select(APPLY.RECEIVABLE_PREMIUM.as("totalPremium"))
                .select(APPLY_APPLICANT.NAME.as("applicantName"), APPLY_APPLICANT.MOBILE.as("applicantMobile"), APPLY_APPLICANT.ID_NO.as("applicantIdNo"), APPLY_APPLICANT.ID_TYPE.as("applicantIdType"), APPLY_APPLICANT.COMPANY_CONTRACT_NAME, APPLY_APPLICANT.COMPANY_CONTRACT_MOBILE)
                .select(APPLY_AGENT.AGENT_ID)
                .select(APPLY_ABANDONED.ABANDONED_DATE, APPLY_ABANDONED.ABANDONED_TYPE, APPLY_ABANDONED.ABANDONED_REMARK)
                .from(APPLY_ABANDONED)
                .leftJoin(APPLY).on(APPLY_ABANDONED.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY.VALID_FLAG.eq(VALID_FLAG.effective.name())))
                .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_APPLICANT.VALID_FLAG.eq(VALID_FLAG.effective.name())))
                .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_AGENT.VALID_FLAG.eq(VALID_FLAG.effective.name())));

        Table abandonedTable = selectJoinStepAbandoned.asTable();

        Apply apply = APPLY.as("a");
        /**
         * select * from apply a
         * inner join apply_payment_transaction apt on a.apply_id = apt.apply_id and apt.valid_flag = 'effective' and apt.payment_type = 'PREPAID_PREMIUM' and apt.fee_type = 'SUSPENSE_PREMIUM' and apt.payment_status = 'PAYMENT_SUCCESS'
         * where a.apply_status in ('APPLY_STATUS_UNDERWRITE_PASS','APPLY_STATUS_PAID_PENDING_ON_UW');
         */
        //预缴成功通过人工核保的单也可以展示在作废列表
        Condition passPrePaid = APPLY.APPLY_ID.isNotNull().andExists(this.getDslContext().select(apply.fields()).from(apply)
                .innerJoin(APPLY_PAYMENT_TRANSACTION).on(apply.APPLY_ID.eq(APPLY_PAYMENT_TRANSACTION.APPLY_ID)
                        .and(APPLY_PAYMENT_TRANSACTION.VALID_FLAG.eq(VALID_FLAG.effective.name()))
                        .and(APPLY_PAYMENT_TRANSACTION.PAYMENT_STATUS.eq(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name()))
                        .and(APPLY_PAYMENT_TRANSACTION.PAYMENT_TYPE.eq(CHARGE_TYPE.PREPAID_PREMIUM.name()))
                        .and(APPLY_PAYMENT_TRANSACTION.FEE_TYPE.eq(PAYMENT_TYPE.SUSPENSE_PREMIUM.name()))
                ).where(apply.APPLY_STATUS.eq(APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name()).and(apply.APPLY_ID.eq(APPLY.APPLY_ID)))
        );
        SelectOrderByStep allSelect = this.getDslContext()
                .select(abandonedTable.fields())
                .from(abandonedTable)
                .union(this.getDslContext()
                        .select(APPLY.fields())
                        .select(APPLY.RECEIVABLE_PREMIUM.as("totalPremium"))
                        .select(APPLY_APPLICANT.NAME.as("applicantName"), APPLY_APPLICANT.MOBILE.as("applicantMobile"), APPLY_APPLICANT.ID_NO.as("applicantIdNo"), APPLY_APPLICANT.ID_TYPE.as("applicantIdType"), APPLY_APPLICANT.COMPANY_CONTRACT_NAME, APPLY_APPLICANT.COMPANY_CONTRACT_MOBILE)
                        .select(APPLY_AGENT.AGENT_ID)
                        .select(APPLY_ABANDONED.ABANDONED_DATE, APPLY_ABANDONED.ABANDONED_TYPE, APPLY_ABANDONED.ABANDONED_REMARK)
                        .from(APPLY)
                        .leftJoin(APPLY_ABANDONED).on(APPLY_ABANDONED.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_ABANDONED.VALID_FLAG.eq(VALID_FLAG.effective.name())))
                        .leftJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_APPLICANT.VALID_FLAG.eq(VALID_FLAG.effective.name())))
                        .leftJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_AGENT.VALID_FLAG.eq(VALID_FLAG.effective.name())))
                        .where(APPLY.APPLY_STATUS.in(applyStatuses).or(passPrePaid)));
        Table allTables = allSelect.asTable();


        //查询产品属性
        SelectJoinStep applyCoverage = this.getDslContext()
                .selectDistinct(APPLY_COVERAGE.PRODUCT_NAME.as("productName"))
                .select(APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_COVERAGE.COVERAGE_PERIOD)
                .select(APPLY_COVERAGE.COVERAGE_PERIOD_UNIT)
                .select(APPLY_COVERAGE.APPLY_ID.as("applyId"))
                .from(APPLY_COVERAGE)
                .leftJoin(APPLY).on(APPLY.APPLY_ID.eq(APPLY_COVERAGE.APPLY_ID));
        List<Condition> coverageConditions = new ArrayList<Condition>();
        coverageConditions.add(APPLY.SALES_BRANCH_ID.in(branchIds));
        coverageConditions.add(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name()));
        coverageConditions.add(APPLY_COVERAGE.VALID_FLAG.eq(VALID_FLAG.effective.name()));
        coverageConditions.add(APPLY_COVERAGE.INSURED_ID.isNotNull());
        applyCoverage.where(coverageConditions);
        Table applyCoverageTable = applyCoverage.asTable();


        SelectJoinStep selectJoinStep = this.getDslContext()
                .select(applyCoverageTable.fields())
                .select(allTables.fields())
                .select(allTables.field(APPLY.APPLY_ID).countOver().as("totalLine"))
                .from(allTables)
                .leftJoin(applyCoverageTable).on(applyCoverageTable.field("applyId").eq(allTables.field(APPLY.APPLY_ID.getName())));
        List<Condition> conditions = new ArrayList<>();
        conditions.add(allTables.field(APPLY.SALES_BRANCH_ID.getName()).in(branchIds)
                .and(allTables.field(APPLY.VALID_FLAG.getName()).eq(VALID_FLAG.effective.name()))
        );

        if (AssertUtils.isNotEmpty(applyListRequest.getApplyStatus())) {
            conditions.add(allTables.field(APPLY.APPLY_STATUS.getName()).eq(applyListRequest.getApplyStatus()));
        }

        if (AssertUtils.isNotEmpty(applyListRequest.getApplySource())) {
            conditions.add(allTables.field(APPLY.APPLY_SOURCE.getName()).eq(applyListRequest.getApplySource()));
        }

        if (AssertUtils.isNotEmpty(applyListRequest.getAbandonedType())) {
            conditions.add(allTables.field(APPLY_ABANDONED.ABANDONED_TYPE.getName()).eq(applyListRequest.getAbandonedType()));
        }
        if (AssertUtils.isNotEmpty(applyListRequest.getKeyword())) {
            conditions.add(allTables.field(APPLY.APPLY_NO.getName()).like("%" + applyListRequest.getKeyword() + "%")
                    .or(allTables.field("applicantName").like("%" + applyListRequest.getKeyword() + "%"))
                    .or(allTables.field("applicantIdNo").like("%" + applyListRequest.getKeyword() + "%"))
                    .or(allTables.field("applicantMobile").like("%" + applyListRequest.getKeyword() + "%"))
            );
        }
        selectJoinStep.where(conditions);
        selectJoinStep.orderBy(allTables.field(APPLY.APPLY_DATE).desc());
        selectJoinStep.offset(applyListRequest.getOffset()).limit(applyListRequest.getPageSize());
        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyListBo.class);
    }

    @Override
    public List<ApplyExtBo> listTimeOutUnpaidApplyPo(BasePageRequest basePageRequest) {

        SelectConditionStep selectConditionStep = this.getDslContext()
                .select(BASE_UNDERWRITE_DECISION.UNDERWRITE_DECISION_ID)
                .from(BASE_UNDERWRITE_DECISION)
                .where(BASE_UNDERWRITE_DECISION.UNDERWRITE_DECISION_CODE.in(
                        DECISION_TYPE.STANDARD.name(), DECISION_TYPE.SUBSTANDARD.name()
                )).and(BASE_UNDERWRITE_DECISION.VALID_FLAG.eq(VALID_FLAG.effective.name()));

        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(APPLY.fields())
                .select(APPLY_PREMIUM.PAYMENT_ID)
                .select(APPLY_PREMIUM.PREMIUM_STATUS)
                .select(APPLY_UNDERWRITE_DECISION.CREATED_DATE.as("applyUnderwriteDecisionDate"))
                .from(APPLY)
                .leftJoin(APPLY_CHANGE).on(APPLY_CHANGE.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_CHANGE_RECORD).on(APPLY_CHANGE.APPLY_CHANGE_ID.eq(APPLY_CHANGE_RECORD.APPLY_CHANGE_ID),
                        APPLY_CHANGE_RECORD.CHANGE_STATUS.eq(CHANGE_STATUS.CHANGED.name()),
                        APPLY_CHANGE_RECORD.CHANGE_TYPE.eq(ApplyTermEnum.CHANGE_TYPE.EXTENDED_PAYMENT_DUE_TIME.name())
                )
                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .innerJoin(APPLY_UNDERWRITE_TASK).on(APPLY_UNDERWRITE_TASK.CONTROL_NO.eq(APPLY.APPLY_ID).and(APPLY_UNDERWRITE_TASK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_UNDERWRITE_DECISION).on(APPLY_UNDERWRITE_DECISION.UNDERWRITE_TASK_ID.eq(APPLY_UNDERWRITE_TASK.UNDERWRITE_TASK_ID).and(APPLY_UNDERWRITE_DECISION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_PREMIUM).on(APPLY_PREMIUM.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));

        //5.8.3  默认14天
        long add14Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -14);
        //默认14天+个险7天延长期
        long add21Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -21);
        //默认14天+团险14天延长期
        long add28Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -28);
        long add30Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -30);
        selectJoinStep.where(APPLY_UNDERWRITE_DECISION.UNDERWRITE_DECISION_ID.in(selectConditionStep.fetchInto(String.class)))
                .and(APPLY.APPLY_STATUS.in(APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name(), APPLY_STATUS_FLAG.APPROVED_HC.name()))
//                // 6.9.5 禁用网销投保单14天自动废弃逻辑
//                .and(APPLY.CHANNEL_TYPE_CODE.ne(CHANNEL_TYPE.ONLINE.name()))
                .and(APPLY_UNDERWRITE_DECISION.CREATED_DATE.lt(
                        DSL.decode().when(APPLY_COVERAGE.PRODUCT_ID.in(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()), add30Day)
                                .when(APPLY_COVERAGE.PRODUCT_ID.notIn(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()),
                                        DSL.decode().when(APPLY_CHANGE_RECORD.CHANGE_TYPE_VALUE.eq(ApplyTermEnum.EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_ONE.name()), add21Day)
                                                .when(APPLY_CHANGE_RECORD.CHANGE_TYPE_VALUE.eq(EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_TWO.name()), add28Day)
                                                .otherwise(add14Day)
                                )
                ))
                .and(APPLY_PREMIUM.PREMIUM_STATUS.ne(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name()))
                .orderBy(APPLY.CREATED_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize());

        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyExtBo.class);
    }

    @Override
    public ApplyExtBo oneTimeOutUnpaidApplyPo(String applyId) {

        SelectConditionStep selectConditionStep = this.getDslContext()
                .select(BASE_UNDERWRITE_DECISION.UNDERWRITE_DECISION_ID)
                .from(BASE_UNDERWRITE_DECISION)
                .where(BASE_UNDERWRITE_DECISION.UNDERWRITE_DECISION_CODE.in(
                        DECISION_TYPE.STANDARD.name(), DECISION_TYPE.SUBSTANDARD.name()
                )).and(BASE_UNDERWRITE_DECISION.VALID_FLAG.eq(VALID_FLAG.effective.name()));

        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(APPLY.fields())
                .select(APPLY_PREMIUM.PAYMENT_ID)
                .select(APPLY_PREMIUM.PREMIUM_STATUS)
                .select(APPLY_UNDERWRITE_DECISION.CREATED_DATE.as("applyUnderwriteDecisionDate"))
                .from(APPLY)
                .leftJoin(APPLY_CHANGE).on(APPLY_CHANGE.APPLY_ID.eq(APPLY.APPLY_ID))
                .leftJoin(APPLY_CHANGE_RECORD).on(APPLY_CHANGE.APPLY_CHANGE_ID.eq(APPLY_CHANGE_RECORD.APPLY_CHANGE_ID),
                        APPLY_CHANGE_RECORD.CHANGE_STATUS.eq(CHANGE_STATUS.CHANGED.name()),
                        APPLY_CHANGE_RECORD.CHANGE_TYPE.eq(ApplyTermEnum.CHANGE_TYPE.EXTENDED_PAYMENT_DUE_TIME.name())
                )
                .leftJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .innerJoin(APPLY_UNDERWRITE_TASK).on(APPLY_UNDERWRITE_TASK.CONTROL_NO.eq(APPLY.APPLY_ID).and(APPLY_UNDERWRITE_TASK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_UNDERWRITE_DECISION).on(APPLY_UNDERWRITE_DECISION.UNDERWRITE_TASK_ID.eq(APPLY_UNDERWRITE_TASK.UNDERWRITE_TASK_ID).and(APPLY_UNDERWRITE_DECISION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_PREMIUM).on(APPLY_PREMIUM.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())));

        //5.8.3  默认14天
        long add14Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -14);
        //默认14天+个险7天延长期
        long add21Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -21);
        //默认14天+团险14天延长期
        long add28Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -28);
        long add30Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -30);
        selectJoinStep.where(APPLY_UNDERWRITE_DECISION.UNDERWRITE_DECISION_ID.in(selectConditionStep.fetchInto(String.class)))
                .and(APPLY.APPLY_STATUS.in(APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name()))
                .and(APPLY_UNDERWRITE_DECISION.CREATED_DATE.lt(
                        DSL.decode().when(APPLY_COVERAGE.PRODUCT_ID.in(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()), add30Day)
                                .when(APPLY_COVERAGE.PRODUCT_ID.notIn(ProductTermEnum.PRODUCT.PRODUCT_5.id(), ProductTermEnum.PRODUCT.PRODUCT_28.id()),
                                        DSL.decode().when(APPLY_CHANGE_RECORD.CHANGE_TYPE_VALUE.eq(ApplyTermEnum.EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_ONE.name()), add21Day)
                                                .when(APPLY_CHANGE_RECORD.CHANGE_TYPE_VALUE.eq(EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_TWO.name()), add28Day)
                                                .otherwise(add14Day))
                ))
                .and(APPLY.APPLY_ID.eq(applyId))
//                // 6.9.5 禁用网销投保单14天自动废弃逻辑
//                .and(APPLY.CHANNEL_TYPE_CODE.ne(CHANNEL_TYPE.ONLINE.name()))
                .orderBy(APPLY.CREATED_DATE);

        System.out.println(selectJoinStep.toString());
        return (ApplyExtBo) selectJoinStep.fetchOneInto(ApplyExtBo.class);
    }

    @Override
    public List<ApplyExtBo> applyInvalidRemindMessage(BasePageRequest basePageRequest) {
        SelectConditionStep selectConditionStep = this.getDslContext()
                .select(BASE_UNDERWRITE_DECISION.UNDERWRITE_DECISION_ID)
                .from(BASE_UNDERWRITE_DECISION)
                .where(BASE_UNDERWRITE_DECISION.UNDERWRITE_DECISION_CODE.in(
                        DECISION_TYPE.STANDARD.name(), DECISION_TYPE.SUBSTANDARD.name()
                )).and(BASE_UNDERWRITE_DECISION.VALID_FLAG.eq(VALID_FLAG.effective.name()));

        SelectJoinStep selectJoinStep = this.getDslContext()
                .selectDistinct(APPLY.fields())
                .select(APPLY_PREMIUM.PAYMENT_ID)
                .select(APPLY_PREMIUM.PREMIUM_STATUS)
                .select(APPLY_AGENT.AGENT_ID)
                .select(APPLY_COVERAGE.PRODUCT_NAME, APPLY_COVERAGE.PRODUCT_ID)
                .select(APPLY_UNDERWRITE_DECISION.CREATED_DATE.as("applyUnderwriteDecisionDate"))
                .select(APPLY_APPLICANT.NAME.as("applicantName"), APPLY_APPLICANT.DELEGATE_MOBILE.nvl(APPLY_APPLICANT.MOBILE).as("applicantMobile"))
                .from(APPLY)
//                .leftJoin(APPLY_CHANGE).on(APPLY_CHANGE.APPLY_ID.eq(APPLY.APPLY_ID))
//                .leftJoin(APPLY_CHANGE_RECORD).on(APPLY_CHANGE.APPLY_CHANGE_ID.eq(APPLY_CHANGE_RECORD.APPLY_CHANGE_ID),
//                        APPLY_CHANGE_RECORD.CHANGE_STATUS.eq(CHANGE_STATUS.CHANGED.name()),
//                        APPLY_CHANGE_RECORD.CHANGE_TYPE.eq(ApplyTermEnum.CHANGE_TYPE.EXTENDED_PAYMENT_DUE_TIME.name())
//                )
                .innerJoin(APPLY_UNDERWRITE_TASK).on(APPLY_UNDERWRITE_TASK.CONTROL_NO.eq(APPLY.APPLY_ID).and(APPLY_UNDERWRITE_TASK.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_UNDERWRITE_DECISION).on(APPLY_UNDERWRITE_DECISION.UNDERWRITE_TASK_ID.eq(APPLY_UNDERWRITE_TASK.UNDERWRITE_TASK_ID).and(APPLY_UNDERWRITE_DECISION.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_PREMIUM).on(APPLY_PREMIUM.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_PREMIUM.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_COVERAGE).on(APPLY_COVERAGE.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_COVERAGE.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())).and(APPLY_COVERAGE.PRIMARY_FLAG.eq(ApplyTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name())))
                .innerJoin(APPLY_AGENT).on(APPLY_AGENT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_AGENT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .innerJoin(APPLY_APPLICANT).on(APPLY_APPLICANT.APPLY_ID.eq(APPLY.APPLY_ID).and(APPLY_APPLICANT.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name())))
                .leftJoin(APPLY_MESSAGE_NOTIFY)
                .on(APPLY_MESSAGE_NOTIFY.APPLY_ID.eq(APPLY.APPLY_ID)
                        .and(APPLY_MESSAGE_NOTIFY.VALID_FLAG.eq(TerminologyConfigEnum.VALID_FLAG.effective.name()))
                        .and(APPLY_MESSAGE_NOTIFY.NOTIFY_TYPE.eq(REMIND_TO_PAY_BEFORE_THE_PAYMENT_GUIDE_EXPIRES_FOR_AGENT.name()))
                        .and(APPLY_MESSAGE_NOTIFY.NOTIFY_STATUS.eq(CiqApplyTermEnum.APPLY_STATUS.SUCCESS.name())));

//        long add5Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -4);
        //默认14天+个险7天延长期  倒数第4天提醒
//        long add12Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -19);
//        long add19Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -26);
//        long add28Day = DateUtils.addStringDayRT(DateUtils.getCurrentTime(), -28);
        selectJoinStep.where(APPLY_UNDERWRITE_DECISION.UNDERWRITE_DECISION_ID.in(selectConditionStep.fetchInto(String.class)))
                .and(APPLY.APPLY_STATUS.in(APPLY_STATUS_FLAG.APPLY_STATUS_UNDERWRITE_PASS.name()))
                //5.8.3 支付默认14天 未支付的第10天，柬埔寨时间9:00发送短信及APP消息提醒业务员进行跟进
                //当前时间的凌晨 <= 核保时间 + 10天 <= 当前时间
                .and(APPLY_UNDERWRITE_DECISION.CREATED_DATE.add(86400000L * 10L).between(DateUtils.timeToTimeLow(DateUtils.getCurrentTime()), DateUtils.getCurrentTime()))
//                .and(DSL.field(DateUtils.getCurrentTime() + "", SQLDataType.BIGINT).le(APPLY_UNDERWRITE_DECISION.CREATED_DATE.add(86400000L * 10L)))
//                .and(APPLY_UNDERWRITE_DECISION.CREATED_DATE.lt(
//                        DSL.decode().when(APPLY_COVERAGE.PRODUCT_ID.eq(ProductTermEnum.PRODUCT.PRODUCT_5.id()), add28Day)
//                                .when(APPLY_COVERAGE.PRODUCT_ID.ne(ProductTermEnum.PRODUCT.PRODUCT_5.id()), DSL.decode().when(APPLY_CHANGE_RECORD.CHANGE_TYPE_VALUE.eq(ApplyTermEnum.EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_ONE.name()), add12Day)
//                                        .when(APPLY_CHANGE_RECORD.CHANGE_TYPE_VALUE.eq(EXTENDED_PAYMENT_DUE_TIME.EXTENDED_PAYMENT_DUE_TIME_TWO.name()), add19Day)
//                                        .otherwise(add5Day))
//                ))
                //未发起支付，或者已支付且支付方式未线下支付的才发消息
                .and(APPLY_PREMIUM.PREMIUM_STATUS.ne(PayNotifyTermEnum.PAY_NOTIFY_STATUS.PAYMENT_SUCCESS.name()))
                .and(APPLY_MESSAGE_NOTIFY.APPLY_MESSAGE_NOTIFY_ID.isNull())
//                // 6.9.5 禁用网销投保单14天自动废弃逻辑
//                .and(APPLY.CHANNEL_TYPE_CODE.ne(CHANNEL_TYPE.ONLINE.name()))
                .orderBy(APPLY.CREATED_DATE)
                .offset(basePageRequest.getOffset())
                .limit(basePageRequest.getPageSize());

        System.out.println(selectJoinStep.toString());
        return selectJoinStep.fetchInto(ApplyExtBo.class);
    }
}
