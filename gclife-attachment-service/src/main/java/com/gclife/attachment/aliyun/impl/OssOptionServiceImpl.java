package com.gclife.attachment.aliyun.impl;

import com.aliyun.oss.OSSClient;
import com.gclife.attachment.aliyun.OssOptionService;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.ByteToInputStream;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.io.InputStream;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 阿里云服务操作类
 */
@Component
@PropertySource("classpath:oss.yml")
public class OssOptionServiceImpl extends BaseBusinessServiceImpl implements OssOptionService {

    @Value("${is_external_network:1}")
    private String isExternalNetwork;

    @Value("${external_domain_suffix:.aliyuncs.com}")
    private String externalDomainSuffix;

    @Value("${internal_domain_suffix:-internal.aliyuncs.com}")
    private String internalDomainSuffix;

    @Value("${protocol:http://}")
    private String protocol;

    /**
     * 上传文件
     *
     * @param ossConfigBo oss_config表中的记录
     * @param key         文件路径
     * @param inputStream 输入流
     * @return boolean
     */
    @Override
    public boolean putObject(OssConfigBo ossConfigBo, String key, InputStream inputStream) {
        // 创建OSSClient
        OSSClient ossClient = new OSSClient(protocol + ossConfigBo.getOssEndpoint() + externalDomainSuffix,
                getConfigValue(ossConfigBo.getAccessKeyId(), "oss_user.access_key_id."),
                getConfigValue(ossConfigBo.getAccessKeySecret(), "oss_user.access_key_secret."));
        try {
            // 创建bucket
            String bucket = getConfigValue(ossConfigBo.getBucket(), "oss_config.bucket.");
            if (!ossClient.doesBucketExist(bucket)) {
                ossClient.createBucket(bucket);
            }
            // 上传文件
            ossClient.putObject(bucket, key, inputStream);
            return true;
        } catch (Exception e) {
            this.getLogger().error("OSS上传文件出错: " + e.getMessage());
            return false;
        } finally {
            if(AssertUtils.isNotNull(ossClient)){
                ossClient.shutdown();
            }
        }
    }

    @Override
    public OSSClient getOssClient(OssConfigBo ossConfigBo) {
        // 创建OSSClient
        return new OSSClient(protocol + ossConfigBo.getOssEndpoint() + externalDomainSuffix,
                getConfigValue(ossConfigBo.getAccessKeyId(), "oss_user.access_key_id."),
                getConfigValue(ossConfigBo.getAccessKeySecret(), "oss_user.access_key_secret."));
    }

    /**
     * 下载文件
     *
     * @param ossConfigBo oss_config表中的记录
     * @param key         图片路径
     * @return byte[]
     */
    @Override
    public byte[] getFileObject(OssConfigBo ossConfigBo, String key) {

        // 创建OSSClient
        OSSClient ossClient = new OSSClient(protocol + ossConfigBo.getOssEndpoint() + externalDomainSuffix,
                getConfigValue(ossConfigBo.getAccessKeyId(), "oss_user.access_key_id."),
                getConfigValue(ossConfigBo.getAccessKeySecret(), "oss_user.access_key_secret."));
        try {

            // 创建bucket
            String bucket = getConfigValue(ossConfigBo.getBucket(), "oss_config.bucket.");
            if (!ossClient.doesBucketExist(bucket)) {
                ossClient.createBucket(bucket);
            }
            // 下载文件
            InputStream inputStream = ossClient.getObject(bucket, key).getObjectContent();

            byte[] bt = new byte[]{};
            if (inputStream != null) {
                bt = ByteToInputStream.input2byte(inputStream);
            }

            inputStream.close();
            return bt;
        } catch (Exception e) {
            this.getLogger().error("OSS下载文件出错：" + e.getMessage());
            return null;
        } finally {
            if(AssertUtils.isNotNull(ossClient)){
                ossClient.shutdown();
            }
        }
    }

}
