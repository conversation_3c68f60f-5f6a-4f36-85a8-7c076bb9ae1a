package com.gclife.attachment.aliyun;

import com.aliyun.oss.OSSClient;
import com.gclife.attachment.model.bo.OssConfigBo;

import java.io.InputStream;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 阿里云服务操作接口
 */
public interface OssOptionService {

    /**
     * 上传文件
     *
     * @param ossConfigBo oss_config表中的记录
     * @param key 文件路径
     * @param inputStream 输入流
     * @return boolean
     */
    public boolean putObject(OssConfigBo ossConfigBo,String key, InputStream inputStream);

    public OSSClient getOssClient(OssConfigBo ossConfigBo);

    /**
     * 下载文件
     *
     * @param ossConfigBo oss_config表中的记录
     * @param key 图片路径
     * @return OSSObject
     */
    public byte[] getFileObject(OssConfigBo ossConfigBo,String key);
}
