package com.gclife.attachment.common.xml;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.xml.entity.Computer;
import com.gclife.attachment.common.xml.entity.User;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.OutputFormat;
import org.dom4j.io.XMLWriter;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;


/**
 * xml转map，map转xml 带属性
 * http://happyqing.iteye.com/blog/2316275
 *
 * <AUTHOR>
 * @since 2016.8.8
 */
public class XmlUtil {

    public static void main(String[] args) throws DocumentException, IOException {
        User user = new User(1111, "116", "51151", new Date(), new Double(48691));
        List<Computer> list = new ArrayList();
        list.add(new Computer("111", "51151", new Date(), new Double(48691)));
        list.add(new Computer("112", "51151", new Date(), new Double(48691)));
        list.add(new Computer("113", "51151", new Date(), new Double(48691)));
        list.add(new Computer("114", "51151", new Date(), new Double(48691)));
        user.setComputers(list);
        user.setComputerList(list);

        Map map = JSON.parseObject(JSON.toJSONString(user));
        System.out.println(JSON.toJSONString(map));
        Document document = map2xml(map, "ROOT");

        byte[] formatXmlByte = formatXml(document);

        OutputStream outputStream = new FileOutputStream("res\\xml\\TEST_MAP_TO_XML.xml");

        outputStream.write(formatXmlByte);

        outputStream.close();

        System.out.println("==========================================OK========================================");
    }

    /**
     * map转xml map中没有根节点的键
     *
     * @param map
     * @param rootName
     */
    public static Document map2xml(Map<String, Object> map, String rootName) {
        Document doc = DocumentHelper.createDocument();
        Element root = DocumentHelper.createElement(rootName);
        doc.add(root);
        map2xml(map, root);
        return doc;
    }


    /**
     * map转xml
     *
     * @param map
     * @param body xml元素
     * @return
     */
    private static Element map2xml(Map<String, Object> map, Element body) {
        Iterator<Map.Entry<String, Object>> entries = map.entrySet().iterator();
        while (entries.hasNext()) {
            Map.Entry<String, Object> entry = entries.next();
            String key = entry.getKey();
            Object value = entry.getValue();
            if (StringUtils.isEmpty(key) || value == null) {
                continue;
            }
            if (key.startsWith("@")) {    //属性
                body.addAttribute(key.substring(1, key.length()), value.toString());
            } else if (key.equals("#text")) { //有属性时的文本
                body.setText(value.toString());
            } else {
                if (value instanceof List) {
                    List list = (List) value;
                    Object obj;
                    for (int i = 0; i < list.size(); i++) {
                        obj = list.get(i);
                        //list里是map或String，不会存在list里直接是list的，
                        if (obj instanceof Map) {
                            Element subElement = body.addElement(key);
                            map2xml((Map) list.get(i), subElement);
                        } else {
                            body.addElement(key).setText((String) list.get(i));
                        }
                    }
                } else if (value instanceof Map) {
                    Element subElement = body.addElement(key);
                    map2xml((Map) value, subElement);
                } else {
                    body.addElement(key).setText(value.toString());
                }
            }
            //System.out.println("Key = " + entry.getKey() + ", Value = " + entry.getValue());
        }
        return body;
    }


    /**
     * 格式化输出xml
     *
     * @param document
     * @return
     * @throws DocumentException
     * @throws IOException
     */
    public static byte[] formatXml(Document document) throws IOException {
        // 格式化输出格式
        OutputFormat format = OutputFormat.createPrettyPrint();
        format.setTrimText(false);
        format.setEncoding("UTF-8");
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 格式化输出流
        XMLWriter xmlWriter = new XMLWriter(byteArrayOutputStream, format);
        // 将document写入到输出流
        xmlWriter.write(document);
        xmlWriter.close();
        return byteArrayOutputStream.toByteArray();
    }

}
