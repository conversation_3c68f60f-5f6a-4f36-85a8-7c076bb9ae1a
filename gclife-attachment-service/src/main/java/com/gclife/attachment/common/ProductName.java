package com.gclife.attachment.common;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ProductName
 * @Description
 * @Date 2019/12/26 上午11:58
 * @Version 1.0
 */
public class ProductName {

    private  static Map<String,String> produceIdNameMap = new HashMap<>();

    static {
        produceIdNameMap.put("PRO88000000000001ZH_CN","“子女成才” 保险");
        produceIdNameMap.put("PRO88000000000001EN_US"," \"Offspring Prosperity\" Insurance");
        produceIdNameMap.put("PRO88000000000001KM_KH"," \"គម្រោងអនាគតក្តីសង្ឃឹមបុត្រធីតា\" ");

        produceIdNameMap.put("PRO88000000000001V2018ZH_CN","“子女成才” 保险");
        produceIdNameMap.put("PRO88000000000001V2018EN_US"," \"Offspring Prosperity\" Insurance");
        produceIdNameMap.put("PRO88000000000001V2018KM_KH"," \"គម្រោងអនាគតក្តីសង្ឃឹមបុត្រធីតា\" ");

        produceIdNameMap.put("PRO88000000000005ZH_CN","“无忧无虑”保险");
        produceIdNameMap.put("PRO88000000000005EN_US","“GC Life Protect” Insurance");
        produceIdNameMap.put("PRO88000000000005KM_KH","\"GC Life ការពារប្រាក់កម្ចី\"");

        produceIdNameMap.put("PRO88000000000003EN_US","\"Life Shield\" Insurance");
        produceIdNameMap.put("PRO88000000000003ZH_CN","“护身符” 保险");
        produceIdNameMap.put("PRO88000000000003KM_KH","\"គម្រោងការពារខ្លួន\"");

        produceIdNameMap.put("PRO88000000000007EN_US","Accidental Injury Hospitalization Allowance Rider");
        produceIdNameMap.put("PRO88000000000007ZH_CN","附加意外伤害住院津贴保险");
        produceIdNameMap.put("PRO88000000000007KM_KH","ធានារ៉ាប់រងបន្ថែមសម្រាប់ប្រាក់ឧបត្ថម្ភសម្រាកនៅមន្ទីរពេទ្យដោយគ្រោះថ្នាក់ជាយថាហេតុ");

        produceIdNameMap.put("PRO88000000000008EN_US","\"Good Future\" Insurance");
        produceIdNameMap.put("PRO88000000000008ZH_CN","“好将来” 保险");
        produceIdNameMap.put("PRO88000000000008KM_KH","\"គម្រោងអនាគតភ្លឺស្វាង\"");

        produceIdNameMap.put("PRO88000000000009EN_US", "“Life Guardian” Insurance");
        produceIdNameMap.put("PRO88000000000009ZH_CN", "“福海岁长”保险");
        produceIdNameMap.put("PRO88000000000009KM_KH",   "សេចក្តីស្រលាញ់យូរអង្វែង");

        produceIdNameMap.put("PRO88000000000004EN_US","Critical Illness Rider Insurance");
        produceIdNameMap.put("PRO88000000000004ZH_CN","附加重大疾病保险");
        produceIdNameMap.put("PRO88000000000004KM_KH","ធានារ៉ាប់រងបន្ថែមសម្រាប់ជំងឺធ្ងន់ធ្ងរ");

        produceIdNameMap.put("PRO8800000000000G3EN_US","“Life Shield” Group Insurance");
        produceIdNameMap.put("PRO8800000000000G3ZH_CN","“护身符”团体保险");
        produceIdNameMap.put("PRO8800000000000G3KM_KH","\"គម្រោងការពារខ្លួន\" ជាក្រុម");

        produceIdNameMap.put("PRO8800000000000G7EN_US","Accidental Injury Hospitalization Allowance Group Rider");
        produceIdNameMap.put("PRO8800000000000G7ZH_CN","附加团体意外伤害住院津贴保险");
        produceIdNameMap.put("PRO8800000000000G7KM_KH","ធានារ៉ាប់រងបន្ថែមជាក្រុមសម្រាប់ប្រាក់ឧបត្ថម្ភសម្រាកនៅមន្ទីរពេទ្យដោយគ្រោះថ្នាក់ជាយថាហេតុ");

        produceIdNameMap.put("PRO8800000000000G11EN_US","Dengue Fever Hospitalization Allowance Group Rider");
        produceIdNameMap.put("PRO8800000000000G11ZH_CN","附加团体登革热病住院津贴保险");
        produceIdNameMap.put("PRO8800000000000G11KM_KH","ធានារ៉ាប់រងបន្ថែមជាក្រុមសម្រាប់ប្រាក់ឧបត្ថម្ភសម្រាកនៅមន្ទីរពេទ្យដោយជំងឺគ្រុនឈាម");

        produceIdNameMap.put("PRO8800000000000G12EN_US","Accidental Injury Medical Insurance Group Rider");
        produceIdNameMap.put("PRO8800000000000G12ZH_CN","附加团体意外伤害医疗保险");
        produceIdNameMap.put("PRO8800000000000G12KM_KH","ធានារ៉ាប់រងបន្ថែមជាក្រុមសម្រាប់ការព្យាបាលដោយគ្រោះថ្នាក់ជាយថាហេតុ");

        produceIdNameMap.put("PRO880000000000013EN_US","GC Enrich Life");
        produceIdNameMap.put("PRO880000000000013ZH_CN","GC 全优保");
        produceIdNameMap.put("PRO880000000000013KM_KH","GC ជីវិតប្រសើរ");

        produceIdNameMap.put("PRO880000000000014EN_US","Critical Illness Plus Rider");
        produceIdNameMap.put("PRO880000000000014ZH_CN","附加新重大疾病保险");
        produceIdNameMap.put("PRO880000000000014KM_KH","អត្ថប្រយោជន៍លើការព្យាបាលជំងឺធ្ងន់ធ្ងរ");

        produceIdNameMap.put("PRO880000000000015EN_US","Accidental Protection Rider");
        produceIdNameMap.put("PRO880000000000015ZH_CN","附加意外保障保险");
        produceIdNameMap.put("PRO880000000000015KM_KH","អត្ថប្រយោជន៍ការពារបន្ថែមលើគ្រោះថ្នាក់ជាយថាហេតុ");

        produceIdNameMap.put("PRO880000000000016AEN_US","Waiver of Premium Rider");
        produceIdNameMap.put("PRO880000000000016AZH_CN","附加豁免保费保险");
        produceIdNameMap.put("PRO880000000000016AKM_KH","អត្ថប្រយោជន៍លើកលែងបុព្វលាភធានារ៉ាប់រង");

        produceIdNameMap.put("PRO880000000000016BEN_US","Waiver of Premium Rider (For Payor - Option 3)");
        produceIdNameMap.put("PRO880000000000016BZH_CN","附加豁免保费保险 (投保人 - 选项3)");
        produceIdNameMap.put("PRO880000000000016BKM_KH","អត្ថប្រយោជន៍លើកលែងបុព្វលាភធានារ៉ាប់រង (សម្រាប់ម្ចាស់បណ្ណសន្យារ៉ាប់រង - ជម្រើសទី៣)");

        produceIdNameMap.put("PRO880000000000017EN_US","GC Group Care");
        produceIdNameMap.put("PRO880000000000017ZH_CN","GC全优团保");
        produceIdNameMap.put("PRO880000000000017KM_KH","GC គ្រុបឃែរ");

        produceIdNameMap.put("PRO880000000000018EN_US","Group Medicare Rider");
        produceIdNameMap.put("PRO880000000000018ZH_CN","附加团体医疗保险");
        produceIdNameMap.put("PRO880000000000018KM_KH","អត្ថប្រយោជន៍បន្ថែមគ្រុបមេឌីឃែរ");

        produceIdNameMap.put("PRO880000000000019EN_US", "GC Scholar");
        produceIdNameMap.put("PRO880000000000019ZH_CN", "GC 教育保");
        produceIdNameMap.put("PRO880000000000019KM_KH", "GC អនាគតបុត្រធីតា");

        produceIdNameMap.put("PRO880000000000020EN_US","GC MultiProtect");
        produceIdNameMap.put("PRO880000000000020ZH_CN","GC 多倍保");
        produceIdNameMap.put("PRO880000000000020KM_KH","GC ពហុគាំពារ");

        produceIdNameMap.put("PRO880000000000021EN_US","GC Neary Care");
        produceIdNameMap.put("PRO880000000000021ZH_CN","GC 佳人保");
        produceIdNameMap.put("PRO880000000000021KM_KH","GC គាំពារនារី");

        produceIdNameMap.put("PRO880000000000022EN_US","Juvenile Critical Illness Rider");
        produceIdNameMap.put("PRO880000000000022ZH_CN","附加少儿重大疾病保险");
        produceIdNameMap.put("PRO880000000000022KM_KH","អត្ថប្រយោជន៍លើការព្យាបាលជំងឺធ្ងន់ធ្ងរសម្រាប់កុមារ");

        produceIdNameMap.put("PRO880000000000020AEN_US","GC MultiProtect");
        produceIdNameMap.put("PRO880000000000020AZH_CN","GC 多倍保");
        produceIdNameMap.put("PRO880000000000020AKM_KH","GC ពហុគាំពារ");

        produceIdNameMap.put("PRO880000000000023AEN_US","Term Protection Rider");
        produceIdNameMap.put("PRO880000000000023AZH_CN","附加定期保障保险");
        produceIdNameMap.put("PRO880000000000023AKM_KH","អត្ថប្រយោជន៍បន្ថែមការការពារ");

        produceIdNameMap.put("PRO880000000000023BEN_US","Term Protection Rider");
        produceIdNameMap.put("PRO880000000000023BZH_CN","附加定期保障保险");
        produceIdNameMap.put("PRO880000000000023BKM_KH","អត្ថប្រយោជន៍បន្ថែមការការពារ");

        produceIdNameMap.put("PRO880000000000024EN_US","GC PureProtect");
        produceIdNameMap.put("PRO880000000000024ZH_CN","GC 守护保");
        produceIdNameMap.put("PRO880000000000024KM_KH","GC ការពារ");

        produceIdNameMap.put("PRO880000000000028EN_US","GC Credit Protect");
        produceIdNameMap.put("PRO880000000000028ZH_CN","GC 信贷保");
        produceIdNameMap.put("PRO880000000000028KM_KH","GC ការពារក្រេឌីត");

        produceIdNameMap.put("PRO880000000000026EN_US","Group Accidental Death And Dismemberment Rider");
        produceIdNameMap.put("PRO880000000000026ZH_CN","附加团体意外死亡和残疾保险");
        produceIdNameMap.put("PRO880000000000026KM_KH","អត្ថប្រយោជន៍បន្ថែមគ្រុបមេឌីឃែរ");

        produceIdNameMap.put("PRO880000000000027EN_US","Group Medicash Rider");
        produceIdNameMap.put("PRO880000000000027ZH_CN","附加新团体医疗保险");
        produceIdNameMap.put("PRO880000000000027KM_KH","អត្ថប្រយោជន៍បន្ថែមគ្រុបមេឌីខាស");

        produceIdNameMap.put("PRO880000000000029EN_US","GC Group Student Care");
        produceIdNameMap.put("PRO880000000000029ZH_CN","GC 团体学平保");
        produceIdNameMap.put("PRO880000000000029KM_KH","GC សម្រាប់សិស្សនិស្សិត");

        produceIdNameMap.put("PRO880000000000033EN_US","Group MediPlus Rider");
        produceIdNameMap.put("PRO880000000000033ZH_CN","附加团体意外无忧医疗保险");
        produceIdNameMap.put("PRO880000000000033KM_KH","អត្ថប្រយោជន៍បន្ថែមគ្រុបមេឌីផ្លាស");

        produceIdNameMap.put("PRO880000000000034EN_US","GC SokSan");
        produceIdNameMap.put("PRO880000000000034ZH_CN","GC 安康保");
        produceIdNameMap.put("PRO880000000000034KM_KH","GC សុខសាន្ត");
    }



    /**
     *
     * @param produceIdLanguage
     * @return
     */
    public static String getProductName(String produceIdLanguage){
        return produceIdNameMap.get(produceIdLanguage);
    }


}
