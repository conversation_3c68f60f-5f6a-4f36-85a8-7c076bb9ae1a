package com.gclife.attachment.common;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.AssertUtils;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.seuksa.itextkhmer.KhmerLigaturizer;

import java.io.*;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

import static com.gclife.attachment.model.config.AttachmentPolicyEnum.CUSTOMER_SERVICE_INSTRUCTION_BOOK;
import static com.gclife.attachment.model.config.AttachmentPolicyEnum.FIRST_ISSUE_BOOK;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.ADDITIONAL;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN;
import static com.gclife.attachment.model.config.AttachmentTermEnum.PENSION_RECEIVE_FREQUENCY.SINGLE;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.*;

/**
 * <AUTHOR>
 * create 18-1-29
 * description:
 */
public class PrintCommon {

    //柬文处理对象
    public static final KhmerLigaturizer khmerLigaturizer = new KhmerLigaturizer();
    public static BaseFont ZH_CN_FONT = null;
    public static BaseFont ZH_CN_FONT1 = null;
    public static BaseFont KM_KH_FONT = null;
    public static BaseFont KM_KH_FONT1 = null;
    public static BaseFont KM_KH_FONT2 = null;
    public static BaseFont EN_US_FONT = null;

    public static BaseFont CALIBRI_FONT = null;

    //PATH 支持语言路径
    public static String ZH_CN_FONT_PATH = File.separator + "fonts" + File.separator + "zh-cn" + File.separator + "hanyi-zhongdengxian.ttf";
    public static String ZH_CN_FONT_PATH1 = File.separator + "fonts" + File.separator + "zh-cn" + File.separator + "stsong.ttf";
    public static String KM_KH_FONT_PATH = File.separator + "fonts" + File.separator + "km-kh" + File.separator + "Nokora.ttf";
    public static String KM_KH_FONT_PATH1 = File.separator + "fonts" + File.separator + "km-kh" + File.separator + "KHMERMEF1.ttf";
    public static String KM_KH_FONT_PATH2 = File.separator + "fonts" + File.separator + "km-kh" + File.separator + "KHMERMEF2.ttf";
    public static String EN_US_FONT_PATH = File.separator + "fonts" + File.separator + "en-us" + File.separator + "Times New Roman.ttf";

    public static String CALIBRI_PATH = File.separator + "fonts" + File.separator + "calibri_0.ttf";
    //CEO签名
    public static final String CEO_Signature = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/CEO_Signature01.png";
    //公司 盖章
    public static final String Company_Stamp = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/Seal.png";
    /**
     * CEO签名 + 公司印章
     */
    public static final String SIGN_SEAL_CEO = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/SignSeal_CEO.png";
    /**
     * 财务CFO签名 + 公司印章
     */
    public static final String SIGN_SEAL_CFO = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/SignSeal_CFO.png";
    /**
     * 高级经理签名 + 公司盖章
     */
    public static final String SIGN_KIM_PETI = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/SignSeal_Senior_Manager.png";

    /**
     * 签约合同 CEO签名 + 公司印章
     */
    public static final String SIGN_IAN_FA = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/Sign_Ian_FA.png";

    //计划书封面图片
    public static final String PLAN_FIRST = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/PLAN_FIRST.png";
    //计划书结尾图片
    public static final String PLAN_LAST = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/PLAN_LAST.jpg";
    /**
     * 封面正在使用的最新版本
     */
    public static final String COVER_VERSION = "221014_";
    /**
     * 条款
     */
    public static final String TERMS_VERSION = "220916_";
    /**
     * 保险费率表
     */
    public static final String PREMIUM_RATE_AND_CASH_VALUE_VERSION = "220916_";
    /**
     * 客户服务指南
     */
    public static final String CUSTOMER_SERVICE_INSTRUCTION_VERSION = "221014_";
    /**
     * 西方　时间格式
     */
    public static final SimpleDateFormat sdfEN_US = new SimpleDateFormat("dd-MM-yyyy");
    public static final SimpleDateFormat sdfEN_US29 = new SimpleDateFormat("dd/MM/yyyy");
    /**
     * 东方　时间格式
     */
    public static final SimpleDateFormat sdfZH_CN = new SimpleDateFormat("yyyy-MM-dd");
    public static final String YYYY = "yyyy";
    public static final String MM = "MM";
    public static final String dd = "dd";
    //禁改 字符串
    public static final String prohibitedString = " - ";
    //金额格式化
    public static final DecimalFormat decimalFormat = new DecimalFormat("###,###,###,###,##0.00");
    //金额格式化
    public static final DecimalFormat decimalFormat1 = new DecimalFormat("###,###,###,###,##0");


    /**初始化字体数据**/
    static {
        try {
            ZH_CN_FONT = BaseFont.createFont(ZH_CN_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            ZH_CN_FONT1 = BaseFont.createFont(ZH_CN_FONT_PATH1, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            KM_KH_FONT = BaseFont.createFont(KM_KH_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            KM_KH_FONT1 = BaseFont.createFont(KM_KH_FONT_PATH1, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            KM_KH_FONT2 = BaseFont.createFont(KM_KH_FONT_PATH2, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            EN_US_FONT = BaseFont.createFont(EN_US_FONT_PATH, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

            CALIBRI_FONT = BaseFont.createFont(CALIBRI_PATH, BaseFont.CP1250, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            System.out.println("字体据初始化异常");
            e.printStackTrace();
        }

    }

    /**
     * 存储时间
     *
     * @param key
     * @param value
     * @param xSize
     * @throws Exception
     */
    public static void setPrintDateTime(List<PrintObject> printObjectList, String key, Long value, int xSize) {
        String dateEN_US = sdfEN_US(value);
        String dateZH_CN = sdfZH_CN(value);
        String dateYyyy = getYyyy(value);
        String dateMm = getMm(value);
        String dateDd = getDd(value);
        String stringDate = null;
        if (AssertUtils.isNotNull(value)) {
            String[] k = new Date(value).toString().split(" ");
            stringDate = k[2] + " " + k[1].toUpperCase() + " " + k[5];
        }
        printObjectList.add(new PrintObject(key + "EN_US1", getPrintString(stringDate, xSize)));
        printObjectList.add(new PrintObject(key + "EN_US", getPrintString(dateEN_US, xSize)));
        printObjectList.add(new PrintObject(key + "ZH_CN", getPrintString(dateZH_CN, xSize)));
        printObjectList.add(new PrintObject(key + "Yyyy", getPrintString(dateYyyy, 1)));
        printObjectList.add(new PrintObject(key + "Mm", getPrintString(dateMm, 1)));
        printObjectList.add(new PrintObject(key + "Dd", getPrintString(dateDd, 1)));
    }

    /**
     * 存储时间
     *
     * @param key
     * @param value
     * @param xSize
     * @throws Exception
     */
    public static void setPrintDateTime(Map<String, Object> printObjectList, String key, Long value, int xSize) {
        String dateEN_US = sdfEN_US(value);
        String dateZH_CN = sdfZH_CN(value);
        String dateYyyy = getYyyy(value);
        String dateMm = getMm(value);
        String dateDd = getDd(value);
        printObjectList.put(key + "EN_US", getPrintString(dateEN_US, xSize));
        printObjectList.put(key + "ZH_CN", getPrintString(dateZH_CN, xSize));
        printObjectList.put(key + "Yyyy", getPrintString(dateYyyy, 1));
        printObjectList.put(key + "Mm", getPrintString(dateMm, 1));
        printObjectList.put(key + "Dd", getPrintString(dateDd, 1));
        String stringDate = null;
        String stringDate1 = null;
        if (AssertUtils.isNotNull(value)) {
            String[] k = new Date(value).toString().split(" ");
            stringDate = k[2] + " " + k[1].toUpperCase() + " " + k[5];
            stringDate1 = k[2] + k[1].toUpperCase() + k[5];
        }
        printObjectList.put(key + "EN_US1", getPrintString(stringDate, xSize));
        printObjectList.put(key + "EN_US2", getPrintString(stringDate1, xSize));
    }

    /**
     * 添加字符
     *
     * @param addStr
     * @param o
     * @param size
     * @return
     */
    public static String getPrintString(String addStr, Object o, int size) {
        String printString = getPrintString(o, size);
        if (AssertUtils.isNotNull(o)) {
            return addStr + printString;
        } else {
            return printString;
        }
    }

    /**
     * 添加字符
     *
     * @param addStr
     * @param o
     * @param nullShow
     * @return
     */
    public static String getPrintString(String addStr, Object o, String nullShow) {
        String printString = getPrintString(o, nullShow);
        if (AssertUtils.isNotNull(o)) {
            return addStr + printString;
        } else {
            return printString;
        }
    }

    /**
     * 保费添加默认字符
     *
     * @param o
     * @return
     */
    public static String getPrintDefaultStr(Object o) {
        String printString = getPrintString(o, "--");
        if (AssertUtils.isNotNull(o)) {
            return "$" + printString;
        } else {
            return printString;
        }
    }

    /**
     * 防止修改打印字符
     *
     * @param o    打印数据对象
     * @param nullShow 禁改 字符长度
     * @return
     */
    public static String getPrintString(Object o, String nullShow) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                return decimalFormat.format(o);
            }
            return o + "";
        }
        return nullShow;
    }

    /**
     * 防止修改打印字符
     *
     * @param o    打印数据对象
     * @param size 禁改 字符长度
     * @return
     */
    public static String getPrintString(Object o, int size) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                return decimalFormat.format(o);
            }
            return o + "";
        }
        String printString = "";
        for (int i = 0; i < size; i++) {
            printString += prohibitedString;
        }
        return printString;
    }

    /**
     * 防止修改打印字符
     *
     * @param o    打印数据对象
     * @param size 禁改 字符长度
     * @return
     */
    public static String getPrintAmountString(Object o, int size) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                return decimalFormat1.format(o);
            }
            return o + "";
        }
        String printString = "";
        for (int i = 0; i < size; i++) {
            printString += prohibitedString;
        }
        return printString;
    }

    /**
     * 防止修改打印字符
     *
     * @param o        打印数据对象
     * @param nullShow 如果对象为null 则展示此字符
     * @return
     */
    public static String getPrintAmountString(Object o, String nullShow) {
        //非null 操作
        if (AssertUtils.isNotNull(o) && AssertUtils.isNotEmpty(o + "")) {
            if (o instanceof BigDecimal) {
                return decimalFormat1.format(o);
            }
            return o + "";
        }
        return nullShow;
    }

    /**
     * 返回西方的时间
     *
     * @param date
     * @return
     */
    public static String sdfEN_US(Long date) {
        if (AssertUtils.isNotNull(date)) {
            return sdfEN_US.format(date);
        }
        return null;
    }

    /**
     * 返回西方的时间(29号产品）
     *
     * @param date
     * @return
     */
    public static String sdfEN_US29(Long date) {
        if (AssertUtils.isNotNull(date)) {
            return sdfEN_US29.format(date);
        }
        return null;
    }

    /**
     * 返回东方的时间
     *
     * @param date
     * @return
     */
    public static String sdfZH_CN(Long date) {
        if (AssertUtils.isNotNull(date)) {
            return sdfZH_CN.format(date);
        }
        return null;
    }

    /**
     * 获取年
     *
     * @param date
     * @return
     */
    public static String getYyyy(Long date) {
        if (AssertUtils.isNotNull(date)) {
            String stringYYYY = new SimpleDateFormat(YYYY).format(date);
            return stringYYYY;
        }
        return null;
    }

    /**
     * 获取月
     *
     * @param date
     * @return
     */
    public static String getMm(Long date) {
        if (AssertUtils.isNotNull(date)) {
            String stringMM = new SimpleDateFormat(MM).format(date);
            return stringMM;
        }
        return null;
    }

    /**
     * 获取天
     *
     * @param date
     * @return
     */
    public static String getDd(Long date) {
        if (AssertUtils.isNotNull(date)) {
            String stringDD = new SimpleDateFormat(dd).format(date);
            return stringDD;
        }
        return null;
    }

    /**
     * 添加图片
     *
     * @param imagePath
     * @param imageWidth
     * @param imageHeight
     * @param x
     * @param y
     * @return
     * @throws Exception
     */
    public static Image addImage(String imagePath, float imageWidth, float imageHeight, float x, float y) throws Exception {
        // 添加图片
        Image image = Image.getInstance(imagePath);
        image.setAlignment(1);
        image.scaleAbsolute(imageWidth, imageHeight);//控制图片大小
        image.setAbsolutePosition(x, y);
        return image;
    }

    /**
     * 添加打印数据
     *
     * @param printObjectList
     * @param key
     * @param value
     * @param xSize
     */
    public static void setPrintData(List<PrintObject> printObjectList, String key, Object value, int xSize) {
        String printString = PrintCommon.getPrintString(value, xSize);
        printObjectList.add(new PrintObject(key, printString));
    }


    /**
     * 单选框
     *
     * @param printObjectList
     * @param checkboxs
     * @param key
     */
    public static void setPrintData(List<PrintObject> printObjectList, String[] checkboxs, String key) {
        for (String checkbox : checkboxs) {
            if (checkbox.equals(key)) {
                continue;
            }
            printObjectList.add(new PrintObject(checkbox, "Off"));
        }
    }

    /**
     * 合并文件　多个　PDF 文件
     *
     * @param decryptedDataList 　多个文件　二进制　集合
     */
    public static byte[] mergePdfFiles(List<byte[]> decryptedDataList) throws Exception {
        return mergePdfFiles(decryptedDataList, null);
    }

    /**
     * 合并文件　多个　PDF 文件
     *
     * @param decryptedDataList 　多个文件　二进制　集合
     */
    public static byte[] mergePdfFilesCoi(List<byte[]> decryptedDataList) throws Exception {
        return mergePdfFilesCoi(decryptedDataList, null);
    }

    /**
     * 合并文件　多个　PDF 文件 增加空白页
     *
     * @param decryptedDataList 　多个文件　二进制　集合
     */
    public static byte[] mergePdfFiles(List<byte[]> decryptedDataList, String language) throws Exception {
        try {

            if (!AssertUtils.isNotEmpty(decryptedDataList)) {
                return null;
            }
            if (decryptedDataList.size() == 1) {
                return decryptedDataList.get(0);
            }

            Document document = new Document(PageSize.A4);

            ByteArrayOutputStream mergeOut = new ByteArrayOutputStream();

            PdfCopy copy = new PdfCopy(document, mergeOut);

            document.open();
            byte[] bytes;
            for (int i = 0; i < decryptedDataList.size(); i++) {
                bytes = decryptedDataList.get(i);
                if (AssertUtils.isNotNull(language)) {
                    bytes = pdfEvenPage(bytes, language);
                }
                PdfReader reader = new PdfReader(bytes);
                copy.addDocument(reader);
                copy.freeReader(reader);
                reader.close();
            }
            document.close();
            return mergeOut.toByteArray();
        } catch (IOException e) {
            throw e;
        } catch (DocumentException e) {
            throw e;
        }
    }


    /**
     * 合并文件　多个　PDF 文件 增加空白页
     *
     * @param decryptedDataList 　多个文件　二进制　集合
     */
    public static byte[] mergePdfFilesCoi(List<byte[]> decryptedDataList, String language) throws Exception {
        try {

            if (!AssertUtils.isNotEmpty(decryptedDataList)) {
                return null;
            }
            if (decryptedDataList.size() == 1) {
                return decryptedDataList.get(0);
            }


            // 创建一个新的 PDF 文档
            Document document = new Document(PageSize.A4);
            ByteArrayOutputStream mergeOut = new ByteArrayOutputStream();
            // 创建一个 PDF 写入器
            PdfCopy copy = new PdfCopy(document, mergeOut);
            // 打开文档
            document.open();
            // 添加要合并的 PDF 文件
            //遍历要合并的PDF文件
            for (int i = 0; i < decryptedDataList.size(); i++) {
                addPdfFile(copy, decryptedDataList.get(i));
            }

            // 关闭文档
            document.close();

            /*//创建一个PdfWriter对象
            PdfWriter writer = PdfWriter.getInstance(document, mergeOut);

            //打开Document对象
            document.open();

             //创建一个PdfImportedPage对象
            PdfImportedPage page = null;

            //创建一个PdfReader对象
            PdfReader reader = null;
            byte[] bytes;
            //遍历要合并的PDF文件
            for (int i = 0; i < decryptedDataList.size(); i++) {
                bytes = decryptedDataList.get(i);
                //加载PDF文件
                reader = new PdfReader(bytes);

                //获取PDF文件的总页数
                int n = reader.getNumberOfPages();

                //遍历每一页，添加到PdfWriter对象中
                for (int j = 1; j <= n; j++) {
                    //创建一个新的页面
                    document.newPage();

                    //导入当前页
                    page = writer.getImportedPage(reader, j);

                    //将当前页添加到PdfWriter对象中
                    writer.addDirectImageSimple(Image.getInstance(page));
                    writer.getCurrentPage().add(page);


                    //创建一个PdfContentByte对象
                    PdfContentByte content = writer.getDirectContent();
                }
            }

            //关闭PdfReader对象
            reader.close();

            //关闭Document对象
            document.close();*/

            return mergeOut.toByteArray();
        } catch (IOException e) {
            throw e;
        } catch (DocumentException e) {
            throw e;
        }
    }

    public static void addPdfFile(PdfCopy copy, byte[] fileByte) throws Exception {
        // 创建一个 PDF 读取器
        PdfReader reader = new PdfReader(fileByte);
        // 获取 PDF 文件的页数
        int numPages = reader.getNumberOfPages();
        // 将 PDF 文件的每一页添加到新的 PDF 文档中
        for (int i = 1; i <= numPages; i++) {
            copy.addPage(copy.getImportedPage(reader, i));
        }
        // 关闭 PDF 读取器
        reader.close();
    }

    public static void addPdfFileCoi(PdfCopy copy, byte[] fileByte) throws Exception {
        // 创建一个 PDF 读取器
        PdfReader reader = new PdfReader(fileByte);
        // 获取 PDF 文件的页数
        int numPages = reader.getNumberOfPages();
        // 将 PDF 文件的每一页添加到新的 PDF 文档中
        for (int i = 1; i <= numPages; i++) {
            copy.addPage(copy.getImportedPage(reader, i));
        }
        // 关闭 PDF 读取器
        reader.close();
    }

    private static void mergePdfFiles(String targetFile, String... sourceFiles) throws IOException {
        PDFMergerUtility merger = new PDFMergerUtility();

        for (String sourceFile : sourceFiles) {
            merger.addSource(sourceFile);
        }

        // 设置MemoryUsageSetting以限制内存使用
        MemoryUsageSetting memoryUsageSetting = MemoryUsageSetting.setupTempFileOnly();
        merger.setDestinationFileName(targetFile);

        merger.mergeDocuments(memoryUsageSetting);

        System.out.println("PDF files merged successfully!");
    }

    public static void setPremiumFrequencyName(String premiumFrequency, List<PrintObject> printObjectList) {
        String premiumFrequencyNameEN_US = null;
        String premiumFrequencyNameZH_CN = null;
        String premiumFrequencyNameKM_KH = null;

        /**
         * 3年，年缴 =      ​3ឆ្នាំ, ការបង់ប្រចាំឆ្នាំ
         * 3年，半年缴 =   ​3ឆ្នាំ, ការបង់ប្រចាំឆមាស
         * 3年，季缴 =      ​3ឆ្នាំ, ការបង់ប្រចាំត្រីមាស
         * 3年，月缴 = ​     3ឆ្នាំ, ការបង់ប្រចាំខែ
         */
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Annual Payment";
            premiumFrequencyNameZH_CN = "3年，每年缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំឆ្នាំ";
        }
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Semi-Annual Payment";
            premiumFrequencyNameZH_CN = "3年，每半年缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំឆមាស";
        }
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Quarterly Payment";
            premiumFrequencyNameZH_CN = "3年，每季度缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំត្រីមាស";
        }
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Monthly Payment";
            premiumFrequencyNameZH_CN = "3年，每月缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំខែ";
        }
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyNameEN_US", premiumFrequencyNameEN_US, 3);
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyNameZH_CN", premiumFrequencyNameZH_CN, 3);
        PrintCommon.setPrintData(printObjectList, "premiumFrequencyNameKM_KH", premiumFrequencyNameKM_KH, 3);
    }

    public static void setPremiumFrequencyName(String premiumFrequency, Map<String, Object> printObjectList) {
        String premiumFrequencyNameEN_US = null;
        String premiumFrequencyNameZH_CN = null;
        String premiumFrequencyNameKM_KH = null;

        /**
         * 3年，年缴 =      ​3ឆ្នាំ, ការបង់ប្រចាំឆ្នាំ
         * 3年，半年缴 =   ​3ឆ្នាំ, ការបង់ប្រចាំឆមាស
         * 3年，季缴 =      ​3ឆ្នាំ, ការបង់ប្រចាំត្រីមាស
         * 3年，月缴 = ​     3ឆ្នាំ, ការបង់ប្រចាំខែ
         */
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.YEAR.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Annual Payment";
            premiumFrequencyNameZH_CN = "3年，每年缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំឆ្នាំ";
        }
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEMIANNUAL.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Semi-Annual Payment";
            premiumFrequencyNameZH_CN = "3年，每半年缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំឆមាស";
        }
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.SEASON.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Quarterly Payment";
            premiumFrequencyNameZH_CN = "3年，每季度缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំត្រីមាស";
        }
        if (AttachmentTermEnum.PRODUCT_PREMIUM_FREQUENCY.MONTH.name().equals(premiumFrequency)) {
            premiumFrequencyNameEN_US = "3years，Monthly Payment";
            premiumFrequencyNameZH_CN = "3年，每月缴费";
            premiumFrequencyNameKM_KH = "3ឆ្នាំ, ការបង់ប្រចាំខែ";
        }
        printObjectList.put( "premiumFrequencyNameEN_US", PrintCommon.getPrintString(premiumFrequencyNameEN_US, 3));
        printObjectList.put("premiumFrequencyNameZH_CN", PrintCommon.getPrintString(premiumFrequencyNameZH_CN, 3));
        printObjectList.put( "premiumFrequencyNameKM_KH", PrintCommon.getPrintString(premiumFrequencyNameKM_KH, 3));
    }


    /**
     * 数据模版打印
     *
     * @param inputBytes
     * @param data
     * @throws Exception
     */
    public static byte[] fillData(byte[] inputBytes, List<PrintObject> data) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PdfStamper stamper = new PdfStamper(new PdfReader(inputBytes), byteArrayOutputStream);
        fillData(stamper, data);
        return byteArrayOutputStream.toByteArray();
    }

    public static void fillData(PdfStamper stamper, List<PrintObject> data) throws Exception {
        AcroFields acroFields = stamper.getAcroFields();

        ArrayList<BaseFont> fontList = new ArrayList<BaseFont>();
//        fontList.add(PrintCommon.ZH_CN_FONT);
        fontList.add(PrintCommon.ZH_CN_FONT1);
        fontList.add(PrintCommon.KM_KH_FONT);
//        fontList.add(PrintCommon.KM_KH_FONT1);
//        fontList.add(PrintCommon.KM_KH_FONT2);
        fontList.add(PrintCommon.EN_US_FONT);
        acroFields.setSubstitutionFonts(fontList);

        for (PrintObject printObject : data) {
            if (AssertUtils.isNotEmpty(printObject.getValue())) {
                acroFields.setField(printObject.getKey(), khmerLigaturizer.process(printObject.getValue())); // 为字段赋值,注意字段名称是区分大小写的
            }
        }
        stamper.setFormFlattening(true);
        stamper.close();
    }


    /**
     * 设置产品名称
     *
     * @param printObjectList
     * @param productId
     * @param i
     */
    public static void setProductName(List<PrintObject> printObjectList, String productId, String i) {
        setProductName(printObjectList, productId, i, null);
    }

    /**
     * 设置产品名称
     *
     * @param printObjectList
     * @param productId
     * @param i
     */
    public static void setProductName(List<PrintObject> printObjectList, String productId, String i, String productLevel) {
        if (!AssertUtils.isNotEmpty(i)) {
            i = "";
        }

        if (!AssertUtils.isNotEmpty(productId)) {
            PrintCommon.setPrintData(printObjectList, "productNameEN_US" + i, null, 2);
            PrintCommon.setPrintData(printObjectList, "productNameZH_CN" + i, null, 2);
            PrintCommon.setPrintData(printObjectList, "productNameKM_KH" + i, null, 2);
            PrintCommon.setPrintData(printObjectList, "productCode" + i, null, 2);
            PrintCommon.setPrintData(printObjectList, "product1Code" + i, null, 2);
            PrintCommon.setPrintData(printObjectList, "seq" + i, 999, 2);
            return;
        }

        Map<String, Object> map = new HashMap<>();
        setProductName(map, productId, null, productLevel, null);
        PrintCommon.setPrintData(printObjectList, "productNameEN_US" + i, map.get("productNameEN_US"), 4);
        PrintCommon.setPrintData(printObjectList, "productNameZH_CN" + i, map.get("productNameZH_CN"), 4);
        PrintCommon.setPrintData(printObjectList, "productNameKM_KH" + i, map.get("productNameKM_KH"), 4);
        PrintCommon.setPrintData(printObjectList, "primaryFlagEN_US" + i, map.get("primaryFlagEN_US"), 4);
        PrintCommon.setPrintData(printObjectList, "primaryFlagZH_CN" + i, map.get("primaryFlagZH_CN"), 4);
        PrintCommon.setPrintData(printObjectList, "primaryFlagKM_KH" + i, map.get("primaryFlagKM_KH"), 4);
        PrintCommon.setPrintData(printObjectList, "productCode" + i, map.get("productCode"), 2);
        PrintCommon.setPrintData(printObjectList, "product1Code" + i, map.get("product1Code"), 2);
        PrintCommon.setPrintData(printObjectList, "seq" + i, map.get("seq"), 2);
    }


    /**
     * 设置产品名称
     *
     * @param map
     * @param productId
     */
    public static void setProductName(Map<String, Object> map, String productId) {
        setProductName(map, productId, null, null, null);
    }

    /**
     * 设置产品名称
     *
     * @param map
     * @param productId
     * @param productLevel
     */
    public static void setProductName(Map<String, Object> map, String productId, String productLevel) {
        setProductName(map, productId, null, productLevel, null);
    }

    /**
     * 设置产品名称
     *
     * @param map
     * @param productId
     * @param productLevel
     */
    public static void setProductName(Map<String, Object> map, String productId, String productLevel, String language) {
        setProductName(map, productId, null, productLevel, language);
    }

    /**
     * 设置产品名称
     *
     * @param map
     * @param productId
     * @param productLevel
     */
    public static void setProductName(Map<String, Object> map, String productId, String i, String productLevel, String language) {
        i = AssertUtils.isNotEmpty(i) ? i : "";
        if (!AssertUtils.isNotEmpty(productId)) {
            map.put("productNameEN_US" + i, PrintCommon.getPrintString(null, 2));
            map.put("productNameZH_CN" + i, PrintCommon.getPrintString(null, 2));
            map.put("productNameKM_KH" + i, PrintCommon.getPrintString(null, 2));
            map.put("productName", PrintCommon.getPrintString(null, 2));
            map.put("productCode", PrintCommon.getPrintString(null, 2));
            map.put("product1Code", PrintCommon.getPrintString(null, 2));
            map.put("seq", 999);
            return;
        }
        String productNameEN_US = ProductName.getProductName(productId + EN_US.name());
        String productNameZH_CN = ProductName.getProductName(productId + ZH_CN.name());
        String productNameKM_KH = ProductName.getProductName(productId + KM_KH.name());
        //险种名称
        if ("PRO88000000000001".equals(productId) || "PRO88000000000001V2018".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US + (productLevel == null ? "" : " - Method " + productLevel));
            map.put("productNameZH_CN" + i, productNameZH_CN + (productLevel == null ? "" : " - " + productLevel + " 方式 "));
            map.put("productNameKM_KH" + i, productNameKM_KH + (productLevel == null ? "" : " - វិធីសាស្រ្ត " + productLevel));
            putPrimaryFlag(map, MAIN.name(), 3, "3");
        } else if ("PRO88000000000003".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 3, "1");
        } else if ("PRO88000000000007".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 7, "7");
        } else if ("PRO88000000000008".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 1, "8");
        } else if ("PRO88000000000004".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 4, "4");
        } else if ("PRO8800000000000G3".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 1, "1+");
        } else if ("PRO8800000000000G7".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 7, "7+");
        } else if (productId.equals("PRO8800000000000G11")) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 11, "11");
        } else if (productId.equals("PRO88000000000009")) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 9, "9");
        } else if (productId.equals("PRO8800000000000G12")) {
            map.put("productNameEN_US" + i, productNameEN_US + (productLevel == null ? "" : " - " + getDuty(productLevel, EN_US.name())));
            map.put("productNameZH_CN" + i, productNameZH_CN + (productLevel == null ? "" : " - " + getDuty(productLevel, ZH_CN.name())));
            map.put("productNameKM_KH" + i, productNameKM_KH + (productLevel == null ? "" : " - " + getDuty(productLevel, KM_KH.name())));
            putPrimaryFlag(map, ADDITIONAL.name(), 12, "12");
        } else if ("PRO88000000000005".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 5, "5");
        } else if ("PRO880000000000013".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 13, "13");
        } else if ("PRO880000000000014".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 14, "14");
        } else if ("PRO880000000000015".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 15, "15");
        } else if ("PRO880000000000016A".equals(productId)) {
            String OPTION = "";
            if ("OPTION_ONE".equals(productLevel)) {
                productNameEN_US += " (For Insured - Option 1)";
                productNameZH_CN += " (被保险人 - 选项1)";
                productNameKM_KH += " (សម្រាប់អ្នកត្រូវបានធានារ៉ាប់រង - ជម្រើសទី១)";
                OPTION = "(01)";
            } else if ("OPTION_TWO".equals(productLevel)) {
                productNameEN_US += " (For Insured - Option 2)";
                productNameZH_CN += " (被保险人 - 选项2)";
                productNameKM_KH += " (សម្រាប់អ្នកត្រូវបានធានារ៉ាប់រង - ជម្រើសទី២)";
                OPTION = "(02)";
            }
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 16 + 1, "16" + OPTION);
        } else if ("PRO880000000000016B".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 16 + 2, "16(03)");
        } else if ("PRO880000000000017".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 17, "17");
        } else if ("PRO880000000000018".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 18, "18");
        } else if ("PRO880000000000021".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 21, "21");
        } else if ("PRO880000000000020".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 20, "20");
        } else if ("PRO880000000000020A".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 20, "20");
        } else if ("PRO880000000000019".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 19, "19");
        } else if ("PRO880000000000022".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 22, "22");
        } else if ("PRO880000000000023A".equals(productId)) {
            String OPTION = "";
            if ("OPTION_ONE".equals(productLevel)) {
                productNameEN_US += " (For Insured - Option 1)";
                productNameZH_CN += " (被保险人 - 选项1)";
                productNameKM_KH += " (សម្រាប់អ្នកត្រូវបានធានារ៉ាប់រង - ជម្រើសទី១)";
                OPTION = "(01)";
            } else if ("OPTION_TWO".equals(productLevel)) {
                productNameEN_US += " (For Insured - Option 2)";
                productNameZH_CN += " (被保险人 - 选项2)";
                productNameKM_KH += " (សម្រាប់អ្នកត្រូវបានធានារ៉ាប់រង - ជម្រើសទី២)";
                OPTION = "(02)";
            }
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 23 + 1, "23" + OPTION);
        } else if ("PRO880000000000023B".equals(productId)) {
            String OPTION = "";
            if ("OPTION_ONE".equals(productLevel)) {
                productNameEN_US += " (For Payor - Option 1)";
                productNameZH_CN += " (投保人 - 选项1)";
                productNameKM_KH += " (សម្រាប់ម្ចាស់បណ្ណសន្យារ៉ាប់រង - ជម្រើសទី១)";
                OPTION = "(01)";
            } else if ("OPTION_TWO".equals(productLevel)) {
                productNameEN_US += " (For Payor - Option 2)";
                productNameZH_CN += " (投保人 - 选项2)";
                productNameKM_KH += " (សម្រាប់ម្ចាស់បណ្ណសន្យារ៉ាប់រង - ជម្រើសទី២)";
                OPTION = "(02)";
            }
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 23 + 2, "23" + OPTION);
        } else if ("PRO880000000000024".equals(productId)) {
            String OPTION = "";
            if ("OPTION_ONE".equals(productLevel)) {
                productNameEN_US += " (Option 1)";
                productNameZH_CN += " (选项1)";
                productNameKM_KH += " (ជម្រើសទី១)";
                OPTION = "(01)";
            } else if ("OPTION_TWO".equals(productLevel)) {
                productNameEN_US += " (Option 2)";
                productNameZH_CN += " (选项2)";
                productNameKM_KH += " (ជម្រើសទី២)";
                OPTION = "(02)";
            }
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 24, "24" + OPTION);
        }else if ("PRO880000000000028".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 28, "28");
        } else if ("PRO880000000000026".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 26, "26");
        } else if ("PRO880000000000027".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 27, "27");
        } else if ("PRO880000000000029".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 29, "29");
        } else if ("PRO880000000000033".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, ADDITIONAL.name(), 33, "33");
        } else if ("PRO880000000000034".equals(productId)) {
            map.put("productNameEN_US" + i, productNameEN_US);
            map.put("productNameZH_CN" + i, productNameZH_CN);
            map.put("productNameKM_KH" + i, productNameKM_KH);
            putPrimaryFlag(map, MAIN.name(), 34, "34");
        }
        map.put("productName" + i, map.get("productName" + language));
    }

    /**
     * 设置主附险表示
     *
     * @param map
     * @param primaryFlag
     * @param seq
     */
    public static void putPrimaryFlag(Map<String, Object> map, String primaryFlag, int seq, String productCode) {
        if (MAIN.name().equals(primaryFlag)) {
            map.put("primaryFlagEN_US", "(Main Product)");
            map.put("primaryFlagZH_CN", "(主险)");
            map.put("primaryFlagKM_KH", "(ផលិតផលមូលដ្ឋាន)");
        } else if (ADDITIONAL.name().equals(primaryFlag)) {
            map.put("primaryFlagEN_US", "(Rider)");
            map.put("primaryFlagZH_CN", "(附加险)");
            map.put("primaryFlagKM_KH", "(ធានារ៉ាប់រងបន្ថែម)");
        }
        map.put("seq", seq);
        map.put("productCode", productCode);
        map.put("product1Code", "#" + productCode);
    }

    public static String getDuty(String dutyId, String language) {
        if (dutyId.equals("PRO8800000000000G12_DUTY_1")) {
            if (ZH_CN.name().equals(language)) {
                return "门诊";
            }
            if (EN_US.name().equals(language)) {
                return "Outpatient";
            }
            if (KM_KH.name().equals(language)) {
                return "ការពិគ្រោះជំងឺក្រៅ";
            }
        }
        if (dutyId.equals("PRO8800000000000G12_DUTY_2")) {
            if (ZH_CN.name().equals(language)) {
                return "住院";
            }
            if (EN_US.name().equals(language)) {
                return "Inpatient";
            }
            if (KM_KH.name().equals(language)) {
                return "ការសម្រាកព្យាបាលនៅមន្ទីរពេទ្យ";
            }
        }
        if (dutyId.equals("PRO8800000000000G12_DUTY_3")) {
            if (ZH_CN.name().equals(language)) {
                return "危重症转院";
            }
            if (EN_US.name().equals(language)) {
                return "Hospital Transfer Due to Critical Condition";
            }
            if (KM_KH.name().equals(language)) {
                return "ការផ្ទេរទៅព្យាបាលនៅមន្ទីរពេទ្យក្រៅប្រទេសដោយសារស្ថានភាពធ្ងន់ធ្ងរ";
            }
        }
        return "";
    }

    /**
     * 险种排序
     *
     * @param coverageListMap
     */
    public static void coverageSort(List<Map<String, Object>> coverageListMap) {
        if (!AssertUtils.isNotEmpty(coverageListMap)) {
            return;
        }
        coverageListMap.sort(new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                //增减员标示
                String changeFlag1 = o1.get("changeFlag") + "";
                String changeFlag2 = o2.get("changeFlag") + "";
                if (!changeFlag1.equals(changeFlag2)) {
                    return (changeFlag1).compareTo(changeFlag2);
                }
                //主附险标示
                String primaryFlag = o1.get("primaryFlag") + "";
                String primaryFlag1 = o2.get("primaryFlag") + "";
                if (!primaryFlag.equals(primaryFlag1)) {
                    return MAIN.name().equals(primaryFlag) ? -1 : 1;
                }
                //下标  标示
                Integer seq1 = Integer.valueOf(o1.get("seq") + "");
                Integer seq2 = Integer.valueOf(o2.get("seq") + "");
                if (!seq1.equals(seq2)) {
                    return (seq1).compareTo(seq2);
                }
                //责任排序
                String dutyId1 = o1.get("dutyId") + "";
                String dutyId2 = o2.get("dutyId") + "";
                if (!dutyId1.equals(dutyId2)) {
                    return (dutyId1).compareTo(dutyId2);
                }
                // 档次
                return (o1.get("productLevel") + "").compareTo(o2.get("productLevel") + "");

            }
        });
    }

    /**
     * 缴费期限
     *
     * @param printObjectList
     * @param productId
     * @param i
     */
    public static void setPremiumPeriod(List<PrintObject> printObjectList, String productId, String i) {
        //缴费期限
        if (!AssertUtils.isNotEmpty(i)) {
            i = "";
        }
        if (!AssertUtils.isNotEmpty(productId)) {
            PrintCommon.setPrintData(printObjectList, "premiumPeriodKM_KH" + i, null, 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodZH_CN" + i, null, 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodEN_US" + i, null, 2);
            return;
        }
        //缴费期限
        if ("PRO88000000000001".equals(productId)) {
            PrintCommon.setPrintData(printObjectList, "premiumPeriodKM_KH" + i, "3ឆ្នាំ", 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodZH_CN" + i, "3年", 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodEN_US" + i, "3 years", 2);
        } else if ("PRO88000000000003".equals(productId) || "PRO88000000000007".equals(productId)) {
            PrintCommon.setPrintData(printObjectList, "premiumPeriodKM_KH" + i, "បង់ផ្តាច់តែម្តង", 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodZH_CN" + i, "一次性全额缴清", 2);
            PrintCommon.setPrintData(printObjectList, "premiumPeriodEN_US" + i, "Single Payment", 2);
        }
    }


    /**
     * 被保人健康告知备注
     *
     * @param printObjectList
     * @param insuredHealthRemark
     */
    public static void setInsuredHealthRemark(List<PrintObject> printObjectList, String insuredHealthRemark) {

        if (AssertUtils.isNotEmpty(insuredHealthRemark)) {
            PrintCommon.setPrintData(printObjectList, "insuredHealthRemarkEN_US", insuredHealthRemark, 2);
            PrintCommon.setPrintData(printObjectList, "insuredHealthRemarkZH_CN", insuredHealthRemark, 2);
            PrintCommon.setPrintData(printObjectList, "insuredHealthRemarkKM_KH", insuredHealthRemark, 2);
        } else {
            PrintCommon.setPrintData(printObjectList, "insuredHealthRemarkEN_US", "(This section is empty) ", 2);
            PrintCommon.setPrintData(printObjectList, "insuredHealthRemarkZH_CN", "（此栏目为空白）", 2);
            PrintCommon.setPrintData(printObjectList, "insuredHealthRemarkKM_KH", "(ប្រអប់នេះគឺទទេ)", 2);
        }

    }

    /**
     * 被保人健康告知备注
     *
     * @param map
     * @param insuredHealthRemark
     */
    public static void setInsuredHealthRemark(Map<String, Object> map, String insuredHealthRemark) {
        if (AssertUtils.isNotEmpty(insuredHealthRemark)) {
            map.put("insuredHealthRemarkEN_US", insuredHealthRemark);
            map.put("insuredHealthRemarkZH_CN", insuredHealthRemark);
            map.put("insuredHealthRemarkKM_KH", insuredHealthRemark);
        } else {
            map.put("insuredHealthRemarkEN_US", "(This section is empty) ");
            map.put("insuredHealthRemarkZH_CN", "（此栏目为空白）");
            map.put("insuredHealthRemarkKM_KH", "(ប្រអប់នេះគឺទទេ)");
        }

    }


    /**
     * 被保人健康告知备注
     *
     * @param printObjectList
     * @param applicantHealthRemark
     */
    public static void setApplicantHealthRemark(List<PrintObject> printObjectList, String applicantHealthRemark) {

        if (AssertUtils.isNotEmpty(applicantHealthRemark)) {
            PrintCommon.setPrintData(printObjectList, "applicantHealthRemarkEN_US", applicantHealthRemark, 2);
            PrintCommon.setPrintData(printObjectList, "applicantHealthRemarkZH_CN", applicantHealthRemark, 2);
            PrintCommon.setPrintData(printObjectList, "applicantHealthRemarkKM_KH", applicantHealthRemark, 2);
        } else {
            PrintCommon.setPrintData(printObjectList, "applicantHealthRemarkEN_US", "(This section is empty) ", 2);
            PrintCommon.setPrintData(printObjectList, "applicantHealthRemarkZH_CN", "（此栏目为空白）", 2);
            PrintCommon.setPrintData(printObjectList, "applicantHealthRemarkKM_KH", "(ប្រអប់នេះគឺទទេ)", 2);
        }
    }


    /**
     * 被保人健康告知备注
     *
     * @param map
     * @param applicantHealthRemark
     */
    public static void setApplicantHealthRemark(Map<String, Object> map, String applicantHealthRemark) {

        if (AssertUtils.isNotEmpty(applicantHealthRemark)) {
            map.put("applicantHealthRemarkEN_US", applicantHealthRemark);
            map.put("applicantHealthRemarkZH_CN", applicantHealthRemark);
            map.put("applicantHealthRemarkKM_KH", applicantHealthRemark);
        } else {
            map.put("applicantHealthRemarkEN_US", "(This section is empty) ");
            map.put("applicantHealthRemarkZH_CN", "（此栏目为空白）");
            map.put("applicantHealthRemarkKM_KH", "(ប្រអប់នេះគឺទទេ)");
        }
    }

    /**
     * 被保人健康告知备注
     *
     * @param printObjectList
     * @param specialContractContent
     */
    public static void getApplySpecialContractContent(List<PrintObject> printObjectList, String specialContractContent) {

        if (AssertUtils.isNotEmpty(specialContractContent)) {
            PrintCommon.setPrintData(printObjectList, "specialContractContentEN_US", specialContractContent, 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentZH_CN", specialContractContent, 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentKM_KH", specialContractContent, 2);
        } else {
            PrintCommon.setPrintData(printObjectList, "specialContractContentEN_US", "(Please fill out this section if there is any other notification that the Company shall be aware of.)", 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentZH_CN", "（如果您有未尽告知，请在此处填写）", 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentKM_KH", "  (ប្រសិនបើមានដំណឹងដែលក្រុមហ៊ុនចាំបាច់ដឹង សូមលោកអ្នកបំពេញនៅទីនេះ) ", 2);
        }

    }

    /**
     * 被保人健康告知备注
     *
     * @param map
     * @param specialContractContent
     */
    public static void getApplySpecialContractContent(Map<String, Object> map, String specialContractContent) {

        if (AssertUtils.isNotEmpty(specialContractContent)) {
            map.put("specialContractContentEN_US", PrintCommon.getPrintString(specialContractContent, 2));
            map.put("specialContractContentZH_CN", PrintCommon.getPrintString(specialContractContent, 2));
            map.put("specialContractContentKM_KH", PrintCommon.getPrintString(specialContractContent, 2));
        } else {
            map.put("specialContractContentEN_US", PrintCommon.getPrintString("(Please fill out this section if there is any other notification that the Company shall be aware of.)", 2));
            map.put("specialContractContentZH_CN", PrintCommon.getPrintString("（如果您有未尽告知，请在此处填写）", 2));
            map.put("specialContractContentKM_KH", PrintCommon.getPrintString("  (ប្រសិនបើមានដំណឹងដែលក្រុមហ៊ុនចាំបាច់ដឹង សូមលោកអ្នកបំពេញនៅទីនេះ) ", 2));
        }

    }

    /**
     * 被保人健康告知备注
     *
     * @param printObjectList
     * @param specialContractContent
     */
    public static void getPolicySpecialContractContent(List<PrintObject> printObjectList, String specialContractContent) {

        if (AssertUtils.isNotEmpty(specialContractContent)) {
            PrintCommon.setPrintData(printObjectList, "specialContractContentEN_US", "Outstanding or Special Agreements：" + specialContractContent, 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentZH_CN", "未尽事宜或特别约定：" + specialContractContent, 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentKM_KH", "ការព្រមព្រៀងដែលមិនទាន់បញ្ជាក់ឬការព្រមព្រៀងពិសេស:" + specialContractContent, 2);
        } else {
            PrintCommon.setPrintData(printObjectList, "specialContractContentEN_US", "Outstanding or Special Agreements：       (This section is empty)", 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentZH_CN", "未尽事宜或特别约定：       （本栏目以下为空白）", 2);
            PrintCommon.setPrintData(printObjectList, "specialContractContentKM_KH", "ការព្រមព្រៀងដែលមិនទាន់បញ្ជាក់ឬការព្រមព្រៀងពិសេស:       (ប្រអប់នេះគឺទទេ)", 2);
        }

    }

    /**
     * 被保人健康告知备注
     *
     * @param map
     * @param specialContractContent
     */
    public static void getPolicySpecialContractContent(Map<String, Object> map, String specialContractContent) {

        if (AssertUtils.isNotEmpty(specialContractContent)) {
            map.put("specialContractContentEN_US", PrintCommon.getPrintString("Outstanding or Special Agreements：" + specialContractContent, 2));
            map.put("specialContractContentZH_CN", PrintCommon.getPrintString("未尽事宜或特别约定：" + specialContractContent, 2));
            map.put("specialContractContentKM_KH", PrintCommon.getPrintString("ការព្រមព្រៀងដែលមិនទាន់បញ្ជាក់ឬការព្រមព្រៀងពិសេស:" + specialContractContent, 2));
        } else {
            map.put("specialContractContentEN_US", PrintCommon.getPrintString("Outstanding or Special Agreements：       (This section is empty)", 2));
            map.put("specialContractContentZH_CN", PrintCommon.getPrintString("未尽事宜或特别约定：       （本栏目以下为空白）", 2));
            map.put("specialContractContentKM_KH", PrintCommon.getPrintString("ការព្រមព្រៀងដែលមិនទាន់បញ្ជាក់ឬការព្រមព្រៀងពិសេស:       (ប្រអប់នេះគឺទទេ)", 2));
        }

    }

    /**
     * 修改 文件 页码
     *
     * @param bytes
     * @return
     * @throws Exception
     */
    public static byte[] updateAndSavePageNumber(byte[] bytes) throws Exception {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PdfReader pdfReader = new PdfReader(bytes);
        //生成文件
        PdfStamper pdfStamper = new PdfStamper(pdfReader, byteArrayOutputStream);
        int numberOfPages = pdfReader.getNumberOfPages();
        for (int i = 1; i <= numberOfPages; i++) {
            PdfContentByte content = pdfStamper.getOverContent(i);
            //进行遮挡
            content.saveState();
            content.setColorFill(BaseColor.WHITE);  //白色遮挡层
            content.rectangle(274.09, 27.89, 65.2, 14.75);
            content.fill();
            content.restoreState();
            //添加文字
            content.beginText();
            content.setFontAndSize(EN_US_FONT, 11.04f);
            content.setTextMatrix(200, 200);
            content.showTextAligned(Element.ALIGN_CENTER, i + " / " + numberOfPages, 594.76f / 2, 30.05f, 0);//左边距、下边距
            content.endText();
        }
        pdfStamper.close();
        pdfReader.close();
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 单选框
     *
     * @param map
     * @param box       框位置名称
     * @param valueList 值
     */
    public static void setSelectionBoxList(Map<String, Object> map, String box, List<String> valueList) {
        if (!AssertUtils.isNotEmpty(valueList)) {
            return;
        } else {
            for (String value : valueList) {
                map.put(box + value, 1);
            }
        }
    }

    /**
     * 单选框
     *
     * @param map
     * @param box    框位置名称
     * @param values 值
     */
    public static void setSelectionBoxList(Map<String, Object> map, String box, String values) {
        if (!AssertUtils.isNotEmpty(values)) {
            return;
        } else {
            setSelectionBoxList(map, box, JSON.parseArray(values, String.class));
        }
    }

    /**
     * 单选框
     *
     * @param map
     * @param box   框位置名称
     * @param value 值
     */
    public static void setSelectionBox(Map<String, Object> map, String box, String value) {
        if (!AssertUtils.isNotEmpty(value)) {
            return;
        } else {
            map.put(box, 1);
        }
    }

    public static String pdfEvenPageText(String language) {
        if (ZH_CN.name().equals(language)) {
            return "此页空白";
        } else if (EN_US.name().equals(language)) {
            return "This Page is Blank";
        } else {
            return "ទំព័រនេះគឺទទេ";
        }
    }

    /**
     * 基数添加空白页
     *
     * @return
     */
    public static byte[] pdfEvenPage(byte[] bytes, String language) throws Exception {
        String printString = pdfEvenPageText(language);
        BaseFont font = PrintCommon.ZH_CN_FONT1;
        float x = 60;
        if (ZH_CN.name().equals(language)) {
            font = PrintCommon.ZH_CN_FONT1;
        } else if (EN_US.name().equals(language)) {
            font = PrintCommon.EN_US_FONT;
            x = 24;
        } else if (KM_KH.name().equals(language)) {
            font = PrintCommon.KM_KH_FONT;
            printString = khmerLigaturizer.process(printString);
            x = 24;
        }

        PdfReader pdfReader = new PdfReader(bytes);
        int numberOfPages = pdfReader.getNumberOfPages();
        if (numberOfPages % 2 != 0) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            PdfStamper stamper = new PdfStamper(pdfReader, byteArrayOutputStream);
            // 添加一页
            stamper.insertPage(pdfReader.getNumberOfPages() + 1, pdfReader.getPageSizeWithRotation(1));

            Rectangle rectangle = pdfReader.getPageSize(pdfReader.getNumberOfPages());

            float pageWidth = rectangle.getWidth();
            float pageHeight = rectangle.getHeight();
            // 设置水印透明度
            PdfGState gs = new PdfGState();
            // 设置填充字体不透明度为0.4f
            gs.setFillOpacity(0.4f);
            // 获取当前页码
            PdfContentByte content = stamper.getUnderContent(pdfReader.getNumberOfPages());
            // 开始
            content.beginText();
            // 设置水印字体参数及大小   (字体参数，字体编码格式，是否将字体信息嵌入到pdf中（一般不需要嵌入），字体大小)
            content.setFontAndSize(font, 60);
            // 设置透明度
            content.setGState(gs);
            // 设置水印对齐方式 水印内容 X坐标 Y坐标 旋转角度
            content.setTextMatrix((pageWidth - x * printString.length()) / 2, (pageHeight - 30) / 2);
            content.newlineShowText(printString);
            // 设置水印颜色(灰色)
            content.setColorFill(BaseColor.GRAY);
            // 结束
            content.endText();
            stamper.close();
            pdfReader.close();
            return byteArrayOutputStream.toByteArray();
        }
        pdfReader.close();
        return bytes;
    }


    /**
     * 制定收益人国际化
     *
     * @param language
     * @param isPresent
     * @return
     */
    public static String getBeneficiaryInformation(String language, boolean isPresent) {
        String beneficiaryInformation = null;
        if (isPresent) {
            if (language.equals(EN_US.name())) {
                beneficiaryInformation = "Designated Beneficiary";
            }
            if (language.equals(ZH_CN.name())) {
                beneficiaryInformation = "指定受益人";
            }
            if (language.equals(KM_KH.name())) {
                beneficiaryInformation = "អ្នកទទួលផលដែលបានកំណត់";
            }
        } else {
            if (language.equals(EN_US.name())) {
                beneficiaryInformation = "Legal Beneficiary";
            }
            if (language.equals(ZH_CN.name())) {
                beneficiaryInformation = "法定受益人";
            }
            if (language.equals(KM_KH.name())) {
                beneficiaryInformation = "អ្នកទទួលផលកំណត់ដោយច្បាប់";
            }
        }
        return beneficiaryInformation;
    }

    public static byte[] lockUp(byte[] bytes, String password) throws Exception {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PdfStamper stamper = new PdfStamper(new PdfReader(bytes), byteArrayOutputStream);
        stamper.setEncryption(password.getBytes(), "GCLIFE-201707".getBytes(), PdfWriter.ALLOW_PRINTING, PdfWriter.ENCRYPTION_AES_128 | PdfWriter.DO_NOT_ENCRYPT_METADATA);
        stamper.close();
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 缴费期限重组
     *
     * @param language
     * @param premiumPeriod
     * @param premiumPeriodUnit
     * @param premiumPeriodUnitName
     * @param premiumFrequency
     * @return
     */
    public static String getPremiumPeriodUnitName(String language, String premiumPeriod, String premiumPeriodUnit, String premiumPeriodUnitName, String premiumFrequency) {
        if (!AssertUtils.isNotNull(premiumPeriodUnitName) || !AssertUtils.isNotNull(premiumPeriod) || !AssertUtils.isNotNull(premiumFrequency) || !AssertUtils.isNotNull(premiumPeriodUnit)) {
            return null;
        }
        if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(premiumPeriodUnit)) {
            return premiumPeriodUnitName + premiumPeriod;
        }
        String premiumFrequencyName = getPremiumFrequencyName(language, premiumFrequency);
        if (AssertUtils.isNotNull(premiumFrequencyName)) {
            return premiumFrequencyName;
        }
        return premiumPeriod + premiumPeriodUnitName;
    }

    /**
     * 缴费周期
     *
     * @param language
     * @param premiumFrequency
     * @return
     */
    public static String getPremiumFrequencyName(String language, String premiumFrequency) {
        if (SINGLE.name().equals(premiumFrequency)) {
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language)) {
                return "បង់ផ្តាច់តែម្តង";
            }
            if (TerminologyConfigEnum.LANGUAGE.ZH_CN.name().equals(language)) {
                return "一次性全额缴清";
            }
            if (TerminologyConfigEnum.LANGUAGE.EN_US.name().equals(language)) {
                return "Single Payment";
            }
        }
        return null;
    }

    /**
     * 保额添加字符
     *
     * @param addStr
     * @param o
     * @param size
     * @return
     */
    public static String getPrintAmountString(String addStr, Object o, int size) {
        String printString = getPrintAmountString(o, size);
        if (AssertUtils.isNotNull(o)) {
            return addStr + printString;
        } else {
            return printString;
        }
    }

    /**
     * 保额添加字符
     *
     * @param addStr
     * @param o
     * @param nullShow
     * @return
     */
    public static String getPrintAmountString(String addStr, Object o, String nullShow) {
        String printString = getPrintAmountString(o, nullShow);
        if (AssertUtils.isNotNull(o)) {
            return addStr + printString;
        } else {
            return printString;
        }
    }

    /**
     * 保额添加默认字符
     *
     * @param o
     * @return
     */
    public static String getPrintAmountDefaultStr(Object o) {
        String printString = getPrintAmountString(o, "--");
        if (AssertUtils.isNotNull(o)) {
            return "$" + printString;
        } else {
            return printString;
        }
    }

    /**
     * 清除产品名称选项信息 就是清除:(Main Product - Option 1)
     *
     * @param map
     */
    public static void removeProductNameInsuranceOption(Map<String, Object> map) {
        if (AssertUtils.isNotEmpty(map)) {
            Object productNameEN_US = map.get("productNameEN_US");
            map.put("productNameEN_US", getNoBracketProductName(productNameEN_US));

            Object productNameZH_CN = map.get("productNameZH_CN");
            map.put("productNameZH_CN", getNoBracketProductName(productNameZH_CN));

            Object productNameKM_KH = map.get("productNameKM_KH");
            map.put("productNameKM_KH", getNoBracketProductName(productNameKM_KH));
        }
    }

    /**
     * 截取产品名称
     *
     * @param productName
     * @return
     */
    private static Object getNoBracketProductName(Object productName) {
        if (AssertUtils.isNotNull(productName)) {
            String strProductName = productName.toString();
            int bracketStart = strProductName.indexOf("(");
            if (-1 != bracketStart) {
                // 截取第一个字符到括号开始就是没有选项的产品名称
                return strProductName.substring(0, bracketStart);
            }
        }
        return productName;
    }

    /**
     * 根据产品ID、语言获取 <b>个险封面<b/>
     *
     * @param productId
     * @param language
     * @return
     */
    public static AttachmentResponse getCover(String productId, String language) {
        // 默认个险通用封面
        String coverType = "COVER";
        // 个险网销#20A
        if ("PRO880000000000020A".equals(productId)) {
            coverType = "ONLINE_COVER";
        }

        return new AttachmentResponse(COVER_VERSION + coverType + "_" + language, FIRST_ISSUE_BOOK.name());
    }

    /**
     * 根据产品ID、语言获取 <b>团险封面<b/>
     *
     * @param productId
     * @param language
     * @return
     */
    public static AttachmentResponse getGroupCover(String productId, String language) {
        // 默认团险#1+封面
        String coverType = "GROUP_COVER";
        // 团险#17
        if ("PRO880000000000017".equals(productId)) {
            coverType = "GROUP_17_COVER";
        }

        return new AttachmentResponse(COVER_VERSION + coverType + "_" + language, FIRST_ISSUE_BOOK.name());
    }

    /**
     * 根据语言获取客户服务指南
     *
     * @param language
     * @return
     */
    public static AttachmentResponse getCustomerServiceInstruction(String language) {
        return new AttachmentResponse(CUSTOMER_SERVICE_INSTRUCTION_VERSION + "CUSTOMER_SERVICE_INSTRUCTION_" + language, CUSTOMER_SERVICE_INSTRUCTION_BOOK.name());
    }
}





















