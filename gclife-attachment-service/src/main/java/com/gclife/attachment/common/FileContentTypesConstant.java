package com.gclife.attachment.common;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * create 19-6-20 下午9:59
 * email: <EMAIL>
 * description:
 **/
public class FileContentTypesConstant {

    public  static Map<String,String> fileContentTypeMap=new HashMap<>();

    static {
        fileContentTypeMap.put("*","application/octet-stream");
        fileContentTypeMap.put("001","application/x-001");
        fileContentTypeMap.put("301","application/x-301");
        fileContentTypeMap.put("323","text/h323");
        fileContentTypeMap.put("906","application/x-906");
        fileContentTypeMap.put("907","drawing/907");
        fileContentTypeMap.put("a11","application/x-a11");
        fileContentTypeMap.put("acp","audio/x-mei-aac");
        fileContentTypeMap.put("ai","application/postscript");
        fileContentTypeMap.put("aif","audio/aiff");
        fileContentTypeMap.put("aifc","audio/aiff");
        fileContentTypeMap.put("aiff","audio/aiff");
        fileContentTypeMap.put("anv","application/x-anv");
        fileContentTypeMap.put("asa","text/asa");
        fileContentTypeMap.put("asf","video/x-ms-asf");
        fileContentTypeMap.put("asp","text/asp");
        fileContentTypeMap.put("asx","video/x-ms-asf");
        fileContentTypeMap.put("au","audio/basic");
        fileContentTypeMap.put("avi","video/avi");
        fileContentTypeMap.put("awf","application/vnd.adobe.workflow");
        fileContentTypeMap.put("biz","text/xml");
        fileContentTypeMap.put("bmp","application/x-bmp");
        fileContentTypeMap.put("bot","application/x-bot");
        fileContentTypeMap.put("c4t","application/x-c4t");
        fileContentTypeMap.put("c90","application/x-c90");
        fileContentTypeMap.put("cal","application/x-cals");
        fileContentTypeMap.put("cat","application/vnd.ms-pki.seccat");
        fileContentTypeMap.put("cdf","application/x-netcdf");
        fileContentTypeMap.put("cdr","application/x-cdr");
        fileContentTypeMap.put("cel","application/x-cel");
        fileContentTypeMap.put("cer","application/x-x509-ca-cert");
        fileContentTypeMap.put("cg4","application/x-g4");
        fileContentTypeMap.put("cgm","application/x-cgm");
        fileContentTypeMap.put("cit","application/x-cit");
        fileContentTypeMap.put("class","java/*");
        fileContentTypeMap.put("cml","text/xml");
        fileContentTypeMap.put("cmp","application/x-cmp");
        fileContentTypeMap.put("cmx","application/x-cmx");
        fileContentTypeMap.put("cot","application/x-cot");
        fileContentTypeMap.put("crl","application/pkix-crl");
        fileContentTypeMap.put("crt","application/x-x509-ca-cert");
        fileContentTypeMap.put("csi","application/x-csi");
        fileContentTypeMap.put("css","text/css");
        fileContentTypeMap.put("cut","application/x-cut");
        fileContentTypeMap.put("dbf","application/x-dbf");
        fileContentTypeMap.put("dbm","application/x-dbm");
        fileContentTypeMap.put("dbx","application/x-dbx");
        fileContentTypeMap.put("dcd","text/xml");
        fileContentTypeMap.put("dcx","application/x-dcx");
        fileContentTypeMap.put("der","application/x-x509-ca-cert");
        fileContentTypeMap.put("dgn","application/x-dgn");
        fileContentTypeMap.put("dib","application/x-dib");
        fileContentTypeMap.put("dll","application/x-msdownload");
        fileContentTypeMap.put("doc","application/msword");
        fileContentTypeMap.put("dot","application/msword");
        fileContentTypeMap.put("drw","application/x-drw");
        fileContentTypeMap.put("dtd","text/xml");
        fileContentTypeMap.put("dwf","Model/vnd.dwf");
        fileContentTypeMap.put("dwf","application/x-dwf");
        fileContentTypeMap.put("dwg","application/x-dwg");
        fileContentTypeMap.put("dxb","application/x-dxb");
        fileContentTypeMap.put("dxf","application/x-dxf");
        fileContentTypeMap.put("edn","application/vnd.adobe.edn");
        fileContentTypeMap.put("emf","application/x-emf");
        fileContentTypeMap.put("eml","message/rfc822");
        fileContentTypeMap.put("ent","text/xml");
        fileContentTypeMap.put("epi","application/x-epi");
        fileContentTypeMap.put("eps","application/x-ps");
        fileContentTypeMap.put("eps","application/postscript");
        fileContentTypeMap.put("etd","application/x-ebx");
        fileContentTypeMap.put("exe","application/x-msdownload");
        fileContentTypeMap.put("fax","image/fax");
        fileContentTypeMap.put("fdf","application/vnd.fdf");
        fileContentTypeMap.put("fif","application/fractals");
        fileContentTypeMap.put("fo","text/xml");
        fileContentTypeMap.put("frm","application/x-frm");
        fileContentTypeMap.put("g4","application/x-g4");
        fileContentTypeMap.put("gbr","application/x-gbr");
        fileContentTypeMap.put("gcd","application/x-gcd");
        fileContentTypeMap.put("gif","image/gif");
        fileContentTypeMap.put("gl2","application/x-gl2");
        fileContentTypeMap.put("gp4","application/x-gp4");
        fileContentTypeMap.put("hgl","application/x-hgl");
        fileContentTypeMap.put("hmr","application/x-hmr");
        fileContentTypeMap.put("hpg","application/x-hpgl");
        fileContentTypeMap.put("hpl","application/x-hpl");
        fileContentTypeMap.put("hqx","application/mac-binhex40");
        fileContentTypeMap.put("hrf","application/x-hrf");
        fileContentTypeMap.put("hta","application/hta");
        fileContentTypeMap.put("htc","text/x-component");
        fileContentTypeMap.put("htm","text/html");
        fileContentTypeMap.put("html","text/html");
        fileContentTypeMap.put("htt","text/webviewhtml");
        fileContentTypeMap.put("htx","text/html");
        fileContentTypeMap.put("icb","application/x-icb");
        fileContentTypeMap.put("ico","image/x-icon");
        fileContentTypeMap.put("ico","application/x-ico");
        fileContentTypeMap.put("iff","application/x-iff");
        fileContentTypeMap.put("ig4","application/x-g4");
        fileContentTypeMap.put("igs","application/x-igs");
        fileContentTypeMap.put("iii","application/x-iphone");
        fileContentTypeMap.put("img","application/x-img");
        fileContentTypeMap.put("ins","application/x-internet-signup");
        fileContentTypeMap.put("isp","application/x-internet-signup");
        fileContentTypeMap.put("IVF","video/x-ivf");
        fileContentTypeMap.put("java","java/*");
        fileContentTypeMap.put("jfif","image/jpeg");
        fileContentTypeMap.put("jpe","image/jpeg");
        fileContentTypeMap.put("jpe","application/x-jpe");
        fileContentTypeMap.put("jpeg","image/jpeg");
        fileContentTypeMap.put("jpg","image/jpeg");
        fileContentTypeMap.put("jpg","application/x-jpg");
        fileContentTypeMap.put("js","application/x-javascript");
        fileContentTypeMap.put("jsp","text/html");
        fileContentTypeMap.put("la1","audio/x-liquid-file");
        fileContentTypeMap.put("lar","application/x-laplayer-reg");
        fileContentTypeMap.put("latex","application/x-latex");
        fileContentTypeMap.put("lavs","audio/x-liquid-secure");
        fileContentTypeMap.put("lbm","application/x-lbm");
        fileContentTypeMap.put("lmsff","audio/x-la-lms");
        fileContentTypeMap.put("ls","application/x-javascript");
        fileContentTypeMap.put("ltr","application/x-ltr");
        fileContentTypeMap.put("m1v","video/x-mpeg");
        fileContentTypeMap.put("m2v","video/x-mpeg");
        fileContentTypeMap.put("m3u","audio/mpegurl");
        fileContentTypeMap.put("m4e","video/mpeg4");
        fileContentTypeMap.put("mac","application/x-mac");
        fileContentTypeMap.put("man","application/x-troff-man");
        fileContentTypeMap.put("math","text/xml");
        fileContentTypeMap.put("mdb","application/msaccess");
        fileContentTypeMap.put("mdb","application/x-mdb");
        fileContentTypeMap.put("mfp","application/x-shockwave-flash");
        fileContentTypeMap.put("mht","message/rfc822");
        fileContentTypeMap.put("mhtml","message/rfc822");
        fileContentTypeMap.put("mi","application/x-mi");
        fileContentTypeMap.put("mid","audio/mid");
        fileContentTypeMap.put("midi","audio/mid");
        fileContentTypeMap.put("mil","application/x-mil");
        fileContentTypeMap.put("mml","text/xml");
        fileContentTypeMap.put("mnd","audio/x-musicnet-download");
        fileContentTypeMap.put("mns","audio/x-musicnet-stream");
        fileContentTypeMap.put("mocha","application/x-javascript");
        fileContentTypeMap.put("movie","video/x-sgi-movie");
        fileContentTypeMap.put("mp1","audio/mp1");
        fileContentTypeMap.put("mp2","audio/mp2");
        fileContentTypeMap.put("mp2v","video/mpeg");
        fileContentTypeMap.put("mp3","audio/mp3");
        fileContentTypeMap.put("mp4","video/mpeg4");
        fileContentTypeMap.put("mpa","video/x-mpg");
        fileContentTypeMap.put("mpd","application/vnd.ms-project");
        fileContentTypeMap.put("mpe","video/x-mpeg");
        fileContentTypeMap.put("mpeg","video/mpg");
        fileContentTypeMap.put("mpg","video/mpg");
        fileContentTypeMap.put("mpga","audio/rn-mpeg");
        fileContentTypeMap.put("mpp","application/vnd.ms-project");
        fileContentTypeMap.put("mps","video/x-mpeg");
        fileContentTypeMap.put("mpt","application/vnd.ms-project");
        fileContentTypeMap.put("mpv","video/mpg");
        fileContentTypeMap.put("mpv2","video/mpeg");
        fileContentTypeMap.put("mpw","application/vnd.ms-project");
        fileContentTypeMap.put("mpx","application/vnd.ms-project");
        fileContentTypeMap.put("mtx","text/xml");
        fileContentTypeMap.put("mxp","application/x-mmxp");
        fileContentTypeMap.put("net","image/pnetvue");
        fileContentTypeMap.put("nrf","application/x-nrf");
        fileContentTypeMap.put("nws","message/rfc822");
        fileContentTypeMap.put("odc","text/x-ms-odc");
        fileContentTypeMap.put("out","application/x-out");
        fileContentTypeMap.put("p10","application/pkcs10");
        fileContentTypeMap.put("p12","application/x-pkcs12");
        fileContentTypeMap.put("p7b","application/x-pkcs7-certificates");
        fileContentTypeMap.put("p7c","application/pkcs7-mime");
        fileContentTypeMap.put("p7m","application/pkcs7-mime");
        fileContentTypeMap.put("p7r","application/x-pkcs7-certreqresp");
        fileContentTypeMap.put("p7s","application/pkcs7-signature");
        fileContentTypeMap.put("pc5","application/x-pc5");
        fileContentTypeMap.put("pci","application/x-pci");
        fileContentTypeMap.put("pcl","application/x-pcl");
        fileContentTypeMap.put("pcx","application/x-pcx");
        fileContentTypeMap.put("pdf","application/pdf");
        fileContentTypeMap.put("pdf","application/pdf");
        fileContentTypeMap.put("pdx","application/vnd.adobe.pdx");
        fileContentTypeMap.put("pfx","application/x-pkcs12");
        fileContentTypeMap.put("pgl","application/x-pgl");
        fileContentTypeMap.put("pic","application/x-pic");
        fileContentTypeMap.put("pko","application/vnd.ms-pki.pko");
        fileContentTypeMap.put("pl","application/x-perl");
        fileContentTypeMap.put("plg","text/html");
        fileContentTypeMap.put("pls","audio/scpls");
        fileContentTypeMap.put("plt","application/x-plt");
        fileContentTypeMap.put("png","image/png");
        fileContentTypeMap.put("png","application/x-png");
        fileContentTypeMap.put("pot","application/vnd.ms-powerpoint");
        fileContentTypeMap.put("ppa","application/vnd.ms-powerpoint");
        fileContentTypeMap.put("ppm","application/x-ppm");
        fileContentTypeMap.put("pps","application/vnd.ms-powerpoint");
        fileContentTypeMap.put("ppt","application/vnd.ms-powerpoint");
        fileContentTypeMap.put("ppt","application/x-ppt");
        fileContentTypeMap.put("pr","application/x-pr");
        fileContentTypeMap.put("prf","application/pics-rules");
        fileContentTypeMap.put("prn","application/x-prn");
        fileContentTypeMap.put("prt","application/x-prt");
        fileContentTypeMap.put("ps","application/x-ps");
        fileContentTypeMap.put("ps","application/postscript");
        fileContentTypeMap.put("ptn","application/x-ptn");
        fileContentTypeMap.put("pwz","application/vnd.ms-powerpoint");
        fileContentTypeMap.put("r3t","text/vnd.rn-realtext3d");
        fileContentTypeMap.put("ra","audio/vnd.rn-realaudio");
        fileContentTypeMap.put("ram","audio/x-pn-realaudio");
        fileContentTypeMap.put("ras","application/x-ras");
        fileContentTypeMap.put("rat","application/rat-file");
        fileContentTypeMap.put("rdf","text/xml");
        fileContentTypeMap.put("rec","application/vnd.rn-recording");
        fileContentTypeMap.put("red","application/x-red");
        fileContentTypeMap.put("rgb","application/x-rgb");
        fileContentTypeMap.put("rjs","application/vnd.rn-realsystem-rjs");
        fileContentTypeMap.put("rjt","application/vnd.rn-realsystem-rjt");
        fileContentTypeMap.put("rlc","application/x-rlc");
        fileContentTypeMap.put("rle","application/x-rle");
        fileContentTypeMap.put("rm","application/vnd.rn-realmedia");
        fileContentTypeMap.put("rmf","application/vnd.adobe.rmf");
        fileContentTypeMap.put("rmi","audio/mid");
        fileContentTypeMap.put("rmj","application/vnd.rn-realsystem-rmj");
        fileContentTypeMap.put("rmm","audio/x-pn-realaudio");
        fileContentTypeMap.put("rmp","application/vnd.rn-rn_music_package");
        fileContentTypeMap.put("rms","application/vnd.rn-realmedia-secure");
        fileContentTypeMap.put("rmvb","application/vnd.rn-realmedia-vbr");
        fileContentTypeMap.put("rmx","application/vnd.rn-realsystem-rmx");
        fileContentTypeMap.put("rnx","application/vnd.rn-realplayer");
        fileContentTypeMap.put("rp","image/vnd.rn-realpix");
        fileContentTypeMap.put("rpm","audio/x-pn-realaudio-plugin");
        fileContentTypeMap.put("rsml","application/vnd.rn-rsml");
        fileContentTypeMap.put("rt","text/vnd.rn-realtext");
        fileContentTypeMap.put("rtf","application/msword");
        fileContentTypeMap.put("rtf","application/x-rtf");
        fileContentTypeMap.put("rv","video/vnd.rn-realvideo");
        fileContentTypeMap.put("sam","application/x-sam");
        fileContentTypeMap.put("sat","application/x-sat");
        fileContentTypeMap.put("sdp","application/sdp");
        fileContentTypeMap.put("sdw","application/x-sdw");
        fileContentTypeMap.put("sit","application/x-stuffit");
        fileContentTypeMap.put("slb","application/x-slb");
        fileContentTypeMap.put("sld","application/x-sld");
        fileContentTypeMap.put("slk","drawing/x-slk");
        fileContentTypeMap.put("smi","application/smil");
        fileContentTypeMap.put("smil","application/smil");
        fileContentTypeMap.put("smk","application/x-smk");
        fileContentTypeMap.put("snd","audio/basic");
        fileContentTypeMap.put("sol","text/plain");
        fileContentTypeMap.put("sor","text/plain");
        fileContentTypeMap.put("spc","application/x-pkcs7-certificates");
        fileContentTypeMap.put("spl","application/futuresplash");
        fileContentTypeMap.put("spp","text/xml");
        fileContentTypeMap.put("ssm","application/streamingmedia");
        fileContentTypeMap.put("sst","application/vnd.ms-pki.certstore");
        fileContentTypeMap.put("stl","application/vnd.ms-pki.stl");
        fileContentTypeMap.put("stm","text/html");
        fileContentTypeMap.put("sty","application/x-sty");
        fileContentTypeMap.put("svg","text/xml");
        fileContentTypeMap.put("swf","application/x-shockwave-flash");
        fileContentTypeMap.put("tdf","application/x-tdf");
        fileContentTypeMap.put("tg4","application/x-tg4");
        fileContentTypeMap.put("tga","application/x-tga");
        fileContentTypeMap.put("tif","image/tiff");
        fileContentTypeMap.put("tif","application/x-tif");
        fileContentTypeMap.put("tiff","image/tiff");
        fileContentTypeMap.put("tld","text/xml");
        fileContentTypeMap.put("top","drawing/x-top");
        fileContentTypeMap.put("torrent","application/x-bittorrent");
        fileContentTypeMap.put("tsd","text/xml");
        fileContentTypeMap.put("txt","text/plain");
        fileContentTypeMap.put("uin","application/x-icq");
        fileContentTypeMap.put("uls","text/iuls");
        fileContentTypeMap.put("vcf","text/x-vcard");
        fileContentTypeMap.put("vda","application/x-vda");
        fileContentTypeMap.put("vdx","application/vnd.visio");
        fileContentTypeMap.put("vml","text/xml");
        fileContentTypeMap.put("vpg","application/x-vpeg005");
        fileContentTypeMap.put("vsd","application/vnd.visio");
        fileContentTypeMap.put("vsd","application/x-vsd");
        fileContentTypeMap.put("vss","application/vnd.visio");
        fileContentTypeMap.put("vst","application/vnd.visio");
        fileContentTypeMap.put("vst","application/x-vst");
        fileContentTypeMap.put("vsw","application/vnd.visio");
        fileContentTypeMap.put("vsx","application/vnd.visio");
        fileContentTypeMap.put("vtx","application/vnd.visio");
        fileContentTypeMap.put("vxml","text/xml");
        fileContentTypeMap.put("wav","audio/wav");
        fileContentTypeMap.put("wax","audio/x-ms-wax");
        fileContentTypeMap.put("wb1","application/x-wb1");
        fileContentTypeMap.put("wb2","application/x-wb2");
        fileContentTypeMap.put("wb3","application/x-wb3");
        fileContentTypeMap.put("wbmp","image/vnd.wap.wbmp");
        fileContentTypeMap.put("wiz","application/msword");
        fileContentTypeMap.put("wk3","application/x-wk3");
        fileContentTypeMap.put("wk4","application/x-wk4");
        fileContentTypeMap.put("wkq","application/x-wkq");
        fileContentTypeMap.put("wks","application/x-wks");
        fileContentTypeMap.put("wm","video/x-ms-wm");
        fileContentTypeMap.put("wma","audio/x-ms-wma");
        fileContentTypeMap.put("wmd","application/x-ms-wmd");
        fileContentTypeMap.put("wmf","application/x-wmf");
        fileContentTypeMap.put("wml","text/vnd.wap.wml");
        fileContentTypeMap.put("wmv","video/x-ms-wmv");
        fileContentTypeMap.put("wmx","video/x-ms-wmx");
        fileContentTypeMap.put("wmz","application/x-ms-wmz");
        fileContentTypeMap.put("wp6","application/x-wp6");
        fileContentTypeMap.put("wpd","application/x-wpd");
        fileContentTypeMap.put("wpg","application/x-wpg");
        fileContentTypeMap.put("wpl","application/vnd.ms-wpl");
        fileContentTypeMap.put("wq1","application/x-wq1");
        fileContentTypeMap.put("wr1","application/x-wr1");
        fileContentTypeMap.put("wri","application/x-wri");
        fileContentTypeMap.put("wrk","application/x-wrk");
        fileContentTypeMap.put("ws","application/x-ws");
        fileContentTypeMap.put("ws2","application/x-ws");
        fileContentTypeMap.put("wsc","text/scriptlet");
        fileContentTypeMap.put("wsdl","text/xml");
        fileContentTypeMap.put("wvx","video/x-ms-wvx");
        fileContentTypeMap.put("xdp","application/vnd.adobe.xdp");
        fileContentTypeMap.put("xdr","text/xml");
        fileContentTypeMap.put("xfd","application/vnd.adobe.xfd");
        fileContentTypeMap.put("xfdf","application/vnd.adobe.xfdf");
        fileContentTypeMap.put("xhtml","text/html");
        fileContentTypeMap.put("xls","application/vnd.ms-excel");
        fileContentTypeMap.put("xls","application/x-xls");
        fileContentTypeMap.put("xlw","application/x-xlw");
        fileContentTypeMap.put("xml","text/xml");
        fileContentTypeMap.put("xpl","audio/scpls");
        fileContentTypeMap.put("xq","text/xml");
        fileContentTypeMap.put("xql","text/xml");
        fileContentTypeMap.put("xquery","text/xml");
        fileContentTypeMap.put("xsd","text/xml");
        fileContentTypeMap.put("xsl","text/xml");
        fileContentTypeMap.put("xslt","text/xml");
        fileContentTypeMap.put("xwd","application/x-xwd");
        fileContentTypeMap.put("x_b","application/x-x_b");
        fileContentTypeMap.put("x_t","application/x-x_t");
    }
}
