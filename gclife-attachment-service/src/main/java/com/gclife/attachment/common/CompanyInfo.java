package com.gclife.attachment.common;

import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.common.util.AssertUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.*;

/**
 * <AUTHOR>
 * @date 8/22/2022
 */
public class CompanyInfo {
    /**
     * 公司名称 英文
     */
    public static final String COMPANY_NAME_EN_US = "GC Life Insurance PLC.";
    /**
     * 公司名称 柬文
     */
    public static final String COMPANY_NAME_KM_KH = "ជីស៊ី ឡាយហ្វ៍ អ៊ិនសួរេន ម.ក";
    /**
     * 公司名称 中文
     */
    public static final String COMPANY_NAME_ZH_CN = "大中华人寿保险股份有限公司";
    /**
     * 公司名称 英文
     */
    public static final String COMPANY_ADDRESS_EN_US = "Building C, Street 169, Sangkat Veal Vong, Khan 7 Makara, Phnom Penh";
    /**
     * 公司名称 柬文
     */
    public static final String COMPANY_ADDRESS_KM_KH = "អគារ C ផ្លូវលេខ ១៦៩ សង្កាត់វាលវង់ ខណ្ឌ៧មករា រាជធានីភ្នំពេញ";
    /**
     * 公司名称 中文
     */
    public static final String COMPANY_ADDRESS_ZH_CN = "C栋大楼, 169号路, 威旺分区, 玛卡拉区, 金边市";
    /**
     * 公司电话
     */
    public static final String COMPANY_TELEPHONE = "*********** / ***********";
    /**
     * 公司网址
     */
    public static final String COMPANY_WEBSITE = "www.gc-life.com.kh";
    /**
     * 公司Logo
     */
    public static final String COMPANY_LOGO = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/Logo.png";
    /**
     * 公司Logo 长版
     */
    public static final String COMPANY_LOGO_LONG = "https://gclife-hk-release.oss-cn-hongkong.aliyuncs.com/gclife/image/printPdfImage/220822/Logo_Long.png";

    /**
     * 获取公司基础信息
     *
     * @param language
     * @return
     */
    public static Map<String, String> getCompanyBaseInfoMap(String language) {
        Map<String, String> companyBaseInfoMap = new HashMap<>();
        // 公司名称 公司地址
        if (EN_US.name().equals(language)) {
            companyBaseInfoMap.put("companyBaseName", COMPANY_NAME_EN_US);
            companyBaseInfoMap.put("companyBaseAddress", COMPANY_ADDRESS_EN_US);
        }
        if (KM_KH.name().equals(language)) {
            companyBaseInfoMap.put("companyBaseName", COMPANY_NAME_KM_KH);
            companyBaseInfoMap.put("companyBaseAddress", COMPANY_ADDRESS_KM_KH);
        }
        if (ZH_CN.name().equals(language)) {
            companyBaseInfoMap.put("companyBaseName", COMPANY_NAME_ZH_CN);
            companyBaseInfoMap.put("companyBaseAddress", COMPANY_ADDRESS_ZH_CN);
        }
        // 公司电话
        companyBaseInfoMap.put("companyBaseTelephone", COMPANY_TELEPHONE);
        // 公司网址
        companyBaseInfoMap.put("companyBaseWebsite", COMPANY_WEBSITE);
        // 公司logo
        companyBaseInfoMap.put("companyBaseLogo", COMPANY_LOGO);
        companyBaseInfoMap.put("companyBaseLogoLong", COMPANY_LOGO_LONG);
        // CEO签名 + 公司印章
        companyBaseInfoMap.put("signSealCEOPicture", PrintCommon.SIGN_SEAL_CEO);
        // 公司印章
        companyBaseInfoMap.put("companySealPicture", PrintCommon.Company_Stamp);
        return companyBaseInfoMap;
    }

    /**
     * 获取公司基础信息
     *
     * @param language
     * @return
     */
    public static List<PrintObject> getCompanyBaseInfoList(String language) {
        List<PrintObject> printObjectList = new ArrayList<>();
        Map<String, String> companyBaseInfoMap = getCompanyBaseInfoMap(language);
        if (AssertUtils.isNotEmpty(companyBaseInfoMap)) {
            for (String key : companyBaseInfoMap.keySet()) {
                printObjectList.add(new PrintObject(key, companyBaseInfoMap.get(key)));
            }
        }
        return printObjectList;
    }
}
