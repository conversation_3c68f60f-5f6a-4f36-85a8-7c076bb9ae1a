package com.gclife.attachment.common.xml.entity;

import java.util.Date;

/**
 * 电脑类
 *
 * <AUTHOR>
 *
 */
public class Computer {

    // 序列号
    private String serialNumber;
    // 品牌名
    private String brandName;
    // 生成日期
    private Date productDate;
    // 价格
    private Double price;

    public Computer() {
        super();
    }

    public Computer(String serialNumber, String brandName, Date productDate, Double price) {
        this.serialNumber = serialNumber;
        this.brandName = brandName;
        this.productDate = productDate;
        this.price = price;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Date getProductDate() {
        return productDate;
    }

    public void setProductDate(Date productDate) {
        this.productDate = productDate;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }
}
