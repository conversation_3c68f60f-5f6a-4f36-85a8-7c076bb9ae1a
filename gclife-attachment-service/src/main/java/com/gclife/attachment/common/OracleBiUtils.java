package com.gclife.attachment.common;

import com.gclife.attachment.model.feign.FileConversionRequest;
import com.gclife.common.util.FileUtils;
import com.gclife.common.util.http.HttpUtils;
import oracle.xdo.template.FOProcessor;
import oracle.xdo.template.RTFProcessor;
import org.dom4j.Document;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.gclife.attachment.common.xml.XmlUtil;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:32 2019/1/21
 * @ Description:
 * @ Modified By:
 * @ Version: $version
 */
public class OracleBiUtils {

    public static void main(String[] args) throws Exception {
        // xml 数据
        Map<String, Object> stringStringMap = new HashMap<>();
        stringStringMap.put("applicationNo", "A1336584976522366");
        stringStringMap.put("no", "000001");
        stringStringMap.put("date", "03 DEC 2018");
        stringStringMap.put("applicationName", "白忠英");
        stringStringMap.put("modeOfPayment", "Semi-Annual ");
        stringStringMap.put("paymentMethod", "Wing Offline");
        stringStringMap.put("installmentNo", "3rd Installment");

        List<Map<String, String>> mapList = new ArrayList<>();

        Map<String, String> map8 = new HashMap<>();
        map8.put("productCode", "#8");
        map8.put("productName", "Good Future Insurance");
        map8.put("paymentItem", "Insurance Premium");
        map8.put("paymentAmount", "$82.22");
        mapList.add(map8);

        Map<String, String> map4 = new HashMap<>();
        map4.put("productCode", "#4");
        map4.put("productName", "Critical Illness Rider");
        map4.put("paymentItem", "Insurance Premium");
        map4.put("paymentAmount", "$52.20");
        mapList.add(map4);

        Map<String, String> map1 = new HashMap<>();
        map1.put("productCode", "#1");
        map1.put("productName", "Life Shield Insurance");
        map1.put("paymentItem", "--------");
        map1.put("paymentAmount", "$116.00");
        mapList.add(map1);

        Map<String, String> map7 = new HashMap<>();
        map7.put("productCode", "#7");
        map7.put("productName", "Accidental Injury Hospitalization Allowance Rider");
        map7.put("paymentItem", "--------");
        map7.put("paymentAmount", "$36.00");
        mapList.add(map7);

        Map<String, String> map3 = new HashMap<>();
        map3.put("productCode", "#3");
        map3.put("productName", "Offspring Prosperity");
        map3.put("paymentItem", "Insurance Premium");
        map3.put("paymentAmount", "$1,012.00");
        mapList.add(map3);

        stringStringMap.put("coverageList", mapList);
        stringStringMap.put("total", "$1,298.42");

        stringStringMap.put("agentCode", "#200126");
        stringStringMap.put("agentName", "ធានារ៉ាប់រងបន្ថែមសម្រាប់ប្រាក់BaiZhong$Yingឧបត្ថម្ភសម្រាក白忠英នៅមន្ទីរពេទ្យដោយគ្រោះថ្នាក់ជាយថាហេតុ");

        stringStringMap.put("A", 0);
        stringStringMap.put("B", 1);
        stringStringMap.put("C", 2);


        Document document = XmlUtil.map2xml(stringStringMap, "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        // rtf 模版
        InputStream inputStream = new FileInputStream("/Users/<USER>/Downloads/POLICY_GC_ENRICH_LIFE_APPLY_ZH_CN.rtf");
        byte[] rtfTemplateBytes = new byte[inputStream.available()];
        inputStream.read(rtfTemplateBytes);
        inputStream.close();

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = refToXsl(rtfTemplateBytes);

        //xsl 模版添加数据返回 rtf
        byte[] rtfBytes = refSaveData(refToXsl, xmlBytes, FOProcessor.FORMAT_RTF);

        //rtf 转pdf oracle
//        byte[] pdfBytes = FileUtils.fileConversion(rtfBytes, DocumentType.RTF, DocumentType.PDF);

        FileConversionRequest fileConversionRequest = new FileConversionRequest();
        fileConversionRequest.setByteFile(rtfBytes);

        byte[] forObject = HttpUtils.getInstance().post(  "http://192.168.11.6:22800/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();

        FileOutputStream fileOutputStream = new FileOutputStream("/Users/<USER>/Downloads/POLICY_GC_ENRICH_LIFE_APPLY_ZH_CN.pdf");
        fileOutputStream.write(forObject);
        fileOutputStream.flush();
        fileOutputStream.close();


    }


    /**
     * 配置 废弃
     */
    public static final String XML_CONFIG = "res" + File.separator + "cnf" + File.separator + "TEST_CFG.xml";


    /**
     * 将数据和 RTF 模板转换为 PDF 文件。
     * <p>
     * 把 Map 数据转为 XML，RTF 模板转为 XSL，再结合生成 RTF 文件，最后通过远程服务转为 PDF。
     *
     * @param ip               远程转换服务的地址
     * @param stringObjectMap  要填充的数据（键值对）
     * @param rtfTemplateBytes RTF 模板文件的字节
     * @return                PDF 文件的字节
     * @throws Exception      如果转换或请求失败，抛出异常
     */
    public static byte[] mapDateAndRtfToPdf(String ip, Map<String, Object> stringObjectMap, byte[] rtfTemplateBytes) throws Exception {
        // 将 Map 转换为 XML 文档
        Document document = XmlUtil.map2xml(stringObjectMap, "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = refToXsl(rtfTemplateBytes);

        //xsl 模版添加数据返回 rtf
        byte[] rtfBytes = refSaveData(refToXsl, xmlBytes, FOProcessor.FORMAT_RTF);

        // 将 RTF 文件转换为 PDF
        FileConversionRequest fileConversionRequest = new FileConversionRequest();
        fileConversionRequest.setByteFile(rtfBytes);

        //通过 HTTP 请求将 RTF 转换为 PDF
        return HttpUtils.getInstance().post(ip + "/file/conversion/single").setParameterJson(fileConversionRequest).execute().getByteArray();
    }


    /**
     * xsl模版 添加数据 生成 rtf
     *
     * @param xsl
     * @param xml
     * @return
     * @throws Exception
     */
    public static byte[] refSaveData(byte[] xsl, byte[] xml, byte format) throws Exception {
        ByteArrayOutputStream returnByteArrayOutputStream = new ByteArrayOutputStream();
        //FOProcessor 添加 模版.xsl 和 配置.xml(语言) 和 数据.xml 生成pdf
        FOProcessor processor = new FOProcessor();
        //xml 配置
//        processor.setConfig(XML_CONFIG);
        //xml 数据
        InputStream xmlInputStream = new ByteArrayInputStream(xml);
        processor.setData(xmlInputStream);
        //xsl 模版
        InputStream rtfInputStream = new ByteArrayInputStream(xsl);
        processor.setTemplate(rtfInputStream);
        //产生pdf输出流
        processor.setOutput(returnByteArrayOutputStream);
        processor.setOutputFormat(format);
        // 输出打印文件
        processor.generate();
        return returnByteArrayOutputStream.toByteArray();
    }

    /**
     * rtf 转 xsl
     *
     * @param rtf
     * @return
     * @throws Exception
     */
    public static byte[] refToXsl(byte[] rtf) throws Exception {
        //读取 rtf 模版 转为 xsl模版
        InputStream input = new ByteArrayInputStream(rtf);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        RTFProcessor rtfProcessor = new RTFProcessor(input); //input template
        rtfProcessor.setOutput(byteArrayOutputStream);  // output file
        rtfProcessor.process();

        return byteArrayOutputStream.toByteArray();
    }

    /**
     * rtf 转 xsl
     *
     * @param rtf
     * @return
     * @throws Exception
     */
    public static byte[] refTo(byte[] rtf, Map<String, Object> stringObjectMap, byte FOProcessorFormat) throws Exception {

        Document document = XmlUtil.map2xml(stringObjectMap, "root");
        byte[] xmlBytes = XmlUtil.formatXml(document);

        //rtf 模版转 xsl 模版 refToXsl
        byte[] refToXsl = refToXsl(rtf);

        //xsl 模版添加数据返回 附件
        return refSaveData(refToXsl, xmlBytes, FOProcessorFormat);
    }

}
