package com.gclife.attachment.service.print.claim;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.policy.claim.PrintCoverageDutyBo;
import com.gclife.attachment.model.policy.claim.PrintReportCustomerBo;
import com.gclife.attachment.model.policy.endorse.AddSubtractInsuredPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.util.AssertUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gclife.common.ProductConfigEnum.PRODUCT_MAIN_PRODUCT_FLAG.MAIN;
import static com.gclife.common.model.config.TerminologyConfigEnum.WHETHER.NO;

/**
 * <AUTHOR>
 * @description
 * @date 2019/9/17 8:27 下午
 */
@Component
public class ClaimData {


    public Map<String, Object> getData(PrintReportCustomerBo printReportCustomerBo) {
        Map<String, Object> map = new HashMap<>();
        map.put("customerName", PrintCommon.getPrintString(printReportCustomerBo.getCustomerName(), 3));
        map.put("applicantName", PrintCommon.getPrintString(printReportCustomerBo.getApplicantName(), 3));
        map.put("policyNo", PrintCommon.getPrintString(printReportCustomerBo.getPolicyNo(), 3));
        String contractTermination = AssertUtils.isNotEmpty(printReportCustomerBo.getContractTermination()) ? printReportCustomerBo.getContractTermination() : NO.name();
        map.put("contractTermination", PrintCommon.getPrintString(contractTermination, 3));
        PrintCommon.setPrintDateTime(map, "acceptDate", printReportCustomerBo.getAcceptDate(),3);
        PrintCommon.setPrintDateTime(map, "printDate", printReportCustomerBo.getSigningBatchDate(),3);
        PrintCommon.setProductName(map,"Main",printReportCustomerBo.getProductId(),null);

        List<PrintCoverageDutyBo> printCoverageDutyList = printReportCustomerBo.getPrintCoverageDutyList();

        List<Map<String, Object>> coverageListMap = new ArrayList<>();
        printCoverageDutyList.forEach(printCoverageDutyBo -> {
            Map<String, Object> coverageMap = new HashMap<>();
            coverageMap.put("productDutyGetCodeName", PrintCommon.getPrintString(printCoverageDutyBo.getProductDutyGetCodeName(), 3));
            coverageMap.put("compensateAmount", PrintCommon.getPrintString(printCoverageDutyBo.getCompensateAmount(), 3));
            PrintCommon.setProductName(coverageMap,printCoverageDutyBo.getProductId());
            coverageListMap.add(coverageMap);
        });
        PrintCommon.coverageSort(coverageListMap);
        map.put("coverageListMap", coverageListMap);

        return map;
    }


}
