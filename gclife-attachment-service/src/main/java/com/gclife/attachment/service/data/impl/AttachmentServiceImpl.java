package com.gclife.attachment.service.data.impl;


import com.gclife.attachment.core.jooq.tables.daos.AttachmentDao;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.service.data.AttachmentService;
import com.gclife.common.exception.RequestException;
import com.gclife.common.service.impl.BaseServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.gclife.attachment.core.jooq.tables.Attachment.ATTACHMENT;


/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件保存处理类
 */

@Service
public class AttachmentServiceImpl extends BaseServiceImpl implements AttachmentService {


    @Autowired
    AttachmentDao attachmentDao;

    /**
     * 保存附件表
     *
     * @param attachmentPo 对应于attachment中的一条记录
     * @return 无返回值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAttachmentPo(AttachmentPo attachmentPo) {
        try {
            if(!AssertUtils.isNotEmpty(attachmentPo.getAttachmentId()) || attachmentPo.isForceSave()){
                //执行新增
                attachmentPo.setCreatedDate(DateUtils.getCurrentTime());
                if (!attachmentPo.isForceSave()) {
                    attachmentPo.setAttachmentId(UUIDUtils.getUUIDShort());
                }
                attachmentDao.insert(attachmentPo);
            }else{
                //执行修改
                attachmentDao.update(attachmentPo);
            }
        }catch (Exception e){
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_SAVE_ATTACHMENT_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_SAVE_ATTACHMENT_ERROR);
        }

    }

    @Override
    public void insertAttachmentPo(AttachmentPo attachmentPo) {
        if (AssertUtils.isNotNull(attachmentPo)) {
            attachmentPo.setCreatedDate(DateUtils.getCurrentTime());
            if (!attachmentPo.isForceSave()) {
                attachmentPo.setAttachmentId(UUIDUtils.getUUIDShort());
            }
            attachmentDao.insert(attachmentPo);
        }
    }

    @Override
    public void updateAttachmentPo(AttachmentPo attachmentPo) {
        if (AssertUtils.isNotNull(attachmentPo)) {
            attachmentDao.update(attachmentPo);
        }
    }

    /**
     * 批量保存附件表
     *
     * @param listAttachmentPo 对应于attachment中的多条记录
     * @return 无返回值
     */
    @Override
    public void saveAttachmentPo(List<AttachmentPo> listAttachmentPo) {
        try {
            for (AttachmentPo attachmentPo:listAttachmentPo){
                if(!AssertUtils.isNotEmpty(attachmentPo.getAttachmentId())){
                    //执行新增
                    attachmentPo.setCreatedDate(DateUtils.getCurrentTime());
                    attachmentPo.setAttachmentId(UUIDUtils.getUUIDShort());
                    attachmentDao.insert(attachmentPo);
                }else{
                    //执行修改
                    attachmentDao.update(attachmentPo);
                }
            }
        }catch (Exception e){
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_SAVE_ATTACHMENT_ERROR.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_SAVE_ATTACHMENT_ERROR);
        }
    }
}