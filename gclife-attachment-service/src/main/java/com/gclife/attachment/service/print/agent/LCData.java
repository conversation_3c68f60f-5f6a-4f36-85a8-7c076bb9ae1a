package com.gclife.attachment.service.print.agent;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.model.agent.*;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.platform.BranchBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.model.ResultObject;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.platform.api.PlatformAreaApi;
import com.gclife.platform.model.response.AreaNameResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/16
 */
@Component
public class LCData extends BaseBusinessServiceImpl {

    @Autowired
    private PlatformAreaApi platformAreaApi;
    @Autowired
    private AttachmentExtDao attachmentExtDao;

    /**
     * 封装寿险顾问电子合同数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     */
    public Map<String, Object> getContractData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        AgentBo agentBo = JSON.parseObject(content, AgentBo.class);

        Map<String, Object> map = new HashMap<>();
        AgentSignedBo agentSigned = agentBo.getAgentSigned();
        if (AssertUtils.isNotNull(agentSigned)) {
            // 签订时间
            PrintCommon.setPrintDateTime(map, "signDate", agentSigned.getHireDate(), 2);
        }
        // 业务员姓名
        map.put("agentName", PrintCommon.getPrintString(agentBo.getAgentName(), 2));

        AgentIdentityBo agentIdentity = agentBo.getAgentIdentity();
        if (AssertUtils.isNotNull(agentIdentity)) {
            // 证件号码
            map.put("idNo", PrintCommon.getPrintString(agentIdentity.getIdNo(), 2));
        }

        AgentDetailBo agentDetail = agentBo.getAgentDetail();
        if (AssertUtils.isNotNull(agentDetail)) {
            String agentAddress = null;
            // 地址拼接
            if (AssertUtils.isNotEmpty(agentDetail.getAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(agentDetail.getAreaCode(), language);
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    agentAddress = respFcResultObject.getData().getAreaName() + " " + agentDetail.getDetailAddress();
                }
            }
            // 居住地
            map.put("agentAddress", PrintCommon.getPrintString(agentAddress, 2));

            String agentSignatureAttachId = agentDetail.getNoWatermarkSignatureAttachId();
            String manageSignatureAttachId = agentDetail.getNoWatermarkManageSignatureAttachId();
            List<String> signatureAttachmentIds = Arrays.asList(agentSignatureAttachId, manageSignatureAttachId);
            List<AttachmentPo> attachmentPos = attachmentExtDao.listAttachmentPoByPk(signatureAttachmentIds);
            if (AssertUtils.isNotEmpty(attachmentPos)) {
                for (AttachmentPo attachmentPo : attachmentPos) {
                    // 动态域名 根据不同环境获取不同的OSS域名
                    String dynamicDomain = getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.");

                    String attachmentId = attachmentPo.getAttachmentId();
                    // 业务员签名
                    if (AssertUtils.isNotEmpty(agentSignatureAttachId) && agentSignatureAttachId.equals(attachmentId)) {
                        map.put("LCSignPicture", dynamicDomain + attachmentPo.getUrl());
                    }
                    // 管理人签名
                    if (AssertUtils.isNotEmpty(manageSignatureAttachId) && manageSignatureAttachId.equals(attachmentId)) {
                        map.put("witnessSignPicture", dynamicDomain + attachmentPo.getUrl());
                    }
                }
            }
        }
        AgentBo managerAgent = agentBo.getManagerAgent();
        if (AssertUtils.isNotNull(managerAgent)) {
            // 管理人姓名
            map.put("witnessName", PrintCommon.getPrintString(managerAgent.getAgentName(), 2));

            AgentLevelBo managerAgentLevel = managerAgent.getAgentLevel();
            if (AssertUtils.isNotNull(managerAgentLevel)) {
                // 管理人职位
                map.put("witnessPosition", PrintCommon.getPrintString(managerAgentLevel.getAgentLevelName(), 2));
            }
        }
        // 公司印章
        Boolean isShowCompanySeal = agentBo.getIsShowCompanySeal();// 是否审核标识
        map.put("isShowCompanySeal", isShowCompanySeal);

        if (AssertUtils.isNotNull(isShowCompanySeal) && isShowCompanySeal) {
            // 审核过后再展示公司印章
//            map.put("companySealPicture", PrintCommon.SIGN_KIM_PETI);
            map.put("companySealPicture", PrintCommon.SIGN_IAN_FA);
        }
        // 公司名称 公司地址
        map.put("companyBaseNameEN_US", CompanyInfo.COMPANY_NAME_EN_US);
        map.put("companyBaseAddressEN_US", CompanyInfo.COMPANY_ADDRESS_EN_US);
        map.put("companyBaseNameKM_KH", CompanyInfo.COMPANY_NAME_KM_KH);
        map.put("companyBaseAddressKM_KH", CompanyInfo.COMPANY_ADDRESS_KM_KH);
        map.put("companyBaseNameZH_CN", CompanyInfo.COMPANY_NAME_ZH_CN);
        map.put("companyBaseAddressZH_CN", CompanyInfo.COMPANY_ADDRESS_ZH_CN);
        return map;
    }

    /**
     * 封装寿险顾问申请表数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     */
    public Map<String, Object> getApplicantFormData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        AgentBo agentBo = JSON.parseObject(content, AgentBo.class);

        Map<String, Object> map = new HashMap<>();
        // 业务员姓名
        map.put("agentName", PrintCommon.getPrintString(agentBo.getAgentName(), 2));

        AgentDetailBo agentDetail = agentBo.getAgentDetail();
        if (AssertUtils.isNotNull(agentDetail)) {
            // 中文拼音或英文名
            map.put("agentPinyin", PrintCommon.getPrintString(agentDetail.getPinyin(), 2));
            // 出生日期
            PrintCommon.setPrintDateTime(map, "birthdayDate", agentDetail.getBirthday(), 2);
            // 国籍
            String nationalityName = agentDetail.getNationalityName();
            map.put("nationalityName", PrintCommon.getPrintString(nationalityName, 2));
            // 性别
            PrintCommon.setSelectionBox(map, "agentSex" + agentDetail.getSex(), agentDetail.getSex());
            // 婚姻状况
            String marriage = agentDetail.getMarriage();
            PrintCommon.setSelectionBox(map, "agentMarriage" + marriage, marriage);
            // 学历
            String degree = agentDetail.getDegree();
            // 除了其它学历是其它选项外，这些学历也视为其它：小学-PRIMARY_SCHOOL 初中-ELEMENTARY_SCHOOL 博士-PHD
            if ("PRIMARY_SCHOOL".equals(degree) || "ELEMENTARY_SCHOOL".equals(degree) || "PHD".equals(degree)) {
                degree = "OTHER";
            }
            map.put("agentDegree", PrintCommon.getPrintString(degree, 2));
            // 其它学历名称
            String otherDegreeName = agentDetail.getDegreeName();
            if ("OTHER".equals(agentDetail.getDegree())) {
                otherDegreeName = agentDetail.getOtherDegree();
            }
            map.put("agentDegreeName", PrintCommon.getPrintString(otherDegreeName, 2));
            // 手机号码
            map.put("agentMobile", PrintCommon.getPrintString(agentDetail.getMobile(), 2));
            // 邮箱
            map.put("agentEmail", PrintCommon.getPrintString(agentDetail.getEmail(), 2));

            String agentAddress = null;
            // 地址拼接
            if (AssertUtils.isNotEmpty(agentDetail.getAreaCode())) {
                ResultObject<AreaNameResponse> respFcResultObject = platformAreaApi.areaNameGet(agentDetail.getAreaCode(), language);
                if (!AssertUtils.isResultObjectDataNull(respFcResultObject) && AssertUtils.isNotEmpty(respFcResultObject.getData().getAreaName())) {
                    agentAddress = respFcResultObject.getData().getAreaName() + " " + agentDetail.getDetailAddress();
                }
            }
            // 居住地
            map.put("agentAddress", agentAddress);
            // 国籍非柬埔寨 视为外籍
            map.put("agentIsForeigner", PrintCommon.getPrintString(!"CAMBODIA".equals(agentDetail.getNationality()), 2));
            // 劳工卡号码
            map.put("agentEmploymentCardNo", PrintCommon.getPrintString(agentDetail.getEmploymentCardNo(), 2));
            // 劳工卡号码有效日期
            PrintCommon.setPrintDateTime(map, "agentEmploymentCardExpDate", agentDetail.getEmploymentCardExpDate(), 2);

            // 个人照片
            String personalPhotosAttachmentId = agentDetail.getPersonalPhotosAttachmentId();
            // 签名
            String agentSignatureAttachId = agentDetail.getNoWatermarkSignatureAttachId();
            String manageSignatureAttachId = agentDetail.getNoWatermarkManageSignatureAttachId();
            List<String> signatureAttachmentIds = Arrays.asList(personalPhotosAttachmentId, agentSignatureAttachId, manageSignatureAttachId);
            List<AttachmentPo> attachmentPos = attachmentExtDao.listAttachmentPoByPk(signatureAttachmentIds);
            if (AssertUtils.isNotEmpty(attachmentPos)) {
                for (AttachmentPo attachmentPo : attachmentPos) {
                    // 动态域名 根据不同环境获取不同的OSS域名
                    String dynamicDomain = getConfigValue(attachmentPo.getAttachmentDomain(), "oss_config.domain.");

                    String attachmentId = attachmentPo.getAttachmentId();
                    // 业务员签名
                    if (AssertUtils.isNotEmpty(agentSignatureAttachId) && agentSignatureAttachId.equals(attachmentId)) {
                        map.put("LCSignPicture", dynamicDomain + attachmentPo.getUrl());
                    }
                    // 管理人签名
                    if (AssertUtils.isNotEmpty(manageSignatureAttachId) && manageSignatureAttachId.equals(attachmentId)) {
                        map.put("witnessSignPicture", dynamicDomain + attachmentPo.getUrl());
                    }
                    // 个人照片
                    if (AssertUtils.isNotEmpty(personalPhotosAttachmentId) && personalPhotosAttachmentId.equals(attachmentId)) {
                        map.put("agentPersonalPhotos", dynamicDomain + attachmentPo.getUrl());
                    }
                }
            }
        }

        // 证件信息
        AgentIdentityBo agentIdentity = agentBo.getAgentIdentity();
        if (AssertUtils.isNotNull(agentIdentity)) {
            // 证件类型
            map.put("agentIdType", PrintCommon.getPrintString(agentIdentity.getIdTypeName(), 2));
            // 证件号码
            map.put("agentIdNo", PrintCommon.getPrintString(agentIdentity.getIdNo(), 2));
            // 证件有效期
            PrintCommon.setPrintDateTime(map, "agentIdExpDate", agentDetail.getIdExpDate(), 2);
        }

        // 个人银行信息
        AgentBankBo agentBank = agentBo.getAgentBank();
        if (AssertUtils.isNotNull(agentBank)) {
            // 银行名称
            map.put("agentBankName", PrintCommon.getPrintString(agentBank.getBankName(), 2));
            // 银行卡号码
            map.put("agentBankNo", PrintCommon.getPrintString(agentBank.getAccountNo(), 2));
        }

        // 工作经历
        List<AgentWorkExperienceBo> agentWorkExperienceList = agentBo.getAgentWorkExperienceList();
        List<Map<String, Object>> agentWorkExperienceMaps = new ArrayList<>();
        if (AssertUtils.isNotEmpty(agentWorkExperienceList)) {
            agentWorkExperienceMaps = agentWorkExperienceList.stream()
                    .sorted(Comparator.comparing(AgentWorkExperienceBo::getIndex))
                    .map(agentWorkExperienceBo -> {
                        Map<String, Object> hashMap = new HashMap<>();
                        // 公司名称
                        String companyName = agentWorkExperienceBo.getCompanyName();
                        hashMap.put("workExperienceCompanyName", PrintCommon.getPrintString(companyName, 2));
                        // 工资
                        String salary = agentWorkExperienceBo.getSalary();
                        hashMap.put("workExperienceSalary", PrintCommon.decimalFormat.format(new BigDecimal(salary)));
                        // 职位
                        String position = agentWorkExperienceBo.getPosition();
                        hashMap.put("workExperiencePosition", PrintCommon.getPrintString(position, 2));
                        // 就职开始时间
                        Long employmentPeriodStartDate = agentWorkExperienceBo.getEmploymentPeriodStartDate();
                        PrintCommon.setPrintDateTime(hashMap, "workExperiencePeriodStartDate", employmentPeriodStartDate, 2);
                        // 就职结束时间
                        Long employmentPeriodEndDate = agentWorkExperienceBo.getEmploymentPeriodEndDate();
                        PrintCommon.setPrintDateTime(hashMap, "workExperiencePeriodEndDate", employmentPeriodEndDate, 2);
                        return hashMap;
                    }).collect(Collectors.toList());
        } else {// 工作经历是空则展示 -  - 横杆
            Map<String, Object> hashMap = new HashMap<>();
            // 公司名称
            hashMap.put("workExperienceCompanyName", "-  -");
            // 工资
            hashMap.put("workExperienceSalary", "-  -");
            // 职位
            hashMap.put("workExperiencePosition", "-  -");
            // 就职开始时间
            PrintCommon.setPrintDateTime(hashMap, "workExperiencePeriodStartDate", null, 2);
            // 就职结束时间
            PrintCommon.setPrintDateTime(hashMap, "workExperiencePeriodEndDate", null, 2);
            agentWorkExperienceMaps.add(hashMap);
        }
        map.put("agentWorkExperienceMaps", agentWorkExperienceMaps);
        // 紧急联络信息
        AgentEmergencyContactBo agentEmergencyContact = agentBo.getAgentEmergencyContact();
        if (AssertUtils.isNotNull(agentEmergencyContact)) {
            // 拼音
            map.put("emergencyPinyin", PrintCommon.getPrintString(agentEmergencyContact.getPinyin(), 2));
            // 姓名
            map.put("emergencyName", PrintCommon.getPrintString(agentEmergencyContact.getName(), 2));
            // 性别
            map.put("emergencySex", PrintCommon.getPrintString(agentEmergencyContact.getSex(), 2));
            // 证件类型
            map.put("emergencyIdTypeName", PrintCommon.getPrintString(agentEmergencyContact.getIdTypeName(), 2));
            // 证件号码
            map.put("emergencyIdNo", PrintCommon.getPrintString(agentEmergencyContact.getIdNo(), 2));
            // 国籍
            map.put("emergencyNationalityName", PrintCommon.getPrintString(agentEmergencyContact.getNationalityName(), 2));
            // 公司名称
            map.put("emergencyCompanyName", PrintCommon.getPrintString(agentEmergencyContact.getCompanyName(), 2));
            // 职业
            map.put("emergencyOccupationName", PrintCommon.getPrintString(agentEmergencyContact.getOccupationName(), 2));
            // 邮箱
            map.put("emergencyEmail", PrintCommon.getPrintString(agentEmergencyContact.getEmail(), 2));
            // 手机号码
            map.put("emergencyMobile", PrintCommon.getPrintString(agentEmergencyContact.getMobile(), 2));
        }

        // 问卷
        List<AgentQuestionnaireAnswerBo> agentQuestionnaireAnswerList = agentBo.getAgentQuestionnaireAnswerList();
        if (AssertUtils.isNotEmpty(agentQuestionnaireAnswerList)) {
            for (AgentQuestionnaireAnswerBo agentQuestionnaireAnswerBo : agentQuestionnaireAnswerList) {
                String questionCode = agentQuestionnaireAnswerBo.getQuestionCode();
                // 问题编号的答案
                String answer = agentQuestionnaireAnswerBo.getAnswer();
                map.put(questionCode + "answer", answer);
                // 问题编号的描述
                String answerDesc = agentQuestionnaireAnswerBo.getAnswerDesc();
                map.put(questionCode + "answerDesc", answerDesc);
            }
        }

        // 推荐人
        AgentBo recommendAgent = agentBo.getRecommendAgent();
        if (AssertUtils.isNotNull(recommendAgent)) {
            // 推荐人名字
            map.put("recommendAgentName", PrintCommon.getPrintString(recommendAgent.getAgentName(), 2));
            map.put("recommendAgentCode", PrintCommon.getPrintString(recommendAgent.getAgentCode(), 2));
        }

        // 申请日期
        AgentSignedBo agentSigned = agentBo.getAgentSigned();
        if (AssertUtils.isNotNull(agentSigned)) {
            Long applyDate = agentSigned.getApplyDate();
            if (!AssertUtils.isNotNull(applyDate)) {
                applyDate = agentSigned.getCommitmentSignDate();
            }
            PrintCommon.setPrintDateTime(map, "agentApplyDate", applyDate, 2);
        }
        // 招聘人、管理人、见证人信息 都是同一人
        AgentBo managerAgent = agentBo.getManagerAgent();
        if (AssertUtils.isNotNull(managerAgent)) {
            // 管理人姓名
            map.put("witnessAgentName", PrintCommon.getPrintString(managerAgent.getAgentName(), 2));
            // 管理人代码
            map.put("witnessAgentCode", PrintCommon.getPrintString(managerAgent.getAgentCode(), 2));

            AgentLevelBo managerAgentLevel = managerAgent.getAgentLevel();
            if (AssertUtils.isNotNull(managerAgentLevel)) {
                // 职位代码
                map.put("witnessAgentLevelCode", PrintCommon.getPrintString(managerAgentLevel.getAgentLevelCode(), 2));
                // 管理人职位
                map.put("witnessAgentPosition", PrintCommon.getPrintString(managerAgentLevel.getAgentLevelName(), 2));
            }
        }
        // 公司印章
        map.put("companySealPicture", PrintCommon.Company_Stamp);
        return map;
    }

    /**
     * 封装电子邮件使用政策接受表数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     */
    public Map<String, Object> getEmailAcceptanceFormData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        AgentBo agentBo = JSON.parseObject(content, AgentBo.class);

        Map<String, Object> map = new HashMap<>();
        AgentSignedBo agentSigned = agentBo.getAgentSigned();
        if (AssertUtils.isNotNull(agentSigned)) {
            // 签名时间
            PrintCommon.setPrintDateTime(map, "signDate", agentSigned.getApplyDate(), 2);
        }
        // 业务员姓名
        map.put("agentName", PrintCommon.getPrintString(agentBo.getAgentName(), 2));
        // 业务员代码
        Boolean isShowCompanySeal = agentBo.getIsShowCompanySeal();
        if (AssertUtils.isNotNull(isShowCompanySeal) && isShowCompanySeal) {
            String agentCode = agentBo.getAgentCode();

            // agentCode 按照渠道加前缀
            BranchBo agentBranch = agentBo.getAgentBranch();
            if (AssertUtils.isNotNull(agentBranch)) {
                String channelTypeCode = agentBranch.getChannelTypeCode();
                String agentCodePrefix = "";
                // 个代 A  中企 C
                if (AttachmentTermEnum.CHANNEL_TYPE.AGENT.name().equals(channelTypeCode)) {
                    agentCodePrefix = "A";
                } else if (AttachmentTermEnum.CHANNEL_TYPE.GMCE.name().equals(channelTypeCode)) {
                    agentCodePrefix = "C";
                }
                agentCode = agentCodePrefix + agentCode;
            }
            // 审核过后再展示业务员代码
            map.put("agentCode", PrintCommon.getPrintString(agentCode, 2));
        }

        AgentDetailBo agentDetail = agentBo.getAgentDetail();
        if (AssertUtils.isNotNull(agentDetail)) {
            String agentSignatureAttachId = agentDetail.getNoWatermarkSignatureAttachId();

            AttachmentPo signatureAttachmentPo = attachmentExtDao.getAttachmentPoByPk(agentSignatureAttachId);
            if (AssertUtils.isNotNull(signatureAttachmentPo)) {
                // 动态域名 根据不同环境获取不同的OSS域名
                String dynamicDomain = getConfigValue(signatureAttachmentPo.getAttachmentDomain(), "oss_config.domain.");

                map.put("LCSignPicture", dynamicDomain + signatureAttachmentPo.getUrl());
            }
        }
        return map;
    }
}