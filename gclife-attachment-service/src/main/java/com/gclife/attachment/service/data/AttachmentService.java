package com.gclife.attachment.service.data;

;
import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.common.service.BaseService;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件service接口，用于保存数据到表
 */
public interface AttachmentService extends BaseService {

    /**
     * 保存附件表
     *
     * @param attachmentPo 对应于attachment中的一条记录
     * @return 无返回值
     */
    public void saveAttachmentPo(AttachmentPo attachmentPo);

    void insertAttachmentPo(AttachmentPo attachmentPo);
    void updateAttachmentPo(AttachmentPo attachmentPo);
    /**
     * 批量保存附件表
     *
     * @param listAttachmentPo 对应于attachment中的多条记录
     * @return 无返回值
     */
    public void saveAttachmentPo(List<AttachmentPo> listAttachmentPo);

}