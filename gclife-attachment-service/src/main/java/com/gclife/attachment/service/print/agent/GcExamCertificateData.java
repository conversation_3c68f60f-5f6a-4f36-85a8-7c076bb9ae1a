package com.gclife.attachment.service.print.agent;
import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.model.agent.AgentBo;
import com.gclife.attachment.model.agent.ExamCertificateBo;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.DateUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/27
 */
@Component
public class GcExamCertificateData extends BaseBusinessServiceImpl {

    /**
     * 封装考试证书数据
     *
     * @param electronicPolicyGeneratorRequest
     * @return
     */
    public List<PrintObject> getCertificateData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        String language = electronicPolicyGeneratorRequest.getLanguage();
        ExamCertificateBo examCertificateBo = JSON.parseObject(content, ExamCertificateBo.class);
        List<PrintObject> printObjectList = new ArrayList<>();
        PrintCommon.setPrintData(printObjectList,"nameKm", examCertificateBo.getAgentNameKm(), 3);
        PrintCommon.setPrintData(printObjectList,"nameEn", examCertificateBo.getAgentNameEn(), 3);
        /*String[] k = new Date(DateUtils.getCurrentTime()).toString().split(" ");
        String examDate1 = k[2] + " " + k[1].toUpperCase() + " " + k[5];*/
        PrintCommon.setPrintData(printObjectList,"examDateEn", examCertificateBo.getExamDateEn(), 3);
        PrintCommon.setPrintData(printObjectList,"examDateKm", examCertificateBo.getExamDateKm(), 3);
        PrintCommon.setPrintData(printObjectList,"certificateNo", examCertificateBo.getCertificateNo(), 3);
       return printObjectList;
    }
}
