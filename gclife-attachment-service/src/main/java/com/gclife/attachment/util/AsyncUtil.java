package com.gclife.attachment.util;

import com.aliyun.oss.OSSClient;
import com.gclife.attachment.dao.OssConfigExtDao;
import com.gclife.attachment.model.bo.OssConfigBo;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.common.util.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @date 2021/12/8
 */
@PropertySource("classpath:oss.yml")
@Slf4j
@Component
public class AsyncUtil {

    @Autowired
    ConfigurableEnvironment env;
    @Value("${external_domain_suffix:.aliyuncs.com}")
    private String externalDomainSuffix;
    @Value("${protocol:http://}")
    private String protocol;
    @Autowired
    private OssConfigExtDao ossConfigExtDao;

    /**
     * 批量将pdf转成图片后 生成Base64
     * 每个Base64都对应着第几张图片
     *
     * @param downLatch
     * @param i                    第几张图片
     * @param bufferedImage
     * @param attachmentRequestMap
     */
    @Async("taskExecutor")
    public void pdf2ImageBase64(CountDownLatch downLatch, int i, BufferedImage bufferedImage, Map<Integer, AttachmentRequest> attachmentRequestMap) {
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            // pdf转图片
            ImageIO.write(bufferedImage, "PNG", out);
            String pdfImageBase64 = new BASE64Encoder().encode(out.toByteArray());

            AttachmentRequest attachmentRequest = new AttachmentRequest();
            attachmentRequest.setFileContent(pdfImageBase64);
            attachmentRequest.setFileSuffix("jpg");

            attachmentRequestMap.put(i, attachmentRequest);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            downLatch.countDown();
        }
    }

    /**
     * 批量上传文件到阿里云OSS
     *
     * @param downLatch
     * @param ossClient
     * @param bucket
     * @param key
     * @param inputStream
     */
    @Async("taskExecutor")
    public void putObject(CountDownLatch downLatch, OSSClient ossClient, String bucket, String key, InputStream inputStream) {
        try {
            ossClient.putObject(bucket, key, inputStream);
        } finally {
            downLatch.countDown();
        }
    }

    /**
     * 单个线程异步 批量上传文件到阿里云OSS
     * 遇到Exception类型的异常则进行重试，最多重试3次，每次重试相隔30秒*2
     *
     * @param ossConfigBo
     * @param keyAndStream key是路径 value是文件流
     */
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 30 * 1000L, multiplier = 2))
    @Async("taskExecutor")
    public void putObjects(OssConfigBo ossConfigBo, Map<String, InputStream> keyAndStream) throws Exception {
        OSSClient ossClient = null;
        try {
            // 创建OSSClient
            ossClient = new OSSClient(protocol + ossConfigBo.getOssEndpoint() + externalDomainSuffix,
                    getConfigValue(ossConfigBo.getAccessKeyId(), "oss_user.access_key_id."),
                    getConfigValue(ossConfigBo.getAccessKeySecret(), "oss_user.access_key_secret."));
            // 创建bucket
            String bucket = getConfigValue(ossConfigBo.getBucket(), "oss_config.bucket.");
            if (!ossClient.doesBucketExist(bucket)) {
                ossClient.createBucket(bucket);
            }
            // 批量上传文件到阿里云OSS
            if (AssertUtils.isNotEmpty(keyAndStream)) {
                for (String key : keyAndStream.keySet()) {
                    ossClient.putObject(bucket, key, keyAndStream.get(key));
                }
                log.info("异步上传 {} 张图片完成", keyAndStream.size());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("异步上传图片时发生意外错误，将会重试3次，重新上传中...");
            throw new Exception(e);
        } finally {
            if (AssertUtils.isNotNull(ossClient)) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 动态获取配置中心 配置值
     *
     * @param configKey
     * @param configKeyPrefix
     * @return
     */
    private String getConfigValue(String configKey, String configKeyPrefix) {
        StringBuilder sb = new StringBuilder();
        if (AssertUtils.isNotNull(configKeyPrefix)) {
            sb.append(configKeyPrefix);
        }

        if (AssertUtils.isNotNull(configKey)) {
            sb.append(configKey);
        }

        return this.env.getProperty(sb.toString());
    }

    /**
     * 异步上传Webm视频
     *
     * @param ossConfigBo
     * @param saveKey
     * @param inputStream
     */
    @Async("taskExecutor")
    public void uploadSourceWebm(OssConfigBo ossConfigBo, String saveKey, InputStream inputStream) {
        String key = null;
        if (AssertUtils.isNotEmpty(saveKey)) {
            key = saveKey.replace(".mp4", ".webm");
        }

        if (AssertUtils.isNotEmpty(key)) {
            OSSClient ossClient = null;
            try {
                ossClient = new OSSClient(protocol + ossConfigBo.getOssEndpoint() + externalDomainSuffix,
                        getConfigValue(ossConfigBo.getAccessKeyId(), "oss_user.access_key_id."),
                        getConfigValue(ossConfigBo.getAccessKeySecret(), "oss_user.access_key_secret."));
                // 创建bucket
                String bucket = getConfigValue(ossConfigBo.getBucket(), "oss_config.bucket.");
                if (!ossClient.doesBucketExist(bucket)) {
                    ossClient.createBucket(bucket);
                }
                // 上传文件
                ossClient.putObject(bucket, key, inputStream);
            } catch (Exception e) {
                log.error("webm转mp4之后 异步上传webm视频出错...");
                e.printStackTrace();
            } finally {

                if (AssertUtils.isNotNull(ossClient)) {
                    ossClient.shutdown();
                }
            }
        }
    }
}
