package com.gclife.attachment.dao.policy.impl.underwriting;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.*;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.LETTER_OF_ACCEPTANCE;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.KM_KH;
import static com.gclife.common.TerminologyConfigEnum.LANGUAGE.ZH_CN;

@Repository("LETTER_OF_ACCEPTANCE")
public class LetterOfAcceptanceDaoImpl extends BaseBusinessServiceImpl implements PrintDao {
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = getPrintData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        applyBookRtfBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtfBytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(applyBookRtfBytes);
        attachmentResponse.setTemplateType(LETTER_OF_ACCEPTANCE.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }


    public Map<String, Object> getPrintData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        ApplyBo applyPrintBo = JSON.parseObject(content, ApplyBo.class);
        Map<String, Object> map = new HashMap<>();
        ApplyApplicantBo applicant = applyPrintBo.getApplicant();
        map.put("applicantName", PrintCommon.getPrintString(applicant.getName(), 3));
        PrintCommon.setPrintDateTime(map, "applyDate", applyPrintBo.getApplyDate(), 3);
        map.put("applicantSex", PrintCommon.getPrintString(applicant.getSex(), 3));
        map.put("applicantSexName", PrintCommon.getPrintString(applicant.getSexName(), 3));
        PrintCommon.setPrintDateTime(map, "applicantBirthday", applicant.getBirthday(), 3);
        map.put("applicantMobile", PrintCommon.getPrintString(applicant.getMobile(), 3));

        ApplyCoverageBo coverageBo = new ApplyCoverageBo();
        ApplyInsuredBo applyInsuredBo = applyPrintBo.getListInsured().get(0);
        if (AssertUtils.isNotNull(applyInsuredBo)) {
            List<ApplyCoverageBo> listCoverage = applyInsuredBo.getListCoverage();
            if (AssertUtils.isNotNull(listCoverage)) {
                Optional<ApplyCoverageBo> optionalApplyCoverageBo = listCoverage.stream().filter(applyCoverageBo -> AttachmentTermEnum.PRODUCT_PRIMARY_FLAG.MAIN.name().equals(applyCoverageBo.getPrimaryFlag())).findFirst();
                if (optionalApplyCoverageBo.isPresent()) {
                    coverageBo = optionalApplyCoverageBo.get();
                }
            }
        }
        PrintCommon.setProductName(map, coverageBo.getProductId(), coverageBo.getProductLevel(), language);
        //保险期限
        String coveragePeriodUnitName = null;
        if (AssertUtils.isNotEmpty(coverageBo.getCoveragePeriod()) && AssertUtils.isNotEmpty(coverageBo.getCoveragePeriodUnitName())) {
            coveragePeriodUnitName = coverageBo.getCoveragePeriod() + " " + coverageBo.getCoveragePeriodUnitName();
            if (TerminologyConfigEnum.LANGUAGE.KM_KH.name().equals(language) && "AGE".equals(coverageBo.getCoveragePeriodUnit())) {
                coveragePeriodUnitName = coverageBo.getCoveragePeriodUnitName() + " " + coverageBo.getCoveragePeriod();
            }
        }
        if ("PRO88000000000009".equals(coverageBo.getProductId())) {
            coveragePeriodUnitName = KM_KH.name().equals(language) ? "រហូតដល់អ្នកត្រូវបានធានារ៉ាប់រងអាយុ 80" : ZH_CN.name().equals(language) ? "至被保险人80岁" : "Until the Insured is 80";
        }
        map.put("coveragePeriodUnitName", PrintCommon.getPrintString(coveragePeriodUnitName, 2));
        BigDecimal amount = AssertUtils.isNotEmpty(coverageBo.getAmount()) ? new BigDecimal(coverageBo.getAmount()) : null;
        map.put("amount", PrintCommon.getPrintString(amount, 2));
        map.put("productLevelName", PrintCommon.getPrintString(coverageBo.getProductLevelName(), 2));

        ApplyLoanBo loanContract = applyPrintBo.getLoanContract();
        if (!AssertUtils.isNotNull(loanContract)) {
            loanContract = new ApplyLoanBo();
        }
        map.put("paymentWayName", PrintCommon.getPrintString(loanContract.getPaymentWayName(), 2));
        map.put("loanInterestRate", PrintCommon.getPrintString(loanContract.getLoanInterestRate(), 2));

        map.put("totalPremium", PrintCommon.getPrintString(coverageBo.getTotalPremium(), 2));
        map.put("loanBranchName", PrintCommon.getPrintString(loanContract.getLoanBranchName(), 2));
        ApplyAgentBo applyAgentBo = applyPrintBo.getApplyAgentBo();
        if (!AssertUtils.isNotNull(applyAgentBo)) {
            applyAgentBo = new ApplyAgentBo();
        }
        map.put("agentCode", PrintCommon.getPrintString(applyAgentBo.getAgentCode(), 2));
        map.put("agentName", PrintCommon.getPrintString(applyAgentBo.getAgentName(), 2));

        return map;
    }

}
