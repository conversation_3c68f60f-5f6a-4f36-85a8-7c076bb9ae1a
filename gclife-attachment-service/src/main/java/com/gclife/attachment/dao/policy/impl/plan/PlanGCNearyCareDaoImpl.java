package com.gclife.attachment.dao.policy.impl.plan;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.GCNearyCareData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/29
 */
@Repository("PLAN_GC_NEARY_CARE")
public class PlanGCNearyCareDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private GCNearyCareData gcNearyCareData;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> stringObjectMap = gcNearyCareData.getPlanData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        byte[] bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), stringObjectMap, applyBookRtfBytes);
        bytes = PlanCommon.updateAndSavePageNumber(bytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.pdfEvenPage(bytes, electronicPolicyGeneratorRequest.getLanguage()));
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.PLAN_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

}