package com.gclife.attachment.dao.policy.impl.apply;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.policy.apply.ApplyBo;
import com.gclife.attachment.model.policy.apply.ApplyInsuredBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.GCSokSanData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.StringUtil;
import lombok.AllArgsConstructor;
import oracle.xdo.template.html.html2fo.B;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("APPLY_GC_SOKSAN")
@AllArgsConstructor
public class ApplyGCSokSanDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    private final GCSokSanData gcSokSanData;

    private final PdfTemplateConfigExtDao pdfTemplateConfigExtDao;

    private final AttachmentBusinessService attachmentBusinessService;

    private final ITextPdfService iTextPdfService;
    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> stringObjectMap = gcSokSanData.getApplyData(electronicPolicyGeneratorRequest);
        ApplyBo applyPrintBo = JSON.parseObject(electronicPolicyGeneratorRequest.getContent(), ApplyBo.class);
        List<ApplyInsuredBo> listInsured = applyPrintBo.getListInsured();
        ApplyInsuredBo applyInsuredBo = listInsured.get(0);
        int ageYear = 0;
        if (AssertUtils.isNotNull(applyInsuredBo)) {
            // 被保人年龄
            if (AssertUtils.isNotNull(applyInsuredBo.getBirthday())) {
                ageYear = StringUtil.getAgeByBirthday(applyInsuredBo.getBirthday());
            }
        }
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        Boolean isHealthNotice = (Boolean) stringObjectMap.get("isHealthNotice");
        // 没有健康告知
        if (!isHealthNotice) {
            pdfTemplateConfigBo.setAttachmentId(pdfTemplateConfigBo.getAttachmentId() + "_GIO");
        }
        // 未成年且没有健康告知
        if (ageYear < 18) {
            pdfTemplateConfigBo.setAttachmentId(pdfTemplateConfigBo.getAttachmentId() + "_MINORS");
            if (!isHealthNotice) {
                pdfTemplateConfigBo.setAttachmentId(pdfTemplateConfigBo.getAttachmentId() + "_MINORS_GIO");
            }
        }
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        byte[] bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), stringObjectMap, applyBookRtfBytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.pdfEvenPage(bytes, electronicPolicyGeneratorRequest.getLanguage()));
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.APPLY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
