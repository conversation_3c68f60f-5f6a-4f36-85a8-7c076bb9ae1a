package com.gclife.attachment.dao;

import com.gclife.attachment.model.bo.DigitalCertificateConfigBo;
import com.gclife.common.dao.base.BaseDao;

/**
 * <AUTHOR>
 * create 18-1-5
 * description: 用于查询digital_certificate_config表中的记录
 */

public interface DigitalCertificateConfigExtDao extends BaseDao {
    /**
     *
     * 查询digital_certificate_config表中的记录
     *
     * @param usageType 证书用途
     * @return DigitalCertificateConfigBo
     */
    DigitalCertificateConfigBo loadDigitalCertificateConfig(String usageType);
}
