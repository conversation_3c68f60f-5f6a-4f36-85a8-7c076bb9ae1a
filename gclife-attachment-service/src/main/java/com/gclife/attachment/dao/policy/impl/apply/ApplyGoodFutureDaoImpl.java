package com.gclife.attachment.dao.policy.impl.apply;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.ApplyData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Map;

import static com.gclife.attachment.model.config.AttachmentPolicyEnum.INSURED_HEALTH_BOOK;

/**
 *  8号产品的
 * <AUTHOR>
 * create 2018/8/28
 * description: 好将来
 *
 * @see ApplyPersonalDaoImpl
 */
@Deprecated

@Repository("APPLY_GOOD_FUTURE")
public class ApplyGoodFutureDaoImpl extends BaseBusinessServiceImpl  implements PrintDao {

    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService ;
    @Autowired
    private ApplyData applyData;
    @Autowired
    private ITextPdfService iTextPdfService;

    /**
     * 中文板打印
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }


    /**
     * 英文版打印
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    /**
     * 柬埔寨版本
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception{
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = applyData.getApplyData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        applyBookRtfBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtfBytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.pdfEvenPage(applyBookRtfBytes, language));
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.APPLY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
        //被保人健康告知书
        iTextPdfService.mergeHealth(policyData, electronicPolicyGeneratorRequest,INSURED_HEALTH_BOOK.name());
    }
}
