package com.gclife.attachment.dao.policy.impl.payment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.payment.GroupPaymentReceiptData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 收据打印
 *
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:13 2018/12/14
 * @ Description:团体保险投保收据
 * @ Modified By:
 * @ Version: $version
 * <AUTHOR>
 */
@Repository("GROUP_RENEWAL_PAYMENT_RECEIPT")
public class GroupRenewalReceiptDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private GroupPaymentReceiptData groupPaymentReceiptData;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
          this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
          this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
          this.print(electronicPolicyGeneratorRequest);
    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] groupPaymentReceiptRtf = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        String content = electronicPolicyGeneratorRequest.getContent();
        List<byte[]> byteList = new ArrayList<>();
        List<AttachmentPaymentPrintBo> paymentPrintBoList = JSONObject.parseArray(content, AttachmentPaymentPrintBo.class);
        for (AttachmentPaymentPrintBo paymentPrintBo : paymentPrintBoList) {
            electronicPolicyGeneratorRequest.setContent(JSON.toJSONString(paymentPrintBo));
            Map<String, Object> paymentReceiptData = groupPaymentReceiptData.getPaymentReceiptData(electronicPolicyGeneratorRequest);
            byte[] bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), paymentReceiptData, groupPaymentReceiptRtf);
            byteList.add(bytes);
        }
        byte[] bytes = PrintCommon.mergePdfFiles(byteList);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

}
