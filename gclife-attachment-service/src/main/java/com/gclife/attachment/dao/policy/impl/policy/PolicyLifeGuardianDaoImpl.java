package com.gclife.attachment.dao.policy.impl.policy;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.LifeGuardianData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Map;

import static com.gclife.attachment.common.PrintCommon.TERMS_VERSION;
import static com.gclife.attachment.model.config.AttachmentPolicyEnum.POLICY_TERMS_BOOK;
import static com.gclife.common.model.config.TerminologyConfigEnum.LANGUAGE.KM_KH;

/**
 * 9号产品
 *
 * <AUTHOR>
 * create 2018/8/28
 * description:
 */

/**
 * <AUTHOR>
 * @date 2018-08-28
 */
@Repository("POLICY_LIFE_GUARDIAN")
public class PolicyLifeGuardianDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private LifeGuardianData lifeGuardianData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String productId = electronicPolicyGeneratorRequest.getProductId();
        electronicPolicyGeneratorRequest.setProductMainId(productId);
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = lifeGuardianData.getPolicyData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        byte[] bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtfBytes);
        //盖章
        if(KM_KH.name().equals(language)){
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            PdfStamper pdfStamper = new PdfStamper(new PdfReader(bytes), byteArrayOutputStream);
            PdfContentByte canvas = pdfStamper.getOverContent(1);
            //添加 CEO 签名
            canvas.addImage(PrintCommon.addImage(PrintCommon.CEO_Signature, 62.93f, 41.67f, 345.06f, 634.09f));
            //添加公司盖章
            canvas.addImage(PrintCommon.addImage(PrintCommon.Company_Stamp, 138.09f, 87.33f, 441.44f, 600.72f));
            pdfStamper.close();
            bytes = byteArrayOutputStream.toByteArray();
        }
        if(AssertUtils.isNotNull(policyData.get("PRO880000000000016"))){
            byte[] applyBookRtf16Bytes = attachmentBusinessService.loadOssObjectByAttachmentId("PRO880000000000016_POLICY_"+language);
            byte[] bytes1 = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtf16Bytes);
            bytes = PrintCommon.mergePdfFiles(Arrays.asList(bytes,bytes1),language);
        }
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.pdfEvenPage(bytes, electronicPolicyGeneratorRequest.getLanguage()));
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.POLICY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
        /******************************************************保险条款************************************************************************/
        byte[] terms_pro88000000000009 = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_PRO88000000000009_" + language);
        byte[] terms_pro88000000000009_cash_value = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_PRO88000000000009_CASH_VALUE_" + language);
        terms_pro88000000000009_cash_value = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, terms_pro88000000000009_cash_value);
        terms_pro88000000000009 = PrintCommon.mergePdfFiles(Arrays.asList(terms_pro88000000000009, terms_pro88000000000009_cash_value));
        AttachmentResponse termsResponse = iTextPdfService.savePdfAndUpload(terms_pro88000000000009);
        termsResponse.setSeq(1l);
        termsResponse.setTemplateType(AttachmentPolicyEnum.POLICY_TERMS_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(termsResponse);
        if (AssertUtils.isNotNull(policyData.get("PRO880000000000016"))) {
            electronicPolicyGeneratorRequest.getAttachmentResponseList().add(new AttachmentResponse(TERMS_VERSION + "TERMS_PRO880000000000016_" + language, POLICY_TERMS_BOOK.name(), 2l));
        }
        /******************************************************首刊 封面************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCover(productId, language));
        /******************************************************客户服务指南************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCustomerServiceInstruction(language));
        // 保险证确认书
        iTextPdfService.addPolicyConfirm(electronicPolicyGeneratorRequest);
    }
}
