package com.gclife.attachment.dao.policy.impl.claim;

import com.alibaba.fastjson.JSONObject;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.claim.PrintReportCustomerBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.claim.ClaimData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 理赔核定
 * @date 2019/12/11 4:00 下午
 */
@Repository("CLAIM_VERIFICATION")
public class ClaimVerificationDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ClaimData claimData;
    @Autowired
    private ITextPdfService iTextPdfService;

    /**
     * 中文板打印
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }


    /**
     * 英文版打印
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    /**
     * 柬埔寨版本
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] templateBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String content = electronicPolicyGeneratorRequest.getContent();
        List<PrintReportCustomerBo> paymentPrintBoList = JSONObject.parseArray(content, PrintReportCustomerBo.class);
        List<byte[]> byteList = new ArrayList<>();
        for (PrintReportCustomerBo printReportCustomerBo : paymentPrintBoList) {
            Map<String, Object> stringObjectMap = claimData.getData(printReportCustomerBo);
            // 公司基础信息
            stringObjectMap.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
            byte[] bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), stringObjectMap, templateBytes);
            byteList.add(bytes);
        }
        templateBytes = PrintCommon.mergePdfFiles(byteList,language);

        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(templateBytes);
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

}
