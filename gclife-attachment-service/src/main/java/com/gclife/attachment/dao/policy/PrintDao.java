package com.gclife.attachment.dao.policy;

import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;

public interface PrintDao {

    /**
     * 中文板打印
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;


    /**
     * 英文版打印
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;

    /**
     * 柬埔寨版本
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;

    /**
     * 打印业务
     *
     * @param electronicPolicyGeneratorRequest
     * @throws Exception
     */
    void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception;

}
