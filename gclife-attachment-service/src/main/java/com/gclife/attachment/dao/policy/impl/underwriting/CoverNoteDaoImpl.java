package com.gclife.attachment.dao.policy.impl.underwriting;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.CompanyInfo;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.apply.CoverNotePrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.COVER_NOTE;

/**
 * @ProjectName: gclife-attachment-service
 * @Package: com.gclife.attachment.dao.policy.impl.underwriting
 * @ClassName: CoverNoteDaoImpl
 * @Author: baizhongying
 * @Description:
 * @Date: 2021/10/20 13:04
 * @Version: 1.0
 */
@Repository("COVER_NOTE")
public class CoverNoteDaoImpl extends BaseBusinessServiceImpl implements PrintDao {
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = getPrintData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        applyBookRtfBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtfBytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(applyBookRtfBytes);
        attachmentResponse.setTemplateType(COVER_NOTE.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

    private Map<String, Object> getPrintData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        Map<String, Object> map = new HashMap<>();
        CoverNotePrintBo applyPrintBo = JSON.parseObject(electronicPolicyGeneratorRequest.getContent(), CoverNotePrintBo.class);
        String language = electronicPolicyGeneratorRequest.getLanguage();
        map.put("applyNo", PrintCommon.getPrintString(applyPrintBo.getApplyNo(), 3));
        map.put("applicantName", PrintCommon.getPrintString(applyPrintBo.getApplicantName(), 3));
        map.put("insuredName", PrintCommon.getPrintString(applyPrintBo.getInsuredName(), 3));
        PrintCommon.setPrintDateTime(map, "gainedDate", applyPrintBo.getGainedDate(), 3);
        // 公司基础信息
        map.putAll(CompanyInfo.getCompanyBaseInfoMap(language));
        return map;
    }
}
