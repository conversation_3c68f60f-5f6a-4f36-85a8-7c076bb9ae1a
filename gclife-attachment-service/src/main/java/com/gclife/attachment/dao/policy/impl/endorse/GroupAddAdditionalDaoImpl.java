package com.gclife.attachment.dao.policy.impl.endorse;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.AttachmentExtDao;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.endorse.EndorseAddSubtractInsuredData;
import com.gclife.attachment.service.print.policy.TeamLifeShieldInsuranceData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.JackSonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.gclife.attachment.common.PrintCommon.TERMS_VERSION;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:51 2019/3/22
 * @ Description: 团险 增加附加险
 * @ Modified By:
 * @ Version: $version
 */
@Repository("GROUP_ADD_ADDITIONAL")
public class GroupAddAdditionalDaoImpl extends BaseBusinessServiceImpl implements PrintDao {


    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private EndorseAddSubtractInsuredData addSubtractInsuredData;
    @Autowired
    private TeamLifeShieldInsuranceData teamLifeShieldInsuranceData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private AttachmentExtDao attachmentExtDao;


    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

        this.print(electronicPolicyGeneratorRequest);
    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<byte[]> bytesList= new ArrayList<>();

        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = addSubtractInsuredData.getData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] bytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        //增加附加险保全
        bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, bytes);
        bytesList.add(bytes);

        List<Map<String, Object>> coverageListMap = (List<Map<String, Object>>) policyData.get("coverageListMap");
        if(AssertUtils.isNotEmpty(coverageListMap)){
            //保险证
            bytes = teamLifeShieldInsuranceData.getPolicyPdfBytes(electronicPolicyGeneratorRequest, policyData);
            if (AssertUtils.isNotNull(bytes)) {
                bytesList.add(bytes);
            }
            //条款
            List<byte[]> termsBytesList = new ArrayList<>();
            for (Map<String, Object> coverageMap : coverageListMap) {
                String productId = coverageMap.get("productId")+"";
                String attachmentId = TERMS_VERSION + "TERMS_" + coverageMap.get("productId") + "_" + language;
                if("PRO8800000000000G12".equals(productId)) {
                    byte[] terms = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_" + coverageMap.get("productId") + "_" + language);
                    byte[] tableOfPremium = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_PRO8800000000000G12_TABLE_OF_PREMIUM_" + language);
                    tableOfPremium = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, tableOfPremium);
                    terms = PrintCommon.mergePdfFiles(Arrays.asList(terms, tableOfPremium));
                    termsBytesList.add(terms);
                }else {
                    termsBytesList.add(attachmentBusinessService.loadOssObjectByAttachmentId(attachmentId));
                }
            }
            bytes = PrintCommon.mergePdfFiles(termsBytesList);
            bytesList.add(bytes);
        }
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.mergePdfFiles(bytesList,language));
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
