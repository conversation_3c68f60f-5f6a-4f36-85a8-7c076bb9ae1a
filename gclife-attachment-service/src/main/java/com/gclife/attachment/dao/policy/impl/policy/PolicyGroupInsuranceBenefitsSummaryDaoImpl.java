package com.gclife.attachment.dao.policy.impl.policy;

import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.print.policy.GroupInsuranceBenefitsSummaryData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

import static com.gclife.attachment.common.PrintCommon.TERMS_VERSION;
import static com.gclife.attachment.model.config.AttachmentPolicyEnum.POLICY_TERMS_BOOK;

/**
 * <AUTHOR>
 * @date 2021/11/9
 */
@Repository("POLICY_GROUP_INSURANCE_BENEFITS_SUMMARY")
public class PolicyGroupInsuranceBenefitsSummaryDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private GroupInsuranceBenefitsSummaryData groupInsuranceBenefitsSummaryData;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = groupInsuranceBenefitsSummaryData.getPolicyData(electronicPolicyGeneratorRequest);
        /******************************************************保险证************************************************************************/
        byte[] bytes = groupInsuranceBenefitsSummaryData.getPolicyPdfBytes(electronicPolicyGeneratorRequest, policyData);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.POLICY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
        /******************************************************条款************************************************************************/
        List<Map<String, Object>> coverageListMap = (List<Map<String, Object>>) policyData.get("coverageListMap");
        long seq = 0L;
        for (Map<String, Object> coverageMap : coverageListMap) {
            AttachmentResponse attachment = new AttachmentResponse(TERMS_VERSION + "TERMS_" + coverageMap.get("productId") + "_" + language, POLICY_TERMS_BOOK.name(), seq++);
            electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachment);
        }
        /******************************************************签收回执************************************************************************/
        iTextPdfService.addAcknowledgmentLetter(policyData, electronicPolicyGeneratorRequest);
        /******************************************************封页首刊************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getGroupCover(electronicPolicyGeneratorRequest.getProductId(), language));
        /******************************************************客户服务指南************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCustomerServiceInstruction(language));
    }
}
