package com.gclife.attachment.dao.policy.impl.agent;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.agent.LCData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/16
 */
@Repository("LC_CONTRACT")
public class LCContractDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private LCData lcData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();

        // 封装寿险顾问电子合同数据
        Map<String, Object> contractData = lcData.getContractData(electronicPolicyGeneratorRequest);

        // 查询pdf_template_config表中的记录
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);

        // 获取rt模板
        byte[] rtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());

        String configValue = this.getConfigValue("resources.service_name.gclife-office-service", null);

        // 合同数据替换模板数据，并生成pdf
        rtfBytes = OracleBiUtils.mapDateAndRtfToPdf(configValue, contractData, rtfBytes);

        // 保存并上传pdf
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.pdfEvenPage(rtfBytes, language), pdfTemplateConfigBo.getPdfTypeDetails());
        attachmentResponse.setTemplateType(electronicPolicyGeneratorRequest.getPdfType());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
