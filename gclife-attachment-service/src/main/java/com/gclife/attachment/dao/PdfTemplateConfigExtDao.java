package com.gclife.attachment.dao;

import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.common.dao.base.BaseDao;

/**
 * <AUTHOR>
 * create 18-1-5
 * description:用于查询pdf_template_config表中的记录
 */

public interface PdfTemplateConfigExtDao extends BaseDao{

    /**
     *
     * 查询pdf_template_config表中的记录
     *
     * @param electronicPolicyGeneratorRequest
     * @return PdfTemplateConfigBo
     */
    PdfTemplateConfigBo loadPdfTemplateConfig(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest);

}
