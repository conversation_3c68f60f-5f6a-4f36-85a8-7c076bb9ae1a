package com.gclife.attachment.dao.policy.impl.endorse;

import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.print.endorse.EndorseAddSubtractInsuredData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository("ENDORSE_GC_GROUP_STUDENT_CARE_COI")
public class EndorseGCGroupStudentCareCoiDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private EndorseAddSubtractInsuredData addSubtractInsuredData;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        //保全增减员COI,只针对29号产品特殊处理
        byte[] policyCOIPdfBytes = addSubtractInsuredData.getPolicyCOIPdfBytes(electronicPolicyGeneratorRequest);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUploadCoi(policyCOIPdfBytes);
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
