package com.gclife.attachment.dao.policy.impl.policy;

import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import org.springframework.stereotype.Repository;

/**
 * 2号产品未添加
 *
 * <AUTHOR>
 * create 18-2-5
 * description: INSURANCE_CERTIFICATE　保险证
 */
@Repository("POLICY_AUSPICIOUS_TRAVEL")
public class PolicyAuspiciousTravelDaoImpl implements PrintDao {

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

    }
}
