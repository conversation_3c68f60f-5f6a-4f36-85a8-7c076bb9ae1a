package com.gclife.attachment.dao.policy.impl.policy.confirm;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.confirm.PolicyConfirmPersonalData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/19
 */
@Repository("POLICY_CONFIRM_PERSONAL")
public class PolicyConfirmPersonaDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private PolicyConfirmPersonalData policyConfirmPersonalData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        Map<String, Object> policyData = policyConfirmPersonalData.getPolicyConfirmData(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setProductId(null);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] policyConfirmRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        policyConfirmRtfBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, policyConfirmRtfBytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.pdfEvenPage(policyConfirmRtfBytes, electronicPolicyGeneratorRequest.getLanguage()));
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.POLICY_CONFIRM.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
