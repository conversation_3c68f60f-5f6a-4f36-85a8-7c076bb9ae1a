package com.gclife.attachment.dao.policy.impl.payment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.payment.AttachmentPaymentPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.payment.EndorsePaymentReceiptData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:53 2019/3/18
 * @ Description:保全收费收据
 * @ Modified By:
 * @ Version: $version
 */
@Repository("POLICY_ENDORSE_PAYMENT_RECEIPT")
public class EndorsePaymentReceiptDaoImpl extends BaseBusinessServiceImpl implements PrintDao {
    @Autowired
    private EndorsePaymentReceiptData endorsePaymentReceiptData;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

        String content = electronicPolicyGeneratorRequest.getContent();
        List<AttachmentPaymentPrintBo> paymentPrintBoList = JSONObject.parseArray(content, AttachmentPaymentPrintBo.class);
        AttachmentPaymentPrintBo paymentPrintBo = paymentPrintBoList.get(0);
        electronicPolicyGeneratorRequest.setContent(JSON.toJSONString(paymentPrintBo));
        Map<String, Object> paymentReceiptData = endorsePaymentReceiptData.getPaymentReceiptData(electronicPolicyGeneratorRequest);

        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] bytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());

        bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), paymentReceiptData, bytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
