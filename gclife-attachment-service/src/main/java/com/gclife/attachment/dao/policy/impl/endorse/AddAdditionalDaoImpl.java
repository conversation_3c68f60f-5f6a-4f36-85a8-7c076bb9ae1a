package com.gclife.attachment.dao.policy.impl.endorse;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.endorse.CoveragePrintBo;
import com.gclife.attachment.model.policy.endorse.ReinstatementPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.endorse.EndorseReinstatementData;
import com.gclife.attachment.service.print.policy.GoodFutureData;
import com.gclife.attachment.service.print.policy.LifeGuardianData;
import com.gclife.attachment.service.print.policy.OffspringProsperitysData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.gclife.attachment.common.PrintCommon.TERMS_VERSION;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:51 2019/3/22
 * @ Description: 增加附加险
 * @ Modified By:
 * @ Version: $version
 */
@Repository("ADD_ADDITIONAL")
public class AddAdditionalDaoImpl extends BaseBusinessServiceImpl implements PrintDao {


    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private OffspringProsperitysData offspringProsperitysData;
    @Autowired
    private GoodFutureData goodFutureData;
    @Autowired
    private LifeGuardianData lifeGuardianData;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private EndorseReinstatementData endorseReinstatementData;
    @Autowired
    private ITextPdfService iTextPdfService;


    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {

        this.print(electronicPolicyGeneratorRequest);
    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<byte[]> endorseByteList = new ArrayList<>();
        String language = electronicPolicyGeneratorRequest.getLanguage();

        Map<String, Object> dataData = endorseReinstatementData.getData(electronicPolicyGeneratorRequest);

        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] inputBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        inputBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), dataData, inputBytes);
        inputBytes = PrintCommon.pdfEvenPage(inputBytes, electronicPolicyGeneratorRequest.getLanguage());
        endorseByteList.add(inputBytes);
        ReinstatementPrintBo reinstatementPrintBo = JSON.parseObject(electronicPolicyGeneratorRequest.getContent(), ReinstatementPrintBo.class);

        List<CoveragePrintBo> coveragePrintList = reinstatementPrintBo.getCoveragePrintList();
        // 所有附加险条款
        coveragePrintList.stream().map(coveragePrintBo -> {
            String productId = coveragePrintBo.getProductId();
            // 如果产品ID最后一个字符是字母就去掉
            char lastCharacter = productId.charAt(productId.length() - 1);
            if (Character.isLetter(lastCharacter)) {
                productId = productId.substring(0, productId.length() - 1);
            }
            return productId;
        }).distinct().forEach(productId -> {
            byte[] terms = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_" + productId + "_" + language);
            endorseByteList.add(terms);
        });
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.mergePdfFiles(endorseByteList, language));
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

}
