package com.gclife.attachment.dao.policy.impl.renewal;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.renewal.AgentBo;
import com.gclife.attachment.model.policy.renewal.RenewalInsuranceConfirmPrintBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.renewal.RenewalData;
import com.gclife.attachment.service.print.renewal.RenewalInsuranceData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.ACKNOWLEDGMENT_LETTER;

/**
 * 续保
 *
 * <AUTHOR> BaiZhongYing
 * @Descrption :
 * @Date : 11:45 2018/12/13
 */
@Repository("POLICY_RENEWAL_INSURANCE")
public class RenewalInsuranceDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private RenewalInsuranceData renewalInsuranceData;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> map = this.renewalInsuranceData.getRenewalInsuranceData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] inputBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        inputBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), map, inputBytes);

        byte[] acknowledgmentLetterBytes = attachmentBusinessService.loadOssObjectByAttachmentId(ACKNOWLEDGMENT_LETTER.name() + "_" + electronicPolicyGeneratorRequest.getLanguage());
        acknowledgmentLetterBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), map, acknowledgmentLetterBytes);

        inputBytes = PrintCommon.mergePdfFiles(Arrays.asList(inputBytes, acknowledgmentLetterBytes), language);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(inputBytes);
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

}
