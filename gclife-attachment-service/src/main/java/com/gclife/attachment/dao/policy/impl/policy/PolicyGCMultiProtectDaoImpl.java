package com.gclife.attachment.dao.policy.impl.policy;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.GCMultiProtectData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Map;

import static com.gclife.attachment.common.PrintCommon.TERMS_VERSION;

/**
 * <AUTHOR>
 * @date 2021/12/23
 */
@Repository("POLICY_GC_MULTI_PROTECT")
public class PolicyGCMultiProtectDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private GCMultiProtectData gcMultiProtectData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String productId = electronicPolicyGeneratorRequest.getProductId();
        electronicPolicyGeneratorRequest.setProductMainId(productId);

        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = gcMultiProtectData.getPolicyData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        applyBookRtfBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtfBytes);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.pdfEvenPage(applyBookRtfBytes, electronicPolicyGeneratorRequest.getLanguage()));
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.POLICY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
        /******************************************************保险条款************************************************************************/
        byte[] terms_pro880000000000020 = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_PRO880000000000020_" + language);
        AttachmentResponse termsResponse = iTextPdfService.savePdfAndUpload(terms_pro880000000000020);
        termsResponse.setSeq(1L);
        termsResponse.setTemplateType(AttachmentPolicyEnum.POLICY_TERMS_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(termsResponse);
        /******************************************************首刊 封面************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCover(productId, language));
        /******************************************************客户服务指南************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCustomerServiceInstruction(language));
        // 保险证确认书
        iTextPdfService.addPolicyConfirm(electronicPolicyGeneratorRequest);
    }

}