package com.gclife.attachment.dao.policy.impl.renewal;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.apply.group.*;
import com.gclife.attachment.model.policy.policy.group.*;
import com.gclife.attachment.model.request.AttachmentRequest;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentByteResponse;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.renewal.GroupRenewalData;
import com.gclife.common.model.ResultObject;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import com.gclife.common.util.DateUtils;
import com.gclife.common.util.UUIDUtils;
import com.gclife.renewal.api.GroupRenewalApi;
//import com.gclife.renewal.model.vo.GroupRenewalAttachmentVo;
import org.modelmapper.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import sun.misc.BASE64Encoder;

import java.util.*;
import java.util.stream.Collectors;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.*;
import static com.gclife.attachment.model.config.AttachmentPolicyEnum.*;
import static com.gclife.common.model.config.TerminologyConfigEnum.WHETHER.NO;

/**
 * 团险续保缴费通知单
 *
 * <AUTHOR> BaiZhongYing
 * @Descrption :
 * @Date : 11:44 2018/12/13
 */
@Repository("GROUP_RENEWAL")
public class GroupRenewalDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private GroupRenewalData groupRenewalData;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        //增加团险续保批单
        Map<String, Object> policyData = this.groupRenewalData.getGroupRenewalData(electronicPolicyGeneratorRequest);

        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        applyBookRtfBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, applyBookRtfBytes);

        electronicPolicyGeneratorRequest.setPdfType(POLICY.name());
        electronicPolicyGeneratorRequest.setProductId(electronicPolicyGeneratorRequest.getProductMainId());
        electronicPolicyGeneratorRequest.setProductMainId(null);
        attachmentBusinessService.electronicPolicyGenerator(null, electronicPolicyGeneratorRequest);

        this.policyConvertApplyData(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setPdfType(APPLY.name());
        attachmentBusinessService.electronicPolicyGenerator(null, electronicPolicyGeneratorRequest);

        Map<String, String> attachmentMap = new HashMap<>();
        List<AttachmentResponse> attachmentResponses = electronicPolicyGeneratorRequest.getAttachmentResponseList();
        Optional<AttachmentResponse> applyAttachmentBoOptional = attachmentResponses.stream().filter(bo -> APPLY_BOOK.name().equals(bo.getTemplateType())).findFirst();
        attachmentMap.put(APPLY_BOOK.name(), applyAttachmentBoOptional.get().getMediaId());
        // 保单
        attachmentResponses.stream()
                .filter(bo -> POLICY_BOOK.name().equals(bo.getTemplateType()))
                .findFirst().ifPresent(attachmentResponse -> attachmentMap.put(POLICY_BOOK.name(), attachmentResponse.getMediaId()));
        // 首刊
        attachmentResponses.stream()
                .filter(bo -> FIRST_ISSUE_BOOK.name().equals(bo.getTemplateType()))
                .findFirst().ifPresent(policyAttachmentBo -> attachmentMap.put(FIRST_ISSUE_BOOK.name(), policyAttachmentBo.getMediaId()));
        /***客户服务指南***/
        Optional<AttachmentResponse> customerServiceInstructionBookOptional = attachmentResponses.stream().filter(bo -> CUSTOMER_SERVICE_INSTRUCTION_BOOK.name().equals(bo.getTemplateType())).findFirst();
        if (customerServiceInstructionBookOptional.isPresent()) {
            attachmentMap.put(CUSTOMER_SERVICE_INSTRUCTION_BOOK.name(), customerServiceInstructionBookOptional.get().getMediaId());
        }
        /***首刊***/
        Optional<AttachmentResponse> firstIssueBookOptional = attachmentResponses.stream().filter(bo -> FIRST_ISSUE_BOOK.name().equals(bo.getTemplateType())).findFirst();
        if (firstIssueBookOptional.isPresent()) {
            attachmentMap.put(FIRST_ISSUE_BOOK.name(), firstIssueBookOptional.get().getMediaId());
        }
        /***签收回执***/
        AttachmentResponse acknowledgmentLetterAttachment = iTextPdfService.addAcknowledgmentLetter(policyData, language);
        if (AssertUtils.isNotNull(acknowledgmentLetterAttachment)) {
            attachmentMap.put(ACKNOWLEDGMENT_LETTER_BOOK.name(), acknowledgmentLetterAttachment.getMediaId());
        }
        /***被保险人清单***/
        Optional<AttachmentResponse> policyInsuredListOptional = attachmentResponses.stream().filter(bo -> POLICY_INSURED_LIST.name().equals(bo.getTemplateType())).findFirst();
        if (policyInsuredListOptional.isPresent()) {
            attachmentMap.put(POLICY_INSURED_LIST.name(), policyInsuredListOptional.get().getMediaId());
        }
        /***保险条款***/
        List<AttachmentResponse> policyTermsBookList = attachmentResponses.stream().filter(bo -> POLICY_TERMS_BOOK.name().equals(bo.getTemplateType()) || PREMIUM_RATE_AND_CASH_VALUE.name().equals(bo.getTemplateType())).collect(Collectors.toList());
        if (AssertUtils.isNotEmpty(policyTermsBookList)) {
            Comparator<AttachmentResponse> comparator = (p1, p2) -> (int) (p1.getSeq() - p2.getSeq());
            policyTermsBookList.sort(comparator);
            List<String> attachmentIdList = policyTermsBookList.stream().map(AttachmentResponse::getMediaId).collect(Collectors.toList());
            attachmentMap.put(POLICY_TERMS_BOOK.name(), JSON.toJSONString(attachmentIdList));
        }

        List<String> attachmentIdList = new ArrayList<>();
        attachmentIdList.add(language);
        AttachmentPolicyEnum[] attachmentTypeFlags = AttachmentPolicyEnum.values();
        for (AttachmentPolicyEnum attachmentTypeFlag : attachmentTypeFlags) {
            String attachmentId = attachmentMap.get(attachmentTypeFlag.name());
            if (!AssertUtils.isNotEmpty(attachmentId)) {
                continue;
            }
            // 条款修改页码
            if (POLICY_TERMS_BOOK.name().equals(attachmentTypeFlag.name())) {
                attachmentIdList.add("UPDATE_PAGE");
                List<String> strings = JSON.parseArray(attachmentId, String.class);
                attachmentIdList.addAll(strings);
                attachmentIdList.add("UPDATE_PAGE");
            } else {
                attachmentIdList.add(attachmentId);
            }
        }
        ResultObject<AttachmentByteResponse> attachmentByteResponseResultObject = attachmentBusinessService.electronicPolicyDownload(null, null, attachmentIdList.toArray(new String[attachmentIdList.size()]));
        //保存
        AttachmentByteResponse data = attachmentByteResponseResultObject.getData();

        applyBookRtfBytes = PrintCommon.mergePdfFiles(Arrays.asList(PrintCommon.pdfEvenPage(applyBookRtfBytes,language), data.getFileByte()));

        AttachmentRequest attachmentRequest = new AttachmentRequest();
        attachmentRequest.setFileContent(new BASE64Encoder().encode(applyBookRtfBytes));
        attachmentRequest.setFileSuffix("pdf");
        attachmentRequest.setFileType("document/pdf");
        AttachmentResponse attachmentResponse = attachmentBusinessService.uploadMediaBase64(AttachmentTermEnum.MEDIA_TYPE.DOCUMENT.name(), attachmentRequest);
        attachmentResponse.setTemplateType(GROUP_RENEWAL.name());
        electronicPolicyGeneratorRequest.setAttachmentResponseList(Arrays.asList(attachmentResponse));
    }

    private void policyConvertApplyData(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) {
        String content = electronicPolicyGeneratorRequest.getContent();
        GroupAttachPolicyBo policyBo = JSON.parseObject(content, GroupAttachPolicyBo.class);
        GroupAttachApplyBo applyBo = new GroupAttachApplyBo();
        applyBo.setApplyId(policyBo.getApplyId());
        applyBo.setApplyNo(policyBo.getApplyNo());
        GroupAttachApplicantBo groupApplicant = policyBo.getGroupApplicant();
        if (AssertUtils.isNotNull(groupApplicant)) {
            applyBo.setGroupApplicant((GroupAttachApplyApplicantBo) this.converterObject(groupApplicant, GroupAttachApplyApplicantBo.class));
        }
        GroupAttachApplyInsuredCollectBo applyInsuredCollect = policyBo.getApplyInsuredCollect();
        if (AssertUtils.isNotNull(applyInsuredCollect)) {
            applyInsuredCollect.setTotalQuantity(applyInsuredCollect.getEffectiveQuantity());
            applyBo.setApplyInsuredCollect(applyInsuredCollect);
        }
        List<GroupAttachCoverageBo> listGroupCoverage = policyBo.getListGroupCoverage();
        if (AssertUtils.isNotEmpty(listGroupCoverage)) {
            List<GroupAttachApplyCoverageBo> groupAttachApplyCoverageBoList = (List<GroupAttachApplyCoverageBo>) this.converterList(listGroupCoverage, new TypeToken<List<GroupAttachApplyCoverageBo>>() {
            }.getType());
            applyBo.setListGroupCoverage(groupAttachApplyCoverageBoList);
        }
        GroupAttachPolicyPremiumBo policyPremium = policyBo.getPolicyPremium();
        if (AssertUtils.isNotNull(policyPremium)) {
            applyBo.setReceivablePremium(policyPremium.getActualPremium());
            applyBo.setReceivableAddPremium(policyPremium.getActualPremium());
        }
        List<GroupAttachApplyAccountBo> accountList = policyBo.getAccountList();
        applyBo.setApplyAccountList(accountList);
        GroupAttachAgentBo groupAgent = policyBo.getGroupAgent();
        applyBo.setGroupAgent((GroupAttachApplyAgentBo) this.converterObject(groupAgent, GroupAttachApplyAgentBo.class));
        applyBo.setApplyDate(policyBo.getEffectiveDate());
        applyBo.setAutoRenewalInsurance(NO.name());
        applyBo.setPaymentMode(policyBo.getPaymentMode());
        electronicPolicyGeneratorRequest.setContent(JSON.toJSONString(applyBo));
    }

}
