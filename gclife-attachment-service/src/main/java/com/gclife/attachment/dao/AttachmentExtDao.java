package com.gclife.attachment.dao;

import com.gclife.attachment.core.jooq.tables.pojos.AttachmentPo;
import com.gclife.attachment.core.jooq.tables.pojos.ImportExportTemplatePo;
import com.gclife.common.dao.base.BaseDao;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 用于操作attachment表的接口
 */
public interface AttachmentExtDao extends BaseDao{


    /**
     * 根据媒体ID获取媒体信息
     *
     * @param mediaId 数据表中主键
     * @return AttachmentPo
     */
    AttachmentPo loadAttachmentPo(String mediaId);

    /**
     * 获取模版
     * @param templateCode
     * @return
     */
    ImportExportTemplatePo getTemplate(String templateCode);

    /**
     * 查询附件集合
     * @param mediaIds
     * @return
     */
    List<AttachmentPo> queryAttachment(List<String> mediaIds);

    /**
     * 根据 attachmentIds 获取 AttachmentPo 集合
     *
     * @param attachmentIds
     * @return
     */
    List<AttachmentPo> listAttachmentPoByPk(List<String> attachmentIds);

    /**
     * 根据主键获取 attachmentId
     *
     * @param attachmentId
     * @return
     */
    AttachmentPo getAttachmentPoByPk(String attachmentId);
}