package com.gclife.attachment.dao.policy.impl.renewal;

import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.print.policy.GCGroupStudentCareData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository("GROUP_RENEWAL_GC_GROUP_STUDENT_CARE_COI")
public class GroupRenewalDaoCoiImpl extends BaseBusinessServiceImpl implements PrintDao {
    @Autowired
    private GCGroupStudentCareData gcGroupStudentCareData;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        /******************************************************COI************************************************************************/
        byte[] policyCOIPdfBytes = gcGroupStudentCareData.getPolicyCOIPdfBytes(electronicPolicyGeneratorRequest);
        AttachmentResponse policyCOIResponse = iTextPdfService.savePdfAndUploadCoi(policyCOIPdfBytes);
        policyCOIResponse.setTemplateType("GROUP_RENEWAL_GC_GROUP_STUDENT_CARE_COI");
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(policyCOIResponse);
    }
}
