package com.gclife.attachment.dao.policy.impl.policy;

import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.print.policy.OffspringProsperitysData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 3号+产品
 *
 * <AUTHOR>
 * create 18+2+5
 * description: INSURANCE_CERTIFICATE 3号产品保险证件
 */
@Repository("POLICY_OFFSPRING_PROSPERITYS")
public class PolicyOffspringProsperitysDaoImpl implements PrintDao {

    @Autowired
    private OffspringProsperitysData offspringProsperitysData;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        electronicPolicyGeneratorRequest.setProductMainId(electronicPolicyGeneratorRequest.getProductId());
        List<PrintObject> printObjectList = offspringProsperitysData.getPolicyData(electronicPolicyGeneratorRequest);
        iTextPdfService.printPolicys(electronicPolicyGeneratorRequest, printObjectList);
    }

}
