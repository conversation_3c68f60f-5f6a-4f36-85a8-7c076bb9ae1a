package com.gclife.attachment.dao.policy.impl.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.policy.policy.PolicyAgentBo;
import com.gclife.attachment.model.policy.policy.PolicyBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.OffspringProsperityData;
import com.gclife.common.util.AssertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 3 号产品 旧版PRO88000000000001 #3号产品 已弃用
 * 新版#3号产品的产品ID是 PRO88000000000001V2018
 * <AUTHOR>
 * create 18+2+5
 * description: INSURANCE_CERTIFICATE 3号产品保险证件
 * @see PolicyOffspringProsperitysDaoImpl
 */
@Deprecated
@Repository("POLICY_OFFSPRING_PROSPERITY")
public class PolicyOffspringProsperityDaoImpl implements PrintDao {

    @Autowired
    private OffspringProsperityData offspringProsperityData;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        electronicPolicyGeneratorRequest.setProductMainId(electronicPolicyGeneratorRequest.getProductId());

        List<PrintObject> printObjectList = offspringProsperityData.getPolicyData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] inputBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        byte[] bytes = PrintCommon.fillData(inputBytes, printObjectList);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.POLICY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
        String content = electronicPolicyGeneratorRequest.getContent();
        PolicyBo policyBo = JSON.parseObject(content, PolicyBo.class);
        PolicyAgentBo policyAgent = policyBo.getPolicyAgent();
        if (!AssertUtils.isNotNull(policyAgent)) {
            policyAgent = new PolicyAgentBo();
        }
        Map<String, Object> map = new HashMap<>();
        //代理人代码
        map.put("agentCode", PrintCommon.getPrintString(policyAgent.getAgentCode(), 3));
        map.put("policyNo", PrintCommon.getPrintString(policyBo.getPolicyNo(), 3));
        map.put("approveDate", PrintCommon.getPrintString(policyBo.getApproveDate(), 3));
        iTextPdfService.addAcknowledgmentLetter(map,electronicPolicyGeneratorRequest);
    }
}
