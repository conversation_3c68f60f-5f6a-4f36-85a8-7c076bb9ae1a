package com.gclife.attachment.dao.policy.impl.endorse;

import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.endorse.EndorseReinstatementData;
import com.gclife.attachment.service.print.endorse.EndorseSurrenderData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 14:45 2018/9/26
 * @ Description: 退保
 * @ Modified By:
 * @ Version:
 */
@Repository("SURRENDER")
public class SurrenderDaoImpl implements PrintDao {
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private EndorseSurrenderData endorseSurrenderData;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }


    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<PrintObject> printObjectList = endorseSurrenderData.getData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] inputBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        byte[] bytes = PrintCommon.fillData(inputBytes, printObjectList);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
