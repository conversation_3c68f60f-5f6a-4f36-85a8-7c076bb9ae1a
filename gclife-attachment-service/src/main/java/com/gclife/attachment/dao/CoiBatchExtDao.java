package com.gclife.attachment.dao;


import com.gclife.attachment.core.jooq.tables.pojos.CoiBatchPo;
import com.gclife.common.dao.base.BaseDao;

import java.util.List;

public interface CoiBatchExtDao extends BaseDao {
    /**
     * 查询附件集合
     * @param batchId
     * @return
     */
    List<CoiBatchPo> queryAttachment(String batchId);

   /* *//**
     * 查询附件集合
     * @param businessId
     * @param language
     * @return
     *//*
    List<CoiBatchPo> queryAttachmentByCondition(String businessId,String language);*/
}
