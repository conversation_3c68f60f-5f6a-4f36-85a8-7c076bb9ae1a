package com.gclife.attachment.dao.policy.impl.plan;

import com.gclife.attachment.common.PrintCommon;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfGState;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

import java.io.ByteArrayOutputStream;

/**
 * <AUTHOR>
 * @description
 * @date 2020/5/19 6:26 下午
 */
public class PlanCommon {


    /**
     * 计划书添加背景
     * 设置页码
     *
     * @param bytes
     * @return
     * @throws Exception
     */
    public static byte[] updateAndSavePageNumber(byte[] bytes) throws Exception {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        PdfReader pdfReader = new PdfReader(bytes);
        //生成文件
        PdfStamper pdfStamper = new PdfStamper(pdfReader, byteArrayOutputStream);
        /* // 重新设置计划书页码
        int numberOfPages = pdfReader.getNumberOfPages() - 2;

        Rectangle rectangle = pdfReader.getPageSize(pdfReader.getNumberOfPages());

        float pageWidth = rectangle.getWidth();

        float x = pageWidth / 2;
        float y = 12f;
        for (int i = 1; i <= numberOfPages; i++) {
            PdfContentByte content = pdfStamper.getOverContent(i + 1);
            //进行遮挡
            content.saveState();
            content.setColorFill(BaseColor.WHITE);
            content.rectangle(x-30, y-10, 60, 20);
            content.fill();
            content.restoreState();
            //添加文字
            content.beginText();
            content.setFontAndSize(EN_US_FONT, 9f);
            content.setTextMatrix(200, 200);
            content.showTextAligned(Element.ALIGN_CENTER, i + " / " + numberOfPages, x, y, 0);//左边距、下边距
            content.endText();
        } */

        // 透明度设置
        PdfGState pdfGState = new PdfGState();
        pdfGState.setFillOpacity(1f);
        //设置首页背景
        PdfContentByte overContentFirst = pdfStamper.getUnderContent(1);
        overContentFirst.setGState(pdfGState);
        overContentFirst.addImage(PrintCommon.addImage(PrintCommon.PLAN_FIRST, 841.75f, 594.33f, 0f, 0f));
        //设置未尾页背景
        PdfContentByte overContentLast = pdfStamper.getOverContent(pdfReader.getNumberOfPages());
        overContentLast.addImage(PrintCommon.addImage(PrintCommon.PLAN_LAST, 841.75f, 594.33f, 0f, 0f));

        pdfStamper.close();
        pdfReader.close();
        return byteArrayOutputStream.toByteArray();
    }

}
