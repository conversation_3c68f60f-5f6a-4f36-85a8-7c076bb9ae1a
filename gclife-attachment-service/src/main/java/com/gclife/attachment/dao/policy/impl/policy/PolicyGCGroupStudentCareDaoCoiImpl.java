package com.gclife.attachment.dao.policy.impl.policy;

import com.alibaba.fastjson.JSON;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.print.policy.GCGroupStudentCareData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import static com.gclife.attachment.model.config.AttachmentPolicyEnum.COI_BOOK;

/**
 * 29号产品coi打印
 * <AUTHOR>
 * @date 2023/08/08
 */
@Repository("POLICY_GC_GROUP_STUDENT_CARE_COI")
public class PolicyGCGroupStudentCareDaoCoiImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private GCGroupStudentCareData gcGroupStudentCareData;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        /******************************************************COI************************************************************************/
        byte[] policyCOIPdfBytes = gcGroupStudentCareData.getPolicyCOIPdfBytes(electronicPolicyGeneratorRequest);
        this.getLogger().info("policyCOIPdfBytes:::::" + JSON.toJSONString(policyCOIPdfBytes.length));
        AttachmentResponse policyCOIResponse = iTextPdfService.savePdfAndUploadCoi(policyCOIPdfBytes);
        this.getLogger().info("policyCOIResponse:::::" + JSON.toJSONString(policyCOIResponse));
        policyCOIResponse.setTemplateType(COI_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(policyCOIResponse);
    }
}
