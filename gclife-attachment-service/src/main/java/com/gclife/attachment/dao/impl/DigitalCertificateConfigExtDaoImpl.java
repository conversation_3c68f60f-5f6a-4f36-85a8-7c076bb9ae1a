package com.gclife.attachment.dao.impl;

import com.gclife.attachment.dao.DigitalCertificateConfigExtDao;
import com.gclife.attachment.model.bo.DigitalCertificateConfigBo;
import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.common.dao.base.impl.BaseDaoImpl;
import com.gclife.common.exception.RequestException;
import org.jooq.Record;
import org.jooq.SelectConditionStep;
import org.springframework.stereotype.Repository;

import static com.gclife.attachment.core.jooq.Tables.DIGITAL_CERTIFICATE_CONFIG;

/**
 * <AUTHOR>
 * create 18-1-5
 * description: 查询digital_certificate_config表的实现类
 */
@Repository
public class DigitalCertificateConfigExtDaoImpl extends BaseDaoImpl implements DigitalCertificateConfigExtDao {

    /**
     * 查询digital_certificate_config表中的记录
     *
     * @param usageType 证书用途
     * @return DigitalCertificateConfigBo
     */
    @Override
    public DigitalCertificateConfigBo loadDigitalCertificateConfig(String usageType) {

        DigitalCertificateConfigBo digitalCertificateConfigBo;
        try {
            SelectConditionStep<Record> selectConditionStep = this.getDslContext()
                    .select(DIGITAL_CERTIFICATE_CONFIG.fields())
                    .from(DIGITAL_CERTIFICATE_CONFIG)
                    .where(DIGITAL_CERTIFICATE_CONFIG.USAGE_TYPE.eq(usageType));


            digitalCertificateConfigBo = selectConditionStep.fetchOneInto(DigitalCertificateConfigBo.class);
        } catch (Exception e) {
            e.printStackTrace();
            this.getLogger().error(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_DIGITAL_CERTIFICATE_CONFIG.getValue());
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_QUERY_DIGITAL_CERTIFICATE_CONFIG);
        }
        return digitalCertificateConfigBo;
    }
}
