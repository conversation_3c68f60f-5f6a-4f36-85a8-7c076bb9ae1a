package com.gclife.attachment.dao.policy.impl.insured;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.insured.TeamLifeOfInsuredData;
import com.gclife.attachment.service.print.policy.TeamLifeShieldInsuranceData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.APPLY;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.GROUP_INSURED_LIST;

/**
 * 团险1+产品被保险人清单
 *
 * <AUTHOR>
 * @date 5/18/2022
 */
@Repository("GROUP_INSURED_G3_LIST")
public class LifeShieldListOfInsuredDaoImpl extends BaseBusinessServiceImpl implements PrintDao {
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private TeamLifeOfInsuredData teamLifeOfInsuredData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private TeamLifeShieldInsuranceData teamLifeShieldInsuranceData;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        List<byte[]> bytesList = new ArrayList<>();
        //投保单
        Map<String, Object> applyStringObjectMap = teamLifeShieldInsuranceData.getApplyData(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setPdfType(APPLY.name());
        PdfTemplateConfigBo applyPdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] applyBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(applyPdfTemplateConfigBo.getAttachmentId());
        byte[] applyBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), applyStringObjectMap, applyBookRtfBytes);
        bytesList.add(applyBytes);

        // 被保人清单
        List<Map<String, Object>> teamLifeOfInsuredDataData = teamLifeOfInsuredData.getData(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setPdfType(GROUP_INSURED_LIST.name());
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] insuredBookRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        for (Map<String, Object> stringObjectMap : teamLifeOfInsuredDataData) {
            byte[] bytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), stringObjectMap, insuredBookRtfBytes);
            bytesList.add(bytes);
        }
        // 合并
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.mergePdfFiles(bytesList, language));
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

}
