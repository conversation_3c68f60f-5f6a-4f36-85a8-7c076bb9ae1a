package com.gclife.attachment.dao.policy.impl.policy;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.config.AttachmentPolicyEnum;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.policy.TeamLifeShieldInsuranceData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.gclife.attachment.common.PrintCommon.TERMS_VERSION;
import static com.gclife.attachment.model.config.AttachmentPolicyEnum.POLICY_TERMS_BOOK;

/**
 * 1号产品
 * 护身福计划书
 */
@Repository("TEAM_POLICY_LIFE_SHIELD_INSURANCE")
public class TeamPolicyLifeShieldInsuranceDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private TeamLifeShieldInsuranceData teamLifeShieldInsuranceData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);

    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        Map<String, Object> policyData = teamLifeShieldInsuranceData.getPolicyData(electronicPolicyGeneratorRequest);
        /******************************************************保险证************************************************************************/
        byte[] bytes = teamLifeShieldInsuranceData.getPolicyPdfBytes(electronicPolicyGeneratorRequest, policyData);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        attachmentResponse.setTemplateType(AttachmentPolicyEnum.POLICY_BOOK.name());
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
        /******************************************************保险条款************************************************************************/
        List<Map<String, Object>> coverageListMap = (List<Map<String, Object>>) policyData.get("coverageListMap");
        long seq = 0L;
        for (Map<String, Object> coverageMap : coverageListMap) {
            AttachmentResponse attachment = null;
            String productId = coverageMap.get("productId")+"";
            if("PRO8800000000000G12".equals(productId)){
                byte[] terms = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_" + coverageMap.get("productId") + "_" + language);
                byte[] tableOfPremium = attachmentBusinessService.loadOssObjectByAttachmentId(TERMS_VERSION + "TERMS_PRO8800000000000G12_TABLE_OF_PREMIUM_" + language);
                tableOfPremium = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), policyData, tableOfPremium);
                terms = PrintCommon.mergePdfFiles(Arrays.asList(terms, tableOfPremium));
                attachment = iTextPdfService.savePdfAndUpload(terms);
                attachment.setSeq(seq++);
                attachment.setTemplateType(AttachmentPolicyEnum.POLICY_TERMS_BOOK.name());
            }else {
                attachment = new AttachmentResponse(TERMS_VERSION + "TERMS_" + coverageMap.get("productId") + "_" + language, POLICY_TERMS_BOOK.name(), seq++);
            }
            electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachment);
        }
        /******************************************************签收回执************************************************************************/
        iTextPdfService.addAcknowledgmentLetter(policyData, electronicPolicyGeneratorRequest);
        /******************************************************封页首刊************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getGroupCover(electronicPolicyGeneratorRequest.getProductId(), language));
        /******************************************************客户服务指南************************************************************************/
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(PrintCommon.getCustomerServiceInstruction(language));
    }


}
