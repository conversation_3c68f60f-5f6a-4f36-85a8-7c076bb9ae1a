package com.gclife.attachment.dao.policy.impl.renewal;

import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.config.AttachmentTermEnum;
import com.gclife.attachment.model.policy.PrintObject;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.renewal.RenewalData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;

/**
 * 续期缴费通知单
 * <AUTHOR> BaiZhongYing
 * @Descrption :
 * @Date : 11:44 2018/12/13
 */
@Repository("POLICY_RENEWAL")
public class RenewalDaoImpl implements PrintDao {

    @Autowired
    private RenewalData renewalData;
    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private ITextPdfService iTextPdfService;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        List<PrintObject> printObjectList = renewalData.getRenewalData(electronicPolicyGeneratorRequest);
        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] inputBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        byte[] bytes = PrintCommon.fillData(inputBytes, printObjectList);
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(bytes);
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }

}
