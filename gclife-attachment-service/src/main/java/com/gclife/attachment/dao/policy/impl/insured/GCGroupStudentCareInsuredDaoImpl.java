package com.gclife.attachment.dao.policy.impl.insured;

import com.gclife.attachment.common.OracleBiUtils;
import com.gclife.attachment.common.PrintCommon;
import com.gclife.attachment.dao.PdfTemplateConfigExtDao;
import com.gclife.attachment.dao.policy.PrintDao;
import com.gclife.attachment.itextpdf.ITextPdfService;
import com.gclife.attachment.model.bo.PdfTemplateConfigBo;
import com.gclife.attachment.model.request.ElectronicPolicyGeneratorRequest;
import com.gclife.attachment.model.response.AttachmentResponse;
import com.gclife.attachment.service.business.AttachmentBusinessService;
import com.gclife.attachment.service.print.insured.GCGroupStudentCareInsuredData;
import com.gclife.attachment.service.print.policy.GCGroupStudentCareData;
import com.gclife.common.service.impl.BaseBusinessServiceImpl;
import com.gclife.common.util.AssertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.gclife.attachment.model.config.AttachmentPdfEnum.APPLY;
import static com.gclife.attachment.model.config.AttachmentPdfEnum.GROUP_INSURED_LIST;

/**
 * <AUTHOR>
 * @date 2023/08/08
 */
@Repository("GROUP_INSURED_29_LIST")
public class GCGroupStudentCareInsuredDaoImpl extends BaseBusinessServiceImpl implements PrintDao {

    @Autowired
    private PdfTemplateConfigExtDao pdfTemplateConfigExtDao;
    @Autowired
    private AttachmentBusinessService attachmentBusinessService;
    @Autowired
    private GCGroupStudentCareInsuredData getData;
    @Autowired
    private ITextPdfService iTextPdfService;
    @Autowired
    private GCGroupStudentCareData gcGroupStudentCareData;

    @Override
    public void printZH_CN(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printEN_US(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void printKM_KH(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        this.print(electronicPolicyGeneratorRequest);
    }

    @Override
    public void print(ElectronicPolicyGeneratorRequest electronicPolicyGeneratorRequest) throws Exception {
        String language = electronicPolicyGeneratorRequest.getLanguage();
        String renewalFlag = electronicPolicyGeneratorRequest.getRenewalFlag();
        List<byte[]> bytesList = new ArrayList<>();
        //投保单
        //续保不需要打印投保单清单
        if (!AssertUtils.isNotEmpty(renewalFlag)) {
            Map<String, Object> applyData = gcGroupStudentCareData.getApplyData(electronicPolicyGeneratorRequest);
            electronicPolicyGeneratorRequest.setPdfType(APPLY.name());
            PdfTemplateConfigBo applyPdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
            byte[] groupApplyDataRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(applyPdfTemplateConfigBo.getAttachmentId());
            byte[] applyBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), applyData, groupApplyDataRtfBytes);
            bytesList.add(applyBytes);
        }
        // 被保人清单
        Map<String, Object> getDataData = getData.getData(electronicPolicyGeneratorRequest);
        electronicPolicyGeneratorRequest.setPdfType(GROUP_INSURED_LIST.name());

        PdfTemplateConfigBo pdfTemplateConfigBo = pdfTemplateConfigExtDao.loadPdfTemplateConfig(electronicPolicyGeneratorRequest);
        byte[] groupInsuranceRtfBytes = attachmentBusinessService.loadOssObjectByAttachmentId(pdfTemplateConfigBo.getAttachmentId());
        byte[] insuranceBytes = OracleBiUtils.mapDateAndRtfToPdf(this.getConfigValue("resources.service_name.gclife-office-service", null), getDataData, groupInsuranceRtfBytes);
        bytesList.add(insuranceBytes);
        // 合并投保单 被保人清单成一个 PDF
        AttachmentResponse attachmentResponse = iTextPdfService.savePdfAndUpload(PrintCommon.mergePdfFiles(bytesList, language));
        electronicPolicyGeneratorRequest.getAttachmentResponseList().add(attachmentResponse);
    }
}
