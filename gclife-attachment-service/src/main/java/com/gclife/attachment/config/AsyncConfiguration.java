package com.gclife.attachment.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR>
 * @date 2021/12/8
 */
@EnableAsync
@Configuration
public class AsyncConfiguration {

    @Bean("taskExecutor")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        // 核心线程数
        taskExecutor.setCorePoolSize(10);
        // 最大线程数
        taskExecutor.setMaxPoolSize(20);
        // 队列大小
        taskExecutor.setQueueCapacity(200);
        // 线程活跃时间
        taskExecutor.setKeepAliveSeconds(60);
        // 默认线程名称
        taskExecutor.setThreadNamePrefix("taskExecutor--");
        // 所有任务结束后再关闭线程池
        // taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        // 到达指定时间，还有线程没执行完，不再等待，关闭线程池
        // taskExecutor.setAwaitTerminationSeconds(60);
        // 创建即初始化线程池
        taskExecutor.initialize();
        return taskExecutor;
    }
}
