package com.gclife.attachment.config.rabbitmq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/10/9
 */
@Configuration
public class RabbitMQTopicConfiguration {

    /**
     * topic交换机名称
     */
    public static final String TOPIC_EXCHANGE = "attachment.topicExchange";
    /**
     * 防止OOM队列名称 不能同时进行产生单证、PDF转图片等操作，否则OOM
     */
    public static final String PREVENT_OOM_QUEUE = "attachment.prevent.oom.queue";
    /**
     * 防止OOM专用路由键
     */
    public static final String PREVENT_OOM_ROUTING_KEY = "attachment.prevent.oom";

    /**
     * 创建topic交换机
     *
     * @return
     */
    @Bean
    public TopicExchange topicExchange() {
        return new TopicExchange(TOPIC_EXCHANGE);
    }

    /**
     * 创建防止OOM专用队列
     *
     * @return
     */
    @Bean
    public Queue preventOOMQueue() {
        return new Queue(PREVENT_OOM_QUEUE, true);
    }

    /**
     * 绑定 防止OOM专用队列到topic交换机，只消费路由键是 attachment.prevent.oom 的消息
     *
     * @return
     */
    @Bean
    public Binding preventOOMBinding() {
        return BindingBuilder.bind(preventOOMQueue()).to(topicExchange()).with(PREVENT_OOM_ROUTING_KEY);
    }
}
