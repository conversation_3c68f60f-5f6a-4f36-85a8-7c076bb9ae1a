package com.gclife.attachment.model.feign;

import com.documents4j.api.DocumentType;

/**
 * @ Author     : BaiZhongYing
 * @ Date       : Created in 15:57 2019/3/29
 * @ Description:
 * @ Modified By:
 * @ Version: 1
 */
public class FileConversionRequest {

    /**
     * 文件流
     */
    private byte[] byteFile;
    /**
     * 修改文件类型
     */
    private String fileType = Value.APPLICATION;
    private String fileSubtype = Value.RTF;
    /**
     * 修改后文件类型
     */
    private String toFileType = Value.APPLICATION;
    private String toFileSubtype = Value.PDF;


    public static class Value {
        public static final String APPLICATION = DocumentType.Value.APPLICATION;
        public static final String TEXT = DocumentType.Value.TEXT;

        public static final String DOC = DocumentType.Value.DOC;
        public static final String DOCX = DocumentType.Value.DOCX;
        public static final String WORD_ANY = DocumentType.Value.WORD_ANY;

        public static final String XLS = DocumentType.Value.XLS;
        public static final String XLSX = DocumentType.Value.XLSX;
        public static final String EXCEL_ANY = DocumentType.Value.EXCEL_ANY;
        public static final String ODS = DocumentType.Value.ODS;

        public static final String PDF = DocumentType.Value.PDF;
        public static final String PDFA = DocumentType.Value.PDFA;

        public static final String RTF = DocumentType.Value.RTF;

        public static final String XML = DocumentType.Value.XML;
        public static final String MHTML = DocumentType.Value.MHTML;

        public static final String CSV = DocumentType.Value.CSV;
        public static final String PLAIN = DocumentType.Value.PLAIN;
    }

    public byte[] getByteFile() {
        return byteFile;
    }

    public void setByteFile(byte[] byteFile) {
        this.byteFile = byteFile;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileSubtype() {
        return fileSubtype;
    }

    public void setFileSubtype(String fileSubtype) {
        this.fileSubtype = fileSubtype;
    }

    public String getToFileType() {
        return toFileType;
    }

    public void setToFileType(String toFileType) {
        this.toFileType = toFileType;
    }

    public String getToFileSubtype() {
        return toFileSubtype;
    }

    public void setToFileSubtype(String toFileSubtype) {
        this.toFileSubtype = toFileSubtype;
    }
}
