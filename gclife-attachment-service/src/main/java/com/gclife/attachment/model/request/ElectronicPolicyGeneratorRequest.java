package com.gclife.attachment.model.request;

import com.gclife.attachment.model.response.AttachmentResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * create 18-1-5
 * description:电子保单生成请求
 */
@Data
public class ElectronicPolicyGeneratorRequest {

    @ApiModelProperty(value = "PDF文件类型", example = "{PLAN:计划书,POLICY:保单,INSURANCE_CERTIFICATE:保险证,APPLY:投保单}")
    String pdfType;

    @ApiModelProperty(value = "PDF文件版本", example = "KM_KH(\"柬埔寨\"),EN_US(\"中文\"),ZH_CN(\"中文\"）")
    String language;

    @ApiModelProperty(value = "产品ID", example = "PRO88000000000002 2号产品Id PRO88000000000001 3号产品Id")
    String productMainId;

    @ApiModelProperty(value = "产品ID", example = "PRO88000000000002 2号产品Id PRO88000000000001 3号产品Id")
    String productId;

    @ApiModelProperty(value = "写入到PDF文件的内容，json字符串", example = "{\"applicant\": \"123\"}")
    String content;

    @ApiModelProperty(value = "使用消息队列进行异步生成标识", example = "true")
    Boolean asyncFlag;

    @ApiModelProperty(value = "PDF转图片标识", example = "true")
    Boolean convertImageFlag;

    @ApiModelProperty(value = "续保类型", example = "{PLAN:计划书,POLICY:保单,INSURANCE_CERTIFICATE:保险证,APPLY:投保单}")
    String renewalFlag;

    @ApiModelProperty(value = "模板id", example = "考试模板id")
    String templateId;

    /**
     * 请求产生返回数据
     */
    private List<AttachmentResponse> attachmentResponseList = new ArrayList<>();

}
