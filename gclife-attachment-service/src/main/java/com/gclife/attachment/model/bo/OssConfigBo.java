package com.gclife.attachment.model.bo;

import com.gclife.attachment.core.jooq.tables.pojos.OssConfigPo;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 映射oss_config表的类
 */
public class OssConfigBo extends OssConfigPo {

    private String accessKeyId;

    private String accessKeySecret;

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }
}