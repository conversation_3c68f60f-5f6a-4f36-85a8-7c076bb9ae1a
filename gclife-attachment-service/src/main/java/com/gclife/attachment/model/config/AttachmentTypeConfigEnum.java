package com.gclife.attachment.model.config;


import com.gclife.common.model.inter.IEnum;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 单证类型定义
 */
public enum AttachmentTypeConfigEnum implements IEnum {

    /**
     * 年月
     */
    YEAR_MONTH("year_month","单证年月","CERTIFY"),

    /**
     * 日
     */
    DAY("day","单证日","CERTIFY"),

    /**
     * 文件名
     */
    FILE_NAME("file_name","单证文件名","CERTIFY"),

    /**
     * 单证类型
     */
    MEDIA_TYPE("media_type","单证类型","CERTIFY"),

    /**
     * 单证附件类型
     */
    CERTIFY_ATTACHMENT("0001","单证附件类型","CERTIFY");


    private String code;

    private String value;

    private String group;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getGroup() {
        return group;
    }

    @Override
    public String getCode(String value, String group) {
        return null;
    }

    @Override
    public String getValue(String code, String group) {
        return null;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    AttachmentTypeConfigEnum(String code, String value, String group) {
        this.code = code;
        this.value = value;
        this.group = group;
    }

}