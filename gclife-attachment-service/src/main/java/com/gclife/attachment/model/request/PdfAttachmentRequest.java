package com.gclife.attachment.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: PDF附件上传接口请求参数
 */

public class PdfAttachmentRequest extends AttachmentRequest {

    @ApiModelProperty(value = "Pdf模板的附件ID",example = "ATTACHMENT_a28a75aa-258-11e7-b411-bbede127cc64")
    private String pdfTemplateId;

    public String getCertificateId() {
        return certificateId;
    }

    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }

    @ApiModelProperty(value = "证书的附件ID",example = "ATTACHMENT_5e2028aa-b475-214-b867-43c4e67e1b27")
    private String certificateId;

    @ApiModelProperty(value = "密钥库的附件ID",example = "ATTACHMENT_37d0f11a-6987-11e7-8b77-1bc68b56d28e")
    private String keyStoreId;

    public String getKeyStoreId() {
        return keyStoreId;
    }

    public void setKeyStoreId(String keyStoreId) {
        this.keyStoreId = keyStoreId;
    }

    public String getPdfTemplateId() {
        return pdfTemplateId;
    }

    public void setPdfTemplateId(String pdfTemplateId) {
        this.pdfTemplateId = pdfTemplateId;
    }

}
