package com.gclife.attachment.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件下载接口返回参数
 */
@ApiModel(value = "Attachment",description = "附件返回参数")
public class AttachmentResponse {

    @ApiModelProperty(example = "媒体ID")
    private String mediaId;
    @ApiModelProperty(example = "路径")
    private String url;
    @ApiModelProperty(example = "标识")
    private String mark;
    @ApiModelProperty(example = "模板名称")
    private String templateName;
    @ApiModelProperty(example = "附件类型")
    private String templateType;
    @ApiModelProperty(example = "排序")
    private Long seq;
    @ApiModelProperty(example = "文件流")
    private byte[] bytes;
    @ApiModelProperty(example = "文件名",hidden = true)
    private String fileName;

    @ApiModelProperty(example = "文件后缀,png",value = "png")
    private String fileSuffix;

    @ApiModelProperty(example = "预览url",value = "png")
    private String attachmentUrl;

    @ApiModelProperty(example = "pdf转图片的url数组")
    private List<String> pdfTransformImageUrl;
    @ApiModelProperty(example = "attachmentDomain")
    private String attachmentDomain;


    public AttachmentResponse() {
    }

    public AttachmentResponse(String mediaId, String templateType) {
        this.mediaId = mediaId;
        this.templateType = templateType;
    }

    public AttachmentResponse(String mediaId, String templateType, Long seq) {
        this.mediaId = mediaId;
        this.templateType = templateType;
        this.seq = seq;
    }

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }



    public String getMark() {
        return mark;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public Long getSeq() {
        return seq;
    }

    public void setSeq(Long seq) {
        this.seq = seq;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileSuffix() {
        return fileSuffix;
    }

    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    public List<String> getPdfTransformImageUrl() {
        return pdfTransformImageUrl;
    }

    public void setPdfTransformImageUrl(List<String> pdfTransformImageUrl) {
        this.pdfTransformImageUrl = pdfTransformImageUrl;
    }

    public String getAttachmentDomain() {
        return attachmentDomain;
    }

    public void setAttachmentDomain(String attachmentDomain) {
        this.attachmentDomain = attachmentDomain;
    }
}
