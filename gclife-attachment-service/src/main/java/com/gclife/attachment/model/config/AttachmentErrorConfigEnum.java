package com.gclife.attachment.model.config;


import com.gclife.common.model.inter.IEnum;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件错误枚举类型定义
 */
public enum AttachmentErrorConfigEnum implements IEnum {

    /**
     * 编码规则
     * 参数错误: 模块名称_PARAMETER_表名称_字段名称_验证结果{IS_NOT_NULL:不能为空,FORMAT_ERROR:格式错误,IS_NOT_FOUND_VALUE:未找到值}
     * 业务错误: 模块名称_BUSINESS_规则描述_验证结果{IS_NOT_FOUND_OBJECT:未找到对象}
     * 数据转换错误: 模块名称_CONVERSION_DTO对象名称_ERROR
     * 保存错误: 模块名称_SAVE_表名称_ERROR
     * 查询错误: 模块名称_QUERY_表名称_查询结果(ERROR:查询出错);
     * 通用错误: 模块名称_FAIL
     */


    ATTACHMENT_PARAMETER_FILES_FORMAT_ERROR("2090000000", "上传文件集合格式异常", "ATTACHMENT"),
    CERTIFY_PARAMETER_ATTACHMENT_TYPE_IS_NOT_NULL("2090000001", "附件类型不能为空", "ATTACHMENT"),
    CERTIFY_PARAMETER_ATTACHMENT_TYPE_IS_NOT_FOUND_VALUE("2090000002", "未找到上传的附件类型值", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_TYPE_NOT_MATCH_FILE_TYPE_FORMAT_ERROR("2090000003", "系统暂不支持此类型的文件上传", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_FILENAME_IS_NOT_NULL("2090000004", "附件名称不能为空", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_FILECONTENT_IS_NOT_NULL("2090000005", "附件内容不能为空", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_FILESUFFEX_IS_NOT_NULL("2090000006", "附件后缀不能为空", "ATTACHMENT"),
    ATTACHMENT_BUSINESS_OSS_ERROR("2090000007", "无访问权限", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_MEDIA_ID_IS_NOT_NULL("2090000008", "媒体ID不能为空", "ATTACHMENT"),
    ATTACHMENT_UPLOAD_ERROR("2090000009", "附件上传异常", "ATTACHMENT"),
    ATTACHMENT_UPLOAD_XLS_ERROR("2090000010", "请上传.xlsx的EXECL表格", "ATTACHMENT"),
    ATTACHMENT_DOWNLOAD_ERROR("2090000011", "附件下载异常", "ATTACHMENT"),
    ATTACHMENT_BUSINESS_GETKEY_ERROR("2090000012", "获取附件上传路径异常", "ATTACHMENT"),
    ATTACHMENT_BUSINESS_ZOOMSUFFIX_ERROR("2090000013", "获取附件缩放后缀异常", "ATTACHMENT"),
    ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_ZOOM("2090000014", "不是图片类型，不能获取缩略图", "ATTACHMENT"),
    ATTACHMENT_BUSINESS_ATTACHMENT_ALIYUN_IS_NOT_FOUND_OBJECT("2090000015", "访问阿里云OSS异常", "ATTACHMENT"),
    ATTACHMENT_BUSINESS_ATTACHMENT_IS_NOT_FOUND_OBJECT("2090000016", "未找到媒体信息", "ATTACHMENT"),
    ATTACHMENT_QUERY_ATTACHMENT_ERROR("2090000017", "附件查询信息查异常", "ATTACHMENT"),
    ATTACHMENT_SAVE_ATTACHMENT_ERROR("2090000018", "保存附件信息异常", "ATTACHMENT"),
    ATTACHMENT_FAIL("2090000019", "文件处理模块异常", "ATTACHMENT"),
    ATTACHMENT_QUERY_DIGITAL_CERTIFICATE_CONFIG("2090000020", "查询数字证书异常", "ATTACHMENT"),
    ATTACHMENT_QUERY_PDF_TEMPLATE_CONFIG("2090000021", "查询PDF模板异常", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_PDF_TYPE_IS_NOT_NULL("2090000022", "PDF类型不能为空", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_PDF_TYPE_DETAILS_IS_NOT_NULL("2090000023", "PDF类型详情不能为空", "ATTACHMENT"),
    ATTACHMENT_POLICY_GENERATE_FAIL("2090000024", "保单生成失败", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_PDF_PRODUCT_ID_IS_NOT_NULL("2090000025", "产品Id 不能为空", "ATTACHMENT"),
    ATTACHMENT_IMPORT_EXPORT_TEMPLATE_QUERY_ATTACHMENT_ERROR("2090000026", "附件查询信息查异常", "ATTACHMENT"),
    ATTACHMENT_IMPORT_EXPORT_TEMPLATE_IS_NOT_FOUND_OBJECT("2090000027", "未找到模版", "ATTACHMENT"),
    ATTACHMENT_PARAMETER_SECEN_STR_IS_NOT_NULL("2090000028", "产生二维码场景字符串不能为空", "ATTACHMENT"),
    ATTACHMENT_BUSINESS_EXPIRES_IN_IS_INVALID("2090000029", "附件已经失效", "ATTACHMENT"),
    ATTACHMENT_APP_EXPIRES_IN_NULL("2090000030", "APP附件不存在", "ATTACHMENT"),
    ATTACHMENT_EXPIRES_IN_NULL("2090000031", "APP附件不存在", "ATTACHMENT"),
    ;


    private String code;

    private String value;

    private String group;

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String getGroup() {
        return group;
    }

    @Override
    public String getCode(String value, String group) {
        return null;
    }

    @Override
    public String getValue(String code, String group) {
        return null;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    AttachmentErrorConfigEnum(String code, String value, String group) {
        this.code = code;
        this.value = value;
        this.group = group;
    }

}