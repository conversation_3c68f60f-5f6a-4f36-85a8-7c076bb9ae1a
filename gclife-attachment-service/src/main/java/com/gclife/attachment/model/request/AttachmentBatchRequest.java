package com.gclife.attachment.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件批量上传接口请求参数
 */
public class AttachmentBatchRequest {

    @ApiModelProperty(value = "附件集合",example = "[{\"fileContent\":\"iVBORw0KGgoAAAANSUhEUgAAAB4AAAAgCAIAAACKIl8oAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABgUlEQVRIic1WMa6CQBCd/WzA2GOrVCQ2VJzBGk9AYmPhSYweQk4AlXewsjb0FPTQOb/YH0Jmd9n5Jn7/q2CYebx5u7MgEBHeg6838bqpd7udlDJJEgDYbDa/ohY2Q4QQthqmh2bqCV7+CwyGVFXFEeWEQTVHssK08E/skO122/d9HMfjICLy58BsiLOek/OHhgghTqeTs6xtW+dq0744nTIzqeo0TT3Pc/Le73f3y1FDFEXG+ADf96cTFKyDPtEs0zTzDkFE2yrNZrMsy5y8PywAUJbl9XoFgKZphrlIkkRvExhWDJlA70eYz+fDo6IoiCYHddd1JKQOfnW93+91uiAI8jx/PB4qqKw3UKvUAYgYhuG48fV6PeY9Ho9E8vP5NDZhMERdXC4XKSUJosVrc3C5XA4PyIYFgPP5PK60WXy73QzUOFo6mxzO0q1WK1p7OBxIqK5rws7ZEnqCAO07pA/baxHWeY2MsdYh0TTTnC8vyVksFjThNUUcfO6f759SfwMXDbpcfrByZgAAAABJRU5ErkJggg==\",\"fileName\":\"333.png\",\"fileSuffix\":\"png\"}]",required = true)
    private String listAttachment;

    public String getListAttachment() {
        return listAttachment;
    }

    public void setListAttachment(String listAttachment) {
        this.listAttachment = listAttachment;
    }
}