package com.gclife.attachment.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件下载接口返回参数，数据流类型
 */
@ApiModel(value = "Attachment",description = "附件字节数组返回参数")
public class AttachmentByteResponse {

    @ApiModelProperty(example = "媒体ID")
    private String mediaId;

    @ApiModelProperty(example = "文件流")
    private byte [] fileByte;

    @ApiModelProperty(example = "文件名",hidden = true)
    private String fileName;

    @ApiModelProperty(example = "文件后缀,png",value = "png")
    private String fileSuffix;

    @ApiModelProperty(example = "路径")
    private String url;

    @ApiModelProperty(example = "pdf转图片的url数组")
    private List<String> pdfTransformImageUrl;

    public String getMediaId() {
        return mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public byte[] getFileByte() {
        return fileByte;
    }

    public void setFileByte(byte[] fileByte) {
        this.fileByte = fileByte;
    }

    public String getFileSuffix() {
        return fileSuffix;
    }

    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<String> getPdfTransformImageUrl() {
        return pdfTransformImageUrl;
    }

    public void setPdfTransformImageUrl(List<String> pdfTransformImageUrl) {
        this.pdfTransformImageUrl = pdfTransformImageUrl;
    }
}