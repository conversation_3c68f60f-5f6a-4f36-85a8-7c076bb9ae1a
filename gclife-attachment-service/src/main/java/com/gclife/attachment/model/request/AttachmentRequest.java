package com.gclife.attachment.model.request;

import com.gclife.common.util.AssertUtils;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 附件上传接口请求参数
 */
public class AttachmentRequest {

    @ApiModelProperty(value = "文件内容", example = "iVBORw0KGgoAAAANSUhEUgAAAB4AAAAgCAIAAACKIl8oAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABgUlEQVRIic1WMa6CQBCd/WzA2GOrVCQ2VJzBGk9AYmPhSYweQk4AlXewsjb0FPTQOb/YH0Jmd9n5Jn7/q2CYebx5u7MgEBHeg6838bqpd7udlDJJEgDYbDa/ohY2Q4QQthqmh2bqCV7+CwyGVFXFEeWEQTVHssK08E/skO122/d9HMfjICLy58BsiLOek/OHhgghTqeTs6xtW+dq0744nTIzqeo0TT3Pc/Le73f3y1FDFEXG+ADf96cTFKyDPtEs0zTzDkFE2yrNZrMsy5y8PywAUJbl9XoFgKZphrlIkkRvExhWDJlA70eYz+fDo6IoiCYHddd1JKQOfnW93+91uiAI8jx/PB4qqKw3UKvUAYgYhuG48fV6PeY9Ho9E8vP5NDZhMERdXC4XKSUJosVrc3C5XA4PyIYFgPP5PK60WXy73QzUOFo6mxzO0q1WK1p7OBxIqK5rws7ZEnqCAO07pA/baxHWeY2MsdYh0TTTnC8vyVksFjThNUUcfO6f759SfwMXDbpcfrByZgAAAABJRU5ErkJggg==", required = true)
    private String fileContent;

    @ApiModelProperty(value = "文件名称", example = "11.png", required = true)
    private String fileName;

    @ApiModelProperty(value = "文件后缀", example = "png", required = true)
    private String fileSuffix;

    @ApiModelProperty(value = "文件大小", example = "123223")
    private String fileSize;

    @ApiModelProperty(value = "文件类型", example = "image/png")
    private String fileType;

    @ApiModelProperty(value = "文件位置", example = "gclife/travel/job/approve/{年月}/{年月日}-approve.xls")
    private String filePosition;

    @ApiModelProperty(value = "有效时常", example = "www.baidu.com", required = true)
    private Long expiresIn;

    @ApiModelProperty(value = "文件路径", example = "www.baidu.com", required = true)
    private String url;
    @ApiModelProperty(value = "attachmentId", example = "www.baidu.com", hidden = true)
    private String attachmentId;

    @ApiModelProperty(value = "是否29coi", example = "是否29coi", required = true)
    private String isCoi;

    @Override
    public String toString() {
        return "{" +
                "\"fileContent.length\":" + (AssertUtils.isNotEmpty(fileContent) ? fileContent.length() : 0) +
                ", \"fileName\":\"" + fileName + '\"' +
                ", \"fileSuffix\":\"" + fileSuffix + '\"' +
                ", \"fileSize\":\"" + fileSize + '\"' +
                ", \"fileType\":\"" + fileType + '\"' +
                ", \"expiresIn\":" + expiresIn +
                ", \"attachmentId\":" + attachmentId +
                '}';
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileContent() {
        return fileContent;
    }

    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileSuffix() {
        return fileSuffix;
    }

    public void setFileSuffix(String fileSuffix) {
        this.fileSuffix = fileSuffix;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    public String getFilePosition() {
        return filePosition;
    }

    public void setFilePosition(String filePosition) {
        this.filePosition = filePosition;
    }

    public String getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }
    public String getIsCoi() {
        return isCoi;
    }

    public void setIsCoi(String isCoi) {
        this.isCoi = isCoi;
    }
}