package com.gclife.attachment.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-16
 * description: 二维码附件生成请求
 */

public class QrCodeAttachmentRequest extends AttachmentRequest{

    @ApiModelProperty(value = "二维码文本",example = "www.baidu.com",required = true)
    private String codeText;
    @ApiModelProperty(value = "二维码文本",example = "www.baidu.com",required = true)
    private Integer size;

    public String getCodeText() {
        return codeText;
    }

    public void setCodeText(String codeText) {
        this.codeText = codeText;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
