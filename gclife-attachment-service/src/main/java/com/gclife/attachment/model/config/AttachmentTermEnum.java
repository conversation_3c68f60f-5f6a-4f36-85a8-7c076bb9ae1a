package com.gclife.attachment.model.config;

/**
 * <AUTHOR>
 * create 17-10-16
 * description: 术语枚举
 */
public class AttachmentTermEnum {

    private AttachmentTermEnum() {
        throw new AssertionError();
    }

    public interface ConstantType {
    }

    /**
     * 消息处理类型
     */
    public enum MESSAGE_PROCESSING_TYPE implements ConstantType {

        /**
         * PDF转图片
         */
        PDF2IMAGE("PDF转图片"),
        /**
         * 生成单证
         */
        GENERATE_DOCUMENTS("生成单证"),

        ;

        private String code;
        private String desc;

        private MESSAGE_PROCESSING_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 上传文件类型
     * 类型名称
     */
    public enum MEDIA_TYPE implements ConstantType {

        /**
         * 图片
         */
        IMAGE("图片"),

        /**
         * 语音
         */
        VOICE("语音"),
        DOCUMENT("文档"),

        /**
         * 缩略图
         */
        THUMB("缩略图"),
        VIDEO("视频"),
        /**
         * PDF转图片
         */
        PDF2IMAGE("PDF转图片"),

        ;

        private String code;
        private String desc;

        private MEDIA_TYPE(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 媒体后缀
     */
    public enum MEDIA_TYPE_SUFFIX implements ConstantType {

        /**
         * 扩展名为PNG的图片
         */
        PNG("png", "IMAGE"),

        /**
         * 扩展名为GIF的图片
         */
        GIF("gif", "IMAGE"),

        /**
         * 扩展名为JPG的图片
         */
        JPG("jpg", "IMAGE"),

        /**
         * 扩展名为BMP的图片
         */
        BMP("bmp", "IMAGE"),

        /**
         * 扩展名为MP4的视频
         */
        MP4("mp4", "VIDEO"),
        /**
         * 扩展名为DOCX的文档
         */
        DOCX("docx", "DOCUMENT"),

        /**
         * 扩展名为DOC的文档
         */
        DOC("doc", "DOCUMENT"),

        /**
         * 扩展名为PDF的文档
         */
        PDF("pdf", "DOCUMENT"),

        /**
         * 扩展名为PDF的文档
         */
        ZIP("zip", "DOCUMENT"),

        /**
         * 扩展名为RAR的文档
         */
        RAR("rar", "DOCUMENT"),

        /**
         * 扩展名为EXCEL的文档
         */
        EXCEL("excel", "DOCUMENT"),
        /**
         * 扩展名为EXCEL的文档
         */
        XLSX("xlsx", "DOCUMENT"),
        /**
         * 扩展名为APK的app
         */
        APK("apk", "APP"),

        /**
         * 扩展名为IPA的app
         */
        IPA("ipa", "APP"),

        /**
         * 扩展名为IPA的app
         */
        APP("app", "APP"),
        ;

        private String code;
        private String desc;
        private String group;

        private MEDIA_TYPE_SUFFIX(String desc, String group) {
            this.group = group;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }

        public String group() {
            return group;
        }
    }


    /**
     * 缩放类型
     */
    public enum IMAGE_ZOOM_TYPE implements ConstantType {

        /**
         * 等比缩放
         */
        ZOOM_ANALOGY("?x-oss-process=image/resize,p_", "等比缩放", "IMAGE"),

        /**
         * 固定长度缩放
         */
        ZOOM_FIXED_FRAME("?x-oss-process=image/resize,m_lfit,h_height,w_width", "固定长宽高缩放长边优先", "IMAGE"),
        ;

        private String code;
        private String desc;
        private String group;

        private IMAGE_ZOOM_TYPE(String code, String desc, String group) {
            this.code = code;
            this.group = group;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }

        public String group() {
            return group;
        }
    }


    /**
     * 图片缩放属性
     */
    public enum IMAGE_ZOOM_ATTRIBUTE implements ConstantType {

        /**
         * 图片高度
         */
        HEIGHT("height", "高"),

        /**
         * 图片缩放比例
         */
        PROPORTION("proportion", "缩放比例"),

        /**
         * 图片宽度
         */
        WIDTH("width", "宽");

        private String code;
        private String desc;

        private IMAGE_ZOOM_ATTRIBUTE(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 启用
     * 类型名称
     */
    public enum ENABLED implements ConstantType {

        /**
         * 可用
         */
        ENABLED("可用"),
        DISABLE("禁用");

        private String code;
        private String desc;

        private ENABLED(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 用途
     * 类型名称
     */
    public enum USAGE implements ConstantType {

        /**
         * 保单
         */
        USAGE_POLICY("USAGE_POLICY", "可用", "USAGE");

        private String code;
        private String desc;
        private String group;

        private USAGE(String code, String desc, String group) {
            this.code = code;
            this.desc = desc;
            this.group = group;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }

        public String group() {
            return group;
        }
    }

    /**
     * 性别
     */
    public enum GENDER implements ConstantType {

        MALE("男"),
        FEMALE("女"),
        ;

        private String desc;

        private GENDER(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }

    }

    /***
     * 证件类型
     */
    public enum ID_TYPE implements ConstantType {
        BIRTH_CERTIFICATE("出生证"),
        ID("身份证"),
        HOUSEHOLD_REGISTER("户口本"),
        HONG_KONG_MACAU_LAISSEZ_PASSER("港澳通行证"),
        PASSPORT("护照"),
        MILITARY_ID("军官证"),
        OTHER("其他");
        private String desc;

        private ID_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    public enum RELATIONSHIP_WITH_THE_INSURED implements ConstantType {
        SPOUSE("配偶"),
        PARENTS("父母"),
        CHILD("子女"),
        BROTHERS("兄弟"),
        SISTERS("姐妹"),
        OTHER("其他"),
        ONESELF("本人");

        private String desc;

        private RELATIONSHIP_WITH_THE_INSURED(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 缴费方式
     */
    public enum PRODUCT_PREMIUM_FREQUENCY implements ConstantType {
        YEAR("年缴"),
        SEMIANNUAL("半年缴"),
        SEASON("季缴"),
        MONTH("月缴"),
        SINGLE("趸缴"),
        ;
        private String desc;

        private PRODUCT_PREMIUM_FREQUENCY(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     *
     */
    public enum PREMIUM_FREQUENCY_CONVERSION_FACTOR implements ConstantType {
        YEAR(1.0),
        SEMIANNUAL(0.52),
        SEASON(0.27),
        MONTH(0.09),
        SINGLE(1.0),;

        private String code;
        private Double value;


        private PREMIUM_FREQUENCY_CONVERSION_FACTOR(Double value) {
            this.value = value;
        }

        public String code() {
            return code;
        }

        public Double value() {
            return value;
        }
    }
    /**
     * 缴费方式形式
     */

    public enum PAYMENT_METHODS implements ConstantType {
        BANK_TRANSFER("银行转账"),
        CHEQUE("支票"),
        CASH("现金支付"),
        BANK_DIRECT_DEBIT("银行代收"),
        ;

        private String desc;

        private PAYMENT_METHODS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }


    /**
     * 婚姻状况
     */

    public enum MARITAL_STATUS implements ConstantType {
        UNMARRIED("未婚"),
        MARRIED("已婚"),
        WIDOWED("丧偶"),
        DIVORCED("离婚"),
        ;

        private String desc;

        private MARITAL_STATUS(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 主附险标示
     */
    public enum PRODUCT_PRIMARY_FLAG implements ConstantType {

        MAIN("主险"),
        ADDITIONAL("附加险");

        private String code;
        private String desc;

        private PRODUCT_PRIMARY_FLAG(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 领取年龄及方式
     */
    public enum PENSION_RECEIVE_FREQUENCY implements ConstantType {


        MONTH("月领"),
        YEAR("年领"),
        SINGLE("一次性领取"),
        ;

        private String code;
        private String desc;

        private PENSION_RECEIVE_FREQUENCY(String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 事件业务类型
     */
    public enum BENEFICIARY_NO implements ConstantType {

        ORDER_ONE("1", "第一"),
        ORDER_TWO("2", "第二"),
        ORDER_THREE("3", "第三"),
        ;
        private String code;
        private String desc;

        BENEFICIARY_NO(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String code() {
            return code;
        }

        public String desc() {
            return desc;
        }
    }

    /**
     * 渠道类型
     */
    public enum CHANNEL_TYPE implements ConstantType {
        /**
         * 个险渠道
         */
        AGENT("个险渠道"),
        /**
         * 银保渠道
         */
        BANK("银保渠道"),
        /**
         * 中企渠道
         */
        GMCE("中企渠道"),
        /**
         * 中介渠道
         */
        BROKER("中介渠道"),
        /**
         * 第三方合作机构
         */
        CUSTOMS("第三方合作机构"),
        /**
         * 行政管理机构
         */
        MANAGER("行政管理机构"),
        /**
         * 合作伙伴
         */
        PARTNER("合作伙伴"),
        ;

        private String desc;

        CHANNEL_TYPE(String desc) {
            this.desc = desc;
        }

        public String desc() {
            return desc;
        }
    }

}
