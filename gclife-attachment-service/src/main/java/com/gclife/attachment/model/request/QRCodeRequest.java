package com.gclife.attachment.model.request;

import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * create 17-11-16
 * description: 二维码附件生成请求
 */

public class QRCodeRequest extends AttachmentRequest{

    @ApiModelProperty(value = "场景字符串",example = "www.baidu.com",required = true)
    private String sceneStr;

    @ApiModelProperty(value = "有效时常",example = "www.baidu.com",required = true)
    private Long expiresIn;

    public String getSceneStr() {
        return sceneStr;
    }

    public void setSceneStr(String sceneStr) {
        this.sceneStr = sceneStr;
    }

    public Long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Long expiresIn) {
        this.expiresIn = expiresIn;
    }
}
