package com.gclife.attachment.qrcode;

/**
 * <AUTHOR>
 * create 17-11-16
 * description:二维码生成接口
 */

public interface QrCodeService {

    /**
     * 生成二维码
     *
     * @param codeText 　二维码文本，一般指url链接
     * @param filePath 文件路径
     * @param fileType 文件后缀名如"png"
     * @param size 二维码像素
     *
     */
    void GenerateQrCode(String codeText, String filePath, String fileType, int size);

    /**
     * 生成二维码图片且返回二进制流
     *
     * @param codeText
     * @param fileType
     * @param size
     * @return
     */
    byte[] GenerateQrCode(String codeText, String fileType, int size);
}
