package com.gclife.attachment.qrcode.impl;

import com.gclife.attachment.model.config.AttachmentErrorConfigEnum;
import com.gclife.attachment.qrcode.QrCodeService;
import com.gclife.common.exception.RequestException;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.EnumMap;
import java.util.Map;

/**
 * <AUTHOR>
 * create 17-11-16
 * description: 二维码生成实现类
 */
@Service
public class QrCodeServiceImpl implements QrCodeService {


    /**
     * 生成二维码
     *
     * @param codeText 　二维码文本，一般指url链接
     * @param filePath 文件路径
     * @param fileType 文件后缀名如"png"
     * @param size 二维码像素
     *
     */
    @Override
    public void GenerateQrCode(String codeText, String filePath, String fileType, int size) {

        try {
            File dstFile = new File(filePath);
            BufferedImage image = getBufferedImage(codeText, size);
            ImageIO.write(image, fileType, dstFile);
        } catch (WriterException e) {
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
        } catch (IOException e) {
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
        }
    }

    /**
     * 生成二维码图片
     *
     * @param codeText
     * @param size
     * @return
     * @throws WriterException
     */
    private BufferedImage getBufferedImage(String codeText, int size) throws WriterException {
        Map<EncodeHintType, Object> hintMap = new EnumMap<>(EncodeHintType.class);
        hintMap.put(EncodeHintType.CHARACTER_SET, "UTF-8");

        // Now with zxing version 3.2.1 you could change border size (white border size to just 1)
        hintMap.put(EncodeHintType.MAX_SIZE, 1);
        hintMap.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);

        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix byteMatrix = qrCodeWriter.encode(codeText, BarcodeFormat.QR_CODE, size,
                size, hintMap);
        int width = byteMatrix.getWidth();
        BufferedImage image = new BufferedImage(width, width,
                BufferedImage.TYPE_INT_RGB);
        image.createGraphics();

        Graphics2D graphics = (Graphics2D) image.getGraphics();
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, width, width);
        graphics.setColor(Color.BLACK);

        for (int i = 0; i < width; i++) {
            for (int j = 0; j < width; j++) {
                if (byteMatrix.get(i, j)) {
                    graphics.fillRect(i, j, 1, 1);
                }
            }
        }
        return image;
    }

    @Override
    public byte[] GenerateQrCode(String codeText, String fileType, int size) {
        try {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            // 生成二维码图片
            BufferedImage image = getBufferedImage(codeText, size);
            ImageIO.write(image, fileType, byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } catch (WriterException | IOException e) {
            throw new RequestException(AttachmentErrorConfigEnum.ATTACHMENT_FAIL);
        }
    }
}
