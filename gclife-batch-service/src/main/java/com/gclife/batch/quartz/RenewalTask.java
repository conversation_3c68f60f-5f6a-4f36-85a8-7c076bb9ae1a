package com.gclife.batch.quartz;

import com.gclife.batch.model.config.BatchErrorConfigEnum;
import com.gclife.common.model.config.TerminologyConfigEnum;
import com.gclife.common.util.UUIDUtils;
import com.gclife.policy.api.PolicyApi;
import com.gclife.renewal.api.RenewalApi;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * Description: 续期定时任务
 * @date 20-8-6
 */
@Component
public class RenewalTask {
    private static final Logger LOGGER = LoggerFactory.getLogger(RenewalTask.class);

    @Autowired
    private PolicyApi policyApi;
    @Autowired
    private RenewalApi renewalApi;

    /**
     * 每天0点生成续期保单
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void renewalPolicyGenerateCheck() {
        String tempUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("renewalPolicyGenerateCheck -- start uuid: " + tempUUID);
            int currentPage = 1;
            String result = "";
            do {
                //请求微服务，获取数据
                result = policyApi.generateRenewalGrab(10000, currentPage);
                currentPage++;
                LOGGER.info("renewalPolicyGenerateCheck -- process uuid:" + tempUUID);

            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));
            LOGGER.info("renewalPolicyGenerateCheck -- end uuid:" + tempUUID);
        } catch (Exception e) {
            LOGGER.info("renewalPolicyGenerateCheck -- error uuid:" + tempUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_GENERATE_RENEWAL_GRAB_ERROR.getValue());
        }
    }

    /**
     * 每天6点扫描续期生成消息通知
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void renewalNotify() {
        String tempUUID = UUIDUtils.getUUIDShort();
        try {
            LOGGER.info("renewalNotify -- start uuid:" + tempUUID);
            int currentPage = 1;
            String result = "";
            do {
                //请求微服务，获取数据
                result = renewalApi.renewalNotify(10000, currentPage);
                currentPage++;
                LOGGER.info("renewalNotify -- process uuid:" + tempUUID);

            } while (TerminologyConfigEnum.WHETHER.NO.name().equals(result));
            LOGGER.info("renewalNotify -- end uuid:"+tempUUID);
        } catch (Exception e) {
            LOGGER.info("renewalNotify -- error uuid:" + tempUUID);
            LOGGER.error(ExceptionUtils.getFullStackTrace(e));
            LOGGER.error(BatchErrorConfigEnum.BATCH_GENERATE_RENEWAL_NOTIFY_ERROR.getValue());
        }
    }

}
